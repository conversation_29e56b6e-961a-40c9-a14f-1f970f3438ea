<%
const { utils, route, config, modelTypes } = it;
const { _, classNameCase, require } = utils;
const apiClassName = classNameCase(route.moduleName+"Class");
const routes = route.routes;
const dataContracts = _.map(modelTypes, "name");

%>

import axios from "../axios"

import { dataToQuery } from '../../utils/requestConcise'

import type { AxiosRequestConfig } from "axios"

import type { GxrcwResponseRegister } from '../../utils/requestConcise'

<% if (dataContracts.length) { %>
import { <%~ dataContracts.join(", ") %> ,baseUrl} from "./<%~ config.fileNames.dataContracts %>"
<% } %>

export class <%= apiClassName %> {

    <% routes.forEach((route) => { %>
        <%~ includeFile('./procedure-call.eta', { ...it, route }) %>
    <% }) %>
}
