
function detectBrowser() {

  var Sys = {};
  var ua = navigator.userAgent.toLowerCase();
  var s;

  (s = ua.match(/firefox\/([\d.]+)/)) ?
  (Sys.firefox = s[1]) :
  (s = ua.match(/chrome\/([\d.]+)/)) ?
  (Sys.chrome = s[1]) :
  (s = ua.match(/opera.([\d.]+)/)) ?
  (Sys.opera = s[1]) :
  (s = ua.match(/rv:([\d.]+)/)) ?
  (Sys.ie = s[1]) :
  (s = ua.match(/msie ([\d.]+)/)) ?
  (Sys.ie = s[1]) :
  (s = ua.match(/version\/([\d.]+).*safari/)) ?
  (Sys.safari = s[1]) :
  0;

  var browser = 'Unknown';
  var b = 0
  if (Sys.ie) {
    browser = 'IE';
  }
  if (Sys.firefox) {
    browser = 'Firefox';
    b = Sys.firefox
  }
  if (Sys.chrome) {
    browser = 'Chrome';
    b = Sys.chrome
  }
  if (Sys.opera) {
    browser = 'Opera';
  }
  if (Sys.safari) {
    browser = 'Safari';
    b = Sys.safari
  }
  return {
    browser: browser,
    b: b
  };
}
var info = detectBrowser()

if (info.browser === "IE" || info.browser === "Chrome" && (info.b).split('.')[0] < 55 || info.browser === "Firefox" && (info.b).split('.')[0] < 58 || info.browser === "Safari" && (info.b).split('.')[0] < 10) {
  var s = 5
  var p = document.createElement("p")
  var timeLock = setInterval(function () {

    p.innerHTML = "浏览器不支持即将跳转引导页面" + (s--) + "秒"
    document.body.appendChild(p)
  }, 1000)
  setTimeout(function () {
    window.clearInterval(timeLock)
    window.location.href = "https://image.gxrc.com/gxrcsite/vip/2021/remind-borwser.html"
  }, 5000)

}