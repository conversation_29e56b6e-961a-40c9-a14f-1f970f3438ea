import { Directive, DirectiveBinding } from 'vue';
import { sanitizeHtml, SanitizeLevel } from '@/utils/security';

interface SafeHtmlBinding extends DirectiveBinding {
  value: string | { content: string; level?: SanitizeLevel; config?: any };
}

/**
 * v-safe-html指令
 * 用法：
 * v-safe-html="htmlContent"
 * v-safe-html="{ content: htmlContent, level: 'relaxed' }"
 * v-safe-html.strict="htmlContent"
 * v-safe-html.relaxed="htmlContent"
 */
export const safeHtml: Directive = {
  mounted(el: HTMLElement, binding: SafeHtmlBinding) {
    updateElement(el, binding);
  },
  
  updated(el: HTMLElement, binding: SafeHtmlBinding) {
    updateElement(el, binding);
  }
};

function updateElement(el: HTMLElement, binding: SafeHtmlBinding) {
  let content = '';
  let level = SanitizeLevel.STRICT;
  let customConfig;

  // 处理不同的传值方式
  if (typeof binding.value === 'string') {
    content = binding.value;
  } else if (typeof binding.value === 'object' && binding.value !== null) {
    content = binding.value.content || '';
    level = binding.value.level || SanitizeLevel.STRICT;
    customConfig = binding.value.config;
  }

  // 处理修饰符
  if (binding.modifiers.strict) {
    level = SanitizeLevel.STRICT;
  } else if (binding.modifiers.relaxed) {
    level = SanitizeLevel.RELAXED;
  }

  // 清理并设置内容
  try {
    const safeContent = sanitizeHtml(content, level, customConfig);
    el.innerHTML = safeContent;
  } catch (error) {
    console.error('Safe HTML directive error:', error);
    // 出错时显示纯文本
    el.textContent = content;
  }
}

export default safeHtml; 