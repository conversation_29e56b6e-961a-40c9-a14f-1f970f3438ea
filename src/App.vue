<template>
  <div class="app-page">
    <router-view class="myCenter-View"></router-view>
    <div v-if="hideRoutes.includes((routeName?.toString()+ '').trim())"></div>
    <div v-else>
      <imFixedView v-if="routeName && !hideRoutes.includes((routeName?.toString()+ '').trim())" ref="imRef"></imFixedView>
    </div>
    <img class="qrcode" :class="{showr: showr}" v-if="!showImg" src="https://image.gxrc.com/gxrcsite/My/<EMAIL>" @click="showQr"/>
  </div>
</template>
<script lang="ts">
import { computed, defineComponent, provide, reactive, toRefs,ref,Ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import imFixedView from './IM/imFixedView.vue';
export default defineComponent({
  name: 'App',
  components: {
    imFixedView,
  },
  setup() {
    const state: any = reactive({});
    const route = useRoute();
    const routeName = computed(()=>route.name)

    const hideRoutes = ['login', 'register', 'imView','findpwd','qa','fillFixed','ServiceCenterGrantAuth','error'];
    
    // const showChat = computed(() => {
    //   return (
    //     hideRoutes.includes(route.name ? route.name.toString() : '')
    //   );
    // });
    const imRef = ref(null)
    provide("openChat", {
      open(id: number) {
        
        if (id > 0) {
          
          imRef.value?.sendChat(id)
        }
      }
    })

    const  showr = ref(false)
    const showQr = ()=>{
      showr.value = !showr.value
    }

    const showImg = ref(true)
    watch(()=>route.name,(val)=>{
      showImg.value =  ['login', 'register', 'imView','findpwd','qa','fillFixed','ServiceCenterGrantAuth','error','analysis'].includes(val)
    })
    return {
      ...toRefs(state),
      hideRoutes,
      routeName,
      imRef,
      showQr,
      showr,
      route,showImg
    };
  },
});
</script>

<style lang="less">
@import 'gxrcw-ui/style.css';
.loading_class_3_11 {
  .el-loading-spinner {
    i {
      font-size: 28px;
      color: #000;
      animation: rotating 2s linear infinite;
    }
  }
}
.qrcode {
    position: fixed;
    width: 220px;
    height: 327px;
    position: fixed;
    bottom: -210px;
    right: 55px;
    transition: all .3s linear;
}
.showr {
    bottom: 10px !important;
    transition: all .3s linear;
}
</style>
