import Cookies from 'js-cookie'
/*
* 设置cookies
* */
export function getCookies(key: string) {
    if(key === 'bid'){
        const value = Cookies.get(key)
        return !isNaN(Number(value)) && Number(value) >= 0 ? value : "0"
    }
    return Cookies.get(key)
}
/*
* 设置Cookies
* */
export function setCookies(key: string, value: any, expiresTime: number,domains: string) {
    let seconds = expiresTime
    let expires = new Date(new Date() * 1 + seconds * 1000)
    
    return Cookies.set(key, value, { expires: expires , path: '/', domain: domains})
}

/*
* 移除Cookies
* */
export function removeCookies(key: string) {
    return Cookies.remove(key)
}