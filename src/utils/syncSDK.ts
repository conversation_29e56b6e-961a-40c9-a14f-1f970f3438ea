export const baiduMap = () => {
  //动态引入百度地图js，一个页面尽量只调用一次，包括页面下的子组件如果能用到，在这个方法回调时再v-if
  // const AK = 'S8sAXjn9Td1YCKMietFY6wB5';
  const AK = 'W6XVdRhBNR9BFLmcPgHYsFTlPvuNIZTZ';

  const BMap_URL =
    'https://api.map.baidu.com/api?v=2.0&ak=' +
    AK +
    '&s=1&callback=onBMapCallback';

  return new Promise(function (resolve, reject) {
    // window.HOST_TYPE='2'
    // let script = document.createElement('script')
    // script.type = 'text/javascript'
    // script.src = "https://api.map.baidu.com/getscript?v=2.0&ak=S8sAXjn9Td1YCKMietFY6wB5&services=&t=20210225162129";
    // script.id = 'baiduMapId'
    // script.onerror = reject
    // document.head.appendChild(script)
    // script.onload = () => {
    //   resolve(true)
    // }
    let timeout = 0;
    let time: any = null;
    if (typeof BMap !== 'undefined') {
      resolve(BMap);
      return true;
    } else {
    }
    // 百度地图异步加载回调处理
    window.onBMapCallback = function () {
      // console.log('百度地图脚本初始化成功...');
      resolve(BMap);
    };
    // 插入script脚本
    let scriptNode = document.createElement('script');
    scriptNode.setAttribute('type', 'text/javascript');
    scriptNode.setAttribute('src', BMap_URL);
    document.body.appendChild(scriptNode);
    time = setInterval(() => {
      timeout++;
      if (timeout > 5) {
        clearInterval(time);
        reject(false);
      }
    }, 1000);
  });
};