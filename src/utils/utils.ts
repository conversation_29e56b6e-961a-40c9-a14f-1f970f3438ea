import {accountLogout } from "@/http/api";
import {ClickLogo } from "@/http/mApiUrl";
import { ElMessage } from 'element-plus';
import router from '../router';
import { getCookies } from "@/utils/common";

export function transElIconName(iconName){
    return 'i'+iconName.replace(/[A-Z]/g,(match)=>'-'+match.toLowerCase())
}

export function accountLogoutEvent(){
    accountLogout({"device":0,"devToken":""}).then((res:any)=>{
        if(res.code==1){
            localStorage.removeItem('gxrcToken');
            ElMessage.success(res.message);
            let bid =parseInt(getCookies("bid"));
            let url="/login"
            if (bid==1) {
              url="/gl/login"
            }
            if (bid==2) {
              url="/lz/login"
            }
            if (bid==4) {
              url="/wz/login"
            }
            if (bid==5) {
              url="/gp/login"
            }
            if (bid==6) {
              url="/bs/login"
            }
            if (bid==7) {
              url="/qz/login"
            }
            if (bid==8) {
              url="/hc/login"
            }
            if (bid==9) {
              url="/bh/login"
            }
            if (bid==11) {
              url="/fcg/login"
            }
            if (bid==12) {
              url="/yl/login"
            }
            if (bid==13) {
              url="/cz/login"
            }
            if (bid==14) {
              url="/gg/login"
            }
            if (bid==15) {
              url="/lb/login"
            }
            if (bid==18) {
              url="/hz/login"
            }
            if (bid==20) {
              url="/pn/login"
            }
            setTimeout(function(){
              router.push({path: url});

            },1000)
          }else{
            ElMessage.error(res.message);
          }
    })
}
//统计广告点击量
 export async function  ClickCount(form:any){
  const res: any = await ClickLogo(form);
}