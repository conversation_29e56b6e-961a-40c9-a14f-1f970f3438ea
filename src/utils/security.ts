import DOMPurify from 'dompurify';

// 严格模式配置 - 只允许基本文本格式化标签
const STRICT_CONFIG = {
  ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'span'],
  ALLOWED_ATTR: ['class'],
  KEEP_CONTENT: true,
  RETURN_DOM: false,
  RETURN_DOM_FRAGMENT: false,
  SANITIZE_DOM: true
};

// 宽松模式配置 - 允许更多标签但仍然安全
const RELAXED_CONFIG = {
  ALLOWED_TAGS: [
    'p', 'br', 'strong', 'em', 'u', 'span', 'div', 'h1', 'h2', 'h3', 
    'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'a', 'img', 'pre'
  ],
  ALLOWED_ATTR: [
    'class', 'href', 'title', 'alt', 'src', 'target'
  ],
  KEEP_CONTENT: true,
  RETURN_DOM: false,
  RETURN_DOM_FRAGMENT: false,
  SANITIZE_DOM: true,
  // 添加链接安全处理
  ADD_ATTR: { 'a': { 'rel': 'noopener noreferrer' } },
  // 处理target="_blank"安全问题
  FORBID_ATTR: ['onclick', 'onerror', 'onload', 'onmouseover']
};

export enum SanitizeLevel {
  STRICT = 'strict',
  RELAXED = 'relaxed',
  CUSTOM = 'custom'
}

/**
 * 清理HTML内容，防止XSS攻击
 * @param html - 需要清理的HTML字符串
 * @param level - 清理级别
 * @param customConfig - 自定义配置（当level为CUSTOM时使用）
 * @returns 清理后的安全HTML字符串
 */
export function sanitizeHtml(
  html: string, 
  level: SanitizeLevel = SanitizeLevel.STRICT,
  customConfig?: any
): string {
  if (!html || typeof html !== 'string') {
    return '';
  }

  let config;
  
  switch (level) {
    case SanitizeLevel.STRICT:
      config = STRICT_CONFIG;
      break;
    case SanitizeLevel.RELAXED:
      config = RELAXED_CONFIG;
      break;
    case SanitizeLevel.CUSTOM:
      config = customConfig || STRICT_CONFIG;
      break;
    default:
      config = STRICT_CONFIG;
  }

  try {
    return DOMPurify.sanitize(html, config);
  } catch (error) {
    console.error('HTML sanitization failed:', error);
    // 失败时返回纯文本
    return DOMPurify.sanitize(html, { ALLOWED_TAGS: [], KEEP_CONTENT: true });
  }
}

/**
 * 为聊天消息专门优化的清理函数
 * @param html - 聊天消息HTML
 * @returns 清理后的HTML
 */
export function sanitizeChatHtml(html: string): string {
  const chatConfig = {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'span'],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true,
    RETURN_DOM: false,
    SANITIZE_DOM: true
  };
  
  return sanitizeHtml(html, SanitizeLevel.CUSTOM, chatConfig);
}

/**
 * 为简历内容专门优化的清理函数
 * @param html - 简历内容HTML
 * @returns 清理后的HTML
 */
export function sanitizeResumeHtml(html: string): string {
  const resumeConfig = {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: ['class'],
    KEEP_CONTENT: true,
    RETURN_DOM: false,
    SANITIZE_DOM: true
  };
  
  return sanitizeHtml(html, SanitizeLevel.CUSTOM, resumeConfig);
} 