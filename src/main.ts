import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import ElementPlus from "element-plus";
import { Expand } from "@element-plus/icons";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import gxrcw from 'gxrcw-ui';
import "./assets/global.css";
// import gxrcw from 'gxrcw-ui';
import "./assets/gt.js";
import "element-plus/dist/index.css";
import { safeHtml } from '@/directives/safeHtml';
let host = "//www.gxrc.com";

const app = createApp(App);
app.use(ElementPlus, {
  locale: zhCn,
});
app.use(gxrcw)
app.use(router);
app.use(store);
// app.use(gxrcw)
app.component("expand", Expand);

// 注册安全HTML指令
app.directive('safe-html', safeHtml);

app.mount("#app");
