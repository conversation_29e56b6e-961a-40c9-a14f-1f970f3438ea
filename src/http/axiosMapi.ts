import instance from "./index"
const ENV = import.meta.env.VITE_APP_M_API;
const axios = ({
    method,
    url,
    data,
    config
}) => {
    method = method.toLowerCase();
    config = config;
    if (method == 'post') {
        // return instance.post(ENV+url, data, { ...config })
        // const safeUrl = encodeURIComponent(ENV) + encodeURIComponent(url);
        // return instance.post(safeUrl, data, Object.assign({}, {timeout: 5000}, config)); // 可以根据需要调整默认超时时间
        return instance.post(ENV + url, data, Object.assign({}, {timeout: 5000}, config));
    } else if (method == 'get') {
        return instance.get(ENV+url, {
            params: data,
            ...config
        })
    } else if (method == 'delete') {
        if (config.type == 1) {
            return instance.delete(ENV+url, {
                data,
                ...config
            })
        }
        else {
            return instance.delete(ENV+url, {
                params: data,
                ...config
            })
        }
    } else if (method == 'put') {
        return instance.put(ENV+url, data, { ...config })
    } else {
        console.error('未知的method' + method)
        return false
    }
}

export default axios;