import axios from './axios';
const home = 'http://************:5020';
const imhost = 'http://************:600';


//记录访问源
export const recordVisitInfoLog = (data:any) => {
    return axios({
        url: "/api/log/recordvisitinfolog",
        method: "post",
        data,
        config: {
            headers: {'Content-Type': 'application/json'},
            timeout: 10000
        }
    })
}
//用户信息---登录
export const accountLogin = (data:any) => {
    return axios({
        url: "/api/account/login",
        method: "post",
        data,
        config: {
            headers: {'Content-Type': 'application/json'},
            timeout: 10000
        }
    })
}
export const accountLogout = (data:any) => {
    return axios({
        url: "/api/account/logout",
        method: "post",
        data,
        config: {
            headers: {'Content-Type': 'application/json'},
            timeout: 10000
        }
    })
}
//获取图形验证码
export const getSecurityImageAsync = (data: any) => {
  return axios({
    url: '/api/account/captcha',
    method: 'get',
    data,
    config: {
      headers: {},
      timeout: 10000,
    },
  });
};


export const myInfo = () => {
    return axios({
        url: "/api/my/info",
        method: "get",
        data:{},
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
// 初始化极验
export const geetestInit = (data: any) => {
  return axios({
    url: '/api/verify/init',
    method: 'get',
    data,
    config: {
      headers: {},
      timeout: 10000,
    },
  });
};
//获取短信验证吗
export const geetestSendCode = (data: any) => {
  return axios({
    url: '/api/verify/sendcode',
    method: 'post',
    data,
    config: {
      headers: {'Content-Type': 'application/json'},
      timeout: 10000,
    },
  });
};

//手机验证码登录
export const loginByPhone = (data: any) => {
  return axios({
    url: '/api/account/loginbyphone',
    method: 'post',
    data,
    config: {
      headers: {'Content-Type': 'application/json'},
      timeout: 10000,
    },
  });
};

//首页
export const getIndexAdList = (data:any) => {
    return axios({
        url: "/api/my/indexadlist",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const getResumeList = (data:any) => {
    return axios({
        url: "/api/resume/resumelist",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const attachmentlist = (data:any) => {
    return axios({
        url: "/api/attachment/attachmentlist",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const attachmentPreview = (data:any) => {
    return axios({
        url: "/api/attachment/preview",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const refreshResume = (data:any) => {
    return axios({
        url: `/api/resume/refreshresume?resumeid=${data}`,
        method: "post",
        data,
        config: {
            headers: {'Content-Type': 'application/json'},
            timeout: 10000
        }
    })
}
export const updateWorkingState = (data:any) => {
    return axios({
        url: `/api/resumepart/updateworkingstate?resumeid=${data.resumeid}&state=${data.state}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const getCareer = (data:any) => {
    return axios({
        url: "/api/resumepart/career",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//没用
// export const getCareerInfo = (data:any) => {
//     return axios({
//         url: `home/api/User/GetCareerInfo`,
//         method: "get",
//         data,
//         config: {
//             headers: {},
//             timeout: 10000
//         }
//     })
// }

export const getNewsList = (data:any) => {
    return axios({
        url: `/api/my/indexnews`,
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const setDefaultResume = (data:any) => {
    return axios({
        url: `/api/resume/setdefaultresume?resumeid=${data}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//oss
export const ossSts = (data:any) => {
    return axios({
        url: `/api/oss/sts`,
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//上传成功回调
export const ossCallbackput = (data:any) => {
    return axios({
        url: `/api/oss/callbackput`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//删除成功回调
export const ossCallbackremoved = (data:any) => {
    return axios({
        url: `/api/oss/callbackremoved`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//上传接口1
export const ossUploadattachmentfile = (data:any) => {
    return axios({
        url: `/api/oss/uploadattachmentfile`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//删除附件
export const ossDelattachmentfile = (data:any) => {
    return axios({
        url: `/api/oss/delattachmentfile?id=${data}`,
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//上传接口2
export const ossUploadattachmentfilewithfilename = (data:any) => {
    return axios({
        url: `/api/oss/uploadattachmentfilewithfilename`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//投递记录
export const myDelivery = (data:any) => {
    return axios({
        url: "/api/my/delivery?" + Object.keys(data).map(key => key + "=" + data[key]).join('&'),
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const mySetDeliverTopResume = (data:any) => {
    return axios({
        url: `/api/my/setdelivertop?deliveryid=${data}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const myInterviewdetail = (data:any) => {
    return axios({
        url: "/api/my/interviewdetail",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const agreeInterview = (data:any) => {
    return axios({
        url: `chat/api/im/agreeinterview`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const refuseInterview = (data:any) => {
    return axios({
        url: `chat/api/im/refuseinterview`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//撤销投递
export const cancelapply = (data:any) => {
    return axios({
        url: `/api/my/cancelapply?deliverid=`+data,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//浏览记录
export const myViewHistory = (data:any) => {
    return axios({
        url: "/api/my/myviewhistory",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//职位收藏
export const myCollection = (data:any) => {
    return axios({
        url: "/api/my/mycollection",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//删除职位收藏
export const myDeleteCollection = (data:any) => {
    return axios({
        url: "/api/my/collection",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//删除过期职位
export const myOverdueCollection = (data:any) => {
    return axios({
        url: "/api/my/overduecollection",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//公司收藏列表
export const companyCollectionction = (data:any) => {
    return axios({
        url: "/api/my/companycollectionction",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//删除公司收藏
export const companyCollection = (data:any) => {
    return axios({
        url: "/api/my/companycollection",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}


//建档立卡
export const createFileStatus = (data:any) => {
    return axios({
        url: "/api/my/createfilestatus",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const createFileStatusSave = (data:any) => {
    return axios({
        url: `/api/my/createfilestatus?state=${data}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//修改密码身份 返回1  是已经有旧密码。返回2 是没有旧密码
export const changePassWordIdentity = (data:any) => {
    return axios({
        url: "/api/setting/changepasswordidentity",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//修改密码返回1时
export const changePassword = (data:any) => {
    return axios({
        url: "/api/setting/changepassword",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//修改密码返回2时
export const newChangePassword = (data:any) => {
    return axios({
        url: "/api/setting/setpassword",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

// 获取手机号带*号
export const getPhone = (data:any) => {
    return axios({
        url: "/api/setting/getphone",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//获取自己绑定手机的验证码
export const sendcodebyphone = (data:any) => {
    return axios({
        url: "/api/verify/sendcodebyphone",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}


//账号绑定
export const accountBind = (data:any) => {
    return axios({
        url: "/api/setting/accountbind",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//微信绑定
export const weixinBind = (data:any) => {
    return axios({
        url: "/api/setting/wxandbind?code="+data,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//微信解绑
export const weixinUNbind = (data:any) => {
    return axios({
        url: "/api/setting/wxunbind",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//职业测评
export const testPositiontestnumber = (data:any) => {
    return axios({
        url: "/api/testevaluation/positiontestnumber",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const testPositiontestreportstate = (data:any) => {
    return axios({
        url: "/api/testevaluation/positiontestreportstate",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const testPositiontestreport = (data:any) => {
    return axios({
        url: `/api/testevaluation/positiontestreport/${data}`,
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
export const testMytest = (data:any) => {
    return axios({
        url: "/api/testevaluation/mytest",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//我的订单
export const myOrderList = (data:any) => {
    return axios({
        url: "/api/order/list",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//找回密码--短信
export const phonePassword = (data: any) => {
  return axios({
    url: '/api/findpassword/phone',
    method: 'post',
    data,
    config: {
      headers: {},
      timeout: 10000,
    },
  });
};
//找回密码--邮箱
export const emailPassword = (data: any) => {
  return axios({
    url: '/api/findpassword/email',
    method: 'post',
    data,
    config: {
      headers: {'Content-Type': 'application/json'},
      timeout: 10000,
    },
  });
};

//获取验证码--邮箱
export const emailSendcode = (data: any) => {
  return axios({
    url: '/api/verify/sendemailcode',
    method: 'post',
    data,
    config: {
    headers: {'Content-Type': 'application/json'},
      timeout: 10000,
    },
  });
};
//验证手机是否被注册
export const isregister = (data: any) => {
  return axios({
    url: '/api/verify/isregister',
    method: 'get',
    data,
    config: {
      headers: {},
      timeout: 10000,
    },
  });
};

//获取微信 state,是否被绑定
export const oauthweixin = (data: any) => {
  return axios({
    url: '/api/oauth/oauthweixin?code=' + data,
    method: 'post',
    data,
    config: {
      headers: {},
      timeout: 10000,
    },
  });
};
//获取微信 头像名称id---没绑定情况下
export const getwxinfo = (id: string, data: any) => {
  return axios({
    url: `/api/oauth/wxinfo/${id}`,
    method: 'get',
    data,
    config: {
      headers: {},
      timeout: 10000,
    },
  });
};
//获取登录页横幅广告
export const getAdlist = (data: any) => {
  return axios({
    url: '/api/ad/adlist',
    method: 'get',
    data,
    config: {
      headers: {},
      timeout: 10000,
    },
  });
};
//二维码登录
//获取key
export const randKey = (data: any) => {
  return axios({
    url: '/api/qrcode/randkey',
    method: 'get',
    data,
    config: {
      headers: {},
      timeout: 10000,
    },
  });
};
// 获取二维码图片
export const getqrcode = (data: any) => {
  return axios({
    url: '/api/qrcode/getqrcode',
    method: 'get',
    data,
    config: {
      headers: {},
      timeout: 10000,
    },
  });
};

// 检测二维码扫描状态
export const  checksScan = (data:any) => {
    return axios({
        url:`/api/qrcode/scan?tokenid=`+data.tokenid+`&siteid=`+data.siteid,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//谁看过我---新消息
export const NewViewMe= (data: any) => {
    return axios({
        url: "/api/my/enterprisevisitfornew",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//获取注册状态
export const registerstep= (data: any) => {
    return axios({
        url: "/api/my/registerstep",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//举报企业
export const report = (data: any) => {
  return axios({
      url: "/api/my/feedback",
      method: "post",
      data,
      config: {
          headers: {},
          timeout: 10000
      }
  })
}
//获取竞争力分析次数
export const deliverdetailcompetepower= (data: any) => {
    return axios({
        url: "/api/my/deliverdetailcompetepower",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//使用一次竞争力分析
export const competionservice= (data: any) => {
    return axios({
        url: `/api/competionservice/buy?positionguid=${data}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}


export const getMyServices= (data: any) => {
    return axios({
        url: `/api/my/services?type=${data}`,
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取网关配置
export const GetWlxtSettingByDistrictId = (data: any) => {
    return axios({
        url: "/home/<USER>/Data/GetWlxtSettingByDistrictId",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//网络授权
export const Authpageinfo = (data: any) => {
    return axios({
        url: "/api/ServiceCenter/authpageinfo",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//网络授权
export const ServiceCenter = (data: any) => {
    return axios({
        url: "/api/ServiceCenter/grantauth",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
