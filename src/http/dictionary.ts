import axios from "../http/axios"
//配置选项类 接口
//  获取民族选项
export const getNationoptions = (data: any) => {
    return axios({
        url: "/api/options/nationoptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取国籍选项
export const getNationalityoptions = (data: any) => {
    return axios({
        url: "/api/options/nationalityoptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取政治面貌
export const getPoliticalstatus = (data: any) => {
    return axios({
        url: "/api/options/politicalstatus",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取工作状态
export const getWorkstatuoptions = (data: any) => {
    return axios({
        url: "/api/options/workstatuoptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取目前所在地---city--籍贯
export const getCity = (data: any) => {
    return axios({
        url: "/api/options/district",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取职能
export const getPosition = (data: any) => {
    return axios({
        url: "/api/options/position",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取行业
export const getIndustry = (data: any) => {
    return axios({
        url: "/api/options/industry",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//  获取公司性质
export const getEntpropertyoptions = (data: any) => {
    return axios({
        url: "/api/options/entpropertyoptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取学历
export const getDegreeoptions = (data: any) => {
    return axios({
        url: "/api/options/degreeoptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取专业类别
export const getMajorResource = (data: any) => {
    return axios({
        url: "/api/options/specialty",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取工作方式
export const getWorkpropertyoptions = (data: any) => {
    return axios({
        url: "/api/options/workpropertyoptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取公司规模
export const getEmployeenumberoptions = (data: any) => {
    return axios({
        url: "/api/options/employeenumberoptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//  获取职位等级
export const getPositionleveloptions = (data: any) => {
    return axios({
        url: "/api/options/positionleveloptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取项目经历参与身份选项
export const getPlayroleitems = (data: any) => {
    return axios({
        url: "/api/resumepart/playroleitems",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取证书类别--字典
export const getCertificatelist = (data: any) => {
    return axios({
        url: "/api/options/certificatelist",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取证书等级
export const getCertificatetypelevels = (data: any) => {
    return axios({
        url: "/api/options/certificatetypelevels",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//  获取语言技能字典
export const getLanguageoptions = (data: any) => {
    return axios({
        url: "/api/options/languageoptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取英语水平能力
export const getEnglishoptions = (data: any) => {
    return axios({
        url: "/api/options/englishoptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取非英语水平能力
export const getOtherlanguageleveloptions = (data: any) => {
    return axios({
        url: "/api/options/otherlanguageleveloptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//  获取驾照
export const getDrivingoptions = (data: any) => {
    return axios({
        url: "/api/options/drivingoptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取电脑水平
export const getComputeroptions = (data: any) => {
    return axios({
        url: "/api/options/computeroptions",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取技术
export const getTechnologylevel = (data: any) => {
    return axios({
        url: "/api/options/technologylevel",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//  获取工作技能标签
export const positionantistops = (data: any) => {
    return axios({
        url: "/api/options/positionantistops",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}