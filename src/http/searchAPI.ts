import axios from "../http/axios"
// 搜索 接口

//  搜索学校名称
export const searchcollege = (data: any) => {
    return axios({
        url: `/api/autocompelete/searchcollege?keyword=${encodeURIComponent(data)}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  搜索企业名称
export const searchEnterpriseName = (data: any) => {
    return axios({
        url: `/api/autocompelete/searchenterprisename?keyword=${encodeURIComponent(data)}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  搜索专业
export const searchmajor = (data: any) => {
    return axios({
        url: `/api/autocompelete/searchmajor?keyword=${encodeURIComponent(data)}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  搜索职位
export const searchPosition = (data: any) => {
    return axios({
        url: `/api/autocompelete/searchposition?keyword=${encodeURIComponent(data)}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//搜索行业--3级
export const searchIndustry = (data: any) => {
    return axios({
        url: `/api/autocompelete/searchindustry?keyword=${encodeURIComponent(data)}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//搜索行业-2级
export const searchsecondindustry = (data: any) => {
    return axios({
        url: `/api/autocompelete/searchsecondindustry?keyword=${encodeURIComponent(data)}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//获取公司名称列表--屏蔽公司页面
export const findEnterprise = (data: any) => {
    return axios({
        url: "/home/<USER>/Enterprise/SearchForMy",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}