import axios from 'axios';
import router from '../router';
import { ElLoading, ElMessage } from 'element-plus';
import store from '../store';
import { sanitizeHtml, SanitizeLevel } from '@/utils/security';

axios.defaults.withCredentials = true
var instance = axios;
let loading: any;

let requestCount = 0;

// 请求数据XSS安全配置
const XSS_REQUEST_SECURITY_CONFIG = {
  // 需要进行XSS清理的字段名模式（支持正则表达式）
  cleanFieldPatterns: [
    /content$/i,
    /description$/i,
    /.*desc$/i,
    /.*detail$/i,
    /message$/i,
    /comment$/i,
    /text$/i,
    /remark$/i,
    /intro$/i,
    /summary$/i,
    /note$/i,
    /announcement$/i,
    /positiondescription$/i,
    /jobperformance$/i,
    /projectdescription$/i,
    /trainingdescription$/i,
    /descontent$/i,
    /opinecontent$/i,
    /replycontent$/i,
    /reason$/i,
    /feedback$/i,
    /review$/i,
    /workexperience$/i,
    /education$/i,
    /skill$/i,
    /hobby$/i,
    /selfintroduction$/i,
    /careergoal$/i
  ],
  
  // 需要处理的HTTP方法
  processedMethods: ['POST', 'PUT', 'PATCH'],
  
  // 跳过XSS清理的API路径模式
  skipPaths: [
    '/api/upload',
    '/api/download',
    '/api/export',
    '/api/import',
    '/oauth',
    '/captcha',
    '/api/file',
    '/login',
    '/register'
  ],
  
  // 默认清理级别
  defaultLevel: SanitizeLevel.STRICT,
  
  // 使用宽松模式的字段模式
  relaxedFieldPatterns: [
    /.*description$/i,
    /.*content$/i,
    /jobperformance$/i,
    /selfintroduction$/i,
    /workexperience$/i,
    /projectdescription$/i,
    /trainingdescription$/i
  ]
};

/**
 * 检查字段名是否需要进行XSS清理
 * @param fieldName - 字段名
 * @returns boolean
 */
const shouldCleanRequestField = (fieldName: string): boolean => {
  return XSS_REQUEST_SECURITY_CONFIG.cleanFieldPatterns.some(pattern => 
    pattern.test(fieldName)
  );
};

/**
 * 检查字段是否应该使用宽松模式清理
 * @param fieldName - 字段名
 * @returns boolean
 */
const shouldUseRelaxedModeForRequest = (fieldName: string): boolean => {
  return XSS_REQUEST_SECURITY_CONFIG.relaxedFieldPatterns.some(pattern => 
    pattern.test(fieldName)
  );
};

/**
 * 检查请求是否需要进行XSS清理
 * @param method - HTTP方法
 * @param url - API路径
 * @returns boolean
 */
const shouldProcessRequest = (method: string, url: string): boolean => {
  // 检查HTTP方法
  if (!XSS_REQUEST_SECURITY_CONFIG.processedMethods.includes(method.toUpperCase())) {
    return false;
  }
  
  // 检查是否在跳过列表中
  return !XSS_REQUEST_SECURITY_CONFIG.skipPaths.some(path => 
    url.toLowerCase().includes(path.toLowerCase())
  );
};

/**
 * 深度清理请求数据中的HTML内容
 * @param data - 需要清理的数据
 * @param maxDepth - 最大递归深度，防止循环引用
 * @returns 清理后的数据
 */
const cleanRequestData = (data: any, maxDepth: number = 10): any => {
  // 防止无限递归
  if (maxDepth <= 0 || data === null || data === undefined) {
    return data;
  }

  // 处理基本类型
  if (typeof data !== 'object') {
    return data;
  }

  // 处理数组
  if (Array.isArray(data)) {
    return data.map(item => cleanRequestData(item, maxDepth - 1));
  }

  // 处理对象
  const cleaned: any = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string' && shouldCleanRequestField(key)) {
      try {
        // 根据字段类型选择清理级别
        const level = shouldUseRelaxedModeForRequest(key) 
          ? SanitizeLevel.RELAXED 
          : XSS_REQUEST_SECURITY_CONFIG.defaultLevel;
          
        cleaned[key] = sanitizeHtml(value, level);
        
        // 在开发环境下记录清理操作
        if (process.env.NODE_ENV === 'development' && value !== cleaned[key]) {
          console.warn(`[Request XSS Security] Field "${key}" was sanitized:`, {
            original: value.substring(0, 100) + (value.length > 100 ? '...' : ''),
            cleaned: cleaned[key].substring(0, 100) + (cleaned[key].length > 100 ? '...' : '')
          });
        }
      } catch (error) {
        console.error(`[Request XSS Security] Error sanitizing field "${key}":`, error);
        // 发生错误时保持原值，避免破坏业务逻辑
        cleaned[key] = value;
      }
    } else if (typeof value === 'object') {
      // 递归处理嵌套对象
      cleaned[key] = cleanRequestData(value, maxDepth - 1);
    } else {
      // 保持其他类型的值不变
      cleaned[key] = value;
    }
  }

  return cleaned;
};

/**
 * 清理FormData中的HTML内容
 * @param formData - FormData对象
 * @returns 清理后的FormData
 */
const cleanFormData = (formData: FormData): FormData => {
  const cleanedFormData = new FormData();
  
  for (const [key, value] of formData.entries()) {
    if (typeof value === 'string' && shouldCleanRequestField(key)) {
      try {
        const level = shouldUseRelaxedModeForRequest(key) 
          ? SanitizeLevel.RELAXED 
          : XSS_REQUEST_SECURITY_CONFIG.defaultLevel;
          
        const cleanedValue = sanitizeHtml(value, level);
        cleanedFormData.append(key, cleanedValue);
        
        // 在开发环境下记录清理操作
        if (process.env.NODE_ENV === 'development' && value !== cleanedValue) {
          console.warn(`[Request XSS Security] FormData field "${key}" was sanitized`);
        }
      } catch (error) {
        console.error(`[Request XSS Security] Error sanitizing FormData field "${key}":`, error);
        cleanedFormData.append(key, value);
      }
    } else {
      cleanedFormData.append(key, value);
    }
  }
  
  return cleanedFormData;
};

const showLoading = () => {
  if (requestCount === 0 && !loading) {
    loading = ElLoading.service({
      text: 'Loading  ',
      background: 'rgba(0, 0, 0, 0.7)',
      spinner: 'el-icon-loading',
    });
  }
  requestCount++;
};

const hideLoading = () => {
  requestCount--;
  if (requestCount == 0) {
    loading.close();
  }
};

let fullPath = '';
router.beforeEach(async (to, from) => {
  //先上im-----不上了--2022-8-24
  // if(to.name&&["fillFixed","fillView",'certified','corporate','contractManagement','sealManagement','signContract','signedInspection'].includes(to.name.toString())){
  //   fullPath = to.fullPath;
  //   next();
  // }else{

  // }
  if (fullPath == '') {
    fullPath = to.fullPath;
  }

  const isLogin = Object.keys(store.getters.userInfo).length > 0
  const skipUrl = ['fillfixed','login','findpwd','oauth/login','oauth/callback','servicecentergrantAuth']
  const white = skipUrl.some(item=> to.fullPath.toLocaleLowerCase().includes(item))
  
  if(!isLogin && !white){
    const loadingInstance = ElLoading.service({text:'正在加载中'})
    let res = await store.dispatch('getUserInfo')
    loadingInstance.close()
    if(Object.keys(res).length == 0){
      if(to.fullPath.toLocaleLowerCase().includes('login')){
        return { path: to.fullPath,replace: true}
      }else{
        return { path: '/login',query:{returnUrl: to.fullPath},replace: true}
      }
    }
  }
  
 
});
router.afterEach((to:any)=>{
  if(to.meta.title){
    document.title = `广西人才网-${to.meta.title}`
  }
})


instance.interceptors.request.use(
  (config) => {
    // showLoading() //不要在拦截器里面直接加loading 不需要显示loading的请求无法直接控制
    const token = window.localStorage.getItem('gxrcToken');
    token && (config.headers.Authorization = `Bearer ${token}`);

    // XSS安全检查和清理用户提交数据
    if (config.method && config.url && shouldProcessRequest(config.method, config.url)) {
      try {
        if (config.data instanceof FormData) {
          // 处理FormData
          config.data = cleanFormData(config.data);
        } else if (config.data && typeof config.data === 'object') {
          // 处理普通对象数据
          config.data = cleanRequestData(config.data);
        }
      } catch (error) {
        console.error('[Request XSS Security] Error during request data sanitization:', error);
        // 发生错误时不影响正常请求，但记录错误
      }
    }

    if (config.method === 'POST' && config.headers['Content-Type'] === 'application/json') {
      config.data = JSON.stringify(config.data);
    }
    return config;
  },
  (error) => Promise.reject(error)
);

instance.interceptors.response.use(
  (response) => {
    hideLoading();
    return response.data;
  },
  async (error) => {
    let message;
    if (error.response && error.response.status) {
      const status = error.response.status;
      let flag = true
      switch (status) {
        case 400:
          message = '请求错误';
          break;
        case 401:
          hideLoading();
            if(!(document.location.pathname=='/')&&!(document.location.pathname.toLowerCase().includes('login'))&&!(document.location.pathname=='/findpwd')&&!(document.location.pathname=='/oAuth/Login')&&!(document.location.pathname=='/oAuth/Callback')&&!(document.location.pathname=='/ServiceCenterGrantAuth')){
              message = '登录信息失效，请重新登录';
            }else{
              flag = false
            }
            if (window.location.href.toLowerCase().indexOf('/fillfixed') > -1) {
              store.commit('updateLoginStatus', false);
              return;
            }
            const path = fullPath?.toLowerCase()

            if(path.includes('login')){
              await router.replace({
                path
              })
            }else{
              await router.replace({
                path:'/login',
                query: { returnUrl: path },
              })
            }
            return
            router.push({
              path: '/login',
              query: { redirect: fullPath },
            });
          break;
        case 404:
          message = '请求地址出错';
          break;
        case 408:
          message = '请求超时';
          break;
        case 500:
          message = '服务器内部错误!';
          break;
        case 501:
          message = '服务未实现!';
          break;
        case 502:
          message = '网关错误!';
          break;
        case 503:
          message = '服务不可用!';
          break;
        case 504:
          message = '网关超时!';
          break;
        case 505:
          message = 'HTTP版本不受支持';
          break;
        default:
          message = '请求失败';
      }
      if (window.location.href.indexOf('/fillFixed') > -1) {
        message = ''
      }
      flag && message && ElMessage.error(message);
      return Promise.reject(error);
    }
    return Promise.reject(error);
  }
);

export default instance;