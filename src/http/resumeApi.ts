import axios from "../http/axios"


//获取简历列表
export const resumelist = (data: any) => {
    return axios({
        url: "/api/resume/resumelist",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//获取简历详情
export const resumeinfo = (data: any) => {
    return axios({
        url: "/api/resume/resumeinfo",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取个人信息
export const baseinfo = (id: number | any, data: any) => {
    return axios({
        url: "/api/resumepart/baseinfo/" + id,
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  保存个人信息 

export const saveBaseinfo = (data: object) => {
    return axios({
        url: "/api/resumepart/savebaseinfo",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取我的优势
export const getResumetags = (id:any,data: any) => {
    return axios({
        url: "/api/resumepart/resumetags/"+id,
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  保存优势标签

export const saveResumetags = (data: object) => {
    return axios({
        url: "/api/resumepart/saveresumetag",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  删除 优势标签

export const removeResumetags = (data: object) => {
    return axios({
        url: "/api/resumepart/resumetag",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
            type:1
        }
    })
}

//  获取我的求职意向
export const getCareer = (data: any) => {
    return axios({
        url: "/api/resumepart/career",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  保存求职意向--旧
// export const saveCareer = (data: object) => {
//     return axios({
//         url: "/api/resumepart/savecareer",
//         method: "post",
//         data,
//         config: {
//             headers: {},
//             timeout: 10000
//         }
//     })
// }
//  保存求职意向--z新
export const saveCareer = (data: object) => {
    return axios({
        url: "/api/resumepart/savecareernew",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  单独保存求职意向--z新
export const savecareerandindustry = (data: object) => {
    return axios({
        url: "/api/register/savecareerandindustry",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
// 单独删除求职意向
export const delcareer = (data: object) => {
    return axios({
        url: "/api/register/delcareer",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000,
            type:1
        }
    })
}




//  获取我的工作经历
export const getWork = (data: any) => {
    return axios({
        url: "/api/resumepart/workinfo",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  保存工作
export const saveWork  = (data: any) => {
    return axios({
        url: "/api/resumepart/saveworkinfo",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  删除工作经历
export const deleteWork = (data: object) => {
    return axios({
        url: "/api/resumepart/workinfo",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
            
        }
    })
}
//从默认简历复制
export const postClonefromresume  = (data: string,type:string) => {
    return axios({
        url: '/api/resumepart/clonefromresume?resumeid='+ data+'&clonetype='+type,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000,
            type:2
        }
    })
}

//  获取教育经历
export const getEducation= (data: any) => {
    return axios({
        url: "/api/resumepart/education",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  删除教育经历
export const deleteEducation = (data: object) => {
    return axios({
        url: "/api/resumepart/education",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
            
        }
    })
}
//  保存教育
export const saveEducation  = (data: any) => {
    return axios({
        url: "/api/resumepart/educationsavve",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  删除教育实践项目经历
export const edupractice = (data: object) => {
    return axios({
        url: "/api/resumepart/edupractice",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
            
        }
    })
}

//  获取项目经历
export const getProject= (data: any) => {
    return axios({
        url: "/api/resumepart/project",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  删除项目经历
export const deleteProject = (data: object) => {
    return axios({
        url: "/api/resumepart/project",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
            
        }
    })
}
//  保存项目经历
export const saveProject  = (data: any) => {
    return axios({
        url: "/api/resumepart/saveproject",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//  获取培训经历
export const getTrain= (data: any) => {
    return axios({
        url: "/api/resumepart/train",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  删除培训经历
export const deleteTrain= (data: object) => {
    return axios({
        url: "/api/resumepart/train",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
            
        }
    })
}
//  保存培训经历
export const saveTrain  = (data: any) => {
    return axios({
        url: "/api/resumepart/savetrain",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//  获取技术经历
export const getTechnology= (data: any) => {
    return axios({
        url: "/api/resumepart/technology",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  删除技术经历
export const deleteTechnology= (data: object) => {
    return axios({
        url: "/api/resumepart/technology",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
            
        }
    })
}
//  保存技术经历
export const saveTechnology = (data: any) => {
    return axios({
        url: "/api/resumepart/savetechnology",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取证书经历
export const getCertificate= (data: any) => {
    return axios({
        url: "/api/resumepart/certificate",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  删除证书经历
export const deleteCertificate= (data: object) => {
    return axios({
        url: "/api/resumepart/certificate",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
            
        }
    })
}
//  保存证书经历
export const saveCertificate = (data: any) => {
    return axios({
        url: "/api/resumepart/savecertificate",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//  获取语言
export const getLanguage= (data: any) => {
    return axios({
        url: "/api/resumepart/language",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  删除语言
export const deleteLanguage= (data: object) => {
    return axios({
        url: "/api/resumepart/language",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
            
        }
    })
}
//  保存语言
export const saveLanguage = (data: any) => {
    return axios({
        url: "/api/resumepart/savelanguage",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取个人描述
export const getDescription= (data: any) => {
    return axios({
        url: "/api/resumepart/description",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  删除个人描述
export const deleteDescription= (data: object) => {
    return axios({
        url: "/api/resumepart/description",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
            
        }
    })
}
//  保存个人描述
export const saveDescription = (data: any) => {
    return axios({
        url: "/api/resumepart/savedescription",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

// 获取其他能力
export const getOtherability= (data: any) => {
    return axios({
        url: "/api/resumepart/otherability",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

// 修改其他能力
export const saveOtherability = (data: any) => {
    return axios({
        url: "/api/resumepart/saveotherability",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//  获取个人简历各项完成度
export const getCompletestate= (id:number,data: null,) => {
    return axios({
        url: "/api/resumepart/completestate/"+id,
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//  获取注册的基本信息
export const registerbaseinfo= (data: any) => {
    return axios({
        url: "/api/register/registerbaseinfo",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  保存注册的基本信息
export const saveregisterbaseinfo= (data: any) => {
    return axios({
        url: "/api/register/saveregisterbaseinfo",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//  获取注册的教育信息
export const registereduinfo= (data: any) => {
    return axios({
        url: "/api/register/registereduinfo",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  保存注册的教育信息
export const saveregistereduinfo= (data: any) => {
    return axios({
        url: "/api/register/saveregistereduinfo",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取注册的工作信息
export const registerworkinfo= (data: any) => {
    return axios({
        url: "/api/register/registerworkinfo",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  保存注册的工作信息
export const saveregisterworkinfo= (data: any) => {
    return axios({
        url: "/api/register/saveregisterworkinfo",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  获取注册的工作信息
export const registercareerinfo= (data: any) => {
    return axios({
        url: "/api/register/registercareerinfo",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//  保存注册的工作信息
export const saveregistercareerinfo= (data: any) => {
    return axios({
        url: "/api/register/saveregistercareerinfo",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//隐私设置--经历公开方式
export const setprivate= (data: any) => {
    return axios({
        url: "/api/resume/setprivacysetting",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//删除简历
export const deleteReusme= (data: any) => {
    return axios({
        url: '/api/resume/resumedelete',
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
        }
    })
}
//新增简历
export const addResume= (data: any) => {
    return axios({
        url:'/api/resume/resumeadd?from='+data,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//修改简历名称
export const resumeNameSave= (data: any) => {
    return axios({
        url: "/api/resume/resumerename",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//谁看过我
export const getViewMe= (data: any) => {
    return axios({
        url: "/api/my/enterprisevisit",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//公司屏蔽
export const getHideEnterprise= (data: any) => {
    return axios({
        url: "/api/my/shieldlist",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//取消屏蔽
export const deleteEnterprise= (data: any) => {
    return axios({
        url: "/api/my/shield",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000,
            type:1
        }
    })
}
//保存屏蔽企业
export const hideEnterprise= (data: any) => {
    return axios({
        url: "/api/my/shieldent",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000,
        }
    })
}
//获取形象照
export const getphoto= (data: any) => {
    return axios({
        url: "/api/photo/avatar",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//上传头像
export const uploadHeadPhoto= (data: any) => {
    return axios({
        url: "/api/photo/uploadavatar",
        method: "post",
        data,
        config: {
            headers: {
                "Content-Type":"multipart/form-data"
            },
            timeout: 10000,
        }
    })
}
//删除头像
export const deletHeadPhoto= (data: any) => {
    return axios({
        url: "/api/photo/avatardelete",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

//获取形象照
export const getAlbumlPhoto= (data: any) => {
    return axios({
        url: "/api/photo/albumlist",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//删除形象照
export const deletedAlbumlPhoto= (data: any) => {
    return axios({
        url: "/api/photo/albumdelete",
        method: "delete",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//形象照排序
export const AlbumSortphoto= (data: any) => {
    return axios({
        url: "/api/photo/albumsort",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000,
        }
    })
}
//上传头像
export const uploadalBumphoto= (data: any) => {
    return axios({
        url: "/api/photo/uploadalbum",
        method: "post",
        data,
        config: {
            headers: {
                "Content-Type":"multipart/form-data"
            },
            timeout: 10000,
        }
    })
}
//简历下载
export const ResumeDoc= (data: any) => {
    return axios({
        url: "/api/DownLoad/ResumeDoc",
        method: "get",
        data,
        config: {
            headers: { "Content-Type": "application/msword"},
            responseType: "blob",

            timeout: 10000,
        }
    })
}
//简历下载
export const ResumePdf= (data: any) => {
    return axios({
        url: "/api/DownLoad/ResumePdf",
        method: "get",
        data,
        config: {
            headers: { "Content-Type": "application/msword"},
            responseType: "blob",

            timeout: 10000,
        }
    })
}
//修改绑定手机
export const changephonebind= (data: any) => {
    return axios({
        url: "/api/setting/changephonebind",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000,
        }
    })
}
//意见反馈列表
export const feedbacklist= (data: any) => {
    return axios({
        url: "/api/my/feedbacklist",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000,
        }
    })
}
//意见反馈--提交
export const feedback= (data: any) => {
    return axios({
        url: "/api/qa/feedback",
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000,
        }
    })
}

//意见反馈获取职位名称和公司名称
export const feedbackID= (data: any) => {
    return axios({
        url: "/api/qa/feedback",
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000,
        }
    })
}
//获取是否确认过求职意向
export const affirmjobintension= (data: any) => {
    return axios({
        url: `/api/resume/affirmjobintension/${data}`,
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000,
        }
    })
}
//获取是否确认过求职意向
export const recommendindustrydata= (id:number,type:boolean,data: null,) => {
    return axios({
        url: `/api/register/recommendindustrydata/${id}/${type}`,
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000,
        }
    })
}
