//用到的 m接口

import axios from './axios';

//获取职位推荐
export const getPositionRecommend = (data:any) => {
    return axios({
        url: `/home/<USER>/Position/RecommendList`,
        method: "get",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//获取职位推荐-2025-4-24改版
export const getPositionRecommendByCaree = (data:any) => {
    return axios({
        url: `/home/<USER>/Position/RecommendByCaree`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}
//统计广告点击量
export const ClickLogo = (data: any) => {
    return axios({
        url: `/home/<USER>/Logo/Click?logoID=${data.logoID}&from=${data.from}`,
        method: "post",
        data,
        config: {
            headers: {},
            timeout: 10000
        }
    })
}



