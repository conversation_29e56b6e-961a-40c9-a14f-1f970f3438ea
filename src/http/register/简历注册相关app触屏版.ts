/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseRegister } from "../../utils/requestConcise";

import {
  ApiRegisterStepRegisterbaseinfoGetParams,
  ApiRegisterStepRegisterblueworkinfoGetParams,
  ApiRegisterStepRegisterworkinfoGetParams,
  ApiResultModelRegisterBaseInfoStep,
  ApiResultModelSaveReturnStep,
  ApiResultModelWorkInfoBlueStep,
  ApiResultModelWorkInfoSaveReturnStep,
  ApiResultModelWorkInfoStep,
  RegisterBaseInfoStep,
  WorkInfoBlueStep,
  WorkInfoStep,
} from "./data-contracts";

export class 简历注册相关app触屏版Class {
  /**
   * No description
   *
   * @tags 简历注册相关APP触屏版
   * @name ApiRegisterStepRegisterbaseinfoGet
   * @summary 获取注册的基本信息 APP
   * @request GET:/api/registerStep/registerbaseinfo
   * @secure
   * @response `200` `ApiResultModelRegisterBaseInfoStep` Success
   */
  static apiRegisterStepRegisterbaseinfoGet = (
    query: ApiRegisterStepRegisterbaseinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelRegisterBaseInfoStep>>>({
      url: `/api/registerStep/registerbaseinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 简历注册相关APP触屏版
   * @name ApiRegisterStepSaveregisterbaseinfoPost
   * @summary 保存注册的基本信息 APP
   * @request POST:/api/registerStep/saveregisterbaseinfo
   * @secure
   * @response `200` `ApiResultModelSaveReturnStep` Success
   */
  static apiRegisterStepSaveregisterbaseinfoPost = (
    data: RegisterBaseInfoStep,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelSaveReturnStep>>>({
      url: `/api/registerStep/saveregisterbaseinfo`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags 简历注册相关APP触屏版
   * @name ApiRegisterStepSaveregisterworkinfoPost
   * @summary 保存注册的工作经历
   * @request POST:/api/registerStep/saveregisterworkinfo
   * @secure
   * @response `200` `ApiResultModelWorkInfoSaveReturnStep` Success
   */
  static apiRegisterStepSaveregisterworkinfoPost = (
    data: WorkInfoStep,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelWorkInfoSaveReturnStep>>>({
      url: `/api/registerStep/saveregisterworkinfo`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags 简历注册相关APP触屏版
   * @name ApiRegisterStepRegisterblueworkinfoGet
   * @summary 获取注册的工作经历
   * @request GET:/api/registerStep/registerblueworkinfo
   * @secure
   * @response `200` `ApiResultModelWorkInfoBlueStep` Success
   */
  static apiRegisterStepRegisterblueworkinfoGet = (
    query: ApiRegisterStepRegisterblueworkinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelWorkInfoBlueStep>>>({
      url: `/api/registerStep/registerblueworkinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 简历注册相关APP触屏版
   * @name ApiRegisterStepSaveregisterblueworkinfoPost
   * @summary 保存注册的工作经历 蓝领  APP
   * @request POST:/api/registerStep/saveregisterblueworkinfo
   * @secure
   * @response `200` `ApiResultModelWorkInfoSaveReturnStep` Success
   */
  static apiRegisterStepSaveregisterblueworkinfoPost = (
    data: WorkInfoBlueStep,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelWorkInfoSaveReturnStep>>>({
      url: `/api/registerStep/saveregisterblueworkinfo`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags 简历注册相关APP触屏版
   * @name ApiRegisterStepRegisterworkinfoGet
   * @summary 获取注册的工作经历   APP
   * @request GET:/api/registerStep/registerworkinfo
   * @secure
   * @response `200` `ApiResultModelWorkInfoStep` Success
   */
  static apiRegisterStepRegisterworkinfoGet = (
    query: ApiRegisterStepRegisterworkinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelWorkInfoStep>>>({
      url: `/api/registerStep/registerworkinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
}
