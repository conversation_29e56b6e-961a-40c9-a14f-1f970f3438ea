/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseRegister } from "../../utils/requestConcise";

import {
  ApiRegisterstepCheckworktimeGetParams,
  ApiRegisterstepCheckworktimebetweenGetParams,
  ApiRegisterstepDescriptionGetParams,
  ApiRegisterstepGerecommendcareerlistGetParams,
  ApiRegisterstepRecommendindustrydataGetParams,
  ApiRegisterstepRegistercareerinfoGetParams,
  ApiRegisterstepRegisterdescribemodelGetParams,
  ApiRegisterstepRegistereduinfoGetParams,
  ApiRegisterstepRegistermatchcareerinfoGetParams,
  ApiRegisterstepVirtualavatarlistGetParams,
  ApiResultModel,
  ApiResultModelEducationInfo,
  ApiResultModelListKeywordItemDto,
  ApiResultModelListResult101,
  ApiResultModelListString,
  ApiResultModelRegisterCareerStepGetInputDto,
  ApiResultModelResumeEditDesDto,
  ApiResultModelSaveReturnStep,
  DeleteCareerDto,
  EducationInfo,
  RegisterCareerIndustryDto,
  RegisterCareerStepInputDto,
  RegisterDescribeModel,
  ResumeEditDesStepDto,
} from "./data-contracts";

export class 所有端公共部分Class {
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepRegistereduinfoGet
   * @summary 获取注册的教育信息
   * @request GET:/api/registerstep/registereduinfo
   * @secure
   * @response `200` `ApiResultModelEducationInfo` Success
   */
  static apiRegisterstepRegistereduinfoGet = (
    query: ApiRegisterstepRegistereduinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelEducationInfo>>>({
      url: `/api/registerstep/registereduinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepCheckworktimeGet
   * @summary 校验参加工作时间
   * @request GET:/api/registerstep/checkworktime
   * @secure
   * @response `200` `ApiResultModel` Success
   */
  static apiRegisterstepCheckworktimeGet = (
    query: ApiRegisterstepCheckworktimeGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModel>>>({
      url: `/api/registerstep/checkworktime` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepCheckworktimebetweenGet
   * @summary 校验参加工作时间 需要两个参数
   * @request GET:/api/registerstep/checkworktimebetween
   * @secure
   * @response `200` `ApiResultModel` Success
   */
  static apiRegisterstepCheckworktimebetweenGet = (
    query: ApiRegisterstepCheckworktimebetweenGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModel>>>({
      url: `/api/registerstep/checkworktimebetween` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepSaveregistereduinfoPost
   * @summary 保存注册的教育信息
   * @request POST:/api/registerstep/saveregistereduinfo
   * @secure
   * @response `200` `ApiResultModelSaveReturnStep` Success
   */
  static apiRegisterstepSaveregistereduinfoPost = (
    data: EducationInfo,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelSaveReturnStep>>>({
      url: `/api/registerstep/saveregistereduinfo`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepRegisterdescribemodelGet
   * @summary 获取注册的 模板信息
   * @request GET:/api/registerstep/registerdescribemodel
   * @secure
   * @response `200` `(RegisterDescribeModel)[]` Success
   */
  static apiRegisterstepRegisterdescribemodelGet = (
    query: ApiRegisterstepRegisterdescribemodelGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<RegisterDescribeModel[]>>>({
      url: `/api/registerstep/registerdescribemodel` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepRegistercareerinfoGet
   * @summary 获取注册的求职意向
   * @request GET:/api/registerstep/registercareerinfo
   * @secure
   * @response `200` `ApiResultModelRegisterCareerStepGetInputDto` Success
   */
  static apiRegisterstepRegistercareerinfoGet = (
    query: ApiRegisterstepRegistercareerinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelRegisterCareerStepGetInputDto>>>({
      url: `/api/registerstep/registercareerinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepGerecommendcareerlistGet
   * @summary 注册 获取推荐的求职意向职位
   * @request GET:/api/registerstep/gerecommendcareerlist
   * @secure
   * @response `200` `ApiResultModelListResult101` Success
   */
  static apiRegisterstepGerecommendcareerlistGet = (
    query: ApiRegisterstepGerecommendcareerlistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelListResult101>>>({
      url: `/api/registerstep/gerecommendcareerlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepRegistermatchcareerinfoGet
   * @summary 使用手填的求职意向职位名 匹配 是否是蓝领职位
   * @request GET:/api/registerstep/registermatchcareerinfo
   * @secure
   * @response `200` `ApiResultModelListResult101` Success
   */
  static apiRegisterstepRegistermatchcareerinfoGet = (
    query: ApiRegisterstepRegistermatchcareerinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelListResult101>>>({
      url: `/api/registerstep/registermatchcareerinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepSaveregistercareerinfoPost
   * @summary 保存注册的求职意向
   * @request POST:/api/registerstep/saveregistercareerinfo
   * @secure
   * @response `200` `ApiResultModel` Success
   */
  static apiRegisterstepSaveregistercareerinfoPost = (
    data: RegisterCareerStepInputDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModel>>>({
      url: `/api/registerstep/saveregistercareerinfo`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepVirtualavatarlistGet
   * @summary 获取虚拟头像
   * @request GET:/api/registerstep/virtualavatarlist
   * @secure
   * @response `200` `ApiResultModelListString` Success
   */
  static apiRegisterstepVirtualavatarlistGet = (
    query: ApiRegisterstepVirtualavatarlistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelListString>>>({
      url: `/api/registerstep/virtualavatarlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepDescriptionGet
   * @summary 获取个人描述
   * @request GET:/api/registerstep/description
   * @secure
   * @response `200` `ApiResultModelResumeEditDesDto` Success
   */
  static apiRegisterstepDescriptionGet = (
    query: ApiRegisterstepDescriptionGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelResumeEditDesDto>>>({
      url: `/api/registerstep/description` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepSavedescriptionPost
   * @summary 保存个人描述(个人优势)  这里描述名称 和描述类型 不用填。
   * @request POST:/api/registerstep/savedescription
   * @secure
   * @response `200` `ApiResultModelSaveReturnStep` Success
   */
  static apiRegisterstepSavedescriptionPost = (
    data: ResumeEditDesStepDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelSaveReturnStep>>>({
      url: `/api/registerstep/savedescription`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepRecommendindustrydataGet
   * @summary 注册 获取推荐行业字典信息
   * @request GET:/api/registerstep/recommendindustrydata
   * @secure
   * @response `200` `ApiResultModelListKeywordItemDto` Success
   */
  static apiRegisterstepRecommendindustrydataGet = (
    query: ApiRegisterstepRecommendindustrydataGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelListKeywordItemDto>>>({
      url: `/api/registerstep/recommendindustrydata` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepSavecareerandindustryPost
   * @summary 求职意向 --保存职位类型和行业类型
   * @request POST:/api/registerstep/savecareerandindustry
   * @secure
   * @response `200` `void` Success
   */
  static apiRegisterstepSavecareerandindustryPost = (
    data: RegisterCareerIndustryDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<void>>>({
      url: `/api/registerstep/savecareerandindustry`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags 所有端公共部分
   * @name ApiRegisterstepDelcareerPost
   * @summary 删除简历求职意向
   * @request POST:/api/registerstep/delcareer
   * @secure
   * @response `200` `void` Success
   */
  static apiRegisterstepDelcareerPost = (data: DeleteCareerDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseRegister<void>>>({
      url: `/api/registerstep/delcareer`,
      method: "POST",
      data: data,
      config: options,
    });
}
