/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseRegister } from "../../utils/requestConcise";

import {
  ApiRegisterStepRegisterbaseinfopcGetParams,
  ApiRegisterStepRegisterworkinfopcGetParams,
  ApiResultModelRegisterBaseInfoStepPC,
  ApiResultModelSaveReturnStep,
  ApiResultModelWorkInfoSaveReturnStep,
  ApiResultModelWorkInfoStep,
  RegisterBaseInfoStepPC,
  WorkInfoBlueStepPC,
  WorkInfoStep,
} from "./data-contracts";

export class 简历注册相关PcClass {
  /**
   * No description
   *
   * @tags 简历注册相关  PC
   * @name ApiRegisterStepRegisterbaseinfopcGet
   * @summary 获取注册基本信息 PC
   * @request GET:/api/RegisterStep/registerbaseinfopc
   * @secure
   * @response `200` `ApiResultModelRegisterBaseInfoStepPC` Success
   */
  static apiRegisterStepRegisterbaseinfopcGet = (
    query: ApiRegisterStepRegisterbaseinfopcGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelRegisterBaseInfoStepPC>>>({
      url: `/api/RegisterStep/registerbaseinfopc` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 简历注册相关  PC
   * @name ApiRegisterStepSaveregisterbaseinfopcPost
   * @summary 保存注册的基本信息 PC
   * @request POST:/api/RegisterStep/saveregisterbaseinfopc
   * @secure
   * @response `200` `ApiResultModelSaveReturnStep` Success
   */
  static apiRegisterStepSaveregisterbaseinfopcPost = (
    data: RegisterBaseInfoStepPC,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelSaveReturnStep>>>({
      url: `/api/RegisterStep/saveregisterbaseinfopc`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags 简历注册相关  PC
   * @name ApiRegisterStepSaveregisterblueworkinfopcPost
   * @summary 获取蓝领注册的工作经历 蓝领 PC
   * @request POST:/api/RegisterStep/saveregisterblueworkinfopc
   * @secure
   * @response `200` `ApiResultModelWorkInfoSaveReturnStep` Success
   */
  static apiRegisterStepSaveregisterblueworkinfopcPost = (
    data: WorkInfoBlueStepPC,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelWorkInfoSaveReturnStep>>>({
      url: `/api/RegisterStep/saveregisterblueworkinfopc`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags 简历注册相关  PC
   * @name ApiRegisterStepRegisterworkinfopcGet
   * @summary 获取注册的工作经历   PC
   * @request GET:/api/RegisterStep/registerworkinfopc
   * @secure
   * @response `200` `ApiResultModelWorkInfoStep` Success
   */
  static apiRegisterStepRegisterworkinfopcGet = (
    query: ApiRegisterStepRegisterworkinfopcGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelWorkInfoStep>>>({
      url: `/api/RegisterStep/registerworkinfopc` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags 简历注册相关  PC
   * @name ApiRegisterStepSaveregisterworkinfopcPost
   * @summary 保存非蓝领注册的工作经历  PC
   * @request POST:/api/RegisterStep/saveregisterworkinfopc
   * @secure
   * @response `200` `ApiResultModelWorkInfoSaveReturnStep` Success
   */
  static apiRegisterStepSaveregisterworkinfopcPost = (
    data: WorkInfoStep,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseRegister<ApiResultModelWorkInfoSaveReturnStep>>>({
      url: `/api/RegisterStep/saveregisterworkinfopc`,
      method: "POST",
      data: data,
      config: options,
    });
}
