/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

//import { envUrl } from '@/request/env'
//export const baseUrl = process.env.NODE_ENV === 'production' ? 'test' : '************:5000'
//export const baseUrl = envUrl().url
export const baseUrl = import.meta.env.VITE_APP_BASE_API;

/**
* WebApi 结果，日期格式处理： [JsonConverter(typeof(DateConverter))]
时间格式处理： [JsonConverter(typeof(DateTimeConverter))]
*/
export interface ApiResultModel {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  /** 返回的内容 */
  data?: any;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelEducationInfo {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  data?: EducationInfo;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelListKeywordItemDto {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  /** 返回的内容 */
  data?: KeywordItemDto[] | null;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelListResult101 {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  /** 返回的内容 */
  data?: Result101[] | null;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelListString {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  /** 返回的内容 */
  data?: string[] | null;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelRegisterBaseInfoStep {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  data?: RegisterBaseInfoStep;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelRegisterBaseInfoStepPC {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  data?: RegisterBaseInfoStepPC;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelRegisterCareerStepGetInputDto {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  data?: RegisterCareerStepGetInputDto;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelResumeEditDesDto {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  data?: ResumeEditDesDto;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelSaveReturnStep {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  data?: SaveReturnStep;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelWorkInfoBlueStep {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  data?: WorkInfoBlueStep;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelWorkInfoSaveReturnStep {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  data?: WorkInfoSaveReturnStep;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

/**
 * api返回结果
 */
export interface ApiResultModelWorkInfoStep {
  /**
   * 状态，0表示请求失败，1表示成功
   * @format int32
   */
  code?: number;
  data?: WorkInfoStep;
  /** 返回消息 */
  message?: string | null;
  /**
   * 服务器时间
   * @format date-time
   */
  now?: string;
}

export interface DeleteCareerDto {
  /**
   * 简历ID
   * @format int32
   */
  resumeID?: number;
  /** 是否删除求职意向1 */
  isDelExpectCareer1?: boolean | null;
  /** 是否删除求职意向2 */
  isDelExpectCareer2?: boolean | null;
  /** 是否删除求职意向3 */
  isDelExpectCareer3?: boolean | null;
}

export interface EducationInfo {
  /**
   * 教育经历id
   * @format int32
   */
  eduID?: number;
  /**
   * 简历id
   * @format int32
   */
  resumeID?: number;
  /** 入学时间 */
  beginTime?: string | null;
  /** 毕业时间 */
  endTime?: string | null;
  /**
   * 学历ID
   * @format int32
   */
  degreeID?: number;
  degree?: string | null;
  /** 毕业院校 */
  university?: string | null;
  /**
   * 专业id
   * @format int32
   */
  majorID?: number;
  /** 专业类别名称 */
  majorIDName?: string | null;
  /** 专业类别  路径 */
  majorPaths?: string | null;
  /** 专业名称 */
  major?: string | null;
  fullTimeFlag?: boolean;
}

/**
 * <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br />
 * @format int32
 */
export enum EnumLoginFrom {
  Value0 = 0,
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
  Value7 = 7,
  Value8 = 8,
  Value9 = 9,
  Value11 = 11,
  Value12 = 12,
  Value13 = 13,
  Value14 = 14,
  Value15 = 15,
}

/**
 * 关键字
 */
export interface KeywordItemDto {
  /**
   * 关键字ID
   * @format int32
   */
  keywordID?: number;
  /** 关键字名称 */
  keywordName?: string | null;
  /**
   * 上级ID
   * @format int32
   */
  parentID?: number;
  /** @format int32 */
  sort?: number;
  /** 是否有下一级 */
  hasNext?: boolean;
  /**
   * 蓝领标志位 0:不是蓝领 1:蓝领
   * @format int32
   */
  blueCollarFlag?: number | null;
}

export interface KeywordSimpleOption {
  /** @format int32 */
  keywordID?: number;
  keywordName?: string | null;
}

export interface RegisterBaseInfoStep {
  /** @minLength 1 */
  jobSeekerName: string;
  /** false 男.   true 女 */
  jobSeekerSex?: boolean;
  jobSeekerBrithday?: string | null;
  /**
   * 人才类型 1-毕业生  2-职场人
   * @format int32
   */
  jobSeekerTalentDegree?: number;
  /**
   * 求职状态 id
   * @format int32
   */
  workingState?: number;
  /**
   * 简历id
   * @format int32
   */
  jobSeekerResumeId?: number;
}

export interface RegisterBaseInfoStepPC {
  /** @minLength 1 */
  jobSeekerName: string;
  /** false 男.   true 女 */
  jobSeekerSex?: boolean;
  jobSeekerBrithday?: string | null;
  /**
   * 人才类型 1-毕业生  2-职场人
   * @format int32
   */
  jobSeekerTalentDegree?: number;
  /**
   * 求职状态 id
   * @format int32
   */
  workingState?: number;
  /**
   * 简历id
   * @format int32
   */
  jobSeekerResumeId?: number;
  /**
   * 首次工作时间
   * @minLength 1
   */
  jobSeekerAttendWorkYear: string;
}

export interface RegisterCareerIndustryDto {
  /** @format int32 */
  resumeId?: number;
  /**
   * 期望职能1 单选  不需要更改值的话，传NULL或者不传
   * @format int32
   */
  career1?: number | null;
  /**
   * 期望职能2  不需要更改值的话，传NULL或者不传
   * @format int32
   */
  career2?: number | null;
  /**
   * 期望职能3   不需要更改值的话，传NULL或者不传
   * @format int32
   */
  career3?: number | null;
  /** 期望行业1 id,多选用英文逗号分隔 */
  industryId1?: string | null;
  /** 期望行业2 id,多选用英文逗号分隔 */
  industryId2?: string | null;
  /** 期望行业3 id,多选用英文逗号分隔 */
  industryId3?: string | null;
}

export interface RegisterCareerStepGetInputDto {
  /**
   * 期望工作职业1
   * @format int32
   */
  expectCareer1?: number | null;
  /**
   * 期望工作职业2
   * @format int32
   */
  expectCareer2?: number | null;
  /**
   * 期望工作职业3
   * @format int32
   */
  expectCareer3?: number | null;
  /** 期望工作职业名称1 */
  expectCareerName1?: string | null;
  /** 期望工作职业名称2 */
  expectCareerName2?: string | null;
  /** 期望工作职业名称3 */
  expectCareerName3?: string | null;
  /** @format int32 */
  resumeId?: number;
  /**
   * 期望工作地1
   * @format int32
   */
  residencyId1?: number;
  /** 期望工作地1 */
  residency1?: string | null;
  /**
   * 期望工作地2
   * @format int32
   */
  residencyId2?: number;
  /** 期望工作地2 */
  residency2?: string | null;
  /**
   * 期望工作地3
   * @format int32
   */
  residencyId3?: number;
  /** 期望工作地4 */
  residency3?: string | null;
  /**
   * 薪酬 是多少就存多少金额
   * @format int32
   */
  salary?: number;
  /** 期望工作职业1 路径 */
  expectCareer1Paths?: string | null;
  /** 期望工作职业2 路径 */
  expectCareer2Paths?: string | null;
  /** 期望工作职业3 路径 */
  expectCareer3Paths?: string | null;
  /** 期望工作地1 路径 */
  residencyId1Paths?: string | null;
  /** 期望工作地2 路径 */
  residencyId2Paths?: string | null;
  /** 期望工作地3 路径 */
  residencyId3Paths?: string | null;
}

export interface RegisterCareerStepInputDto {
  /**
   * 期望工作职业1
   * @format int32
   */
  expectCareer1?: number | null;
  /**
   * 期望工作职业2
   * @format int32
   */
  expectCareer2?: number | null;
  /**
   * 期望工作职业3
   * @format int32
   */
  expectCareer3?: number | null;
  /** 期望工作职业名称1 */
  expectCareerName1?: string | null;
  /** 期望工作职业名称2 */
  expectCareerName2?: string | null;
  /** 期望工作职业名称3 */
  expectCareerName3?: string | null;
  /** @format int32 */
  resumeId?: number;
  /**
   * 期望工作地1
   * @format int32
   */
  residencyId1?: number;
  /** 期望工作地1 */
  residency1?: string | null;
  /**
   * 期望工作地2
   * @format int32
   */
  residencyId2?: number;
  /** 期望工作地2 */
  residency2?: string | null;
  /**
   * 期望工作地3
   * @format int32
   */
  residencyId3?: number;
  /** 期望工作地4 */
  residency3?: string | null;
  /**
   * 薪酬 是多少就存多少金额
   * @format int32
   */
  salary?: number;
}

export interface RegisterDescribeModel {
  model?: string | null;
  /** @format int32 */
  modelType?: number;
  modelName?: string | null;
}

/**
 * 大数据推荐职位实体类
 */
export interface Result101 {
  /** @format int32 */
  typeid?: number;
  typename?: string | null;
  /**
   * 分数
   * @format double
   */
  score?: number;
  /**
   * 是否蓝领职位 1是， 0不是
   * @format int32
   */
  bluecollarflag?: number;
  paths?: string | null;
}

export interface ResumeEditDesDto {
  /** @format int32 */
  desId?: number;
  /** @format int32 */
  resumeId?: number;
  /**
   * 描述名称
   * @minLength 1
   * @maxLength 30
   */
  desName: string;
  /**
   * 描述内容
   * @minLength 1
   * @maxLength 500
   */
  desContent: string;
  /**
   * 描述类型
   * @format int32
   */
  descType?: number;
  /** 是否蓝领 保存不需要 */
  isBlue?: boolean;
}

export interface ResumeEditDesStepDto {
  /** @format int32 */
  desId?: number;
  /** @format int32 */
  resumeId?: number;
  /** 描述名称 默认为自我评价 */
  desName?: string | null;
  /**
   * 描述类型 默认为3
   * @format int32
   */
  descType?: number;
  /**
   * 描述内容
   * @minLength 1
   * @maxLength 500
   */
  desContent: string;
}

export interface SaveReturnStep {
  /**
   * 保存完之后的id  ( 保存工作经历就返回wordid)
   * @format int32
   */
  id?: number;
  /**
   * 简历id
   * @format int32
   */
  resumeID?: number;
}

export interface WorkInfoBlueStep {
  /** @format int32 */
  workID?: number;
  /** @format int32 */
  resumeID?: number;
  /**
   * 职位类别
   * @format int32
   */
  positionID?: number;
  /** 职位名 */
  positionName?: string | null;
  /** 首次参加工作时间 */
  jobSeekerAttendWorkYear?: string | null;
}

export interface WorkInfoBlueStepPC {
  /** @format int32 */
  workID?: number;
  /** @format int32 */
  resumeID?: number;
  /**
   * 职位类别
   * @format int32
   */
  positionID?: number;
  /** 职位名 */
  positionName?: string | null;
}

export interface WorkInfoSaveReturnStep {
  /**
   * 工作id
   * @format int32
   */
  workID?: number;
  /**
   * 简历id
   * @format int32
   */
  resumeid?: number;
  /** 是否蓝领 */
  isBlue?: boolean;
}

export interface WorkInfoStep {
  /** @format int32 */
  workID?: number;
  /** @format int32 */
  resumeID?: number;
  /** 公司名称 */
  company?: string | null;
  /**
   * 职位类别
   * @format int32
   */
  positionID?: number;
  /** 职位类别 路径，获取时用，保存不用 */
  positionPaths?: string | null;
  /** 职位类别名称 */
  positionIDName?: string | null;
  /** 是否是蓝领职位，此字段保存不需要用。 */
  isBluePosition?: boolean;
  /** 职位名称 */
  position?: string | null;
  beginTime?: string | null;
  /** null或者空代表“至今” */
  endTime?: string | null;
  /**
   * 工作描述
   * @maxLength 2000
   */
  desc?: string | null;
  salaryVisable?: boolean;
  /** @format int32 */
  salary?: number;
  /** 首次参加工作时间 */
  jobSeekerAttendWorkYear?: string | null;
  /** 自定义工作技能 保存不用这个 */
  customKeywords?: string | null;
  /** 工作技能  保存不用这个 */
  positionKeywordIds?: string | null;
  /** 技能 保存用这个字段提交信息 */
  keywordIds?: KeywordSimpleOption[] | null;
}

export interface ApiRegisterStepRegisterbaseinfoGetParams {
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  from?: EnumLoginFrom;
}

export interface ApiRegisterStepRegisterblueworkinfoGetParams {
  /** @format int32 */
  jobseekerid?: number;
  /** @format int32 */
  resumeid?: number;
  /**
   * @format int32
   * @default 0
   */
  workid?: number;
}

export interface ApiRegisterStepRegisterworkinfoGetParams {
  /** @format int32 */
  resumeid?: number;
  /**
   * 不填或者0，则取最新一条
   * @format int32
   * @default 0
   */
  workid?: number;
}

export interface ApiRegisterstepRegistereduinfoGetParams {
  /** @format int32 */
  resumeid?: number;
}

export interface ApiRegisterstepCheckworktimeGetParams {
  jobseekerattendworkyear?: string;
}

export interface ApiRegisterstepCheckworktimebetweenGetParams {
  jobseekerattendworkyear?: string;
  birthday?: string;
}

export interface ApiRegisterstepRegisterdescribemodelGetParams {
  /** @format int32 */
  resumeid?: number;
  /**
   * 0工作描述，1 项目经历描述，2
   * @format int32
   * @default 0
   */
  modeltype?: number;
}

export interface ApiRegisterstepRegistercareerinfoGetParams {
  /** @format int32 */
  resumeid?: number;
}

export interface ApiRegisterstepGerecommendcareerlistGetParams {
  /** @format int32 */
  resumeid?: number;
  /**
   * 获取数量
   * @format int32
   * @default 6
   */
  count?: number;
}

export interface ApiRegisterstepRegistermatchcareerinfoGetParams {
  /** 用户手填的职位名 */
  careername?: string;
  /**
   * 默认取1个匹配
   * @format int32
   * @default 1
   */
  count?: number;
}

export interface ApiRegisterstepVirtualavatarlistGetParams {
  /**
   * 是否男生，false则为女生
   * @default false
   */
  isboy?: boolean;
}

export interface ApiRegisterstepDescriptionGetParams {
  /** @format int32 */
  resumeid?: number;
  /** @format int32 */
  desid?: number;
}

export interface ApiRegisterstepRecommendindustrydataGetParams {
  /**
   * KeywordId
   * @format int32
   * @default 0
   */
  keywordid?: number;
  /**
   * 是否从缓存里面读取
   * @default true
   */
  withcache?: boolean;
}

export interface ApiRegisterStepRegisterbaseinfopcGetParams {
  /** <br />&nbsp; 网站 = 0<br />&nbsp; android手机 = 1<br />&nbsp; iphone手机 = 2<br />&nbsp; 手机网页版 = 3<br />&nbsp; 微信公众平台 = 4<br />&nbsp; 微信小程序 = 5<br />&nbsp; AGX = 7<br />&nbsp; 企业微信小程序 = 8<br />&nbsp; 校园网站 = 9<br />&nbsp; ZhiGT = 11<br />&nbsp; LiuZhouWondersYun = 12<br />&nbsp; 抖音小程序 = 13<br />&nbsp; 支付宝小程序 = 14<br />&nbsp; 芝麻工作证 = 15<br /> */
  from?: EnumLoginFrom;
}

export interface ApiRegisterStepRegisterworkinfopcGetParams {
  /** @format int32 */
  resumeid?: number;
  /**
   * 不填或者0，则取最新一条
   * @format int32
   * @default 0
   */
  workid?: number;
}
