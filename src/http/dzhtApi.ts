import axios from './axios';
const home = 'http://************:5020';
const imhost = 'http://************:600';


export const getAuthInfo = (data: any) => {
    return axios({
      url: '/api/sign/auth/info',
      method: 'get',
      data,
      config: {
        headers: {},
        timeout: 10000,
      },
    });
  };

export const getContractCheckuserexists = (data: any) => {
    return axios({
      url: `/api/sign/contract/checkuserexists?Name=${data.signName}&Phone=${data.signPhone}`,
      method: 'get',
      data,
      config: {
        headers: {},
        timeout: 10000,
      },
    });
  };

  export const getContractList = (data: any) => {
    let url=`/api/sign/contract/pagelist?Page=${data.Page}&PageSize=${data.PageSize}`

    if(data.PersonStatus!=''&&data.PersonStatus!='-1'){
      url=url+`&PersonStatus=${data.PersonStatus}`
    }
    if(data.Name!=''){
      url=url+`&Name=${data.Name}`
    }
    if(data.SenderName!=''){
      url=url+`&SenderName=${data.SenderName}`
    }

    return axios({
      url: url,
      method: 'get',
      data,
      config: {
        headers: {},
        timeout: 10000,
      },
    });
  };

  export const getSealList = (data: any) => {
    return axios({
      url: `/api/sign/seal/pagelist?Page=${data.Page}&PageSize=${data.PageSize}`,
      method: 'get',
      data,
      config: {
        headers: {},
        timeout: 10000,
      },
    });
  };
  
  export const getContractDetail = (data: any) => {
    return axios({
      url: `/api/sign/contract/${data}`,
      method: 'get',
      data,
      config: {
        headers: {},
        timeout: 10000,
      },
    });
  };
  export const getContractSealList = (data: any) => {
    return axios({
      url: `/api/sign/seal/list`,
      method: 'get',
      data,
      config: {
        headers: {},
        timeout: 10000,
      },
    });
  };

  export const getContractWillCheck = (data:any) => {
    return axios({
        url: `/api/sign/contract/will/check/${data}`,
        method: "post",
        data: { willType: 0 },
        config: {
            headers: {},
            timeout: 10000
        }
    })
}

export const postContractRefuse = (data:any) => {
  return axios({
      url: `/api/sign/contract/refuse/${data}`,
      method: "post",
      data,
      config: {
          headers: {},
          timeout: 10000
      }
  })
}
export const postContractSign = (id,data) => {
  return axios({
      url: `/api/sign/contract/sign/${id}`,
      method: "post",
      data:data,
      config: {
          headers: {},
          timeout: 10000
      }
  })
}

export const postAuthLegalfaceqrcode = (data:any) => {
  return axios({
      url: `/api/sign/auth/legalfaceqrcode`,
      method: "post",
      data:data,
      config: {
          headers: {},
          timeout: 10000
      }
  })
}

export const postContractSignatureCheck = (data:any) => {
  return axios({
      url: `/api/sign/contract/signaturecheck`,
      method: "post",
      data:data,
      config: {
          headers: {},
          timeout: 10000
      }
  })
}
export const getAuthHasright = (data: any) => {
  return axios({
    url: `/api/sign/auth/hasright`,
    method: 'get',
    data,
    config: {
      headers: {},
      timeout: 10000,
    },
  });
};