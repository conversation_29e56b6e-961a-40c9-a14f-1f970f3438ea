/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiAutocompeleteSearchcityPostParams,
  ApiAutocompeleteSearchcollegePostParams,
  ApiAutocompeleteSearchenterprisenamePostParams,
  ApiAutocompeleteSearchindustryPostParams,
  ApiAutocompeleteSearchmajorPostParams,
  ApiAutocompeleteSearchpositionPostParams,
  ApiAutocompeleteSearchpositionwithwordpartPostParams,
  ApiAutocompeleteSearchsecondindustryPostParams,
  SimpleDic,
  SimpleListDic,
} from "./data-contracts";

export class AutocompeleteClass {
  /**
   * No description
   *
   * @tags autocompelete
   * @name ApiAutocompeleteSearchcollegePost
   * @summary 搜索学校名称
   * @request POST:/api/autocompelete/searchcollege
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiAutocompeleteSearchcollegePost = (
    query: ApiAutocompeleteSearchcollegePostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/autocompelete/searchcollege` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags autocompelete
   * @name ApiAutocompeleteSearchenterprisenamePost
   * @summary 搜索企业名称
   * @request POST:/api/autocompelete/searchenterprisename
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiAutocompeleteSearchenterprisenamePost = (
    query: ApiAutocompeleteSearchenterprisenamePostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/autocompelete/searchenterprisename` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags autocompelete
   * @name ApiAutocompeleteSearchcityPost
   * @summary 搜索城市
   * @request POST:/api/autocompelete/searchcity
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiAutocompeleteSearchcityPost = (
    query: ApiAutocompeleteSearchcityPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/autocompelete/searchcity` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags autocompelete
   * @name ApiAutocompeleteSearchindustryPost
   * @summary 搜索行业
   * @request POST:/api/autocompelete/searchindustry
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiAutocompeleteSearchindustryPost = (
    query: ApiAutocompeleteSearchindustryPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/autocompelete/searchindustry` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags autocompelete
   * @name ApiAutocompeleteSearchmajorPost
   * @summary 搜索专业
   * @request POST:/api/autocompelete/searchmajor
   * @secure
   * @response `200` `RestfulResultListSimpleDic` Success
   */
  static apiAutocompeleteSearchmajorPost = (
    query: ApiAutocompeleteSearchmajorPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<SimpleDic>>>({
      url: `/api/autocompelete/searchmajor` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags autocompelete
   * @name ApiAutocompeleteSearchsecondindustryPost
   * @summary 搜索二级行业
   * @request POST:/api/autocompelete/searchsecondindustry
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiAutocompeleteSearchsecondindustryPost = (
    query: ApiAutocompeleteSearchsecondindustryPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/autocompelete/searchsecondindustry` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags autocompelete
   * @name ApiAutocompeleteSearchpositionPost
   * @summary 搜索职位类别
   * @request POST:/api/autocompelete/searchposition
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiAutocompeleteSearchpositionPost = (
    query: ApiAutocompeleteSearchpositionPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/autocompelete/searchposition` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags autocompelete
   * @name ApiAutocompeleteSearchpositionwithwordpartPost
   * @summary 搜索职位类别 分词版本 2025.5.28
   * @request POST:/api/autocompelete/searchpositionwithwordpart
   * @secure
   * @response `200` `RestfulResultSimpleListDic` Success
   */
  static apiAutocompeleteSearchpositionwithwordpartPost = (
    query: ApiAutocompeleteSearchpositionwithwordpartPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<SimpleListDic>>>({
      url: `/api/autocompelete/searchpositionwithwordpart` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
}
