/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import { ActionResult, ApiLogintokenLoginGetParams } from "./data-contracts";

export class LogintokenClass {
  /**
   * No description
   *
   * @tags logintoken
   * @name ApiLogintokenLoginGet
   * @summary 微信小程序 使用Token登录
   * @request GET:/api/logintoken/login
   * @secure
   * @response `200` `RestfulResultActionResult` Success
   */
  static apiLogintokenLoginGet = (
    query: ApiLogintokenLoginGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ActionResult>>>({
      url: `/api/logintoken/login` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
}
