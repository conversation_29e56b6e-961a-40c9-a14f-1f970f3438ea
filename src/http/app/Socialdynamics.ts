/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiResultModel,
  ApiSocialDynamicsCommentListByLocationGetParams,
  ApiSocialDynamicsDefaultCommentsGetParams,
  ApiSocialDynamicsMoreCommentsGetParams,
  ApiSocialDynamicsMyFollowsGetParams,
  ApiSocialdynamicsCollectionarticlelistGetParams,
  ApiSocialdynamicsCollectionlistGetParams,
  ApiSocialdynamicsComplaintlistGetParams,
  ApiSocialdynamicsLikesarticlelistGetParams,
  ApiSocialdynamicsLikeslistGetParams,
  ApiSocialdynamicsSetcommentlikePostParams,
  ApiSocialdynamicsSetdynamicsarticlecollectionPostParams,
  ApiSocialdynamicsSetdynamicsarticlelikePostParams,
  ApiSocialdynamicsSetdynamicscollectionPostParams,
  ApiSocialdynamicsSetdynamicslikePostParams,
  ApiSocialdynamicsZixunbyidGetParams,
  ApiSocialdynamicsZixunlistGetParams,
  ApiSocialdynamicsZixunsearchlistGetParams,
  CommentPost,
  DisoverArticleDetailDto,
  NewsChannelModel,
  NewsChannelTabModel,
  PagedListDisoverListItemDto,
  PagedListSimpleSocialDynamicsInfoOutput,
  PagedListSocialDynamicsArticleDto,
  PagedListSocialDynamicsComment,
  PagedListSocialDynamicsComplaintItemDto,
  SocialDynamicsFollowOutput,
} from "./data-contracts";

export class SocialdynamicsClass {
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsSetcommentPost
   * @summary 对动态的评论  评论或者回复
   * @request POST:/api/socialdynamics/setcomment
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiSocialdynamicsSetcommentPost = (data: CommentPost, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/socialdynamics/setcomment`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsDeletcommentHostguidCommentidCommenttypePost
   * @summary 删除评论
   * @request POST:/api/socialdynamics/deletcomment/{hostguid}/{commentid}/{commenttype}
   * @secure
   * @response `200` `ApiResultModel` Success
   */
  static apiSocialdynamicsDeletcommentHostguidCommentidCommenttypePost = (
    hostguid: string,
    commentid: number,
    commenttype: number,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ApiResultModel>>>({
      url: `/api/socialdynamics/deletcomment/${hostguid}/${commentid}/${commenttype}`,
      method: "POST",
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsSetcommentlikePost
   * @summary 针对评论  点赞或取消点赞
   * @request POST:/api/socialdynamics/setcommentlike
   * @secure
   * @response `200` `ApiResultModel` Success
   */
  static apiSocialdynamicsSetcommentlikePost = (
    query: ApiSocialdynamicsSetcommentlikePostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ApiResultModel>>>({
      url: `/api/socialdynamics/setcommentlike` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsSetdynamicscollectionPost
   * @summary 针对动态 收藏或取消
   * @request POST:/api/socialdynamics/setdynamicscollection
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiSocialdynamicsSetdynamicscollectionPost = (
    query: ApiSocialdynamicsSetdynamicscollectionPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/socialdynamics/setdynamicscollection` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsSetdynamicslikePost
   * @summary 针对动态 点赞或取消
   * @request POST:/api/socialdynamics/setdynamicslike
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiSocialdynamicsSetdynamicslikePost = (
    query: ApiSocialdynamicsSetdynamicslikePostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/socialdynamics/setdynamicslike` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsSetdynamicsarticlecollectionPost
   * @summary 针对文章  收藏或取消收藏
   * @request POST:/api/socialdynamics/setdynamicsarticlecollection
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiSocialdynamicsSetdynamicsarticlecollectionPost = (
    query: ApiSocialdynamicsSetdynamicsarticlecollectionPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/socialdynamics/setdynamicsarticlecollection` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsSetdynamicsarticlelikePost
   * @summary 针对文章  点赞或取消点赞
   * @request POST:/api/socialdynamics/setdynamicsarticlelike
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiSocialdynamicsSetdynamicsarticlelikePost = (
    query: ApiSocialdynamicsSetdynamicsarticlelikePostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/socialdynamics/setdynamicsarticlelike` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialDynamicsFollowEnterpriseGuidGet
   * @summary 求职者关注企业
   * @request GET:/api/socialDynamics/follow/{enterpriseGuid}
   * @secure
   * @response `200` `void` Success
   */
  static apiSocialDynamicsFollowEnterpriseGuidGet = (
    enterpriseGuid: string,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/socialDynamics/follow/${enterpriseGuid}`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialDynamicsUnFollowEnterpriseGuidDelete
   * @summary 求职者取消关注企业
   * @request DELETE:/api/socialDynamics/unFollow/{enterpriseGuid}
   * @secure
   * @response `200` `void` Success
   */
  static apiSocialDynamicsUnFollowEnterpriseGuidDelete = (
    enterpriseGuid: string,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/socialDynamics/unFollow/${enterpriseGuid}`,
      method: "DELETE",
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialDynamicsDefaultCommentsGet
   * @summary 获取初始化评论
   * @request GET:/api/socialDynamics/defaultComments
   * @secure
   * @response `200` `RestfulResultPagedListSocialDynamicsComment` Success
   */
  static apiSocialDynamicsDefaultCommentsGet = (
    query: ApiSocialDynamicsDefaultCommentsGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListSocialDynamicsComment>>>({
      url: `/api/socialDynamics/defaultComments` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialDynamicsCommentListByLocationGet
   * @summary 定位评论点赞的列表  获取评论列表
   * @request GET:/api/socialDynamics/CommentListByLocation
   * @secure
   * @response `200` `RestfulResultPagedListSocialDynamicsComment` Success
   */
  static apiSocialDynamicsCommentListByLocationGet = (
    query: ApiSocialDynamicsCommentListByLocationGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListSocialDynamicsComment>>>({
      url: `/api/socialDynamics/CommentListByLocation` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialDynamicsMoreCommentsGet
   * @summary 获取更多折叠的评论
   * @request GET:/api/socialDynamics/moreComments
   * @secure
   * @response `200` `RestfulResultPagedListSocialDynamicsComment` Success
   */
  static apiSocialDynamicsMoreCommentsGet = (
    query: ApiSocialDynamicsMoreCommentsGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListSocialDynamicsComment>>>({
      url: `/api/socialDynamics/moreComments` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsLikeslistGet
   * @summary 我点赞的动态列表
   * @request GET:/api/socialdynamics/likeslist
   * @secure
   * @response `200` `RestfulResultPagedListSimpleSocialDynamicsInfoOutput` Success
   */
  static apiSocialdynamicsLikeslistGet = (
    query: ApiSocialdynamicsLikeslistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListSimpleSocialDynamicsInfoOutput>>>({
      url: `/api/socialdynamics/likeslist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsCollectionlistGet
   * @summary 我收藏的动态列表
   * @request GET:/api/socialdynamics/collectionlist
   * @secure
   * @response `200` `RestfulResultPagedListSimpleSocialDynamicsInfoOutput` Success
   */
  static apiSocialdynamicsCollectionlistGet = (
    query: ApiSocialdynamicsCollectionlistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListSimpleSocialDynamicsInfoOutput>>>({
      url: `/api/socialdynamics/collectionlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsCollectionarticlelistGet
   * @summary 我收藏的文章列表
   * @request GET:/api/socialdynamics/collectionarticlelist
   * @secure
   * @response `200` `RestfulResultPagedListSocialDynamicsArticleDto` Success
   */
  static apiSocialdynamicsCollectionarticlelistGet = (
    query: ApiSocialdynamicsCollectionarticlelistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListSocialDynamicsArticleDto>>>({
      url: `/api/socialdynamics/collectionarticlelist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsLikesarticlelistGet
   * @summary 我点赞的文章列表
   * @request GET:/api/socialdynamics/likesarticlelist
   * @secure
   * @response `200` `RestfulResultPagedListSocialDynamicsArticleDto` Success
   */
  static apiSocialdynamicsLikesarticlelistGet = (
    query: ApiSocialdynamicsLikesarticlelistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListSocialDynamicsArticleDto>>>({
      url: `/api/socialdynamics/likesarticlelist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsComplaintlistGet
   * @summary 获取我的投诉列表
   * @request GET:/api/socialdynamics/complaintlist
   * @secure
   * @response `200` `RestfulResultPagedListSocialDynamicsComplaintItemDto` Success
   */
  static apiSocialdynamicsComplaintlistGet = (
    query: ApiSocialdynamicsComplaintlistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListSocialDynamicsComplaintItemDto>>>({
      url: `/api/socialdynamics/complaintlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsZixunlistGet
   * @summary 获取发现文章列表
   * @request GET:/api/socialdynamics/zixunlist
   * @secure
   * @response `200` `RestfulResultPagedListDisoverListItemDto` Success
   */
  static apiSocialdynamicsZixunlistGet = (
    query: ApiSocialdynamicsZixunlistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListDisoverListItemDto>>>({
      url: `/api/socialdynamics/zixunlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsZixunsearchlistGet
   * @summary 获取搜索求职资讯列表  搜索
   * @request GET:/api/socialdynamics/zixunsearchlist
   * @secure
   * @response `200` `RestfulResultPagedListDisoverListItemDto` Success
   */
  static apiSocialdynamicsZixunsearchlistGet = (
    query: ApiSocialdynamicsZixunsearchlistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagedListDisoverListItemDto>>>({
      url: `/api/socialdynamics/zixunsearchlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsNewschannelGet
   * @summary 后台返回资讯Tab信息
   * @request GET:/api/socialdynamics/newschannel
   * @secure
   * @response `200` `RestfulResultListNewsChannelModel` Success
   */
  static apiSocialdynamicsNewschannelGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<NewsChannelModel>>>({
      url: `/api/socialdynamics/newschannel`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsNewschanneltabGet
   * @summary 新发现  后台返回资讯Tab
   * @request GET:/api/socialdynamics/newschanneltab
   * @secure
   * @response `200` `RestfulResultListNewsChannelTabModel` Success
   */
  static apiSocialdynamicsNewschanneltabGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<NewsChannelTabModel>>>({
      url: `/api/socialdynamics/newschanneltab`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialdynamicsZixunbyidGet
   * @summary 文章详情
   * @request GET:/api/socialdynamics/zixunbyid
   * @secure
   * @response `200` `RestfulResultDisoverArticleDetailDto` Success
   */
  static apiSocialdynamicsZixunbyidGet = (
    query: ApiSocialdynamicsZixunbyidGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<DisoverArticleDetailDto>>>({
      url: `/api/socialdynamics/zixunbyid` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags socialdynamics
   * @name ApiSocialDynamicsMyFollowsGet
   * @summary 查询当前登录求职者的关注企业列表
   * @request GET:/api/socialDynamics/myFollows
   * @secure
   * @response `200` `RestfulResultListSocialDynamicsFollowOutput` Success
   */
  static apiSocialDynamicsMyFollowsGet = (
    query: ApiSocialDynamicsMyFollowsGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<SocialDynamicsFollowOutput>>>({
      url: `/api/socialDynamics/myFollows` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
}
