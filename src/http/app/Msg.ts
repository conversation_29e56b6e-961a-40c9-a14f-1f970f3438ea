/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiMsgMsgclickPostParams,
  ApiMsgSavepositionpushsettingPostParams,
  MsgSettingDto,
  MsgSubscribeCallbackDto,
} from "./data-contracts";

export class MsgClass {
  /**
   * No description
   *
   * @tags msg
   * @name ApiMsgGetmsgsettingGet
   * @summary 获取消息推送开关设置
   * @request GET:/api/msg/getmsgsetting
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiMsgGetmsgsettingGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/msg/getmsgsetting`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags msg
   * @name ApiMsgSavemsgsettingPost
   * @summary 保存消息推送开关设置
   * @request POST:/api/msg/savemsgsetting
   * @secure
   * @response `200` `void` Success
   */
  static apiMsgSavemsgsettingPost = (data: MsgSettingDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/msg/savemsgsetting`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags msg
   * @name ApiMsgGetpositionpushsettingGet
   * @summary 获取职位推荐开关设置
   * @request GET:/api/msg/getpositionpushsetting
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiMsgGetpositionpushsettingGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/msg/getpositionpushsetting`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags msg
   * @name ApiMsgSavepositionpushsettingPost
   * @summary 保存职位推荐开关设置
   * @request POST:/api/msg/savepositionpushsetting
   * @secure
   * @response `200` `void` Success
   */
  static apiMsgSavepositionpushsettingPost = (
    query: ApiMsgSavepositionpushsettingPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/msg/savepositionpushsetting` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags msg
   * @name ApiMsgMsgsubscribequeryTemplateidPost
   * @summary 支付宝订阅消息  获取当前用户的模板订阅关系
   * @request POST:/api/msg/msgsubscribequery/{templateid}
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiMsgMsgsubscribequeryTemplateidPost = (
    templateid: string,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/msg/msgsubscribequery/${templateid}`,
      method: "POST",
      config: options,
    });
  /**
   * No description
   *
   * @tags msg
   * @name ApiMsgMsgsubscribecallbackPost
   * @summary 支付宝订阅消息  订阅回调接口
   * @request POST:/api/msg/msgsubscribecallback
   * @secure
   * @response `200` `void` Success
   */
  static apiMsgMsgsubscribecallbackPost = (
    data: MsgSubscribeCallbackDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/msg/msgsubscribecallback`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags msg
   * @name ApiMsgMsgclickPost
   * @summary 统计点击推送消息
   * @request POST:/api/msg/msgclick
   * @secure
   * @response `200` `void` Success
   */
  static apiMsgMsgclickPost = (query: ApiMsgMsgclickPostParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/msg/msgclick` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
}
