/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  IndexPositionListItem,
  InteractionRedDot,
  RecommendMsgOutput,
  SeeMeStatOutput,
} from "./data-contracts";

export class InteractionClass {
  /**
   * No description
   *
   * @tags interaction
   * @name ApiInteractionPosrecommendGet
   * @summary 消息互动--职位推荐
   * @request GET:/api/interaction/posrecommend
   * @secure
   * @response `200` `RestfulResultListIndexPositionListItem` Success
   */
  static apiInteractionPosrecommendGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<IndexPositionListItem>>>({
      url: `/api/interaction/posrecommend`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags interaction
   * @name ApiInteractionMeinterestGet
   * @summary 对我感兴趣
   * @request GET:/api/interaction/meinterest
   * @secure
   * @response `200` `RestfulResultListIndexPositionListItem` Success
   */
  static apiInteractionMeinterestGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<IndexPositionListItem>>>({
      url: `/api/interaction/meinterest`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags interaction
   * @name ApiInteractionNewpositionGet
   * @summary 消息互动--新职位
   * @request GET:/api/interaction/newposition
   * @secure
   * @response `200` `RestfulResultListIndexPositionListItem` Success
   */
  static apiInteractionNewpositionGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<IndexPositionListItem>>>({
      url: `/api/interaction/newposition`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags interaction
   * @name ApiInteractionSeemeGet
   * @summary 看过我
   * @request GET:/api/interaction/seeme
   * @secure
   * @response `200` `RestfulResultListIndexPositionListItem` Success
   */
  static apiInteractionSeemeGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<IndexPositionListItem>>>({
      url: `/api/interaction/seeme`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags interaction
   * @name ApiInteractionSeemestatGet
   * @summary 看过我--图表
   * @request GET:/api/interaction/seemestat
   * @secure
   * @response `200` `RestfulResultSeeMeStatOutput` Success
   */
  static apiInteractionSeemestatGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<SeeMeStatOutput>>>({
      url: `/api/interaction/seemestat`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags interaction
   * @name ApiInteractionReddotGet
   * @summary 获取菜单红点
   * @request GET:/api/interaction/reddot
   * @secure
   * @response `200` `RestfulResultInteractionRedDot` Success
   */
  static apiInteractionReddotGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<InteractionRedDot>>>({
      url: `/api/interaction/reddot`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags interaction
   * @name ApiInteractionRecommendmsgGet
   * @summary 推荐职位消息
   * @request GET:/api/interaction/recommendmsg
   * @secure
   * @response `200` `RestfulResultRecommendMsgOutput` Success
   */
  static apiInteractionRecommendmsgGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<RecommendMsgOutput>>>({
      url: `/api/interaction/recommendmsg`,
      method: "GET",
      config: options,
    });
}
