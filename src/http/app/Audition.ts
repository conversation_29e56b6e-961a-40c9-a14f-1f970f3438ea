/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  AIAuditionItemOutput,
  PageBaseModel,
  PagedListAIAuditionItemOutput,
} from "./data-contracts";

export class AuditionClass {
  /**
   * No description
   *
   * @tags audition
   * @name ApiAuditionAiauditionIdGet
   * @summary 获取一条AI面试信息
   * @request GET:/api/audition/aiaudition/{id}
   * @secure
   * @response `200` `RestfulResultAIAuditionItemOutput` Success
   */
  static apiAuditionAiauditionIdGet = (id: number, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<AIAuditionItemOutput>>>({
      url: `/api/audition/aiaudition/${id}`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags audition
   * @name ApiAuditionAiauditionlistPost
   * @request POST:/api/audition/aiauditionlist
   * @secure
   * @response `200` `RestfulResultPagedListAIAuditionItemOutput` Success
   */
  static apiAuditionAiauditionlistPost = (data: PageBaseModel, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<PagedListAIAuditionItemOutput>>>({
      url: `/api/audition/aiauditionlist`,
      method: "POST",
      data: data,
      config: options,
    });
}
