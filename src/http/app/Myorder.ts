/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import { ApiMyorderPaystatequeryPostParams, PayStateQueryViewModel } from "./data-contracts";

export class MyorderClass {
  /**
   * No description
   *
   * @tags myorder
   * @name ApiMyorderPaystatequeryPost
   * @summary 查询订单的支付状态(旧版MyOrder/PayStateQuery)
   * @request POST:/api/myorder/paystatequery
   * @secure
   * @response `200` `RestfulResultPayStateQueryViewModel` Success
   */
  static apiMyorderPaystatequeryPost = (
    query: ApiMyorderPaystatequeryPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PayStateQueryViewModel>>>({
      url: `/api/myorder/paystatequery` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
}
