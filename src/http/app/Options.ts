/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiOptionsCertificatelistGetParams,
  ApiOptionsDegreeoptionsGetParams,
  ApiOptionsDistrictGetParams,
  ApiOptionsIndustryGetParams,
  ApiOptionsPositionGetParams,
  ApiOptionsPositionantistopsGetParams,
  ApiOptionsSpecialtyGetParams,
  ApiOptionsWorkstatuoptionsstudentGetParams,
  DicNodeDto,
  KeywordDto,
  KeywordItemDto,
  KeywordSimpleOption,
  PositionAntistopsDto,
} from "./data-contracts";

export class OptionsClass {
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsDistrictGet
   * @summary 城市数据
   * @request GET:/api/options/district
   * @secure
   * @response `200` `RestfulResultListKeywordItemDto` Success
   */
  static apiOptionsDistrictGet = (
    query: ApiOptionsDistrictGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<KeywordItemDto>>>({
      url: `/api/options/district` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsPositionGet
   * @summary 职能数据
   * @request GET:/api/options/position
   * @secure
   * @response `200` `RestfulResultListKeywordItemDto` Success
   */
  static apiOptionsPositionGet = (
    query: ApiOptionsPositionGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<KeywordItemDto>>>({
      url: `/api/options/position` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsIndustryGet
   * @summary 行业数据
   * @request GET:/api/options/industry
   * @secure
   * @response `200` `RestfulResultListKeywordItemDto` Success
   */
  static apiOptionsIndustryGet = (
    query: ApiOptionsIndustryGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<KeywordItemDto>>>({
      url: `/api/options/industry` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsDegreeoptionsGet
   * @summary 获取学历选项
   * @request GET:/api/options/degreeoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsDegreeoptionsGet = (
    query: ApiOptionsDegreeoptionsGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/degreeoptions` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsWorkstatuoptionsGet
   * @summary 获取工作状态选项
   * @request GET:/api/options/workstatuoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsWorkstatuoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/workstatuoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsWorkstatuoptionsstudentGet
   * @summary 获取工作状态选项 学生接口
   * @request GET:/api/options/workstatuoptionsstudent
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsWorkstatuoptionsstudentGet = (
    query: ApiOptionsWorkstatuoptionsstudentGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/workstatuoptionsstudent` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsLanguageoptionsGet
   * @summary 获取语言技能选项
   * @request GET:/api/options/languageoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsLanguageoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/languageoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsEnglishoptionsGet
   * @summary 获取英语水平选项
   * @request GET:/api/options/englishoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsEnglishoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/englishoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsOtherlanguageleveloptionsGet
   * @summary 非英语的听说能力
   * @request GET:/api/options/otherlanguageleveloptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsOtherlanguageleveloptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/otherlanguageleveloptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsDrivingoptionsGet
   * @summary 获取驾照选项
   * @request GET:/api/options/drivingoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsDrivingoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/drivingoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsComputeroptionsGet
   * @summary 电脑水平选项
   * @request GET:/api/options/computeroptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsComputeroptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/computeroptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsCardoptionsGet
   * @summary 身份证类别选项
   * @request GET:/api/options/cardoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsCardoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/cardoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsWorkpropertyoptionsGet
   * @summary 工作方式选项
   * @request GET:/api/options/workpropertyoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsWorkpropertyoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/workpropertyoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsSalaryonlyvalueoptionsGet
   * @summary 月薪选项，固定数值，只返回数值。Id无效
   * @request GET:/api/options/salaryonlyvalueoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsSalaryonlyvalueoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/salaryonlyvalueoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsSalaryoptionsGet
   * @summary 月薪字典
   * @request GET:/api/options/salaryoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsSalaryoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/salaryoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsEntpropertyoptionsGet
   * @summary 企业性质选项
   * @request GET:/api/options/entpropertyoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsEntpropertyoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/entpropertyoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsEmployeenumberoptionsGet
   * @summary 公司规模
   * @request GET:/api/options/employeenumberoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsEmployeenumberoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/employeenumberoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsPositionleveloptionsGet
   * @summary 职位等级
   * @request GET:/api/options/positionleveloptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsPositionleveloptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/positionleveloptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsDescriptiontypesGet
   * @summary 主题字典
   * @request GET:/api/options/descriptiontypes
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsDescriptiontypesGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/descriptiontypes`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsCertificatetypelevelsGet
   * @summary 证书等级
   * @request GET:/api/options/certificatetypelevels
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsCertificatetypelevelsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/certificatetypelevels`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsCertificatelistGet
   * @summary 证书字典
   * @request GET:/api/options/certificatelist
   * @secure
   * @response `200` `RestfulResultListDicNodeDto` Success
   */
  static apiOptionsCertificatelistGet = (
    query: ApiOptionsCertificatelistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<DicNodeDto>>>({
      url: `/api/options/certificatelist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsTechnologylevelGet
   * @summary 技术能力
   * @request GET:/api/options/technologylevel
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsTechnologylevelGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/technologylevel`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsSpecialtyGet
   * @summary 获取专业类别
   * @request GET:/api/options/specialty
   * @secure
   * @response `200` `RestfulResultListKeywordDto` Success
   */
  static apiOptionsSpecialtyGet = (
    query: ApiOptionsSpecialtyGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<KeywordDto>>>({
      url: `/api/options/specialty` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsNationoptionsGet
   * @summary 获取民族选项
   * @request GET:/api/options/nationoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsNationoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/nationoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsNationalityoptionsGet
   * @summary 获取国籍选项
   * @request GET:/api/options/nationalityoptions
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsNationalityoptionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/nationalityoptions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsPoliticalstatusGet
   * @summary 政治面貌
   * @request GET:/api/options/politicalstatus
   * @secure
   * @response `200` `RestfulResultListKeywordSimpleOption` Success
   */
  static apiOptionsPoliticalstatusGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<KeywordSimpleOption>>>({
      url: `/api/options/politicalstatus`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags options
   * @name ApiOptionsPositionantistopsGet
   * @summary 获取工作技能关键词
   * @request GET:/api/options/positionantistops
   * @secure
   * @response `200` `RestfulResultListPositionAntistopsDto` Success
   */
  static apiOptionsPositionantistopsGet = (
    query: ApiOptionsPositionantistopsGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PositionAntistopsDto>>>({
      url: `/api/options/positionantistops` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
}
