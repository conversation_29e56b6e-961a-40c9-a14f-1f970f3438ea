/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  AffirmJobIntensionDto,
  ApiResultModel,
  ApiResultModelUploadAnalyzedViewModel,
  ApiResumeOptimizationexpectjobGetParams,
  ApiResumeOptimizationmessageGetParams,
  ApiResumeOptimizationnomoremessagePostParams,
  ApiResumeRefreshresumePostParams,
  ApiResumeResumeaddPostParams,
  ApiResumeResumealloptimizationlistGetParams,
  ApiResumeResumeanalysisPostParams,
  ApiResumeResumedeleteDeleteParams,
  ApiResumeResumeinfoGetParams,
  ApiResumeResumeoptimizationlistGetParams,
  ApiResumeSetdefaultresumePostParams,
  ApiResumeShareresumeGetParams,
  ApplicationPlatform,
  PrivacySettingDto,
  ResumeAnalysisResultDto,
  ResumeInfoDto,
  ResumeOptimizationModel,
  ResumeOptimiztionExpectJob,
  ResumeOptimiztionParam,
  ResumeReNameModel,
  ShareResumeDto,
} from "./data-contracts";

export class ResumeClass {
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeResumelistGet
   * @summary 获取简历列表
   * @request GET:/api/resume/resumelist
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiResumeResumelistGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/resume/resumelist`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeSetdefaultresumePost
   * @summary 设置默认简历
   * @request POST:/api/resume/setdefaultresume
   * @secure
   * @response `200` `void` Success
   */
  static apiResumeSetdefaultresumePost = (
    query: ApiResumeSetdefaultresumePostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resume/setdefaultresume` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeResumerenamePost
   * @summary 简历重命名
   * @request POST:/api/resume/resumerename
   * @secure
   * @response `200` `void` Success
   */
  static apiResumeResumerenamePost = (data: ResumeReNameModel, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resume/resumerename`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeResumeaddPost
   * @summary 新增简历
   * @request POST:/api/resume/resumeadd
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiResumeResumeaddPost = (
    query: ApiResumeResumeaddPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/resume/resumeadd` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeResumedeleteDelete
   * @summary 删除简历
   * @request DELETE:/api/resume/resumedelete
   * @secure
   * @response `200` `void` Success
   */
  static apiResumeResumedeleteDelete = (
    query: ApiResumeResumedeleteDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resume/resumedelete` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeRefreshresumePost
   * @summary 刷新简历
   * @request POST:/api/resume/refreshresume
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiResumeRefreshresumePost = (
    query: ApiResumeRefreshresumePostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/resume/refreshresume` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeSetprivacysettingPost
   * @summary 简历公开设置
   * @request POST:/api/resume/setprivacysetting
   * @secure
   * @response `200` `void` Success
   */
  static apiResumeSetprivacysettingPost = (data: PrivacySettingDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resume/setprivacysetting`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeResumeoptimizationlistGet
   * @summary 获取简历优化列表
   * @request GET:/api/resume/resumeoptimizationlist
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiResumeResumeoptimizationlistGet = (
    query: ApiResumeResumeoptimizationlistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/resume/resumeoptimizationlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeOptimizationmessageGet
   * @summary 首页 优化提示
   * @request GET:/api/resume/optimizationmessage
   * @secure
   * @response `200` `ApiResultModel` Success
   */
  static apiResumeOptimizationmessageGet = (
    query: ApiResumeOptimizationmessageGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ApiResultModel>>>({
      url: `/api/resume/optimizationmessage` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeOptimizationexpectjobGet
   * @summary 首页 提醒意向职位
   * @request GET:/api/resume/optimizationexpectjob
   * @secure
   * @response `200` `RestfulResultResumeOptimiztionExpectJob` Success
   */
  static apiResumeOptimizationexpectjobGet = (
    query: ApiResumeOptimizationexpectjobGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ResumeOptimiztionExpectJob>>>({
      url: `/api/resume/optimizationexpectjob` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeOptimizationcloseonmessagePost
   * @summary 首页优化提示  操作关闭
   * @request POST:/api/resume/optimizationcloseonmessage
   * @secure
   * @response `200` `void` Success
   */
  static apiResumeOptimizationcloseonmessagePost = (
    data: ResumeOptimiztionParam,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resume/optimizationcloseonmessage`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeResumealloptimizationlistGet
   * @summary 简历优化列表 返回所有(未优化已优化项)
   * @request GET:/api/resume/resumealloptimizationlist
   * @secure
   * @response `200` `RestfulResultResumeOptimizationModel` Success
   */
  static apiResumeResumealloptimizationlistGet = (
    query: ApiResumeResumealloptimizationlistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ResumeOptimizationModel>>>({
      url: `/api/resume/resumealloptimizationlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeResumeinfoGet
   * @summary 获取简历全部信息
   * @request GET:/api/resume/resumeinfo
   * @secure
   * @response `200` `RestfulResultResumeInfoDto` Success
   */
  static apiResumeResumeinfoGet = (
    query: ApiResumeResumeinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ResumeInfoDto>>>({
      url: `/api/resume/resumeinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeOptimizationnomoremessagePost
   * @summary 优化列表不再提醒
   * @request POST:/api/resume/optimizationnomoremessage
   * @secure
   * @response `200` `void` Success
   */
  static apiResumeOptimizationnomoremessagePost = (
    query: ApiResumeOptimizationnomoremessagePostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resume/optimizationnomoremessage` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeResumeprivacyhideallPost
   * @summary 一键简历隐藏设置
   * @request POST:/api/resume/resumeprivacyhideall
   * @secure
   * @response `200` `void` Success
   */
  static apiResumeResumeprivacyhideallPost = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resume/resumeprivacyhideall`,
      method: "POST",
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeUploadfilePost
   * @summary 上传简历文件，调用麦穗TIP接口解析后保存
   * @request POST:/api/resume/uploadfile
   * @secure
   * @response `200` `void` Success
   */
  static apiResumeUploadfilePost = (
    data: {
      /** @format binary */
      file?: File;
    },
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resume/uploadfile`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeUploadresumeanalyzedPost
   * @summary 【上传简历】AI解析上传简历文件 ，Anlayzed 新在用
   * @request POST:/api/resume/uploadresumeanalyzed
   * @secure
   * @response `200` `ApiResultModelUploadAnalyzedViewModel` Success
   */
  static apiResumeUploadresumeanalyzedPost = (
    data: {
      /** @format binary */
      file?: File;
    },
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ApiResultModelUploadAnalyzedViewModel>>>({
      url: `/api/resume/uploadresumeanalyzed`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
 * No description
 * 
 * @tags resume
 * @name ApiResumeAffirmjobintensionPlatformGet
 * @summary 求职意向是否需要弹窗
JobIntensionType:  0：无需弹窗，1：需要弹窗
IsExpectCareer:是否待完善
 * @request GET:/api/resume/affirmjobintension/{platform}
 * @secure
 * @response `200` `RestfulResultAffirmJobIntensionDto` Success
 */
  static apiResumeAffirmjobintensionPlatformGet = (
    platform: ApplicationPlatform,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<AffirmJobIntensionDto>>>({
      url: `/api/resume/affirmjobintension/${platform}`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeResumeanalysisPost
   * @summary 简历解析
   * @request POST:/api/resume/resumeanalysis
   * @secure
   * @response `200` `RestfulResultResumeAnalysisResultDto` Success
   */
  static apiResumeResumeanalysisPost = (
    query: ApiResumeResumeanalysisPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ResumeAnalysisResultDto>>>({
      url: `/api/resume/resumeanalysis` + dataToQuery(query),
      method: "POST",
      data: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeShareresumeGet
   * @summary 简历分享的网址
   * @request GET:/api/resume/shareresume
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiResumeShareresumeGet = (
    query: ApiResumeShareresumeGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/resume/shareresume` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resume
   * @name ApiResumeShareresumebyemailPost
   * @summary 简历分享到邮箱
   * @request POST:/api/resume/shareresumebyemail
   * @secure
   * @response `200` `void` Success
   */
  static apiResumeShareresumebyemailPost = (data: ShareResumeDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resume/shareresumebyemail`,
      method: "POST",
      data: data,
      config: options,
    });
}
