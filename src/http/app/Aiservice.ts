/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  AIResumeItemTipDto,
  AnswerInputDto,
  AuthUrlModel,
  CloneJobSeekerResumePart,
  ResumeItemOptimizeInputDto,
} from "./data-contracts";

export class AiserviceClass {
  /**
   * No description
   *
   * @tags aiservice
   * @name ApiAiserviceTipResumepartGet
   * @summary 获取AI 简历界面首次打开显示的信息
   * @request GET:/api/aiservice/tip/{resumepart}
   * @secure
   * @response `200` `RestfulResultAIResumeItemTipDto` Success
   */
  static apiAiserviceTipResumepartGet = (
    resumepart: CloneJobSeekerResumePart,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<AIResumeItemTipDto>>>({
      url: `/api/aiservice/tip/${resumepart}`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags aiservice
   * @name ApiAiserviceRemainingtimesGet
   * @summary 每日剩余次数
   * @request GET:/api/aiservice/remainingtimes
   * @secure
   * @response `200` `RestfulResultInt32` Success
   */
  static apiAiserviceRemainingtimesGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Int32>>>({
      url: `/api/aiservice/remainingtimes`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags aiservice
   * @name ApiAiserviceGetxfauthurlPost
   * @summary 前端获取鉴权链接信息，前端使用
   * @request POST:/api/aiservice/getxfauthurl
   * @secure
   * @response `200` `RestfulResultAuthUrlModel` Success
   */
  static apiAiserviceGetxfauthurlPost = (
    data: ResumeItemOptimizeInputDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<AuthUrlModel>>>({
      url: `/api/aiservice/getxfauthurl`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags aiservice
   * @name ApiAiserviceChatsavePost
   * @summary 保存答案
   * @request POST:/api/aiservice/chatsave
   * @secure
   * @response `200` `void` Success
   */
  static apiAiserviceChatsavePost = (data: AnswerInputDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/aiservice/chatsave`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags aiservice
   * @name ApiAiserviceChatPost
   * @summary 后端直接返回内容--【后端测试用】前端请勿使用，
   * @request POST:/api/aiservice/chat
   * @secure
   * @response `200` `RestfulResultString` Success
   */
  static apiAiserviceChatPost = (data: ResumeItemOptimizeInputDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<String>>>({
      url: `/api/aiservice/chat`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags aiservice
   * @name ApiAiserviceNewxfauthurlPost
   * @summary 前端获取鉴权链接信息，前端使用 -deepseekv3
   * @request POST:/api/aiservice/newxfauthurl
   * @secure
   * @response `200` `RestfulResultAuthUrlModel` Success
   */
  static apiAiserviceNewxfauthurlPost = (
    data: ResumeItemOptimizeInputDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<AuthUrlModel>>>({
      url: `/api/aiservice/newxfauthurl`,
      method: "POST",
      data: data,
      config: options,
    });
}
