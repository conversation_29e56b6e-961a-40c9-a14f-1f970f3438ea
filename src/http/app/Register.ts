/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiRegisterRegisterbaseinfoGetParams,
  ApiRegisterRegistercareerinfoGetParams,
  ApiRegisterRegisterdescribemodelGetParams,
  ApiRegisterRegistereduinfoGetParams,
  ApiRegisterRegisterworkinfoGetParams,
  DeleteCareerDto,
  EducationInfo,
  KeywordItemDto,
  RegisterBaseInfo,
  RegisterCareerDto,
  RegisterCareerIndustryDto,
  RegisterCareerInputDto,
  RegisterDescribeModel,
  WorkInfo,
} from "./data-contracts";

export class RegisterClass {
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterRegisterbaseinfoGet
   * @summary 获取注册的基本信息
   * @request GET:/api/register/registerbaseinfo
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiRegisterRegisterbaseinfoGet = (
    query: ApiRegisterRegisterbaseinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/register/registerbaseinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterSaveregisterbaseinfoPost
   * @summary 保存注册的基本信息
   * @request POST:/api/register/saveregisterbaseinfo
   * @secure
   * @response `200` `void` Success
   */
  static apiRegisterSaveregisterbaseinfoPost = (
    data: RegisterBaseInfo,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/register/saveregisterbaseinfo`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterRegistereduinfoGet
   * @summary 获取注册的教育信息
   * @request GET:/api/register/registereduinfo
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiRegisterRegistereduinfoGet = (
    query: ApiRegisterRegistereduinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/register/registereduinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterSaveregistereduinfoPost
   * @summary 保存注册的教育信息
   * @request POST:/api/register/saveregistereduinfo
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiRegisterSaveregistereduinfoPost = (data: EducationInfo, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/register/saveregistereduinfo`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterRegisterworkinfoGet
   * @summary 获取注册的工作经历
   * @request GET:/api/register/registerworkinfo
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiRegisterRegisterworkinfoGet = (
    query: ApiRegisterRegisterworkinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/register/registerworkinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterRegisterdescribemodelGet
   * @summary 获取注册的 模板信息
   * @request GET:/api/register/registerdescribemodel
   * @secure
   * @response `200` `RestfulResultListRegisterDescribeModel` Success
   */
  static apiRegisterRegisterdescribemodelGet = (
    query: ApiRegisterRegisterdescribemodelGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<RegisterDescribeModel>>>({
      url: `/api/register/registerdescribemodel` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterSaveregisterworkinfoPost
   * @summary 保存注册的工作经历
   * @request POST:/api/register/saveregisterworkinfo
   * @secure
   * @response `200` `void` Success
   */
  static apiRegisterSaveregisterworkinfoPost = (data: WorkInfo, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/register/saveregisterworkinfo`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterRegistercareerinfoGet
   * @summary 获取注册的求职意向
   * @request GET:/api/register/registercareerinfo
   * @secure
   * @response `200` `RestfulResultRegisterCareerDto` Success
   */
  static apiRegisterRegistercareerinfoGet = (
    query: ApiRegisterRegistercareerinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<RegisterCareerDto>>>({
      url: `/api/register/registercareerinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterSaveregistercareerinfoPost
   * @summary 保存注册的求职意向
   * @request POST:/api/register/saveregistercareerinfo
   * @secure
   * @response `200` `void` Success
   */
  static apiRegisterSaveregistercareerinfoPost = (
    data: RegisterCareerInputDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/register/saveregistercareerinfo`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterRecommendindustrydataKeywordidWithcacheGet
   * @summary 注册 获取推荐行业字典信息
   * @request GET:/api/register/recommendindustrydata/{keywordid}/{withcache}
   * @secure
   * @response `200` `RestfulResultListKeywordItemDto` Success
   */
  static apiRegisterRecommendindustrydataKeywordidWithcacheGet = (
    keywordid: number,
    withcache: boolean,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<KeywordItemDto>>>({
      url: `/api/register/recommendindustrydata/${keywordid}/${withcache}`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterSavecareerandindustryPost
   * @summary 求职意向 --保存职位类型和行业类型
   * @request POST:/api/register/savecareerandindustry
   * @secure
   * @response `200` `void` Success
   */
  static apiRegisterSavecareerandindustryPost = (
    data: RegisterCareerIndustryDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/register/savecareerandindustry`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags register
   * @name ApiRegisterDelcareerPost
   * @summary 删除简历求职意向
   * @request POST:/api/register/delcareer
   * @secure
   * @response `200` `void` Success
   */
  static apiRegisterDelcareerPost = (data: DeleteCareerDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/register/delcareer`,
      method: "POST",
      data: data,
      config: options,
    });
}
