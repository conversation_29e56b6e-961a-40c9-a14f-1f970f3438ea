/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiLivepositionLivepositionlistGetParams,
  ApiLivepositionLivepositionlistforbroadcastGetParams,
  ApiLivepositionLivingpositionlistGetParams,
  LivePositionListForBroadcastModel,
  LivingPositionItemOutput,
  PagerPositionInfoForLiveModel,
  PositionLiveSeachBaseModel,
  PreachPositionOutput,
} from "./data-contracts";

export class LivepositionClass {
  /**
   * No description
   *
   * @tags liveposition
   * @name ApiLivepositionPositionlistPost
   * @summary 获取某场直播的职位(允许匿名访问)
   * @request POST:/api/liveposition/positionlist
   * @secure
   * @response `200` `RestfulResultPagerPositionInfoForLiveModel` Success
   */
  static apiLivepositionPositionlistPost = (
    data: PositionLiveSeachBaseModel,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagerPositionInfoForLiveModel>>>({
      url: `/api/liveposition/positionlist`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags liveposition
   * @name ApiLivepositionLivepositionlistforbroadcastGet
   * @summary 从缓存查询某场直播带岗的职位，如果缓存失效重新从数据库查询并设置新的缓存，缓存有效时长为10分钟
   * @request GET:/api/liveposition/livepositionlistforbroadcast
   * @secure
   * @response `200` `RestfulResultLivePositionListForBroadcastModel` Success
   */
  static apiLivepositionLivepositionlistforbroadcastGet = (
    query: ApiLivepositionLivepositionlistforbroadcastGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<LivePositionListForBroadcastModel>>>({
      url: `/api/liveposition/livepositionlistforbroadcast` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags liveposition
   * @name ApiLivepositionLivingpositionlistGet
   * @summary 获取直播进行中职位列表
   * @request GET:/api/liveposition/livingpositionlist
   * @deprecated
   * @secure
   * @response `200` `RestfulResultListLivingPositionItemOutput` Success
   */
  static apiLivepositionLivingpositionlistGet = (
    query: ApiLivepositionLivingpositionlistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<LivingPositionItemOutput>>>({
      url: `/api/liveposition/livingpositionlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags liveposition
   * @name ApiLivepositionLivepositionlistGet
   * @summary 获取直播的职位列表
   * @request GET:/api/liveposition/livepositionlist
   * @secure
   * @response `200` `RestfulResultListLivingPositionItemOutput` Success
   */
  static apiLivepositionLivepositionlistGet = (
    query: ApiLivepositionLivepositionlistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<LivingPositionItemOutput>>>({
      url: `/api/liveposition/livepositionlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags liveposition
   * @name ApiLivepositionPreachpositionrecordLiveguidGet
   * @summary 获取正在宣讲的职位
   * @request GET:/api/liveposition/preachpositionrecord/{liveguid}
   * @secure
   * @response `200` `RestfulResultPreachPositionOutput` Success
   */
  static apiLivepositionPreachpositionrecordLiveguidGet = (
    liveguid: string,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PreachPositionOutput>>>({
      url: `/api/liveposition/preachpositionrecord/${liveguid}`,
      method: "GET",
      config: options,
    });
}
