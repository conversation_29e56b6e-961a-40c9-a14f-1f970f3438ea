/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiResumepartCareerGetParams,
  ApiResumepartCertificateDeleteParams,
  ApiResumepartCertificateGetParams,
  ApiResumepartClonefromresumePostParams,
  ApiResumepartDescriptionDeleteParams,
  ApiResumepartDescriptionGetParams,
  ApiResumepartEducationDeleteParams,
  ApiResumepartEducationGetParams,
  ApiResumepartEdupracticeDeleteParams,
  ApiResumepartEdupracticeGetParams,
  ApiResumepartLanguageDeleteParams,
  ApiResumepartLanguageGetParams,
  ApiResumepartOtherabilityGetParams,
  ApiResumepartPlayroleitemsGetParams,
  ApiResumepartProjectDeleteParams,
  ApiResumepartProjectGetParams,
  ApiResumepartSavedescriptionPostParams,
  ApiResumepartSaveprojectPostParams,
  ApiResumepartSavetechnologyPostParams,
  ApiResumepartSaveworkinfoPostParams,
  ApiResumepartTechnologyDeleteParams,
  ApiResumepartTechnologyGetParams,
  ApiResumepartTrainDeleteParams,
  ApiResumepartTrainGetParams,
  ApiResumepartUpdateworkingstatePostParams,
  ApiResumepartWorkinfoDeleteParams,
  ApiResumepartWorkinfoGetParams,
  CareerPartDto,
  CompleteItem,
  EducationOnePartDto,
  EnumLoginFrom,
  InsertDescribeAI,
  PlayRoleItemsDto,
  ResumeEditCareerDto,
  ResumeEditCertDto,
  ResumeEditDesDto,
  ResumeEditEducationDto,
  ResumeEditLanguageDto,
  ResumeEditLanguageOutPutDto,
  ResumeEditOtherAbilityDto,
  ResumeEditPracticeDto,
  ResumeEditProjectDto,
  ResumeEditTechnologyDto,
  ResumeEditTrainDto,
  ResumeEditWorkDto,
  ResumeTagDeleteDto,
  ResumeTagInputDto,
  ResumeTagTotalDto,
  ResumeTagTotalDtoResumeId,
} from "./data-contracts";

export class ResumepartClass {
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartCareerGet
   * @summary 获取求职意向
   * @request GET:/api/resumepart/career
   * @secure
   * @response `200` `RestfulResultCareerPartDto` Success
   */
  static apiResumepartCareerGet = (
    query: ApiResumepartCareerGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<CareerPartDto>>>({
      url: `/api/resumepart/career` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSavecareerPost
   * @summary 保存求职意向
   * @request POST:/api/resumepart/savecareer
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartSavecareerPost = (data: ResumeEditCareerDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/savecareer`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSavecareernewPost
   * @summary 保存求职意向(新版)
   * @request POST:/api/resumepart/savecareernew
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartSavecareernewPost = (
    data: ResumeEditCareerDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/savecareernew`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartWorkinfoGet
   * @summary 获取工作经历
   * @request GET:/api/resumepart/workinfo
   * @secure
   * @response `200` `RestfulResultResumeEditWorkDto` Success
   */
  static apiResumepartWorkinfoGet = (
    query: ApiResumepartWorkinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ResumeEditWorkDto>>>({
      url: `/api/resumepart/workinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartWorkinfoDelete
   * @summary 删除工作经历
   * @request DELETE:/api/resumepart/workinfo
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartWorkinfoDelete = (
    query: ApiResumepartWorkinfoDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/workinfo` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSaveworkinfoPost
   * @summary 保存工作经历
   * @request POST:/api/resumepart/saveworkinfo
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartSaveworkinfoPost = (
    data: ResumeEditWorkDto,
    query?: ApiResumepartSaveworkinfoPostParams,
    options?: AxiosRequestConfig,
    
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/saveworkinfo` + dataToQuery(query),
      method: "POST",
      query: query,
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartPlayroleitemsGet
   * @summary 获取项目经历参与身份选项
   * @request GET:/api/resumepart/playroleitems
   * @secure
   * @response `200` `RestfulResultListPlayRoleItemsDto` Success
   */
  static apiResumepartPlayroleitemsGet = (
    query: ApiResumepartPlayroleitemsGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PlayRoleItemsDto>>>({
      url: `/api/resumepart/playroleitems` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartProjectGet
   * @summary 获取项目经历
   * @request GET:/api/resumepart/project
   * @secure
   * @response `200` `RestfulResultResumeEditProjectDto` Success
   */
  static apiResumepartProjectGet = (
    query: ApiResumepartProjectGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ResumeEditProjectDto>>>({
      url: `/api/resumepart/project` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartProjectDelete
   * @summary 删除项目经历
   * @request DELETE:/api/resumepart/project
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartProjectDelete = (
    query: ApiResumepartProjectDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/project` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSaveprojectPost
   * @summary 保存项目经历
   * @request POST:/api/resumepart/saveproject
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartSaveprojectPost = (
    query: ApiResumepartSaveprojectPostParams,
    data: ResumeEditProjectDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/saveproject` + dataToQuery(query),
      method: "POST",
      query: query,
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartEducationGet
   * @summary 获取教育经历
   * @request GET:/api/resumepart/education
   * @secure
   * @response `200` `RestfulResultEducationOnePartDto` Success
   */
  static apiResumepartEducationGet = (
    query: ApiResumepartEducationGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<EducationOnePartDto>>>({
      url: `/api/resumepart/education` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartEducationDelete
   * @summary 删除教育经历
   * @request DELETE:/api/resumepart/education
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartEducationDelete = (
    query: ApiResumepartEducationDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/education` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSaveeducationPost
   * @summary 保存教育经历
   * @request POST:/api/resumepart/saveeducation
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiResumepartSaveeducationPost = (
    data: ResumeEditEducationDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/resumepart/saveeducation`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartEdupracticeGet
   * @summary 获取实践经历
   * @request GET:/api/resumepart/edupractice
   * @secure
   * @response `200` `RestfulResultResumeEditPracticeDto` Success
   */
  static apiResumepartEdupracticeGet = (
    query: ApiResumepartEdupracticeGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ResumeEditPracticeDto>>>({
      url: `/api/resumepart/edupractice` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartEdupracticeDelete
   * @summary 删除实践经历
   * @request DELETE:/api/resumepart/edupractice
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartEdupracticeDelete = (
    query: ApiResumepartEdupracticeDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/edupractice` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSaveedupracticePost
   * @summary 保存实践经历
   * @request POST:/api/resumepart/saveedupractice
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartSaveedupracticePost = (
    data: ResumeEditPracticeDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/saveedupractice`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartTrainGet
   * @summary 获取培训经历
   * @request GET:/api/resumepart/train
   * @secure
   * @response `200` `RestfulResultResumeEditTrainDto` Success
   */
  static apiResumepartTrainGet = (
    query: ApiResumepartTrainGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ResumeEditTrainDto>>>({
      url: `/api/resumepart/train` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartTrainDelete
   * @summary 删除培训经历
   * @request DELETE:/api/resumepart/train
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartTrainDelete = (
    query: ApiResumepartTrainDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/train` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSavetrainPost
   * @summary 保存培训经历
   * @request POST:/api/resumepart/savetrain
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartSavetrainPost = (data: ResumeEditTrainDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/savetrain`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartTechnologyGet
   * @summary 获取技术能力
   * @request GET:/api/resumepart/technology
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiResumepartTechnologyGet = (
    query: ApiResumepartTechnologyGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/resumepart/technology` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartTechnologyDelete
   * @summary 删除技术能力
   * @request DELETE:/api/resumepart/technology
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartTechnologyDelete = (
    query: ApiResumepartTechnologyDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/technology` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSavetechnologyPost
   * @summary 保存技术能力
   * @request POST:/api/resumepart/savetechnology
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartSavetechnologyPost = (
    query: ApiResumepartSavetechnologyPostParams,
    data: ResumeEditTechnologyDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/savetechnology` + dataToQuery(query),
      method: "POST",
      query: query,
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartCertificateGet
   * @summary 获取证书
   * @request GET:/api/resumepart/certificate
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiResumepartCertificateGet = (
    query: ApiResumepartCertificateGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/resumepart/certificate` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartCertificateDelete
   * @summary 删除证书
   * @request DELETE:/api/resumepart/certificate
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartCertificateDelete = (
    query: ApiResumepartCertificateDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/certificate` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSavecertificatePost
   * @summary 保存证书
   * @request POST:/api/resumepart/savecertificate
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartSavecertificatePost = (
    data: ResumeEditCertDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/savecertificate`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartLanguageGet
   * @summary 获取语言能力
   * @request GET:/api/resumepart/language
   * @secure
   * @response `200` `RestfulResultResumeEditLanguageOutPutDto` Success
   */
  static apiResumepartLanguageGet = (
    query: ApiResumepartLanguageGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ResumeEditLanguageOutPutDto>>>({
      url: `/api/resumepart/language` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartLanguageDelete
   * @summary 删除语言能力
   * @request DELETE:/api/resumepart/language
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartLanguageDelete = (
    query: ApiResumepartLanguageDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/language` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSavelanguagePost
   * @summary 保存语言能力
   * @request POST:/api/resumepart/savelanguage
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartSavelanguagePost = (
    data: ResumeEditLanguageDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/savelanguage`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartDescriptionGet
   * @summary 获取个人描述
   * @request GET:/api/resumepart/description
   * @secure
   * @response `200` `RestfulResultResumeEditDesDto` Success
   */
  static apiResumepartDescriptionGet = (
    query: ApiResumepartDescriptionGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ResumeEditDesDto>>>({
      url: `/api/resumepart/description` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartDescriptionDelete
   * @summary 删除个人描述
   * @request DELETE:/api/resumepart/description
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartDescriptionDelete = (
    query: ApiResumepartDescriptionDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/description` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSavedescriptionPost
   * @summary 保存个人描述
   * @request POST:/api/resumepart/savedescription
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartSavedescriptionPost = (
    query: ApiResumepartSavedescriptionPostParams,
    data: ResumeEditDesDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/savedescription` + dataToQuery(query),
      method: "POST",
      query: query,
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartDescribeairecordResumeidDescribetypeFromPost
   * @summary 保存描述次数
   * @request POST:/api/resumepart/describeairecord/{resumeid}/{describetype}/{from}
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartDescribeairecordResumeidDescribetypeFromPost = (
    resumeid: number,
    describetype: number,
    from: EnumLoginFrom,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/describeairecord/${resumeid}/${describetype}/${from}`,
      method: "POST",
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartDescribeairecordnewPost
   * @summary 保存描述次数
   * @request POST:/api/resumepart/describeairecordnew
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartDescribeairecordnewPost = (
    data: InsertDescribeAI,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/describeairecordnew`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartOtherabilityGet
   * @summary 获取其他能力
   * @request GET:/api/resumepart/otherability
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiResumepartOtherabilityGet = (
    query: ApiResumepartOtherabilityGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/resumepart/otherability` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSaveotherabilityPost
   * @summary 修改其他能力
   * @request POST:/api/resumepart/saveotherability
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartSaveotherabilityPost = (
    data: ResumeEditOtherAbilityDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/saveotherability`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartClonefromresumePost
   * @summary 从默认简历复制
   * @request POST:/api/resumepart/clonefromresume
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartClonefromresumePost = (
    query: ApiResumepartClonefromresumePostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/clonefromresume` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartResumetagsResumeidGet
   * @summary 我的优势标签
   * @request GET:/api/resumepart/resumetags/{resumeid}
   * @secure
   * @response `200` `RestfulResultResumeTagTotalDto` Success
   */
  static apiResumepartResumetagsResumeidGet = (resumeid: number, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<ResumeTagTotalDto>>>({
      url: `/api/resumepart/resumetags/${resumeid}`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartSaveresumetagPost
   * @summary 保存优势标签
   * @request POST:/api/resumepart/saveresumetag
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiResumepartSaveresumetagPost = (data: ResumeTagInputDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/resumepart/saveresumetag`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartResumetagPost
   * @summary 删除优势标签
   * @request POST:/api/resumepart/resumetag
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartResumetagPost = (data: ResumeTagDeleteDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/resumetag`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartResumetagDelete
   * @summary 删除优势标签
   * @request DELETE:/api/resumepart/resumetag
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartResumetagDelete = (data: ResumeTagDeleteDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/resumetag`,
      method: "DELETE",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartUpdateworkingstatePost
   * @summary 保存个人目前工作状态
   * @request POST:/api/resumepart/updateworkingstate
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartUpdateworkingstatePost = (
    query: ApiResumepartUpdateworkingstatePostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/updateworkingstate` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartCompletestateResumeidGet
   * @summary 获取个人简历各项完成度
   * @request GET:/api/resumepart/completestate/{resumeid}
   * @secure
   * @response `200` `RestfulResultCompleteItem` Success
   */
  static apiResumepartCompletestateResumeidGet = (resumeid: number, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<CompleteItem>>>({
      url: `/api/resumepart/completestate/${resumeid}`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags resumepart
   * @name ApiResumepartTagsPost
   * @summary 批量更新系统标签
   * @request POST:/api/resumepart/tags
   * @secure
   * @response `200` `void` Success
   */
  static apiResumepartTagsPost = (data: ResumeTagTotalDtoResumeId, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/resumepart/tags`,
      method: "POST",
      data: data,
      config: options,
    });
}
