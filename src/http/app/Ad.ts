/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  AdDto,
  ApiAdAdlistGetParams,
  ApiAdIndexpopupadlistGetParams,
  ApiAdNoticeadlistGetParams,
  ApiAdRecordpushinfologPostParams,
  ApiAdSetsdkadlogPostParams,
  ApiAdSetvistadlogPostParams,
  PagedListAdvertisementNoticeListOutput,
} from "./data-contracts";

export class AdClass {
  /**
   * No description
   *
   * @tags ad
   * @name ApiAdAdlistGet
   * @summary 个人登录页广告(10分钟缓存)
   * @request GET:/api/ad/adlist
   * @secure
   * @response `200` `RestfulResultListAdDto` Success
   */
  static apiAdAdlistGet = (query: ApiAdAdlistGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<AdDto>>>({
      url: `/api/ad/adlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags ad
   * @name ApiAdIndexpopupadlistGet
   * @summary 个人首页弹窗广告
   * @request GET:/api/ad/indexpopupadlist
   * @secure
   * @response `200` `RestfulResultListAdDto` Success
   */
  static apiAdIndexpopupadlistGet = (
    query: ApiAdIndexpopupadlistGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<AdDto>>>({
      url: `/api/ad/indexpopupadlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags ad
   * @name ApiAdSetvistadlogPost
   * @summary 记录浏览广告
   * @request POST:/api/ad/setvistadlog
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiAdSetvistadlogPost = (
    query: ApiAdSetvistadlogPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/ad/setvistadlog` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags ad
   * @name ApiAdSetsdkadlogPost
   * @summary 记录个人浏览第三方广告
   * @request POST:/api/ad/setsdkadlog
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiAdSetsdkadlogPost = (query: ApiAdSetsdkadlogPostParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/ad/setsdkadlog` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags ad
   * @name ApiAdNoticeadlistGet
   * @summary 通知中心 个人首页弹窗广告
   * @request GET:/api/ad/noticeadlist
   * @secure
   * @response `200` `RestfulResultPagedListAdvertisementNoticeListOutput` Success
   */
  static apiAdNoticeadlistGet = (query: ApiAdNoticeadlistGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<PagedListAdvertisementNoticeListOutput>>>({
      url: `/api/ad/noticeadlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags ad
   * @name ApiAdRecordpushinfologPost
   * @summary 记录推送记录
   * @request POST:/api/ad/recordpushinfolog
   * @secure
   * @response `200` `void` Success
   */
  static apiAdRecordpushinfologPost = (
    query: ApiAdRecordpushinfologPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/ad/recordpushinfolog` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
}
