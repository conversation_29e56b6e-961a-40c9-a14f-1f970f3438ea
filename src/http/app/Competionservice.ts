/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiCompetionserviceBuyPostParams,
  ApiCompetionserviceInfoGetParams,
} from "./data-contracts";

export class CompetionserviceClass {
  /**
   * No description
   *
   * @tags competionservice
   * @name ApiCompetionserviceInfoGet
   * @summary 未投递竞争力排序情况 以及是否有查询竞争力权限
   * @request GET:/api/competionservice/info
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiCompetionserviceInfoGet = (
    query: ApiCompetionserviceInfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/competionservice/info` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags competionservice
   * @name ApiCompetionserviceBuyPost
   * @summary 扣点购买竞争力服务
   * @request POST:/api/competionservice/buy
   * @secure
   * @response `200` `void` Success
   */
  static apiCompetionserviceBuyPost = (
    query: ApiCompetionserviceBuyPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/competionservice/buy` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
}
