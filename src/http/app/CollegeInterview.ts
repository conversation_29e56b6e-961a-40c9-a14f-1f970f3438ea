/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiCollegeInterviewApplyGetParams,
  ApiCollegeInterviewChangeTrackGetParams,
  ApiCollegeInterviewChangeZoneGetParams,
  ApplyPrcoess,
  ApplySimpleModel,
  ApplyTrack,
  CollegeInterviewApplyOutput,
  CollegeInterviewZoneMode,
} from "./data-contracts";

export class CollegeInterviewClass {
  /**
   * No description
   *
   * @tags CollegeInterview
   * @name ApiCollegeInterviewApplyGet
   * @summary 报名
   * @request GET:/api/CollegeInterview/Apply
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiCollegeInterviewApplyGet = (
    query: ApiCollegeInterviewApplyGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/CollegeInterview/Apply` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags CollegeInterview
   * @name ApiCollegeInterviewChangeZoneGet
   * @summary 更换赛区
   * @request GET:/api/CollegeInterview/ChangeZone
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiCollegeInterviewChangeZoneGet = (
    query: ApiCollegeInterviewChangeZoneGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/CollegeInterview/ChangeZone` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags CollegeInterview
   * @name ApiCollegeInterviewChangeTrackGet
   * @summary 更换赛道
   * @request GET:/api/CollegeInterview/ChangeTrack
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiCollegeInterviewChangeTrackGet = (
    query: ApiCollegeInterviewChangeTrackGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/CollegeInterview/ChangeTrack` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags CollegeInterview
   * @name ApiCollegeInterviewApplyInfoGet
   * @summary 报名信息
   * @request GET:/api/CollegeInterview/ApplyInfo
   * @secure
   * @response `200` `RestfulResultCollegeInterviewApplyOutput` Success
   */
  static apiCollegeInterviewApplyInfoGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<CollegeInterviewApplyOutput>>>({
      url: `/api/CollegeInterview/ApplyInfo`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags CollegeInterview
   * @name ApiCollegeInterviewApplySimpleInfoGet
   * @summary 简易报名信息
   * @request GET:/api/CollegeInterview/ApplySimpleInfo
   * @secure
   * @response `200` `RestfulResultApplySimpleModel` Success
   */
  static apiCollegeInterviewApplySimpleInfoGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<ApplySimpleModel>>>({
      url: `/api/CollegeInterview/ApplySimpleInfo`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags CollegeInterview
   * @name ApiCollegeInterviewApplyProcessGet
   * @summary 参赛进程信息
   * @request GET:/api/CollegeInterview/ApplyProcess
   * @secure
   * @response `200` `RestfulResultListApplyPrcoess` Success
   */
  static apiCollegeInterviewApplyProcessGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<ApplyPrcoess>>>({
      url: `/api/CollegeInterview/ApplyProcess`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags CollegeInterview
   * @name ApiCollegeInterviewZoneListGet
   * @summary 赛区列表
   * @request GET:/api/CollegeInterview/ZoneList
   * @secure
   * @response `200` `RestfulResultListCollegeInterviewZoneMode` Success
   */
  static apiCollegeInterviewZoneListGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<CollegeInterviewZoneMode>>>({
      url: `/api/CollegeInterview/ZoneList`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags CollegeInterview
   * @name ApiCollegeInterviewTrackListGet
   * @summary 赛道列表
   * @request GET:/api/CollegeInterview/TrackList
   * @secure
   * @response `200` `RestfulResultListApplyTrack` Success
   */
  static apiCollegeInterviewTrackListGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<ApplyTrack>>>({
      url: `/api/CollegeInterview/TrackList`,
      method: "GET",
      config: options,
    });
}
