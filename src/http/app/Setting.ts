/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiSettingAccountbindGetParams,
  ApiSettingChangeidentityPostParams,
  ApiSettingWxandbindPostParams,
  AppleBindDto,
  ChangePasswordDto,
  DestoryAccountDto,
  DestroyReasonDto,
  NNIABindDto,
  PhoneBindDto,
  QqBindDto,
  SetPasswordDto,
  VipIdentityAccountResult,
  WxBindDto,
} from "./data-contracts";

export class SettingClass {
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingGetphoneGet
   * @summary 获取手机号带*号
   * @request GET:/api/setting/getphone
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiSettingGetphoneGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/setting/getphone`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingChangepasswordidentityPost
   * @summary 修改密码的身份
   * @request POST:/api/setting/changepasswordidentity
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiSettingChangepasswordidentityPost = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/setting/changepasswordidentity`,
      method: "POST",
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingSetpasswordPost
   * @summary 修改密码(ChangePassWordIdentity返回2时)
   * @request POST:/api/setting/setpassword
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingSetpasswordPost = (data: SetPasswordDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/setpassword`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingChangepasswordPost
   * @summary 修改密码(ChangePassWordIdentity返回1时)
   * @request POST:/api/setting/changepassword
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingChangepasswordPost = (data: ChangePasswordDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/changepassword`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingAccountbindGet
   * @summary 账号绑定信息
   * @request GET:/api/setting/accountbind
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiSettingAccountbindGet = (
    query: ApiSettingAccountbindGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/setting/accountbind` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingChangephonebindPost
   * @summary 修改手机绑定
   * @request POST:/api/setting/changephonebind
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingChangephonebindPost = (data: PhoneBindDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/changephonebind`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingQqbindPost
   * @summary QQ绑定
   * @request POST:/api/setting/qqbind
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingQqbindPost = (data: QqBindDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/qqbind`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingQqunbindPost
   * @summary QQ解绑
   * @request POST:/api/setting/qqunbind
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingQqunbindPost = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/qqunbind`,
      method: "POST",
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingNniabindPost
   * @summary 国家网络身份认证(National Network Identity Authentication)绑定
   * @request POST:/api/setting/nniabind
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingNniabindPost = (data: NNIABindDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/nniabind`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingNniaunbindPost
   * @summary 国家网络身份认证(National Network Identity Authentication)解绑
   * @request POST:/api/setting/nniaunbind
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingNniaunbindPost = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/nniaunbind`,
      method: "POST",
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingApplebindPost
   * @summary 苹果绑定
   * @request POST:/api/setting/applebind
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingApplebindPost = (data: AppleBindDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/applebind`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingAppleunbindPost
   * @summary 苹果解绑
   * @request POST:/api/setting/appleunbind
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingAppleunbindPost = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/appleunbind`,
      method: "POST",
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingWxbindPost
   * @summary 微信绑定
   * @request POST:/api/setting/wxbind
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingWxbindPost = (data: WxBindDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/wxbind`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingWxandbindPost
   * @summary PC端微信账号绑定
   * @request POST:/api/setting/wxandbind
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingWxandbindPost = (
    query: ApiSettingWxandbindPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/wxandbind` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingWxunbindPost
   * @summary 微信解绑
   * @request POST:/api/setting/wxunbind
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingWxunbindPost = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/wxunbind`,
      method: "POST",
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingDestroyreasonlistGet
   * @summary 获取注销账号的原因列表(99:表示其他，这个时候需要求职者手动填写原因)
   * @request GET:/api/setting/destroyreasonlist
   * @secure
   * @response `200` `RestfulResultListDestroyReasonDto` Success
   */
  static apiSettingDestroyreasonlistGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<DestroyReasonDto>>>({
      url: `/api/setting/destroyreasonlist`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingDestoryaccountPost
   * @summary 注销账号
   * @request POST:/api/setting/destoryaccount
   * @secure
   * @response `200` `void` Success
   */
  static apiSettingDestoryaccountPost = (data: DestoryAccountDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/setting/destoryaccount`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags setting
   * @name ApiSettingChangeidentityPost
   * @summary 切换身份
   * @request POST:/api/setting/changeidentity
   * @secure
   * @response `200` `RestfulResultVipIdentityAccountResult` Success
   */
  static apiSettingChangeidentityPost = (
    query: ApiSettingChangeidentityPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<VipIdentityAccountResult>>>({
      url: `/api/setting/changeidentity` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
}
