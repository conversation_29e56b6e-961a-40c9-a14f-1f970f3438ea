/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  AlbumDto,
  AlbumSortModel,
  ApiPhotoAlbumdeleteDeleteParams,
  ApiPhotoUploadavatarIsoptionalavatarPostParams,
} from "./data-contracts";

export class PhotoClass {
  /**
   * No description
   *
   * @tags photo
   * @name ApiPhotoAlbumlistGet
   * @summary 相册列表
   * @request GET:/api/photo/albumlist
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiPhotoAlbumlistGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/photo/albumlist`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags photo
   * @name ApiPhotoAvatarGet
   * @summary 获取头像
   * @request GET:/api/photo/avatar
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiPhotoAvatarGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/photo/avatar`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags photo
   * @name ApiPhotoAlbumdeleteDelete
   * @summary 相册删除(形象照大图，一共有4张)
   * @request DELETE:/api/photo/albumdelete
   * @secure
   * @response `200` `void` Success
   */
  static apiPhotoAlbumdeleteDelete = (
    query: ApiPhotoAlbumdeleteDeleteParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/photo/albumdelete` + dataToQuery(query),
      method: "DELETE",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags photo
   * @name ApiPhotoAlbumsortPost
   * @summary 相册排序
   * @request POST:/api/photo/albumsort
   * @secure
   * @response `200` `void` Success
   */
  static apiPhotoAlbumsortPost = (data: AlbumSortModel, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/photo/albumsort`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags photo
   * @name ApiPhotoAvatardeleteDelete
   * @summary 头象删除（简历上的头像）
   * @request DELETE:/api/photo/avatardelete
   * @secure
   * @response `200` `void` Success
   */
  static apiPhotoAvatardeleteDelete = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/photo/avatardelete`,
      method: "DELETE",
      config: options,
    });
  /**
   * No description
   *
   * @tags photo
   * @name ApiPhotoUploadalbumPost
   * @summary 上传相册
   * @request POST:/api/photo/uploadalbum
   * @secure
   * @response `200` `RestfulResultAlbumDto` Success
   */
  static apiPhotoUploadalbumPost = (
    data: {
      /** @format binary */
      image?: File;
    },
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<AlbumDto>>>({
      url: `/api/photo/uploadalbum`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags photo
   * @name ApiPhotoUploadavatarIsoptionalavatarPost
   * @summary 上传头像
   * @request POST:/api/photo/uploadavatar/{isoptionalavatar}
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiPhotoUploadavatarIsoptionalavatarPost = (
    { isoptionalavatar, ...query }: ApiPhotoUploadavatarIsoptionalavatarPostParams,
    data: {
      /** @format binary */
      image?: File;
    },
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/photo/uploadavatar/${isoptionalavatar}` + dataToQuery(query),
      method: "POST",
      query: query,
      data: data,
      config: options,
    });
}
