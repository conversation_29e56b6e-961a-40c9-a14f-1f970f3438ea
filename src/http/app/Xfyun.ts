/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import { AuthUrlModel } from "./data-contracts";

export class XfyunClass {
  /**
   * No description
   *
   * @tags xfyun
   * @name ApiXfyunXunfeiauthurlGet
   * @summary 获取鉴权
   * @request GET:/api/xfyun/xunfeiauthurl
   * @secure
   * @response `200` `RestfulResultAuthUrlModel` Success
   */
  static apiXfyunXunfeiauthurlGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<AuthUrlModel>>>({
      url: `/api/xfyun/xunfeiauthurl`,
      method: "GET",
      config: options,
    });
}
