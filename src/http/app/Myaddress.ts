/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiMyaddressAreacityjsonGetParams,
  AreaCityDto,
  JobseekerAddressDto,
  MyAddressDto,
} from "./data-contracts";

export class MyaddressClass {
  /**
   * No description
   *
   * @tags myaddress
   * @name ApiMyaddressAddresslistGet
   * @summary 我的地址列表
   * @request GET:/api/myaddress/addresslist
   * @secure
   * @response `200` `RestfulResultMyAddressDto` Success
   */
  static apiMyaddressAddresslistGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<MyAddressDto>>>({
      url: `/api/myaddress/addresslist`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags myaddress
   * @name ApiMyaddressDeleteIdDelete
   * @summary 删除地址
   * @request DELETE:/api/myaddress/delete/{id}
   * @secure
   * @response `200` `void` Success
   */
  static apiMyaddressDeleteIdDelete = (id: number, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/myaddress/delete/${id}`,
      method: "DELETE",
      config: options,
    });
  /**
   * No description
   *
   * @tags myaddress
   * @name ApiMyaddressGetaddressIdGet
   * @summary 获取编辑地址
   * @request GET:/api/myaddress/getaddress/{id}
   * @secure
   * @response `200` `RestfulResultJobseekerAddressDto` Success
   */
  static apiMyaddressGetaddressIdGet = (id: number, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<JobseekerAddressDto>>>({
      url: `/api/myaddress/getaddress/${id}`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags myaddress
   * @name ApiMyaddressEditPost
   * @summary 保存地址
   * @request POST:/api/myaddress/edit
   * @secure
   * @response `200` `void` Success
   */
  static apiMyaddressEditPost = (data: JobseekerAddressDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/myaddress/edit`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags myaddress
   * @name ApiMyaddressAreacityjsonGet
   * @summary 获取省市县
   * @request GET:/api/myaddress/areacityjson
   * @secure
   * @response `200` `RestfulResultListAreaCityDto` Success
   */
  static apiMyaddressAreacityjsonGet = (
    query: ApiMyaddressAreacityjsonGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<AreaCityDto>>>({
      url: `/api/myaddress/areacityjson` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
}
