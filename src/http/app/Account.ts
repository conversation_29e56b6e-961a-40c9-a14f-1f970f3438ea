/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  <PERSON><PERSON><PERSON>ult,
  ApiAccountBindminiaccountPostParams,
  ApiAccountDoswitchaccounttojobseekerPostParams,
  ApiAccountJumptoanywhereGetParams,
  AppSignInModel,
  DouYinEncrypModel,
  EnumLoginFromdevice,
  LoginByNNIAOutputDto,
  LoginByPhoneInputDto,
  LoginByPhoneOutputDto,
  LoginByWxQQOutputDto,
  LoginInputDto,
  LoginOutDto,
  NNIALoginDto,
  OneLoginInputDto,
  OneLoginIntegrateInputDto,
  QqLoginDto,
  QrCodeLoginInputDto,
  QrCodeLoginOutputDto,
  WeixinMiniLoginDto,
  ZhiFuBaoEncrypBaseModel,
} from "./data-contracts";

export class AccountClass {
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountLoginPost
   * @summary 登录
   * @request POST:/api/account/login
   * @secure
   * @response `200` `RestfulResultLoginByPhoneOutputDto` Success
   */
  static apiAccountLoginPost = (data: LoginInputDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LoginByPhoneOutputDto>>>({
      url: `/api/account/login`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountLoginbyphonePost
   * @summary 手机号登录
   * @request POST:/api/account/loginbyphone
   * @secure
   * @response `200` `RestfulResultLoginByPhoneOutputDto` Success
   */
  static apiAccountLoginbyphonePost = (data: LoginByPhoneInputDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LoginByPhoneOutputDto>>>({
      url: `/api/account/loginbyphone`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountDoswitchaccounttojobseekerPost
   * @summary 企业 切换 求职者身份 登录
   * @request POST:/api/account/doswitchaccounttojobseeker
   * @secure
   * @response `200` `RestfulResultLoginByPhoneOutputDto` Success
   */
  static apiAccountDoswitchaccounttojobseekerPost = (
    query: ApiAccountDoswitchaccounttojobseekerPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<LoginByPhoneOutputDto>>>({
      url: `/api/account/doswitchaccounttojobseeker` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountDoswitchaccounttoenterprisePost
   * @summary 求职者身份切换企业
   * @request POST:/api/account/doswitchaccounttoenterprise
   * @secure
   * @response `200` `RestfulResultString` Success
   */
  static apiAccountDoswitchaccounttoenterprisePost = (
    data: EnumLoginFromdevice,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<String>>>({
      url: `/api/account/doswitchaccounttoenterprise`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountLogindouyinbyphonePost
   * @summary 手机验证码登录 抖音小程序专用
   * @request POST:/api/account/logindouyinbyphone
   * @secure
   * @response `200` `RestfulResultLoginByPhoneOutputDto` Success
   */
  static apiAccountLogindouyinbyphonePost = (
    data: LoginByPhoneInputDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<LoginByPhoneOutputDto>>>({
      url: `/api/account/logindouyinbyphone`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountLoginbyweixinminiPost
   * @summary 微信小程序登录
   * @request POST:/api/account/loginbyweixinmini
   * @secure
   * @response `200` `RestfulResultLoginByPhoneOutputDto` Success
   */
  static apiAccountLoginbyweixinminiPost = (
    data: WeixinMiniLoginDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<LoginByPhoneOutputDto>>>({
      url: `/api/account/loginbyweixinmini`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountCaptchaGet
   * @summary 生成中文验证码
   * @request GET:/api/account/captcha
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiAccountCaptchaGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/account/captcha`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountLogoutPost
   * @summary 登出，请清空本地的token跟refreshtoken
   * @request POST:/api/account/logout
   * @secure
   * @response `200` `void` Success
   */
  static apiAccountLogoutPost = (data: LoginOutDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/account/logout`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountJumptoanywhereGet
   * @request GET:/api/account/jumptoanywhere
   * @secure
   * @response `200` `RestfulResultActionResult` Success
   */
  static apiAccountJumptoanywhereGet = (
    query: ApiAccountJumptoanywhereGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ActionResult>>>({
      url: `/api/account/jumptoanywhere` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountBindminiaccountPost
   * @request POST:/api/account/bindminiaccount
   * @secure
   * @response `200` `RestfulResultString` Success
   */
  static apiAccountBindminiaccountPost = (
    query: ApiAccountBindminiaccountPostParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<String>>>({
      url: `/api/account/bindminiaccount` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountLiuZhouWondersYunLoginPhoneGet
   * @summary 柳州龙城市民云APP人才集团专区页面登录/注册广西人才网
   * @request GET:/api/account/liuZhouWondersYunLogin/{phone}
   * @secure
   * @response `200` `RestfulResultLoginByPhoneOutputDto` Success
   */
  static apiAccountLiuZhouWondersYunLoginPhoneGet = (phone: string, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LoginByPhoneOutputDto>>>({
      url: `/api/account/liuZhouWondersYunLogin/${phone}`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountDouyinminiloginPost
   * @summary 抖音小程序登录
   * @request POST:/api/account/douyinminilogin
   * @secure
   * @response `200` `RestfulResultLoginByPhoneOutputDto` Success
   */
  static apiAccountDouyinminiloginPost = (data: DouYinEncrypModel, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LoginByPhoneOutputDto>>>({
      url: `/api/account/douyinminilogin`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountZhifubaominiloginPost
   * @summary 支付宝小程序登录
   * @request POST:/api/account/zhifubaominilogin
   * @secure
   * @response `200` `RestfulResultLoginByPhoneOutputDto` Success
   */
  static apiAccountZhifubaominiloginPost = (
    data: ZhiFuBaoEncrypBaseModel,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<LoginByPhoneOutputDto>>>({
      url: `/api/account/zhifubaominilogin`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountOneloginPost
   * @summary OneLogin app
   * @request POST:/api/account/onelogin
   * @secure
   * @response `200` `RestfulResultLoginByPhoneOutputDto` Success
   */
  static apiAccountOneloginPost = (data: OneLoginInputDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LoginByPhoneOutputDto>>>({
      url: `/api/account/onelogin`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountOneloginintegratePost
   * @summary onelogin 手机验证
   * @request POST:/api/account/oneloginintegrate
   * @secure
   * @response `200` `RestfulResultLoginByPhoneOutputDto` Success
   */
  static apiAccountOneloginintegratePost = (
    data: OneLoginIntegrateInputDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<LoginByPhoneOutputDto>>>({
      url: `/api/account/oneloginintegrate`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountLoginbyqqPost
   * @summary qq登录
   * @request POST:/api/account/loginbyqq
   * @secure
   * @response `200` `RestfulResultLoginByWxQQOutputDto` Success
   */
  static apiAccountLoginbyqqPost = (data: QqLoginDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LoginByWxQQOutputDto>>>({
      url: `/api/account/loginbyqq`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountLoginbywxPost
   * @summary 微信登录
   * @request POST:/api/account/loginbywx
   * @secure
   * @response `200` `RestfulResultLoginByWxQQOutputDto` Success
   */
  static apiAccountLoginbywxPost = (data: QqLoginDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LoginByWxQQOutputDto>>>({
      url: `/api/account/loginbywx`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountLoginbynniaPost
   * @summary 国家网络身份认证登录
   * @request POST:/api/account/loginbynnia
   * @secure
   * @response `200` `RestfulResultLoginByNNIAOutputDto` Success
   */
  static apiAccountLoginbynniaPost = (data: NNIALoginDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LoginByNNIAOutputDto>>>({
      url: `/api/account/loginbynnia`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountLoginbyqrcodePost
   * @summary 二维码扫码登录网页版(旧版QrLogin/Index接口)
   * @request POST:/api/account/loginbyqrcode
   * @secure
   * @response `200` `RestfulResultQrCodeLoginOutputDto` Success
   */
  static apiAccountLoginbyqrcodePost = (data: QrCodeLoginInputDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<QrCodeLoginOutputDto>>>({
      url: `/api/account/loginbyqrcode`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags account
   * @name ApiAccountApplesigninPost
   * @summary Apple验证
   * @request POST:/api/account/applesignin
   * @secure
   * @response `200` `RestfulResultLoginByWxQQOutputDto` Success
   */
  static apiAccountApplesigninPost = (data: AppSignInModel, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LoginByWxQQOutputDto>>>({
      url: `/api/account/applesignin`,
      method: "POST",
      data: data,
      config: options,
    });
}
