/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiOssDelattachmentfileGetParams,
  ApiOssUploadattachmentfilewithfilenamePostParams,
  OssPutObjectCallback,
} from "./data-contracts";

export class OssClass {
  /**
   * No description
   *
   * @tags oss
   * @name ApiOssStsGet
   * @summary sts服务器
   * @request GET:/api/oss/sts
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiOssStsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/oss/sts`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags oss
   * @name ApiOssCallbackputPost
   * @summary 上传成功回调
   * @request POST:/api/oss/callbackput
   * @secure
   * @response `200` `void` Success
   */
  static apiOssCallbackputPost = (data: OssPutObjectCallback, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/oss/callbackput`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags oss
   * @name ApiOssCallbackremovedPost
   * @summary 删除成功回调
   * @request POST:/api/oss/callbackremoved
   * @secure
   * @response `200` `void` Success
   */
  static apiOssCallbackremovedPost = (data: OssPutObjectCallback, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/oss/callbackremoved`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags oss
   * @name ApiOssUploadattachmentfilePost
   * @summary 简历附件上传
   * @request POST:/api/oss/uploadattachmentfile
   * @secure
   * @response `200` `void` Success
   */
  static apiOssUploadattachmentfilePost = (
    data: {
      /** @format binary */
      file?: File;
    },
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/oss/uploadattachmentfile`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags oss
   * @name ApiOssUploadattachmentfilewithfilenamePost
   * @summary 简历附件上传
   * @request POST:/api/oss/uploadattachmentfilewithfilename
   * @secure
   * @response `200` `void` Success
   */
  static apiOssUploadattachmentfilewithfilenamePost = (
    query: ApiOssUploadattachmentfilewithfilenamePostParams,
    data: {
      /** @format binary */
      file?: File;
    },
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/oss/uploadattachmentfilewithfilename` + dataToQuery(query),
      method: "POST",
      query: query,
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags oss
   * @name ApiOssDelattachmentfileGet
   * @summary 删除附件
   * @request GET:/api/oss/delattachmentfile
   * @secure
   * @response `200` `void` Success
   */
  static apiOssDelattachmentfileGet = (
    query: ApiOssDelattachmentfileGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/oss/delattachmentfile` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags oss
   * @name ApiOssOcrupdatefilePost
   * @summary 图片识别
   * @request POST:/api/oss/ocrupdatefile
   * @secure
   * @response `200` `RestfulResultString` Success
   */
  static apiOssOcrupdatefilePost = (
    data: {
      /** @format binary */
      file?: File;
    },
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<String>>>({
      url: `/api/oss/ocrupdatefile`,
      method: "POST",
      data: data,
      config: options,
    });
}
