/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import { ApiAttachmentPreviewGetParams } from "./data-contracts";

export class AttachmentClass {
  /**
   * No description
   *
   * @tags attachment
   * @name ApiAttachmentAttachmentlistGet
   * @summary 附件列表
   * @request GET:/api/attachment/attachmentlist
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiAttachmentAttachmentlistGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/attachment/attachmentlist`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags attachment
   * @name ApiAttachmentPreviewGet
   * @summary 附件预览(只返回url)
   * @request GET:/api/attachment/preview
   * @secure
   * @response `200` `RestfulResultObject` Success
   */
  static apiAttachmentPreviewGet = (
    query: ApiAttachmentPreviewGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Object>>>({
      url: `/api/attachment/preview` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
}
