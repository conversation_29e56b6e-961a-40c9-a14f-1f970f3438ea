/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiDownLoadResumeDocGetParams,
  ApiDownLoadResumePdfGetParams,
  IActionResult,
} from "./data-contracts";

export class DownLoadClass {
  /**
   * No description
   *
   * @tags DownLoad
   * @name ApiDownLoadResumeDocGet
   * @summary 简历下载doc版
   * @request GET:/api/DownLoad/ResumeDoc
   * @secure
   * @response `200` `RestfulResultIActionResult` Success
   */
  static apiDownLoadResumeDocGet = (
    query: ApiDownLoadResumeDocGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<IActionResult>>>({
      url: `/api/DownLoad/ResumeDoc` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags DownLoad
   * @name ApiDownLoadResumePdfGet
   * @summary 简历下载pdf版
   * @request GET:/api/DownLoad/ResumePdf
   * @secure
   * @response `200` `RestfulResultIActionResult` Success
   */
  static apiDownLoadResumePdfGet = (
    query: ApiDownLoadResumePdfGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<IActionResult>>>({
      url: `/api/DownLoad/ResumePdf` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
}
