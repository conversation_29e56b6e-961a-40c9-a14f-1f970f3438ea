/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiBigdataCoursedatasGetParams,
  CourseClickLogModel,
  CourseDataModel,
} from "./data-contracts";

export class BigdataClass {
  /**
   * No description
   *
   * @tags bigdata
   * @name ApiBigdataCoursedatasGet
   * @summary 获取推荐的课程
   * @request GET:/api/bigdata/coursedatas
   * @secure
   * @response `200` `RestfulResultListCourseDataModel` Success
   */
  static apiBigdataCoursedatasGet = (
    query: ApiBigdataCoursedatasGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<CourseDataModel>>>({
      url: `/api/bigdata/coursedatas` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags bigdata
   * @name ApiBigdataClickPost
   * @summary 添加点击日志
   * @request POST:/api/bigdata/click
   * @secure
   * @response `200` `void` Success
   */
  static apiBigdataClickPost = (data: CourseClickLogModel, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/bigdata/click`,
      method: "POST",
      data: data,
      config: options,
    });
}
