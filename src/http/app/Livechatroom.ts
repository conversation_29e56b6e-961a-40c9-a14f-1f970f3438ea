/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiLivechatroomAddrGetParams,
  ApiLivechatroomHistorymsgpagelistLiveguidGetParams,
  AskPositionInput,
  PagerHistoryMsgItemOutput,
  PostPositionInput,
  ResultOutput,
  SendMsgInput,
  SendNoticeMsgInput,
} from "./data-contracts";

export class LivechatroomClass {
  /**
   * No description
   *
   * @tags livechatroom
   * @name ApiLivechatroomHistorymsgpagelistLiveguidGet
   * @summary 查询历史聊天数据
   * @request GET:/api/livechatroom/historymsgpagelist/{liveguid}
   * @secure
   * @response `200` `RestfulResultPagerHistoryMsgItemOutput` Success
   */
  static apiLivechatroomHistorymsgpagelistLiveguidGet = (
    { liveguid, ...query }: ApiLivechatroomHistorymsgpagelistLiveguidGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<PagerHistoryMsgItemOutput>>>({
      url: `/api/livechatroom/historymsgpagelist/${liveguid}` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags livechatroom
   * @name ApiLivechatroomAddrGet
   * @summary 获取聊天室的地址
   * @request GET:/api/livechatroom/addr
   * @secure
   * @response `200` `RestfulResultListString` Success
   */
  static apiLivechatroomAddrGet = (
    query: ApiLivechatroomAddrGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<String>>>({
      url: `/api/livechatroom/addr` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags livechatroom
   * @name ApiLivechatroomSendnoticemsgPost
   * @summary 发送通知消息：进入直播间、点赞、分享直播间
   * @request POST:/api/livechatroom/sendnoticemsg
   * @secure
   * @response `200` `RestfulResultResultOutput` Success
   */
  static apiLivechatroomSendnoticemsgPost = (
    data: SendNoticeMsgInput,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ResultOutput>>>({
      url: `/api/livechatroom/sendnoticemsg`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags livechatroom
   * @name ApiLivechatroomSendmsgPost
   * @summary 发送聊天内容
   * @request POST:/api/livechatroom/sendmsg
   * @secure
   * @response `200` `RestfulResultResultOutput` Success
   */
  static apiLivechatroomSendmsgPost = (data: SendMsgInput, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<ResultOutput>>>({
      url: `/api/livechatroom/sendmsg`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags livechatroom
   * @name ApiLivechatroomAskpositionPost
   * @summary 求讲解职位
   * @request POST:/api/livechatroom/askposition
   * @secure
   * @response `200` `RestfulResultResultOutput` Success
   */
  static apiLivechatroomAskpositionPost = (data: AskPositionInput, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<ResultOutput>>>({
      url: `/api/livechatroom/askposition`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags livechatroom
   * @name ApiLivechatroomPositionPost
   * @summary 投递职位调用发送消息
   * @request POST:/api/livechatroom/position
   * @secure
   * @response `200` `RestfulResultResultOutput` Success
   */
  static apiLivechatroomPositionPost = (data: PostPositionInput, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<ResultOutput>>>({
      url: `/api/livechatroom/position`,
      method: "POST",
      data: data,
      config: options,
    });
}
