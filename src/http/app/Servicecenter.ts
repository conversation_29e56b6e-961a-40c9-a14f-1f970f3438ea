/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiServiceCenterAuthpageinfoGetParams,
  ApiServiceCenterGrantauthGetParams,
  VAuthorizationPageInfo,
} from "./data-contracts";

export class ServicecenterClass {
  /**
   * No description
   *
   * @tags servicecenter
   * @name ApiServiceCenterAuthpageinfoGet
   * @summary 请求logo、用户协议URL
   * @request GET:/api/ServiceCenter/authpageinfo
   * @secure
   * @response `200` `RestfulResultVAuthorizationPageInfo` Success
   */
  static apiServiceCenterAuthpageinfoGet = (
    query: ApiServiceCenterAuthpageinfoGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<VAuthorizationPageInfo>>>({
      url: `/api/ServiceCenter/authpageinfo` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags servicecenter
   * @name ApiServiceCenterGrantauthGet
   * @summary 进行授权
   * @request GET:/api/ServiceCenter/grantauth
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiServiceCenterGrantauthGet = (
    query: ApiServiceCenterGrantauthGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/ServiceCenter/grantauth` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
}
