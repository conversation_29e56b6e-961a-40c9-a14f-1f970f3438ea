/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiMeetingmyReserveresumeprintGetParams,
  ApiMeetingmyTicketGetParams,
  QueryTicketModel,
  ReserveResumePrintInput,
  ReserveResumePrintOutput,
  SchoolMeetingInfoForTicketOutput,
  TicketCollectionModel,
} from "./data-contracts";

export class MeetingmyClass {
  /**
   * No description
   *
   * @tags meetingmy
   * @name ApiMeetingmyReserveresumeprintPost
   * @summary 预约简历打印
   * @request POST:/api/meetingmy/reserveresumeprint
   * @secure
   * @response `200` `void` Success
   */
  static apiMeetingmyReserveresumeprintPost = (
    data: ReserveResumePrintInput,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/meetingmy/reserveresumeprint`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags meetingmy
   * @name ApiMeetingmyReserveresumeprintGet
   * @summary 获取预约简历打印信息
   * @request GET:/api/meetingmy/reserveresumeprint
   * @secure
   * @response `200` `RestfulResultReserveResumePrintOutput` Success
   */
  static apiMeetingmyReserveresumeprintGet = (
    query: ApiMeetingmyReserveresumeprintGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<ReserveResumePrintOutput>>>({
      url: `/api/meetingmy/reserveresumeprint` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags meetingmy
   * @name ApiMeetingmySchoolmeetinginfoforticketSchoolmeetingidGet
   * @summary 获取用于领券的校园招聘会信息
   * @request GET:/api/meetingmy/schoolmeetinginfoforticket/{schoolmeetingid}
   * @secure
   * @response `200` `RestfulResultSchoolMeetingInfoForTicketOutput` Success
   */
  static apiMeetingmySchoolmeetinginfoforticketSchoolmeetingidGet = (
    schoolmeetingid: number,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<SchoolMeetingInfoForTicketOutput>>>({
      url: `/api/meetingmy/schoolmeetinginfoforticket/${schoolmeetingid}`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags meetingmy
   * @name ApiMeetingmyTicketcollectionPost
   * @summary 领取门票
   * @request POST:/api/meetingmy/ticketcollection
   * @secure
   * @response `200` `RestfulResultString` Success
   */
  static apiMeetingmyTicketcollectionPost = (
    data: TicketCollectionModel,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<String>>>({
      url: `/api/meetingmy/ticketcollection`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags meetingmy
   * @name ApiMeetingmyTokenGet
   * @summary 获取token
   * @request GET:/api/meetingmy/token
   * @secure
   * @response `200` `RestfulResultString` Success
   */
  static apiMeetingmyTokenGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<String>>>({
      url: `/api/meetingmy/token`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags meetingmy
   * @name ApiMeetingmyTicketGet
   * @summary 查询门票信息
   * @request GET:/api/meetingmy/ticket
   * @secure
   * @response `200` `RestfulResultQueryTicketModel` Success
   */
  static apiMeetingmyTicketGet = (
    query: ApiMeetingmyTicketGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<QueryTicketModel>>>({
      url: `/api/meetingmy/ticket` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
}
