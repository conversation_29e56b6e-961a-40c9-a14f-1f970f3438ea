/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import { FindPasswordEmailDto, FindPasswordPhoneDto } from "./data-contracts";

export class FindpasswordClass {
  /**
   * No description
   *
   * @tags findpassword
   * @name ApiFindpasswordPhonePost
   * @summary 通过手机找回密码
   * @request POST:/api/findpassword/phone
   * @secure
   * @response `200` `void` Success
   */
  static apiFindpasswordPhonePost = (data: FindPasswordPhoneDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/findpassword/phone`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags findpassword
   * @name ApiFindpasswordEmailPost
   * @summary 通过邮件找回密码
   * @request POST:/api/findpassword/email
   * @secure
   * @response `200` `void` Success
   */
  static apiFindpasswordEmailPost = (data: FindPasswordEmailDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/findpassword/email`,
      method: "POST",
      data: data,
      config: options,
    });
}
