/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiLiveListGetParams,
  ApiLiveLiveinfoLiveguidGetParams,
  ApiLiveRemindlistGetParams,
  ApiLiveSetremindPostParams,
  ApiLiveSetupPostParams,
  LiveDetailDto,
  LiveInfoDto,
  LiveRecordHistoryOutput,
  LiveSampleQuestions,
  PagedListLiveInfoDto,
} from "./data-contracts";

export class LiveClass {
  /**
   * No description
   *
   * @tags live
   * @name ApiLiveListGet
   * @summary 直播列表（预告和过期的）
   * @request GET:/api/live/list
   * @secure
   * @response `200` `RestfulResultPagedListLiveInfoDto` Success
   */
  static apiLiveListGet = (query: ApiLiveListGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<PagedListLiveInfoDto>>>({
      url: `/api/live/list` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags live
   * @name ApiLiveLastSixLiveGet
   * @summary 获取最近已经结束的六场直播的信息
   * @request GET:/api/live/lastSixLive
   * @secure
   * @response `200` `RestfulResultListLiveInfoDto` Success
   */
  static apiLiveLastSixLiveGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LiveInfoDto>>>({
      url: `/api/live/lastSixLive`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags live
   * @name ApiLiveSetupPost
   * @summary 点赞
   * @request POST:/api/live/setup
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiLiveSetupPost = (query: ApiLiveSetupPostParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/live/setup` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags live
   * @name ApiLiveRemindlistGet
   * @summary 提醒开播列表
   * @request GET:/api/live/remindlist
   * @secure
   * @response `200` `RestfulResultPagedListLiveInfoDto` Success
   */
  static apiLiveRemindlistGet = (query: ApiLiveRemindlistGetParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<PagedListLiveInfoDto>>>({
      url: `/api/live/remindlist` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags live
   * @name ApiLiveSetremindPost
   * @summary 设置提醒开播
   * @request POST:/api/live/setremind
   * @secure
   * @response `200` `RestfulResultBoolean` Success
   */
  static apiLiveSetremindPost = (query: ApiLiveSetremindPostParams, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<Boolean>>>({
      url: `/api/live/setremind` + dataToQuery(query),
      method: "POST",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags live
   * @name ApiLiveSamplequestionsGet
   * @summary 获取常用示例问题列表
   * @request GET:/api/live/samplequestions
   * @secure
   * @response `200` `RestfulResultListLiveSampleQuestions` Success
   */
  static apiLiveSamplequestionsGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LiveSampleQuestions>>>({
      url: `/api/live/samplequestions`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags live
   * @name ApiLiveLiveinfoLiveguidGet
   * @summary 通过直播guid，获取直播的信息(允许匿名访问)
   * @request GET:/api/live/liveinfo/{liveguid}
   * @secure
   * @response `200` `RestfulResultLiveDetailDto` Success
   */
  static apiLiveLiveinfoLiveguidGet = (
    { liveguid, ...query }: ApiLiveLiveinfoLiveguidGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<LiveDetailDto>>>({
      url: `/api/live/liveinfo/${liveguid}` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
  /**
   * No description
   *
   * @tags live
   * @name ApiLiveLiveRecordHistoriesLiveGuidGet
   * @summary 获取一个直播频道下面的所有录制视频信息
   * @request GET:/api/live/liveRecordHistories/{liveGuid}
   * @secure
   * @response `200` `RestfulResultListLiveRecordHistoryOutput` Success
   */
  static apiLiveLiveRecordHistoriesLiveGuidGet = (liveGuid: string, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<LiveRecordHistoryOutput>>>({
      url: `/api/live/liveRecordHistories/${liveGuid}`,
      method: "GET",
      config: options,
    });
}
