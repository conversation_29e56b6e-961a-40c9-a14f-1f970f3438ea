/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import axios from "../axios";

import { dataToQuery } from "../../utils/requestConcise";

import type { AxiosRequestConfig } from "axios";

import type { GxrcwResponseApp } from "../../utils/requestConcise";

import {
  ApiVerifyIsregisterGetParams,
  GeeTestRegisterDto,
  GeetestValidateEmailDto,
  GeetestValidateInputBaseDto,
  GeetestValidateInputDto,
} from "./data-contracts";

export class VerifyClass {
  /**
   * No description
   *
   * @tags verify
   * @name ApiVerifyInitGet
   * @summary 极验初始化
   * @request GET:/api/verify/init
   * @secure
   * @response `200` `RestfulResultGeeTestRegisterDto` Success
   */
  static apiVerifyInitGet = (options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<GeeTestRegisterDto>>>({
      url: `/api/verify/init`,
      method: "GET",
      config: options,
    });
  /**
   * No description
   *
   * @tags verify
   * @name ApiVerifySendcodePost
   * @summary 验证极验参数并发短信
   * @request POST:/api/verify/sendcode
   * @secure
   * @response `200` `void` Success
   */
  static apiVerifySendcodePost = (data: GeetestValidateInputDto, options?: AxiosRequestConfig) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/verify/sendcode`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags verify
   * @name ApiVerifyBindphonesendcodePost
   * @summary 绑定手机的发送验证码
   * @request POST:/api/verify/bindphonesendcode
   * @secure
   * @response `200` `void` Success
   */
  static apiVerifyBindphonesendcodePost = (
    data: GeetestValidateInputDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/verify/bindphonesendcode`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags verify
   * @name ApiVerifySendemailcodePost
   * @summary 验证极验参数并发邮箱验证码
   * @request POST:/api/verify/sendemailcode
   * @secure
   * @response `200` `void` Success
   */
  static apiVerifySendemailcodePost = (
    data: GeetestValidateEmailDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/verify/sendemailcode`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags verify
   * @name ApiVerifySendcodebyphonePost
   * @summary 获取自己绑定手机的验证码（极验方式）
   * @request POST:/api/verify/sendcodebyphone
   * @secure
   * @response `200` `void` Success
   */
  static apiVerifySendcodebyphonePost = (
    data: GeetestValidateInputBaseDto,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/verify/sendcodebyphone`,
      method: "POST",
      data: data,
      config: options,
    });
  /**
   * No description
   *
   * @tags verify
   * @name ApiVerifyIsregisterGet
   * @summary 判断手机号是否注册
   * @request GET:/api/verify/isregister
   * @secure
   * @response `200` `void` Success
   */
  static apiVerifyIsregisterGet = (
    query: ApiVerifyIsregisterGetParams,
    options?: AxiosRequestConfig,
  ) =>
    axios<Promise<GxrcwResponseApp<void>>>({
      url: `/api/verify/isregister` + dataToQuery(query),
      method: "GET",
      query: query,
      config: options,
    });
}
