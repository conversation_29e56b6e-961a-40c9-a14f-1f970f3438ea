import instance from "./index"
let ENV = import.meta.env.VITE_APP_BASE_API;
import type { AxiosRequestConfig } from "axios"
const axios = <T>({
    method,
    url,
    data,
    config
}): Promise<T> => {
    method = method.toLowerCase();
    config = config;
    if (method == 'post') {
        return instance.post(url, data, { ...config })
    } else if (method == 'get') {
        return instance.get(url, {
            params: data,
            ...config
        })
    } else if (method == 'delete') {
        if (config.type == 1) {
            return instance.delete(url, {
                data,
                ...config
            })
        }
        else {
            return instance.delete(url, {
                params: data,
                ...config
            })
        }
    } else if (method == 'put') {
        return instance.put(url, data, { ...config })
    } else {
        console.error('未知的method' + method)
        return false
    }
}

export default axios;