declare module '*.vue' {
  import { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}
declare const initGeetest:any
declare const Print:any
interface Window {
  onBMapCallback:()=>void
  reSizeIframe:(value: string)=>void
  changeSetInput:(id: string, value: Event)=>void
}