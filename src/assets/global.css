*{margin:0;padding:0;}
ul,li{list-style:none;padding:0;margin:0;}
body{color:#333;}
a{ color:#333;text-decoration:none;}
a:active{background: #e8e8e8;}
.clearfix:after{content:"";display:block;height:0;clear:both;}.clearfix {zoom:1;}
.clear{height:0; clear:both;overflow:hidden;}
.fl{float:left;}.fr{float:right;}
.red{color: red;}
.bg-white { background: #fff;}
#app {height: 100%;background: #F4F5F9;font-family: Arial,PingFang SC,Hiragino Sans GB,Microsoft YaHei,WenQuanYi Micro Hei,sans-serif;}
/* 新加 */
.blue{color: #457CCF;}
.blue2{color: #5F9EFC;}
.orange{color:#FC5C5B;}
.btn-blue{background: #457CCF !important;color: #fff !important;font-size: 14px !important;}
.el-button{min-height: unset !important;}
.tc{text-align: center;}
.tr{text-align: right;}
.tl{text-align: left;}
.ti{text-indent: 2em;}
.ellipsis{overflow:hidden;text-overflow:ellipsis;white-space:nowrap; }
.ellipsis2{overflow: hidden;text-overflow: ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2; }
.bg-gray{background: #f2f2f2;}
.bg-fafa{background: #fafafa;}


.w1200 {
    width: 1200px;
    margin: 0 auto;
}
.bottom-line li{
    position: relative;
}
.bottom-line li:not(:last-child):after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: 20px;
    bottom: 0;
    left: 20px;
    border-bottom: 1px solid #f2f2f2;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
}
.message{padding: 20px;text-align: center;color:#999;font-size: 14px;}
.salary{color: #FC5C5B;}


.btn-info-edit{font-size: 13px;color: #457CCF;cursor: pointer;}
.btn-pop{cursor: pointer;}
.btn-info-edit i{padding-right: 7px;}
.borderLine{border-bottom: 1px solid #F2F2F2;}
.borderLine2{
    position: relative;
}
.borderLine2:after{
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: 24px;
    bottom: 0;
    left: 24px;
    border-bottom: 1px solid #F2F2F2;
    /* border-bottom: 1px solid #ee0404;  */

    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
}

.el-input__inner{cursor: text;}
  