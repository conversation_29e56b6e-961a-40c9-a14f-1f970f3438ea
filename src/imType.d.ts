interface sessionsItem {
  //云信的类型
  id: string;
  isBlack: boolean;
  lastMsg: Record<string, unknown>;
  msgReceiptTime: number;
  scene: string;
  to: string;
  unread: number;
  updateTime: number;
}
interface sessionsItemMy {
  //我们接口返回的类型
  accID: string;
  avatar: string;
  blocking: boolean;
  curMemberType: number;
  curPositionName: string;
  lastContent: string;
  localID: number;
  name: string;
  time: string;
  topSetting: number;
  updatetime: number;
}
interface sessions {
  //聊天好友列表需要的类型
  accID: string;
  avatar: string;
  blocking: boolean;
  curMemberType: number;
  curPositionName: string;
  id: string;
  isBlack: boolean;
  lastContent: string;
  localID: number;
  name: string;
  time: string;
  topSetting: number;
  unread: number;
  updatetime: number;
}
