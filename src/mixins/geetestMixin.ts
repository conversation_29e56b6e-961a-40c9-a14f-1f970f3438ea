import { geetestInit } from "@/http/api"
import { useStore } from "vuex";
const store = useStore();

export const initGeet = () => {
    return methods.initGee();
}
const methods = {
  async initGee() {
    let result = await geetestInit();
    if (result.code !== 1) {
      console.error('Geetest initialization failed with code:', result.code);
    throw new Error(`Geetest initialization failed with code: ${result.code}`);
    }
    // let data=result.data;
    let data = result && result.data ? result.data : null;
    return new Promise((resolve, reject) => {
      initGeetest(
        {
          // 以下配置参数来自服务端 SDK
          gt: data.gt,
          challenge: data.challenge,
          offline: !data.success,
          new_captcha: data.newCaptcha,
          product: 'bind',
        },
        function (captchaObj: any) {
          captchaObj.onReady(function () {
              resolve(captchaObj);
            })
            .onSuccess(function () {
            })
            .onError(function () {
               window.location.reload()
             });
        }
      );
    });
  }
}
