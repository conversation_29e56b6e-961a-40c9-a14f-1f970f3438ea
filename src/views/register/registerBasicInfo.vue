<template>
  <div class="register_radius_box register_basic_info">
    <div class="title">
      <img src="../../assets/register/icon_sf.png" alt="icon" />
      <h1>个人信息</h1>
      <p>30秒填写基本信息，立刻查看高薪职位</p>
    </div>

    <div class="form">
      <el-form
        style="width: 100%"
        :model="sizeForm"
        label-width="110px"
        :label-position="'left'"
        :rules="rules"
        ref="ruleForm"
      >
        <el-form-item label="姓名" prop="jobSeekerName">
          <el-input v-model="sizeForm.jobSeekerName" placeholder="请填写姓名" />
        </el-form-item>

        <el-form-item label="姓别" prop="jobSeekerSex">
          <el-radio-group v-model="sizeForm.jobSeekerSex">
            <el-radio border :label="false">男</el-radio>
            <el-radio border :label="true">女</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="当前身份" prop="jobSeekerTalentDegree">
          <div class="identity_form_item">
            <el-radio-group
              v-model="sizeForm.jobSeekerTalentDegree"
              @change="groupChange"
            >
              <el-radio border :label="2">
                <h1>职场人</h1>
                <p>指有正式工作经验的人群</p>
              </el-radio>
              <el-radio border :label="1"
                ><h1>学生</h1>
                <p>指在校生、应届生、往届生</p></el-radio
              >
            </el-radio-group>
          </div>
        </el-form-item>

        <el-form-item label="出生年月" prop="jobSeekerBrithday">
          <el-date-picker
            v-model="sizeForm.jobSeekerBrithday"
            type="month"
            value-format="YYYY-MM"
            format="YYYY-MM"
            placeholder="选择出生年月"
            :default-value="new Date('2000-01-01')"
            :disabled-date="disabledDate"
            @change="changeBrithday"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="求职状态" prop="workingState">
          <el-select v-model="sizeForm.workingState" placeholder="选择求职状态">
            <el-option
              v-for="item in workingStateList"
              :label="item.keywordName"
              :value="item.keywordID"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="
            sizeForm.jobSeekerTalentDegree &&
            sizeForm.jobSeekerTalentDegree == 2
          "
          label="参加工作时间"
          prop="jobSeekerAttendWorkYear"
        >
          <div class="time_to_work">
            <el-date-picker
              v-model="sizeForm.jobSeekerAttendWorkYear"
              type="month"
              value-format="YYYY-MM"
              format="YYYY-MM"
              placeholder="选择参加工作时间"
              :disabled-date="disabledDate2"
            >
            </el-date-picker>
            <p v-if="getWorkTime">
              {{ getWorkTime }}
            </p>
          </div>
        </el-form-item>

        <el-form-item
          v-if="
            sizeForm.jobSeekerTalentDegree &&
            sizeForm.jobSeekerTalentDegree == 1
          "
          label="参加实习时间"
          prop="jobSeekerAttendWorkYear"
        >
          <div class="time_to_work">
            <div style="align-items: center; display: flex">
              <el-date-picker
                v-model="sizeForm.jobSeekerAttendWorkYear"
                type="month"
                value-format="YYYY-MM"
                format="YYYY-MM"
                placeholder="选择参加实习时间"
                :disabled="internships"
                :disabled-date="disabledDate2"
              >
              </el-date-picker>

              <el-checkbox
                @change="internshipsChange"
                style="margin-left: 10px"
                v-model="internships"
                label="无实习经验"
              ></el-checkbox>
            </div>
            <p v-if="getWorkTime">
              {{ getWorkTime }}
            </p>
          </div>
        </el-form-item>
      </el-form>
      <div style="margin: 0 auto; margin-top: 25px; width: 180px">
        <el-button
          @click="onSubmit"
          class="register_button"
          style="width: 180px"
          type="primary"
          :loading="loading"
          >下一步</el-button
        >
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from "vue";
import { ElForm, ElMessage } from "element-plus";

import { 简历注册相关PcClass } from "../../http/register/简历注册相关Pc";
import { 所有端公共部分Class } from "../../http/register/所有端公共部分";

import { OptionsClass } from "../../http/app/Options";
import { RegisterBaseInfoStepPC } from "../../http/register/data-contracts";
import { KeywordSimpleOption } from "../../http/app/data-contracts";
import { useRouter } from "vue-router";
import { group } from "console";

const router = useRouter();

const sizeForm = ref<RegisterBaseInfoStepPC>({
  jobSeekerBrithday: "",
  jobSeekerName: "",
  jobSeekerAttendWorkYear: "",
});
const internships = ref(false);
const internshipsChange = () => {
  sizeForm.value.jobSeekerAttendWorkYear = "";
};

const workingStateList = ref<KeywordSimpleOption[]>([]);
const getWorkingState = async () => {
  workingStateList.value = [];
  nextTick(async () => {
    const data = await OptionsClass.apiOptionsWorkstatuoptionsstudentGet({
      student: sizeForm.value.jobSeekerTalentDegree == 2 ? false : true,
    });
    if (data.code == 1) {
      workingStateList.value = data.data;
    }
  });
};

const groupChange = (val: number) => {
  internships.value = false
  sizeForm.value.workingState = "";
  getWorkingState();
  sizeForm.value.jobSeekerAttendWorkYear = getPreviousSixMonths();
  
};

const getSaveInfo = async () => {
  const data = await 简历注册相关PcClass.apiRegisterStepRegisterbaseinfopcGet({
    from: 0,
  });
  if (data.code == 1) {
    sizeForm.value = data.data;
    if (sizeForm.value.workingState == 0) {
      sizeForm.value.workingState = "";
    }
    if (
      sizeForm.value.jobSeekerAttendWorkYear == "0" ||
      !sizeForm.value.jobSeekerAttendWorkYear
    ) {
      if (sizeForm.value.jobSeekerTalentDegree == 1) {
        internships.value = true;
      }
    }
    getWorkingState();
  }
};

getSaveInfo();

const ruleForm = ref<InstanceType<typeof ElForm>>();

const getPreviousSixMonths = (): string => {
  const currentDate = new Date();
  currentDate.setMonth(currentDate.getMonth() - 6);
  const year = currentDate.getFullYear();
  const month = String(currentDate.getMonth() + 1).padStart(2, "0");
  return `${year}-${month}`;
};

const loading = ref(false);
const onSubmit = () => {
  ruleForm.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      const data =
        await 简历注册相关PcClass.apiRegisterStepSaveregisterbaseinfopcPost({
          ...sizeForm.value,
        });
      if (data.code == 1) {
        // ElMessage({
        //   type: "success",
        //   message: data.message || "保存成功",
        // });
        setTimeout(() => {
          loading.value = false;
          if (sizeForm.value.jobSeekerTalentDegree == 2) {
            router.push(
              `/register/registerRouteView/registerExperience?resumeID=${data.data?.resumeID}&jobSeekerTalentDegree=${sizeForm.value.jobSeekerTalentDegree}`
            );
          } else {
            if (internships.value) {
              router.push(
                `/register/registerRouteView/registerEducation?resumeID=${data.data?.resumeID}&jobSeekerTalentDegree=${sizeForm.value.jobSeekerTalentDegree}`
              );
            } else {
              router.push(
                `/register/registerRouteView/registerExperience?resumeID=${data.data?.resumeID}&jobSeekerTalentDegree=${sizeForm.value.jobSeekerTalentDegree}`
              );
            }
          }
        }, 50);
      } else {
        loading.value = false;
        ElMessage({
          type: "error",
          message: data.message || "保存失败",
        });
      }
    } else {
      loading.value = false;
      console.log("error submit!!");
      return false;
    }
  });
};
const getWorkTime = computed(() => {
  // if(sizeForm.value.jobSeekerAttendWorkYear === '0') return ''
  // const startDate = sizeForm.value.jobSeekerAttendWorkYear + '-00'
  // console.log(startDate);
  if (
    !sizeForm.value.jobSeekerAttendWorkYear ||
    sizeForm.value.jobSeekerAttendWorkYear == "0"
  )
    return "";

  const diffMonths = Math.floor(
    (new Date().getTime() -
      new Date(sizeForm.value.jobSeekerAttendWorkYear).getTime()) /
      (1000 * 60 * 60 * 24 * 30)
  );

  const years = Math.floor(diffMonths / 12);
  const months = diffMonths % 12;

  if (years === 0) return "1年以内经验";
  if (months === 0) return `${years}年工作经验`;

  return `约${months <= 5 ? years : years + 1}年工作经验`;
});

// const getWorkTime = (startDate: string) => {
//   console.log(startDate);

//   const diffMonths = Math.floor(
//     (new Date().getTime() - new Date(startDate).getTime()) /
//       (1000 * 60 * 60 * 24 * 30)
//   );

//   const years = Math.floor(diffMonths / 12);
//   const months = diffMonths % 12;

//   if (years === 0) return "1年以内经验";
//   if (months === 0) return `${years}年工作经验`;

//   return `约${months <= 5 ? years : years + 1}年工作经验`;
// };

const disabledDate = (time: Date) => {
  const now = new Date();
  return time.getTime() >= now.setFullYear(now.getFullYear() - 16);
};

const disabledDate2 = (time: Date) => {
  return time.getTime() > new Date().getTime();
};

const validateJobSeekerAttendWorkYear = async (
  rule,
  value: string,
  callback: (error?: Error) => void
) => {
  if (sizeForm.value.jobSeekerTalentDegree == 2) {
    if (!value) {
      callback(new Error("请选择参加工作时间"));
      return;
    } else {
      const data =
        await 所有端公共部分Class.apiRegisterstepCheckworktimebetweenGet({
          jobseekerattendworkyear: sizeForm.value.jobSeekerAttendWorkYear,
          birthday: sizeForm.value.jobSeekerBrithday || "",
        });
      if (data.code == 1) {
        callback();
        return;
      } else {
        callback(new Error(data.message || ""));
        return;
      }
    }
  } else {
    if (internships.value) {
      callback();
    } else {
      if (!value) {
        callback(new Error("请选择参加实习时间"));
      } else {
        callback();
      }
    }
  }
};

const changeBrithday = () => {
  ruleForm.value?.validateField("jobSeekerAttendWorkYear", () => {});
};

const rules = {
  jobSeekerName: [
    { required: true, message: "请输入姓名", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        const chineseRegex = /^[\u4e00-\u9fa5]{2,10}$/;
        const englishRegex = /^[a-zA-Z]{2,10}$/;
        if (!chineseRegex.test(value) && !englishRegex.test(value)) {
          callback(new Error("2-10个汉字或2-10个字母"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  jobSeekerSex: [{ required: true, message: "请选择姓别", trigger: "blur" }],
  jobSeekerTalentDegree: [
    { required: true, message: "请选择当前身份", trigger: "change" },
  ],
  jobSeekerBrithday: [
    { required: true, message: "请选择出生年月", trigger: "change" },
  ],
  workingState: [{ required: true, message: "选择求职状态", trigger: "blur" }],
  jobSeekerAttendWorkYear: [
    {
      required: true,
      validator: validateJobSeekerAttendWorkYear,
      trigger: "change",
    },
  ],
};
</script>
<style lang="less" scoped>
@import "./css/style.less";
.register_basic_info {
  padding: 43px 59px 50px 59px;
  .form {
    .identity_form_item {
      h1 {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 500;
        font-size: 15px;
        color: #9a9ea5;
        font-style: normal;
        text-transform: none;
        line-height: 25px;
      }
      p {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #d0d2d9;
        text-align: center;
        font-style: normal;
        text-transform: none;
        line-height: 24px;
      }
    }
    .time_to_work {
      p {
        font-family:
          Source Han Sans CN,
          Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #9a9ea5;
        text-align: center;
        font-style: normal;
        text-transform: none;
        text-align: left;
      }
    }
  }
  .el-radio {
    padding: 12px 40px;
    background: #f5f7fa;
    height: auto;
    text-align: center;
    line-height: 0;
    border-color: #f5f7fa;
  }
  .el-radio.is-checked {
    background: #f0f8ff;
    border-color: var(--el-color-primary);
    h1,
    p {
      color: #247bff;
    }
  }
  ::v-deep(.el-radio__input) {
    opacity: 0;
    width: 0;
  }
  ::v-deep(.el-radio__label) {
    padding: 0;
  }

  ::v-deep(.el-date-editor) {
    width: 100%;
  }

  ::v-deep(.el-select) {
    width: 100%;
  }
}
</style>
