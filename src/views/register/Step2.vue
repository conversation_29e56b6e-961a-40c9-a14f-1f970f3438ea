<template>
  <div class="register-step-mn clearfix">
    <subheading></subheading>
    <stepBar :step="2"></stepBar>
    <div class="content w1200 edit-unify">
      <el-config-provider :locale="localeZH">
        <div class="box">
          <h1>教育经历</h1>
          <el-form
            :model="educationForm"
            class="demo-form-inline"
            label-width="100px"
            label-position="left"
            ref="refeducationForm"
            :rules="rulesA"
            :inline="true"
          >
            <el-form-item label="毕业院校" class="w520 universities" prop="university">
              <el-select
                v-model="educationForm.university"
                filterable
                placeholder="请输入院校名称"
                allow-create
                :remote-method="collegeNameText"
                :loading="loadingSEl"
                remote
              >
                <el-option
                  v-for="(item, index) in collegeList"
                  :key="index"
                  :label="item"
                  :value="item"
                  class
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="学历" class="w520 clearfix" required>
              <el-select
                v-model="educationForm.degree"
                placeholder="请选择"
                class="fl"
                @change="selectdegree"
              >
                <el-option
                  v-for="(p, index) in educationalList"
                  :key="index"
                  :label="p.keywordName"
                  :value="p.keywordID"
                  @click="educationForm.degreeID = p.keywordID"
                ></el-option>
              </el-select>
              <el-radio-group v-model="fullTimeFlag" class="fl radio-group2">
                <el-radio label="全日制" name="1" @click="educationForm.fullTimeFlag = true"></el-radio>
                <el-radio label="非全日制" name="2" @click="educationForm.fullTimeFlag = false"></el-radio>
              </el-radio-group>
              <div class="el-form-item__error" v-if="showdegree">请选择学历</div>
            </el-form-item>
            <el-form-item label="专业类别" class="w520" prop="majorIDName" v-if="iscollege">
              <el-input
                v-model="educationForm.majorIDName"
                placeholder="请选择"
                @focus="dialogVisibleA = true"
                suffix-icon="el-icon-arrow-down"
                readonly
              ></el-input>
              <seleSpecialty
                @confirmSpeciality="confirmSpeciality"
                v-if="dialogVisibleA"
                :hideValue="educationForm.majorID"
              ></seleSpecialty>
            </el-form-item>
            <el-form-item label="专业名称" class="w520" v-if="iscollege">
              <el-input v-model="educationForm.major" placeholder="请输入专业名称"></el-input>
            </el-form-item>
            <el-form-item label="在校时间" required>
              <div class="timeBox fl">
                <el-form-item prop="beginTime">
                  <el-date-picker
                    v-model="educationForm.beginTime"
                    type="month"
                    value-format="YYYY-MM"
                    placeholder="请选择"
                    class="w240"
                    :disabled-date="disabledDate"
                  ></el-date-picker>
                </el-form-item>
              </div>

              <div class="line fl">至</div>
              <div class="timeBox fl">
                <el-form-item prop="endTime" class="w240 fl">
                  <el-date-picker
                    v-model="educationForm.endTime"
                    type="month"
                    value-format="YYYY-MM"
                    placeholder="请选择"
                  ></el-date-picker>
                </el-form-item>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <!-- `````````````工作经历````````````````````` -->
        <div class="box" v-if="workid == '1'">
          <h1>工作经历</h1>
          <el-form
            :inline="true"
            :model="workForm"
            class="demo-form-inline"
            label-width="100px"
            label-position="left"
            ref="refWorkForm"
            :rules="rulesB"
          >
            <el-form-item label="公司名称" class="w520" prop="company">
              <el-select
                v-model="workForm.company"
                filterable
                placeholder="请输入"
                allow-create
                :remote-method="entNameText"
                :loading="loadingSEl"
                remote
              >
                <el-option
                  v-for="(item, index) in entTextList"
                  :key="index"
                  :label="item"
                  :value="item"
                  class
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="职能" class="w520" prop="positionIDName">
              <el-input
                v-model="workForm.positionIDName"
                placeholder="请输入或选择职能"
                suffix-icon="el-icon-arrow-down"
                @focus="dialogVisibleB = true"
              ></el-input>
              <seleCareer @confirm="confirmCareer" v-if="dialogVisibleB"></seleCareer>
            </el-form-item>
            <el-form-item label="职位名称" class="w520" c prop="position">
              <el-select
                v-model="workForm.position"
                filterable
                allow-create
                placeholder="请输入"
                @input="positionText"
              >
                <el-option
                  v-for="item in positionTextList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                  class="select-search"
                >
                  <p class="tit">{{ item.name }}</p>
                  <span class="sub">{{ item.fullname }}</span>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="在职时间" required style="width: 100%">
              <div class="timeBox fl">
                <el-form-item prop="beginTime">
                  <el-date-picker
                    v-model="workForm.beginTime"
                    type="month"
                    value-format="YYYY-MM"
                    placeholder="请选择"
                    class="w240"
                    :clearable="false"
                    :disabled-date="disabledDate"
                  ></el-date-picker>
                </el-form-item>
              </div>

              <div class="line fl">至</div>
              <div class="timeBox fl">
                <el-form-item prop="endTime" class="w240 fl">
                  <el-date-picker
                    v-model="workForm.endTime"
                    type="month"
                    value-format="YYYY-MM"
                    :disabled-date="disabledDate"
                    placeholder="请选择"
                  ></el-date-picker>
                </el-form-item>
              </div>
              <div>
                <el-checkbox label="至今" name="toTaday" v-model="toTaday" class="totaday"></el-checkbox>
              </div>
            </el-form-item>
            <el-form-item label="目前薪资" required class="xinzi">
              <el-select
                    v-model="workForm.salary"
                    placeholder="请选择目前薪资"
                    class="w520"
                    @change="selectsalaryVisable"
                    clearable
                  >
                    <el-option v-for="(p, i) in SalaryList" :key="i" :label="p.text" :value="p.id"></el-option>
                  </el-select>
              <!-- <el-row :gutter="20">
                <el-col :span="18">
                  
                </el-col>
                <el-col :span="1">&#12288;</el-col>
                <el-col :span="5">
                  <el-checkbox label="隐藏薪资" name="salaryVisable" v-model="workForm.salaryVisable"></el-checkbox>
                </el-col>
              </el-row> -->
              <span class="el-input__suffix yuan">元/月</span>
              <div class="el-form-item__error" v-if="showsalaryVisable">请选择目前薪资</div>
            </el-form-item>
            <el-form-item label="工作描述" style="width: 620px" prop="desc" class="workDescription">
          <el-input
            v-model="workForm.desc"
            type="textarea"
            maxlength="2000"
            show-word-limit
            clearable
            size="medium"
            class="JobDescription"
            placeholder="描述工作内容、职务、团队成就等……"
          ></el-input>
          <div class="othersWrite">
            <el-popover v-model:visible="visible" placement="bottom" :width="451">
              <template #reference>
                <span @click="visible = true" class="how">别人怎么写？</span>
              </template>
              <div class="describe-popover">
                <h2 class="tit">项目描述</h2>
                <p class="doc-p">职务描述应提供与工作内容相关的细节，以便HR快速了解您之前的工作内容。</p>
                <p class="li">例：</p>
                <p>1.策划新版广西人才网个人后台，完成产品设计和UI设计工作</p>
                <p>2.负责广西人才网侧边栏UI设计工作</p>
                <p>3.在广西人才网销售组超额完成年度销售计划</p>
                <p>4.为多客户提供完善的业务办理指南，制定符合该企业的业务方案</p>
              </div>
            </el-popover>
          </div>
        </el-form-item>


          </el-form>
        </div>
        <div class="tc">
          <el-button class="sumBtn" type="primary" @click="onSubmit" :loading="loading">保存到下一步</el-button>
        </div>
      </el-config-provider>
    </div>
  </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  computed,
  watch,
  ref,
  Ref,
  onBeforeMount,
  onMounted,
} from "vue";
import subheading from "@/views/register/head.vue";
import stepBar from "@/views/register/stepBar.vue";
import seleICity from "@/components/seleCity.vue";
import seleCareer from "@/components/seleCareer.vue";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
import SalaryList from "../../store/SalarySelection"; //薪资字典
import zhCn from "element-plus/lib/locale/lang/zh-cn";
import { searchcollege, searchEnterpriseName, searchPosition } from "../../http/searchAPI";
import seleSpecialty from "@/components/seleSpecialty.vue";
import {
  registereduinfo,
  saveregistereduinfo,
  registerworkinfo,
  saveregisterworkinfo,
} from "../../http/resumeApi";
import { getDegreeoptions } from "../../http/dictionary";
export default defineComponent({
  components: {
    subheading,
    stepBar,
    seleICity,
    seleCareer,
    SalaryList,
    seleSpecialty,
  },

  setup() {
    const store = useStore();
    const refeducationForm: Ref<any> = ref(null);
    const refWorkForm: Ref<any> = ref(null);
    const router = useRouter();
    const route = useRoute();
    const state = reactive({
      educationForm: {
        beginTime: "",
        degree: '',
        degreeID: 0,
        eduID: 0,
        endTime: "",
        fullTimeFlag: false,
        major: "",
        majorID: 0,
        majorIDName: "",
        resumeID: 0,
        university: "",
      } as any,
      workForm: {
        salary: ''
      } as any,
      loading: false,
      workid: computed(() => {
        return route.params.workid;
      }),
      dialogVisibleA: false,
      dialogVisibleB: false,
      SalaryList: SalaryList,
      fullTimeFlag: "" as any,
      educationalList: "", //学历字典列表
      collegeList: "", //x搜索到的学校 列表
      entTextList: "", //搜索到的公司名称
      iscollege: true,
      positionTextList: ref([]),
      localeZH: zhCn,
      toTaday: false,
      showdegree: false,
      showsalaryVisable: false,
      loadingSEl: false,
      visible: ref(false),
      disabledDate(time: any) {
        return time.getTime() > Date.now();
      },
      disabledDateEND(time: any) {
        return time.getTime() < Date.now(state.workForm.beginTime);
      },
      disabledDateEND2(time: any) {
        return time.getTime() < Date.now(state.educationForm.beginTime);
      },

    });
    const rulesA = {
      university: [
        {
          required: true,
          message: "请输入毕业院校",
          trigger: "change",
        },
      ],
      degree: [
        {
          // required: true,
          message: "请选择学历",
          trigger: "change",
        },
      ],
      majorIDName: [
        {
          required: true,
          message: "请选择专业类别",
          trigger: "change",
        },
      ],
      beginTime: [
        {
          required: true,
          message: "请选择开始时间",
          trigger: "change",
        },
      ],
      endTime: [
        {
          required: true,
          message: "请选择结束时间",
          trigger: "change",
        },
      ],
    };
    const rulesB = {
      company: [
        {
          required: true,
          message: "请填写公司名称",
          trigger: ['change', 'blur'],
        },
      ],
      positionIDName: [
        {
          required: true,
          message: "请选择职能",
          trigger: ['blur', 'change'],
        },
      ],
      position: [
        {
          required: true,
          message: "请填写职位名称",
          trigger: ['change', 'blur'],
        },
      ],
      beginTime: [
        {
          required: true,
          message: "请选择开始时间",
          trigger: "change",
        },
      ],
      // endTime: [
      //   {
      //     required: true,
      //     message: "请选择结束时间",
      //     trigger: "change",
      //   },
      // ],
      salary: [
        {
          required: true,
          message: "请选择目前薪资",
          trigger: "change",
        },
      ],
      desc: [
        {
          required: true,
          message: "请输入工作描述",
          trigger: "blur",
        },
      ]
    };
    watch(
      () => state.workForm.endTime,
      (newValue, oldValue) => {
        state.toTaday = state.workForm.endTime ? false : true
      }
    );
    watch(
      () => state.toTaday,
      (newValue, oldValue) => {
        if (state.toTaday) {
          state.workForm.endTime = ''
        }
      }
    );
    watch(
      () => state.educationForm.degreeID,
      (newValue, oldValue) => {
        state.iscollege = newValue > 353 ? true : false;
      }
    );
    onMounted(() => {
      methods.getinfo();
      methods.GETeducationalList();
    });
    let prevent=1;
    const methods = {
      //获取基本信息
      async getinfo() {
        let form = {
          resumeid: route.params.resumeId,
        };
        let res: any = await registereduinfo(form);
        if (res.code == 1) {
          // state.educationForm = res.data;
          state.educationForm.beginTime = res.data.beginTime || '';
          state.educationForm.degree = res.data.degree || '';
          state.educationForm.degreeID = res.data.degreeID || 0;
          state.educationForm.eduID = res.data.eduID || 0;
          state.educationForm.endTime = res.data.endTime || '';
          state.educationForm.fullTimeFlag = res.data.fullTimeFlag ? true : false;
          state.educationForm.major = res.data.major || '';
          state.educationForm.majorID = res.data.majorID || 0;
          state.educationForm.majorIDName = res.data.majorIDName || '';
          state.educationForm.resumeID = res.data.resumeID || 0;
          state.educationForm.university = res.data.university || '';
          state.fullTimeFlag = res.data.fullTimeFlag
            ? ref("全日制")
            : ref("非全日制");
          if (state.workid == "1") {
            this.getworkInfo(res.data.resumeID);
          }
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //获取工作经历
      async getworkInfo(id: number) {
        let form = {
          resumeid: id,
        };
        let res: any = await registerworkinfo(form);
        if (res.code == 1) {
          state.workForm = res.data;
          state.workForm.positionIDName = res.data.positionIDName || ''
          state.workForm.salary = res.data.salary || ''
          state.toTaday = state.workForm.endTime ? false : true
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //  保存
      async saveData() {
        if(prevent===2){
          return false
        }
        state.loading = true;
        let form = state.educationForm;
        prevent=2;
        let res: any = await saveregistereduinfo(form);
        prevent=1;
        if (res.code == 1) {
          if (state.workid == "1") {
            this.saveWorkData();
          }
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          router.push(`/register/step3/${route.params.resumeId}`);
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
        state.loading = false;
      },
      //保存工作经历
      async saveWorkData() {
        if (state.toTaday) {
          state.workForm.endTime = ''
        }
        let res2: any = await saveregisterworkinfo(state.workForm);
      },
      // 学历
      async GETeducationalList() {
        let result = store.state.educationalList;
        if (result.length <= 0) {
          let res: any = await getDegreeoptions("");
          state.educationalList = res.data;
          store.commit("setEducationalList", res.data);
        } else {
          state.educationalList = result;
        }
      },
      // 就读学校--搜索
      async getCollegeNameText(text: any) {
        let data: any = await searchcollege(text);
        if (data.code == 1 && data.data.count > 0) {
          state.collegeList = data.data.result;
        }
      },
      async getEntNameText(text: any) {
        let data: any = await searchEnterpriseName(text);
        if (data.code == 1 && data.data.count > 0) {
          state.entTextList = data.data.result;
        }
      },
      async debounce(text: any) {
        let data: any = await searchPosition(text);
        if (data.code == 1 && data.data.length > 0) {
          state.positionTextList = data.data;
        }
      },
    };
    const fun = {
      onSubmit() {
        let a = 0;
        let b = 1;
        let c = 1;
        let d = 1;
        if (state.educationForm.beginTime > state.educationForm.endTime) {
          ElMessage.warning("在校时间的结束时间不能早于开始时间");
          return false;
        }
        if ((state.workForm.beginTime > state.workForm.endTime) && state.workForm.endTime) {
          ElMessage.warning("工作时间的结束时间不能早于开始时间");
          return false;
        }
        //学历和目前薪资单独拿出来判断---暂时找不到方案优化
        if (!state.educationForm.degree) {
          state.showdegree = true;
          c = 0
        }
        if (state.workid == "1"&&!state.workForm.salary&&!state.workForm.salaryVisable) {
          state.showsalaryVisable = true;
          d = 0
        }
        refeducationForm.value.validate((valid: boolean) => {
          a = valid ? 1 : 0;
        });
        if (state.workid == "1") {
          refWorkForm.value.validate((valid: boolean) => {
            b = valid ? 1 : 0;
          });
        }
        
        if (a == 1 && b == 1 && c && d) {
          methods.saveData();
        } else {
          ElMessage.warning("请填写完整信息");
        }
      },
      //接收子集传来的数据---专业类别选择
      confirmSpeciality(data: any) {
        state.dialogVisibleA = false;
        if (data) {
          state.educationForm.majorIDName = data.name;
          state.educationForm.majorID = data.id;
          state.educationForm.major = data.name;
        }
      },
      //接收从子集传过来的数据
      confirmCareer(p: any) {
        state.dialogVisibleB = false;
        if (!p) {
          return false;
        }
        if (!state.workForm.position) {
          state.workForm.position = p.keywordName;

        }
        state.workForm.positionIDName = p.keywordName;
        state.workForm.positionID = p.keywordID;
      },
      collegeNameText(query: string) {
        if (query) {
          state.loadingSEl = true
          setTimeout(() => {
            methods.getCollegeNameText(query);
            state.loadingSEl = false
          }, 200)
        } else {
        }
      },
      entNameText(query: string) {
          if (query) {
          state.loadingSEl = true
          setTimeout(() => {
            methods.getEntNameText(query);
            state.loadingSEl = false
          }, 200)
        } else {
        }
      },
      positionText(val: any) {
        methods.debounce(val.data);
      },
      //学历和目前薪资单独拿出来判断---暂时找不到方案优化
      selectdegree(val: any) {
        state.showdegree = false;
      },
      selectsalaryVisable(val: any) {
        state.showsalaryVisable = false;
      }

    };
    return {
      ...toRefs(state),
      ...fun,
      rulesA,
      rulesB,
      refeducationForm,
      refWorkForm,
    };
  },
});
</script>
<style lang="less">
.register-step-mn {
  background: #f4f5f9;
  padding-bottom: 50px;
  .content {
    background: #fff;
    .box {
      padding: 40px;
    }
    h1 {
      font-size: 24px;
      color: #333;
      padding-bottom: 40px;
    }
  }
  .timeBox {
    width: 240px;
  }
  .totaday {
    padding-left: 10px;
  }
  .line {
    width: 41px;
    text-align: center;
  }
  .sumBtn {
    background: #5f9efc;
    border-radius: 4px;
    width: 194px;
    height: 55px;
    padding: 0 0 0 0;
    line-height: 55px;
    margin: 80px 0 50px;
    font-size: 18px;
  }
  //每一项编辑弹窗的统一样式---star
  .edit-unify {
    .el-form-item__label {
      font-size: 15px;
      color: #666;
    }
    .el-input__inner {
      border: 1px solid #f2f2f2;
      height: 45px;
      font-size: 14px;
      color: #333;
    }

    .w240 {
      .el-input__inner {
        width: 240px;
      }
    }
    .w180 {
      .el-input__inner {
        width: 180px;
      }
    }
    .w520 {
      .el-input {
        width: 520px;
      }
    }
    .w280 {
      .el-input__inner {
        width: 280px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .btn-end {
      text-align: right;
      .sub {
        font-size: 16px;
        width: 128px;
        height: 45px;
        background: #457ccf;
      }
      .cel {
        font-size: 16px;
        border: 1px solid #f2f2f2;
        width: 126px;
      }
    }
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label-wrap
      > .el-form-item__label:before,
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label::after {
      content: "*";
      color: var(--el-color-danger);
      margin-right: 4px;
    }
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label-wrap
      > .el-form-item__label:before,
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label:before {
      content: "" !important;
      color: var(--el-color-danger);
      margin-right: 0px;
    }
  }
  .radio-group2 {
    .el-radio {
      width: 96px;
      height: 43px;
      background: #ffffff;
      color: #999999;
      border: 1px solid #f2f2f2;
      text-align: center;
      font-size: 16px;
      margin: 0 0 0px 20px;
      .el-radio__input {
        opacity: 0;
      }
    }
    .is-checked {
      background: #f2f7ff;
      color: #5f9efc;
      border: 1px solid #5f9efc;
    }
  }
  .xinzi .yuan {
    left: 430px;
  }
  .el-select .el-input__inner {
    cursor: text;
  }
  .othersWrite .how {
    font-size: 12px;
    color: #457ccf;
    cursor: pointer;
}

}
.select-search.el-select-dropdown__item {
  height: auto;
  line-height: normal;
  padding: 10px 24px;
  .tit {
    color: #457ccf;
    font-size: 14px;
  }
  span.sub {
    color: #999;
    font-size: 12px;
  }
}
.JobDescription {
      .el-textarea__inner {
        height: 130px;
      }
    }
    .describe-popover {
  width: 450;
  h2.tit {
    font-size: 14px;
    color: #333333;
    padding: 10px 0 10px 0;
  }
  p {
    font-size: 12px;
    color: #666666;
    line-height: 24px;
  }
  p.li {
    font-size: 13px;
    color: #457ccf;
    padding: 5px 0;
  }
  p.doc-p {
    color: #999;
  }
}
</style>