<template>
  <div class="register-first-mn">
      <subheading></subheading>
    <div class="content w1200">
      <div class="tip-box">
        <h1><i class="iconfont icon-tick1"></i><label> 注册成功！</label></h1>
        <p>立即创建简历，便可投递适合您的职位</p>
      </div>
      <div class="sele-box">
        <h2 class="tit">请选择人才类型</h2>
        <ul class="clearfix">
          <li @click="type = 2" :class="type == 2 ? 'sel fl' : 'fl'">
            <div class="img putong">
              <!-- <el-image
                style="width: 120px; height: 120px"
                src="/src/static/img/putong.png"
                fit="cover"
              ></el-image> -->
            </div>
            <h2>非应届毕业生</h2>
            <p>
              标准模板，推荐使用！<br />
              毕业一年以上的社会人士
            </p>
            <span v-if="type == 2">✔</span>
          </li>
          <li @click="type = 1" :class="type == 1 ? 'sel fr' : 'fr'">
            <div class="img biyesheng">
              <!-- <el-image
                style="width: 120px; height: 120px"
                src="/src/static/img/biyesheng.png"
                fit="cover"
              ></el-image> -->
            </div>
            <h2>应届毕业生</h2>
            <p>
              可投递企事业单位特招的毕业生职位<br />
              在校生或者工作1年内的毕业生
            </p>
            <span v-if="type == 1">✔</span>
          </li>
        </ul>
      </div>
      <el-button type="primary" @click="next" :disabled='type==0?true:false'>下一步</el-button>
    </div>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import subheading from "@/views/register/head.vue";

export default defineComponent({
   components: { subheading },

  setup() {
    const router = useRouter();
    const route = useRoute();
    const state = reactive({
      type: 0,
    });
    const methods = {};
    const fun = {
      next() {
        if(state.type==0){
          return false
        }
        router.push(`/register/step1/${state.type}`)
      },
    };
    return { ...toRefs(state),...fun };
  },
});
</script>
<style lang="less">
.register-first-mn {
  background: #f4f5f9;
  padding-bottom: 50px;
  .content {
    margin: 32px auto;
    text-align: center;
    background: #fff;
  }
  .tip-box {
    padding: 45px 0;
    border-bottom: 1px solid #f2f2f2;
    h1 {
        color: #5f9efc;
            display: inline-flex;
      label {
        
        font-size: 28px;
        font-weight: 500;
      }
      i.iconfont {
        font-size: 40px;
        padding-right: 12px;
           
      }
    }
    p {
      color: #333;
      font-size: 18px;
      padding: 25px 0 0 0;
    }
  }
  .sele-box {
    h2 {
      color: #333;
      font-size: 24px;
      font-weight: 500;
    }
    h2.tit {
      padding: 40px 0;
    }
    ul {
      width: 684px;
      margin: auto;
    }
    li {
      width: 282px;
      height: 280px;
      border: 1px solid #f2f2f2;
      border-radius: 8px;
      position: relative;
      cursor: pointer;
    }
    li.sel {
      border: 1px solid #5f9efc;
    }
    p {
      color: #333;
      font-size: 14px;
      padding: 20px 0 0 0;
    }
    .img {
      width: 282px;
      height: 175px;
    }
    .putong{
      background: url(/src/static/img/putong.png) no-repeat center center;
    }
    .biyesheng{
      background: url(/src/static/img/biyesheng.png) no-repeat center center;
    }
    span {
      display: block;
      position: absolute;
      right: 0;
      bottom: 0;
      width: 32px;
      height: 32px;
      text-align: center;
      line-height: 32px;
      color: #fff;
      font-size: 20px;
      background: #5f9efd;
      
      border-radius: 8px 0 8px 0;
    }
  }
  .el-button {
    background: #5f9efc;
    border-radius: 4px;
    width: 194px;
    height: 55px;
    padding: 0 0 0 0;
    line-height: 55px;
    margin: 80px 0 50px ;
    font-size: 18px;
  }
}


</style>