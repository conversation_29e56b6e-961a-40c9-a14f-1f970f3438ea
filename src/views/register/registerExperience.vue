<template>
  <div class="register_radius_box register_basic_info">
    <div class="title">
      <img src="../../assets/register/icon_gz.png" alt="icon" />
      <h1>{{typeTitle}}经历</h1>
      <p>请填写最近一份{{typeTitle}}信息</p>
    </div>

    <div class="form">
      <el-form
        style="width: 100%"
        :model="sizeForm"
        label-width="110px"
        :label-position="'left'"
        :rules="rules"
        ref="ruleForm"
      >
        <el-form-item label="职位名称" prop="positionIDs">
          <div class="position_box">
            <rc-cascader
              v-model="sizeForm.positionIDs"
              ref="industryCascader"
              width="532"
              :title="'职位名称'"
              height="40"
              clearVisible
              :options="positionseList"
              :valueId="'keywordID'"
              :asyncCommit="false"
              :multiple="false"
              :label="'keywordName'"
              :children="'children'"
              :placeholderSelect="'请选择'"
              :timelyClose="true"
              :deepFlex="3"
              :showApplySearchValue="true"
              @change="positionChange($event)"
              @clear="clear"
            ></rc-cascader>
          </div>
        </el-form-item>

        <el-form-item
          label="公司名称"
          prop="company"
          v-if="!currentPositionsIsblueCollar"
        >
          <!-- <el-input v-model="sizeForm.company" placeholder="请填写公司名称" /> -->

          <el-autocomplete
            v-model="sizeForm.company"
            :fetch-suggestions="querySearchAsyncCompanyName"
            placeholder="请填写公司名称"
            @select="handleSelectCompanyName"
            maxlength="30"
            show-word-limit
          >
            <template #default="data">
              <div class="positions_name" v-for="item in data">
                <p v-html="item"></p>
              </div>
            </template>
          </el-autocomplete>
          <p
            v-if="sizeForm.company"
            style="
              margin: 0;
              font-size: 12px;
              line-height: 18px;
              color: #9a9ea5;
            "
          >
            已为你屏蔽该公司,招聘方不会看到你的简历,安心求职吧
          </p>
        </el-form-item>

        <el-form-item
          label="拥有技能"
          prop="havingSkills"
          v-if="!currentPositionsIsblueCollar && showSkillsSelectFormItem"
        >
          <div class="Skills_box">
            <div class="simulate_input" @click="getworkSkills()">
              <div class="content">
                <p
                  class="placeholder"
                  v-if="sizeForm.havingSkills?.length <= 0"
                >
                  请选择拥有技能
                </p>
                <div class="select__tags" v-else>
                  <el-tag
                    @close.stop="deleteitem(index)"
                    v-for="(tag, index) in sizeForm.havingSkills"
                    :key="index"
                    closable
                    :type="''"
                  >
                    {{ tag.keywordName }}
                  </el-tag>
                </div>
              </div>
              <div class="icon">
                <!-- <i>{{ sizeForm.havingSkills?.length }}/5</i> -->
                <span class="el-input__count-inner">{{ sizeForm.havingSkills?.length }}/5</span>
                <!-- <i class="el-icon-arrow-down"></i> -->
              </div>
            </div>
            <seleworkSkill
              ref="seleworkSkillRef"
              @confirm="confirmworkSkill"
              v-if="dialogVisibleSkill"
              :hideValue="sizeForm.havingSkills"
              :careerId="Number(lastPosition)"
              :maxCount="5"
            ></seleworkSkill>
          </div>
        </el-form-item>

        <el-form-item
          label="在职时间"
          prop="timeToWork"
          v-if="!currentPositionsIsblueCollar"
        >
          <!-- <el-date-picker v-model="sizeForm.timeToWork" value-format="YYYY-MM" format="YYYY-MM" type="monthrange" range-separator="至" start-placeholder="开始月份"
            end-placeholder="结束月份" :disabled-date="disabledDate">
          </el-date-picker> -->
          <div style="align-items: center; display: flex">
            <el-date-picker
              v-model="sizeForm.beginTime"
              value-format="YYYY-MM"
              format="YYYY-MM"
              type="month"
              placeholder="选择开始月份"
              :disabled-date="disabledDateBegin"
            >
            </el-date-picker>
            <div style="margin: 0 15px">-</div>
            <el-date-picker
              v-model="sizeForm.endTime"
              value-format="YYYY-MM"
              format="YYYY-MM"
              type="month"
              :disabled-date="disabledDateEnd"
              placeholder="选择结束月份"
              :disabled="!sizeForm.beginTime || sizeForm.soFar"
            >
            </el-date-picker>

            <el-checkbox
              @change="soFarChange"
              style="margin-left: 10px"
              v-model="sizeForm.soFar"
              label="至今"
            ></el-checkbox>
          </div>
        </el-form-item>

        <el-form-item
          label="职位描述"
          prop="desc"
          v-if="!currentPositionsIsblueCollar"
        >
          <el-input
            v-model="sizeForm.desc"
            type="textarea"
            :rows="5"
            maxlength="2000"
            show-word-limit
            :placeholder="`请输入${typeTitle}内容：&#10;如：&#10;1.主要负责绩效考评相关工作&#10;2.完成招聘绩效考核获得优秀评级`"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="register_button_box">
        <div
          class="register_button previous_step"
          style="margin: 0 auto; margin-top: 3px; width: 180px"
          @click="previousStep"
        >
          上一步
        </div>
        <div style="margin: 0 auto; margin-top: 3px">
          <el-button
            @click="onSubmit"
            class="register_button"
            style="width: 180px"
            type="primary"
            :loading="loading"
            >下一步</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from "vue";
import { ElForm } from "element-plus";
import { OptionsClass } from "../../http/app/Options";
import { KeywordItemDto } from "../../http/app/data-contracts";
import rCascader from "gxrcw-ui";
import { AutocompeleteClass } from "../../http/app/Autocompelete";
import { descValidatePass } from "./validator";

import { 简历注册相关PcClass } from "../../http/register/简历注册相关Pc";
import { 所有端公共部分Class } from "../../http/register/所有端公共部分";
import { WorkInfoStep } from "../../http/register/data-contracts";
import seleworkSkill from "@/components/seleworkSkill.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { positionantistops } from "../../http/dictionary";
const route = useRoute();
const router = useRouter();

const resumeID = ref(0);
const jobSeekerTalentDegree = ref(0);

resumeID.value = Number(route.query.resumeID);
jobSeekerTalentDegree.value = Number(route.query.jobSeekerTalentDegree);

const sizeForm = ref<
  WorkInfoStep & {
    timeToWork?: string[];
    havingSkills: any[];
    positionIDs: string;
    soFar: boolean;
  }
>({
  jobSeekerAttendWorkYear: "",
  timeToWork: [],
  havingSkills: [],
  positionIDs: "",
  soFar: false,
});

const typeTitle = computed(() => {
  if (route.query.jobSeekerTalentDegree == 1) {
    return "实习";
  } else {
    return "工作";
  }
});

const ruleForm = ref<InstanceType<typeof ElForm>>();

const positionseList = ref<KeywordItemDtoS[]>([]);

const getSaveInfo = async () => {
  const data = await 简历注册相关PcClass.apiRegisterStepRegisterworkinfopcGet({
    resumeid: resumeID.value,
    workid: 0,
  });
  if (data.code == 1 && data && data.data) {
    console.log(data.data);

    for (let key in data.data) {
      sizeForm.value[key] = data.data[key];
    }

    if (data.data.beginTime && !data.data.endTime) {
      sizeForm.value.soFar = true;
    }

    if (data.data.positionID) {
      sizeForm.value.positionIDs = data.data.positionPaths + "";
    } else {
      sizeForm.value.positionIDs = data.data.position || "";
    }

    if (data.data.isBluePosition) {
      currentPositionsIsblueCollar.value = true;
    } else {
      currentPositionsIsblueCollar.value = false;
    }

    if (data.data.keywordIds) {
      sizeForm.value.havingSkills = data.data.keywordIds.map((item) => ({
        keywordID: item.keywordID,
        keywordName: item.keywordName,
        selected: true,
      }));
    }

    getSkillsShow();
  }
};

getSaveInfo();

const soFarChange = (e: boolean) => {
  if (e) {
    sizeForm.value.endTime = "";
  }
};

const disabledDateBegin = (time: Date) => {
  return (
    time.getTime() >= Date.now() ||
    new Date("1990-01-01").getTime() > time.getTime()
  );
};

const disabledDateEnd = (time: Date) => {
  if (!sizeForm.value.beginTime) {
    return new Date("1990-01-01").getTime() > time.getTime();
  } else {
    const begin = new Date(sizeForm.value.beginTime);
    // begin.setFullYear(begin.getFullYear() + 1);

    return time.getTime() >= Date.now() || begin.getTime() > time.getTime();
  }
};

const currentPositionsIsblueCollar = ref(true);
const positionChange = async (event: any) => {
  if (event.keywordID) {
    currentPositionsIsblueCollar.value = event.blueCollarFlag;
  } else {
    if (event.length > 30) {
      sizeForm.value.positionIDs = event.slice(0, 30);
      ElMessage.warning("职位名称最多输入30个字");
    }

    const data =
      await 所有端公共部分Class.apiRegisterstepRegistermatchcareerinfoGet({
        careername: event,
        count: 1,
      });
    if (Array.isArray(data.data)) {
      currentPositionsIsblueCollar.value = Boolean(data.data[0].bluecollarflag);
    }
  }
  sizeForm.value.havingSkills = [];

  nextTick(() => {
    ruleForm.value?.validateField("positionName", () => {});
    getSkillsShow();
  });
};

const showSkillsSelectFormItem = ref(false);
const getSkillsShow = async () => {
  let data: any = await positionantistops({
    positiontypeid: lastPosition.value,
  });
  if (data.code == 1 && data.data.length > 0) {
    showSkillsSelectFormItem.value = true;
  } else {
    showSkillsSelectFormItem.value = false;
  }
};

const clear = () => {
  ruleForm.value?.validateField("positionName", () => {});
  sizeForm.value.havingSkills = [];
};

const dialogVisibleSkill = ref(false);

const querySearchAsyncCompanyName = async (queryString: string, cb: any) => {
  if (!queryString) return cb([]);
  const data =
    await AutocompeleteClass.apiAutocompeleteSearchenterprisenamePost({
      keyword: queryString,
      size: 10,
    });
  if (data.code == 1) {
    cb(data.data.result);
  } else {
    cb([]);
  }
};

const handleSelectCompanyName = (item: string) => {
  sizeForm.value.company = item || "";
};

const getworkSkills = () => {
  if (!lastPosition.value) {
    ElMessage({
      showClose: true,
      message: `请选择职位类型`,
      type: "warning",
    });
    return false;
  }
  dialogVisibleSkill.value = true;
};

const lastPosition = computed(() => {
  if (sizeForm.value.positionIDs) {
    const list = sizeForm.value.positionIDs.split(",");
    if (list.length > 0) {
      return list[list.length - 1];
    } else {
      return "";
    }
  } else {
    return "";
  }
});

const deleteitem = (ind: number) => {
  sizeForm.value.havingSkills.splice(ind, 1);
};
const confirmworkSkill = (arr: any, type: number) => {
  console.log(arr);

  dialogVisibleSkill.value = false;
  if (type == 1) {
    sizeForm.value.havingSkills = arr;
  }
};

const getOptionsPositions = async () => {
  const data = await OptionsClass.apiOptionsPositionGet({
    parentid: 0,
    withcache: false,
  });
  if (data.code == 1) {
    positionseList.value = buildTree(data.data);
  }
};

type KeywordItemDtoS = KeywordItemDto & { children: KeywordItemDtoS[] };
function buildTree(data: KeywordItemDto[]): KeywordItemDtoS[] {
  const map = new Map<number, KeywordItemDtoS>();
  const tree: KeywordItemDtoS[] = [];

  // 先将所有节点放入 map 中
  data.forEach((item) => {
    map.set(item.keywordID || 0, { ...item, children: [] });
  });

  // 再将子节点挂载到父节点的 children 字段下
  data.forEach((item) => {
    if (item.parentID === 0) {
      tree.push(map.get(item.keywordID || 0)!);
    } else {
      const parent = map.get(item.parentID || 0);
      if (parent) {
        parent.children.push(map.get(item.keywordID || 0)!);
      }
    }
  });

  return tree;
}

getOptionsPositions();
const previousStep = () => {
  router.go(-1);
};
const loading = ref(false);

function containsChinese(str: string): boolean {
  const chineseRegex = /[\u4e00-\u9fa5]/;
  return chineseRegex.test(str);
}
const onSubmit = () => {
  ruleForm.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      const obj = Object.assign({}, sizeForm.value);

      if (!containsChinese(obj.positionIDs)) {
        const idsList = obj.positionIDs.split(",");

        obj.positionID = Number(idsList[idsList.length - 1]);
        delete obj.position;
      } else {
        delete obj.positionID;
        obj.position = obj.positionIDs;
      }

      obj.keywordIds = obj.havingSkills.map((item) => ({
        keywordID: item.keywordID,
        keywordName: item.keywordName,
      }));

      delete obj.timeToWork;
      delete obj.positionIDs;
      delete obj.havingSkills;

      if (currentPositionsIsblueCollar.value) {
        obj.company = "";
        const data =
          await 简历注册相关PcClass.apiRegisterStepSaveregisterblueworkinfopcPost(
            {
              workID: obj.workID,
              resumeID: resumeID.value,
              positionID: obj.positionID,
              positionName: obj.position,
            }
          );
        if (data.code == 1) {
          // ElMessage({
          //   type: "success",
          //   message: data.message || "保存成功",
          // });
          setTimeout(() => {
            loading.value = false;
            router.push(
              `/register/registerRouteView/registerEducation?resumeID=${resumeID.value}&jobSeekerTalentDegree=${jobSeekerTalentDegree.value}`
            );
          }, 50);
        } else {
          loading.value = false;
          ElMessage({
            type: "error",
            message: data.message || "保存失败",
          });
        }
        return;
      } else {
        const data =
          await 简历注册相关PcClass.apiRegisterStepSaveregisterworkinfopcPost({
            ...obj,
            resumeID: resumeID.value,
          });
        if (data.code == 1) {
          // ElMessage({
          //   type: "success",
          //   message: data.message || "保存成功",
          // });
          setTimeout(() => {
            loading.value = false;
            router.push(
              `/register/registerRouteView/registerEducation?resumeID=${resumeID.value}&jobSeekerTalentDegree=${jobSeekerTalentDegree.value}`
            );
          }, 50);
        } else {
          loading.value = false;
          ElMessage({
            type: "error",
            message: data.message || "保存失败",
          });
        }
      }
    } else {
      console.log("error submit!!");
      return false;
    }
  });
};

const validateTime = (
  rule,
  value: string,
  callback: (error?: Error) => void
) => {
  nextTick(() => {
    if (!sizeForm.value.beginTime) {
      callback(new Error("请选择在职时间"));
    }
    if (!sizeForm.value.endTime && !sizeForm.value.soFar) {
      callback(new Error("请选择在职时间"));
    }
    callback();
  });
};

const rules = {
  positionIDs: [
    { required: true, message: "请选择职位名称", trigger: "change" },
  ],
  company: [{ required: true, message: "请填写公司名称", trigger: "blur" }],

  havingSkills: [
    { required: true, message: "请选择拥有技能", trigger: "change" },
  ],

  timeToWork: [{ required: true, validator: validateTime, trigger: "change" }],

  desc: [{ required: true, validator: descValidatePass, trigger: "blur" }],
};
</script>
<style lang="less" scoped>
@import "./css/style.less";

.register_basic_info {
  padding: 43px 59px 50px 59px;

  .form {
    .register_button_box {
      .previous_step {
        background: #fff;
        border: 1px solid #d0d2d9;
        color: #666666;
      }
    }

    .Skills_box {
      min-height: 40px;

      .simulate_input {
        cursor: pointer;
        -webkit-appearance: none;
        background-color: var(
          --el-input-background-color,
          var(--el-color-white)
        );
        background-image: none;
        border-radius: var(
          --el-input-border-radius,
          var(--el-border-radius-base)
        );
        border: var(--el-input-border, var(--el-border-base));
        box-sizing: border-box;
        color: var(--el-input-font-color, var(--el-text-color-regular));
        display: inline-block;
        font-size: inherit;
        min-height: 40px;
        line-height: 40px;
        outline: none;
        padding-right: 40px;
        transition: var(--el-transition-border);
        width: 100%;
        position: relative;

        .content {
          min-height: 40px;

          .placeholder {
            color: var(--el-text-color-placeholder);
            padding-left: 14px;
          }

          .select__tags {
            min-height: 40px;
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .el-tag {
              margin: 2px;
            }
          }
        }

        .icon {
          position: absolute;
          height: 100%;
          right: 8px;
          top: 0;
          text-align: center;
          color: var(--el-color-info);
          transition: all 0.3s;
          pointer-events: none;
          z-index: 2;
          cursor: pointer;
          font-size: 12px;
        }
      }

      :hover {
        border-color: #c0c4cc;
      }
    }

    .is-error {
      ::v-deep(.gxrc-cascader) {
        .gxrc-input__inner {
          border-color: var(--el-color-danger);
        }
      }

      .simulate_input {
        border-color: var(--el-color-danger);
      }
    }
  }

  ::v-deep(.el-date-editor) {
    width: 100%;
  }

  ::v-deep(.el-select) {
    width: 100%;
  }
  ::v-deep(.el-autocomplete) {
    width: 100%;
  }
}
</style>
