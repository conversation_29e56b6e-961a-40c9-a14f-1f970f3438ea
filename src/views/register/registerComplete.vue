<template>
  <div class="register_radius_box register_complete">
    <el-progress
      type="circle"
      :percentage="progress"
      status="exception"
      :width="150"
      :stroke-width="15"
      stroke-linecap="butt"
      :color="color"
    >
      <template #default="{ percentage }">
        <p class="percentage-value">
          <span :style="{ color }"> {{ percentage }} </span><i>%</i>
        </p>
        <span class="percentage-label">完整度</span>
      </template>
    </el-progress>

    <h1>简历填写完成！请等待审核</h1>
    <h2>您的简历将在1-2个工作日内完成审核，请耐心等待</h2>

    <!-- <div
      class="register_button button"
      style="margin: 0 auto; margin-top: 55px; width: 180px"
      @click="onSubmit"
    >
      前往个人中心
    </div> -->

    <div style="margin: 0 auto; margin-top: 55px">
          <el-button
            @click="onSubmit"
              class="register_button button"
            style="width: 180px"
            type="primary"
            :loading="loading"
            >前往个人中心</el-button
          >
        </div>
  </div>

  <p class="tips">根据您的求职意向，以下岗位可能适合您</p>
  <!-- 搜索 -->
  <div class="search">
    <div class="search-bar">
      <div class="searchBox">
        <div class="searchType">
          <el-select v-model="searchValue" value-key="schType">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              @click="schType = item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="inp">
          <el-input v-model="keyName" placeholder="请输入职位关键字"></el-input>
        </div>
        <!-- 城市 -->
        <div class="pop">
          <el-input
            v-model="cityName"
            placeholder="选择城市"
            suffix-icon="el-icon-caret-bottom"
            @focus="dialogVisibleA = true"
            :readonly="true"
          ></el-input>
          <seleICity
            @confirmCity="confirmCity"
            v-if="dialogVisibleA"
            title="城市"
            :maxCount="1"
            :hideValue="[cityId]"
          ></seleICity>
        </div>
        <!-- 行业 -->
        <div class="pop">
          <el-input
            v-model="IndustryName"
            placeholder="选择行业"
            suffix-icon="el-icon-arrow-down"
            readonly
            @focus="dialogVisibleB = true"
          ></el-input>
          <seleIndustryThreeLevel
            :hideValue="Industryid"
            @confirm="confirmIndustry"
            :maxCount="1"
            v-if="dialogVisibleB"
          ></seleIndustryThreeLevel>
        </div>
        <!-- 职位 -->
        <div class="pop">
          <el-input
            v-model="CareerName"
            placeholder="选择职位"
            suffix-icon="el-icon-arrow-down"
            readonly
            @focus="dialogVisibleC = true"
          ></el-input>
          <seleCareer
            @confirm="confirmCareer"
            v-if="dialogVisibleC"
            :hideValue="CareerId"
          ></seleCareer>
        </div>
      </div>
      <div class="btn" @click="search()">
        <el-button type="primary">搜索</el-button>
      </div>

      <div class="line">
        <a href="//s.gxrc.com" target="_blank">高级搜索</a>
      </div>
    </div>
  </div>

  <div class="list list_12_5">
    <el-row
      :gutter="12"
      v-if="Array.isArray(positionList) && positionList.length > 0"
    >
      <el-col :span="8" v-for="(item, index) in positionList" :key="index">
        <el-card class="box-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span
                ><a
                  :href="`//www.gxrc.com/jobDetail/${item.positionGuid}`"
                  target="_blank"
                  :title="item.positionName"
                  class="tit"
                  >{{ item.positionName }}</a
                ></span
              >
              <el-button type="text">{{ item.payPackage }}</el-button>
            </div>
          </template>
          <div class="text item">
            <a
              :href="`//www.gxrc.com/company/${item.enterpriseGuid}`"
              target="_blank"
              :title="item.enterpriseName"
              >{{ item.enterpriseName }}</a
            >
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-empty v-else-if="positionList" description="暂无推荐职位"></el-empty>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import seleIndustryThreeLevel from "@/components/seleIndustryThreeLevel.vue";
import seleICity from "@/components/seleCity.vue";
import seleCareer from "@/components/seleCareer.vue";
import { getCookies } from "@/utils/common";
import { getPositionRecommendByCaree } from "@/http/mApiUrl";
import { ResumepartClass } from "../../http/app/Resumepart";
import { useRoute } from "vue-router";
import { ElLoading } from "element-plus";
import { useStore } from "vuex";
const route = useRoute();
const router = useRouter();

const resumeID = ref(0);
const jobSeekerTalentDegree = ref(0);
const expectCareer = ref(0);

resumeID.value = Number(route.query.resumeID);
jobSeekerTalentDegree.value = Number(route.query.jobSeekerTalentDegree);
expectCareer.value = Number(route.query.expectCareer);

const positionList = ref<any[] | null>(null);
const dialogVisibleA = ref(false);
const dialogVisibleB = ref(false);
const dialogVisibleC = ref(false);
const cityId = ref(0);
const cityName = ref("");
const IndustryName = ref("");
const Industryid = ref(0);
const CareerName = ref("");
const CareerId = ref(0);
const keyName = ref("");

const searchValue = ref("搜职位");
const options = ref([
  {
    value: 1,
    label: "搜职位",
  },
  {
    value: 2,
    label: "搜公司",
  },
]);

//接收子级传过来的值--城市
const confirmCity = (arr: any) => {
  dialogVisibleA.value = false;
  if (!arr) return false;
  cityName.value = arr[0].keywordName;
  cityId.value = arr[0].keywordID;
};
//接收从子集传过来的数据
const confirmIndustry = (p: any) => {
  dialogVisibleB.value = false;
  if (!p) {
    return false;
  }
  Industryid.value = p[0].keywordID;
  IndustryName.value = p[0].keywordName;
};
//接收从子集传过来的数据---职位
const confirmCareer = (p: any) => {
  dialogVisibleC.value = false;
  if (p) {
    CareerName.value = p.keywordName;
    CareerId.value = p.keywordID;
  }
};
const goMy = () => {
  router.push({ path: "/" });
};
// 搜索
const search = () => {
  let url = `//s.gxrc.com/sJob?schType=${schType.value}&district=${cityId.value}&posType=${CareerId.value}&page=1&pageSize=20&keyword=${keyName.value}&industry=${Industryid.value}`;
  window.location.href = url;
};

const schType = ref(1);
const progress = ref(0);

const color = computed(() => {
  return progress.value >= 50 ? "#3486FF" : "#FFB45D";
});
const districtID = computed(() => {
  return getCookies("bid");
});

onMounted(() => {
  getPositionData();
});
const getPositionData = async () => {
  console.log(document.querySelector(".list_12_5"));

  const loading = ElLoading.service({
    text: "加载中...",
    target: document.querySelector(".list_12_5"),
    background: "rgba(0, 0, 0, 0)",
  });
  let parameter = {
    districtId: districtID.value || 0,
    positionCareeID: expectCareer.value,
    page: 1,
    pagesize: 15,
  };
  const res = await getPositionRecommendByCaree(parameter);

  loading.close();
  if (res.code == 1) {
    positionList.value = res.data;
  }
};

const getCompletestate = async () => {
  const data = await ResumepartClass.apiResumepartCompletestateResumeidGet(
    resumeID.value
  );
  if (data.code == 1) {
    progress.value = data.data.totalScore;
  }
};
getCompletestate();
const store = useStore();
const loading = ref(false)
const onSubmit = async () => {
  loading.value = true
  await store.dispatch("getUserInfo");
  loading.value = false
  router.push("/");
};
</script>
<style lang="less" scoped>
@import "./css/style.less";
.list {
  padding: 0 0 200px 0;
  min-height: 100px;
  box-sizing: border-box;
  .el-card {
    border: none;
    margin-bottom: 12px;
    :deep(.el-card__header) {
      padding: 0;
    }
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    overflow: hidden;
    padding: 14px 14px 5px 14px;

    span {
      flex: 1;
      width: 0;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .el-button {
      padding: 0;
    }
    .el-button--text {
      font-weight: bold;
      font-size: 12px;
      color: #eb584b;
    }
  }
  .tit {
    color: #333;
    font-size: 17px;
    font-weight: 600;
  }
  :deep(.el-card__header) {
    border-bottom: none;
    padding: 10px 24px 0 24px;
  }

  .el-button--text {
    span {
      color: #fc5c5b;
      font-size: 17px;
      font-weight: bold;
    }
    float: right;
  }
  :deep(.el-card__body) {
    padding: 0 14px 15px 14px;

    .text {
      overflow: hidden;
    }
    a {
      font-size: 13px;
      color: #666666;

      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
.tips {
  text-align: center;

  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #247bff;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin: 36px 0 14px 0;
}

.search {
  padding: 20px 0;
  margin-bottom: 12px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;

  width: 100%;
  .search-bar {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 14px;
    box-sizing: border-box;
  }
  .searchBox {
    border: 1px solid #f2f2f2;
    border-right: none;
    height: 40px;
    border-radius: 4px 0 0 4px;
    display: flex;
    align-items: center;
    flex: 1;
  }
  .searchType {
    width: 100px;
    line-height: 40px;
  }
  :deep(.el-input__inner) {
    border: none;
  }
  .pop {
    width: 100px;
    cursor: pointer;
    font-size: 12px;
    .el-input {
      font-size: 12px;
    }
  }
  .inp {
    .el-input__inner {
      width: 500px;
    }
  }
  .btn {
    width: 96px;
    .el-button {
      padding: 0 0 0 0;
      line-height: 40px;
      text-align: center;

      width: 96px;
      height: 40px;
      background: #247bff;
      border-radius: 6px 6px 6px 6px;
    }
  }
  .line {
    width: 50px;
    margin-left: 14px;
    a {
      width: 50px;
      height: 40px;
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #247bff;
      line-height: 40px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}

.register_complete {
  min-height: 376px;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 37px 0;
  h2 {
    font-family:
      Source Han Sans CN,
      Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #9a9ea5;
    text-align: center;
    margin-top: 4px;
  }
  h1 {
    font-family:
      Source Han Sans CN,
      Source Han Sans CN;
    font-weight: bold;
    font-size: 22px;
    color: #222222;
    text-align: center;
    margin-top: 34px;
  }
  .button {
    padding: 10px 28px;
    margin-top: 21px !important;
  }

  .el-progress.is-exception .el-progress__text {
    .percentage-value {
      font-size: 32px;
      color: #333;
      font-weight: bolder;
      i {
        font-size: 16px;
      }
    }
    .percentage-label {
      width: 48px;
      height: 24px;
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #222222;
      text-align: center;
    }
  }
}
</style>
