<template>
  <div class="register_radius_box register_success">
    <div class="icon">
      <el-icon><SuccessFilled /></el-icon>
    </div>
    <h1>注册成功！</h1>
    <p>立即创建简历，便可投递适合您的职位</p>
    <div class="register_button" style="margin-top: 55px" @click="createResume">创建简历</div>
  </div>
</template>
<script lang="ts" setup>
import { SuccessFilled } from "@element-plus/icons";
import { useRouter } from "vue-router";
const router = useRouter()
const createResume = ()=>{
  router.push('/register/registerRouteView/registerBasicInfo')
}
</script>
<style lang="less" scoped>
@import "./css/style.less";
.register_success {

  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  h1 {
    font-family: Source <PERSON>, Source <PERSON>s CN;
    font-weight: bold;
    font-size: 26px;
    color: #222222;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-top: 51px;
  }
  p {
    margin-top: 13px;

    font-family: Source <PERSON>, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #9a9ea5;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .icon {
    width: 175px;
    height: 175px;
    background: rgb(243, 248, 255);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    .el-icon {
      font-size: 125px;
      color: #247bff;
    }
  }
}
</style>
