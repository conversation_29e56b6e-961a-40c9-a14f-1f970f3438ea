<template>
  <div class="step-bar w1200">
    <ul class="clearfix">
      <li
        :class="{
          'is-finishA': step > 0,
          whitedelta: step == 1,
          bluedeltaA: step >= 2,
        }"
      >
        个人信息/求职意向
        <div class="delta">
          <span class="triangle-topright"></span
          ><span class="triangle-bottomright"></span>
        </div>
      </li>
      <li :class="{ 'is-finishB': step > 1 , whitedelta: step <= 2,bluedeltaB: step > 2}">
        教育背景/工作经历
        <div class="delta">
          <span class="triangle-topright"></span
          ><span class="triangle-bottomright"></span>
        </div>
      </li>
      <li :class="{ 'is-finishC': step > 2 }">完成</li>
    </ul>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs } from "vue";
import { useStore } from "vuex";
export default {
  props: {
    step: {
      type: Number,
      default: 1,
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const state = reactive({});
    return { ...toRefs(state) };
  },
};
</script>
<style lang="less">
.step-bar {
  padding: 40px 0 24px 0;
  ul {
    background: #fff;
    overflow: hidden;
  }
  li {
    width: 400px;
    height: 48px;
    text-align: center;
    line-height: 48px;
    font-size: 18px;
    color: #bbb;
    float: left;
    position: relative;
  }
  .delta {
    position: absolute;
    z-index: 9;
    right: 0;
    top: 13px;
  }
  .triangle-topright {
    width: 0;
    height: 0;
    position: absolute;
    top: -14px;
    right: -0px;
  }
  .triangle-bottomright {
    width: 0;
    height: 0;
    top: 10px;
    position: absolute;
    right: -2px;
  }

  li.whitedelta {
    span.triangle-topright {
      border-top: 25px solid #fff;
      border-left: 25px solid transparent;
    }
    span.triangle-bottomright {
      border-bottom: 40px solid #ffffff;
      border-left: 40px solid transparent;
    }
  }
  li.bluedeltaA {
    span.triangle-topright {
      border-top: 25px solid #3385ff;
      border-left: 25px solid transparent;
    }
    span.triangle-bottomright {
      border-bottom: 40px solid #3385ff;
      border-left: 40px solid transparent;
    }
  }
  li.bluedeltaB {
    span.triangle-topright {
      border-top: 25px solid #156ae8;
      border-left: 25px solid transparent;
    }
    span.triangle-bottomright {
      border-bottom: 40px solid #156ae8;
      border-left: 40px solid transparent;
    }
  }
  li.is-finishA {
    background: #5f9efc;
    color: #fff;
  }
  li.is-finishB {
    background: #3385ff;
    color: #fff;
  }
  li.is-finishC {
    background: #156ae8;
    color: #fff;
  }
  li:not(:last-child)::before {
    transform: rotate(-45deg) translateY(-9px);
    transform-origin: 0 0;
    content: "";
    display: inline-block;
    position: absolute;
    height: 40px;
    width: 5px;
    top: 6px;
    right: 20px;
    background: #f4f5f9;
  }
  li:not(:last-child)::after {
    content: "";
    display: inline-block;
    position: absolute;
    height: 40px;
    width: 5px;
    right: 21px;
    background: #f4f5f9;
    bottom: 1px;
  }
  li:not(:last-child)::after {
    transform: rotate(45deg) translateY(9px);
    transform-origin: 100% 100%;
  }
  *,
  :before,
  :after {
    box-sizing: border-box;
  }
}
</style>