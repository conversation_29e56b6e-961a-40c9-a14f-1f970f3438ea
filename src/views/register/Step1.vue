<template>
  <div class="register-step-mn clearfix">
    <subheading></subheading>
    <stepBar :step="1"></stepBar>
    <div class="content w1200 edit-unify">
      <el-config-provider :locale="localeZH">
        <div class="box">
          <h1>个人信息</h1>
          <el-form
            :model="basicForm"
            label-width="120px"
            label-position="left"
            :rules="rulesA"
            ref="refBasicForm"
          >
            <el-form-item label="姓名" class="w520" prop="jobSeekerName">
              <el-input
                v-model="basicForm.jobSeekerName"
                placeholder="请输入姓名"
              ></el-input>
            </el-form-item>

            <el-form-item label="性别" class="radio-group" prop="jobSeekerSex">
              <el-radio-group v-model="sex">
                <el-radio
                  label="男"
                  name="1"
                  @click="basicForm.jobSeekerSex = false"
                ></el-radio>
                <el-radio
                  label="女"
                  name
                  @click="basicForm.jobSeekerSex = true"
                ></el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="出生年月" prop="jobSeekerBrithday">
              <el-date-picker
                placeholder="选择时间"
                v-model="basicForm.jobSeekerBrithday"
                class="w520"
                format="YYYY-MM"
                value-format="YYYY-MM"
                type="month"
                :default-value="[2000]"
                :disabled-date="disabledDate"
                :clearable="false"
              ></el-date-picker>
            </el-form-item>
            <el-form-item
              label="目前所在地"
              prop="residencyName"
              class="w520 work-suffix"
            >
              <el-input
                v-model="basicForm.residencyName"
                placeholder="请选择目前所在地"
                suffix-icon="el-icon-arrow-down"
                @focus="dialogVisibleA = true"
                readonly
              ></el-input>
              <seleICity
                @confirmCity="currentCity"
                v-if="dialogVisibleA"
                title="目前所在地"
                :maxCount="1"
                :hideValue="[basicForm.jobSeekerResidency]"
              ></seleICity>
            </el-form-item>
            <el-form-item label="籍贯" class="w520 work-suffix" prop="domicileName">
              <el-input
                v-model="basicForm.domicileName"
                placeholder="请选择籍贯"
                suffix-icon="el-icon-arrow-down"
                @focus="SelectDomicileNameCity()"
                clearable
              ></el-input>
              <seleICity
                @confirmCity="confirmDomicileNameCity"
                v-if="dialogVisibleE"
                title="籍贯"
                :maxCount="1"
                :hideValue="[basicForm.domicile]"
              ></seleICity>
            </el-form-item>
            <el-form-item label="工作经验" required>
              <el-radio-group v-model="isHaveWork" class="radio-group">
                <el-radio label="有" @click="haswork = true"></el-radio>
                <el-radio label="无" @click="haswork = false"></el-radio>
              </el-radio-group>
              <div class="el-form-item__error" v-if="!isHaveWork">请选择</div>
            </el-form-item>
            <el-form-item
              label="参加工作时间"
              class="w520"
              v-if="haswork"
              prop="jobSeekerAttendWorkYear"
            >
              <el-date-picker
                placeholder="选择参加工作时间"
                v-model="basicForm.jobSeekerAttendWorkYear"
                format="YYYY-MM"
                value-format="YYYY-MM"
                type="month"
                :disabled-date="disabledDate2"
              ></el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <!-- `````````````求职意向````````````````````` -->
        <div class="box">
          <h1>求职意向</h1>
          <el-form
            :inline="true"
            :model="careerForm"
            class="demo-form-inline"
            label-width="100px"
            label-position="left"
            :rules="rulesB"
            ref="refCareerForm"
          >
            <el-form-item label="期望职位" class="w520" prop="career">
              <el-input
                v-model="careerForm.career"
                placeholder="请输入或选择职位"
                suffix-icon="el-icon-arrow-down"
                readonly
                @focus="dialogVisibleC = true"
              ></el-input>
              <seleCareer
                @confirm="confirmCareer"
                v-if="dialogVisibleC"
                :hideValue="careerForm.careerId"
              ></seleCareer>
            </el-form-item>
            <el-form-item
              label="期望行业"
              class="w520"
              prop="expectIndustryNames"
            >
              <el-input
                v-model="careerForm.expectIndustryNames"
                placeholder="请选择"
                suffix-icon="el-icon-arrow-down"
                readonly
                @focus="dialogVisibleD = true"
              ></el-input>
              <seleIndustry
                :hideValue="[
                  careerForm.industryId1,
                  careerForm.industryId2,
                  careerForm.industryId3,
                ]"
                @confirm="confirmIndustry"
                :positionId="careerForm.careerId"
                :maxCount="3"
                v-if="dialogVisibleD"
              ></seleIndustry>
            </el-form-item>
            <el-form-item label="目前状态" prop="workStatus">
              <el-select
                v-model="careerForm.workStatus"
                placeholder="请选择目前状态"
                class="w520"
              >
                <el-option
                  v-for="(p, i) in CurrentStatusList"
                  :key="i"
                  :label="p.keywordName"
                  :value="p.keywordID"
                  @click="careerForm.workStatusId = p.keywordID"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="期望工作地" prop="residency">
              <el-input
                v-model="careerForm.residency"
                placeholder="请选择"
                class="w520"
                @focus="dialogVisibleB = true"
                readonly
              ></el-input>
              <seleICity
                @confirmCity="confirmCity"
                v-if="dialogVisibleB"
                title="期望工作地"
                :maxCount="3"
                :hideValue="[
                  careerForm.residencyId1,
                  careerForm.residencyId2,
                  careerForm.residencyId3,
                ]"
              ></seleICity>
            </el-form-item>
            <el-form-item label="期望薪资" prop="salary" style="width: 100%">
              <el-col :span="14">
                <el-select
                  v-model="careerForm.salary"
                  placeholder="请选择期望薪资"
                  class="w520"
                >
                  <el-option
                    v-for="(p, i) in SalaryList"
                    :key="i"
                    :label="p.text"
                    :value="p.id"
                  ></el-option>
                </el-select>
                <span class="el-input__suffix yuan">元/月</span>
              </el-col>
              <el-col :span="4">&#12288;</el-col>
            </el-form-item>
          </el-form>
        </div>
        <div class="tc">
          <el-button
            class="sumBtn"
            type="primary"
            @click="onSubmit"
            :loading="loading"
            >保存到下一步</el-button
          >
        </div>
      </el-config-provider>
    </div>
  </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  computed,
  watch,
  ref,
  Ref,
  onBeforeMount,
} from "vue";
import subheading from "@/views/register/head.vue";
import stepBar from "@/views/register/stepBar.vue";
import seleICity from "@/components/seleCity.vue";
import seleCareer from "@/components/seleCareer.vue";
import seleIndustry from "@/components/seleIndustry.vue";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
import zhCn from "element-plus/lib/locale/lang/zh-cn";
import SalaryList from "../../store/SalarySelection"; //薪资字典
import {
  registerbaseinfo,
  saveregisterbaseinfo,
  registercareerinfo,
  saveregistercareerinfo,
} from "../../http/resumeApi";

import { getWorkstatuoptions } from "../../http/dictionary";
export default defineComponent({
  components: {
    subheading,
    stepBar,
    seleICity,
    seleCareer,
    seleIndustry,
    SalaryList,
  },

  setup() {
    const store = useStore();
    const router = useRouter();
    const route = useRoute();
    const refBasicForm: Ref<any> = ref(null);
    const refCareerForm: Ref<any> = ref(null);
    const state = reactive({
      type: 0,
      basicForm: {} as any,
      careerForm: {
        residency: "",
        careerForm: "",
        career: "",
        careerId: 0,
        industry1: "",
        industry2: "",
        industry3: "",
        industryId1: 0,
        industryId2: 0,
        industryId3: 0,
        residency1: "",
        residency2: "",
        residency3: "",
        residencyId1: 0,
        residencyId2: 0,
        residencyId3: 0,
        resumeId: 0,
        salary: "",
        workStatus: "",
        workStatusId: 0,
      } as any,
      sex: ref(),
      isHaveWork: ref(),
      loading: false,
      // resumeid: 0,
      dialogVisibleA: false,
      dialogVisibleB: false,
      dialogVisibleC: false,
      dialogVisibleD: false,
      dialogVisibleE: false,
      // expectIndustry: [],
      // expectWorkPlaceName: [],
      // expectIndustryNames: "", //期望行业
      haswork: false, //是否有工作经验
      // residency: "", //期望工作地
      CurrentStatusList: [], //目前状态
      SalaryList: SalaryList,
      localeZH: zhCn,
      disabledDate(time: any) {
        return time.getTime() > Date.now() - 3600 * 1000 * 24 * 365 * 16;
      },
      disabledDate2(time: any) {
        return time.getTime() > Date.now();
      },
    });
    const validateBrithday = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请选择出生年月"));
      } else if (Date.now() - value.getTime < 504576000000) {
        callback(new Error("请选择的出生年月不得小于16岁"));
      } else {
        callback();
      }
    };
    const validateWork = (rule: any, value: any, callback: any) => {
      if (value === "" || value === "0") {
        callback(new Error("请选择参加工作时间"));
      } else if (value < state.basicForm.jobSeekerBrithday) {
        callback(new Error("参加工作时间不得早于出生时间"));
      } else {
        callback();
      }
    };

    const rulesA = {
      jobSeekerName: [
        {
          required: true,
          message: "请输入姓名",
          trigger: "blur",
        },
      ],
      jobSeekerSex: [
        {
          required: true,
          message: "请选择性别",
          trigger: "change",
        },
      ],
      domicileName: [
        {
          required: true,
          message: "请选择籍贯",
          trigger: "change",
        },
      ],
      jobSeekerBrithday: [{ validator: validateBrithday, trigger: "blur" }],
      residencyName: [
        {
          required: true,
          message: "请选择目前所在地",
          trigger: "change",
        },
      ],
      jobSeekerAttendWorkYear: [{ validator: validateWork, trigger: "blur" }],
    };
    const rulesB = {
      career: [
        {
          required: true,
          message: "请选择期望职位",
          trigger: "change",
        },
      ],
      industryId1: [
        {
          required: true,
          message: "请选择期望工作行业",
          trigger: "change",
        },
      ],
      workStatus: [
        {
          required: true,
          message: "请选择目前求职状态",
          trigger: "change",
        },
      ],
      residency: [
        {
          required: true,
          message: "请选择期望工作地",
          trigger: "change",
        },
      ],

      expectIndustryNames: [
        {
          required: true,
          message: "请选择期望行业",
          trigger: "change",
        },
      ],
      salary: [
        {
          required: true,
          message: "请选择目前薪资",
          trigger: "change",
        },
      ],
    };

    watch(
      () => state.basicForm,
      (newValue, oldValue) => {
        state.sex = state.basicForm.jobSeekerSex ? ref("女") : ref("男");
        state.isHaveWork =
          state.basicForm.jobSeekerAttendWorkYear != "0"
            ? ref("有")
            : ref("无");
        state.haswork =
          state.basicForm.jobSeekerAttendWorkYear != "0" ? true : false;
      }
    );
    onBeforeMount(() => {
      methods.getinfo();
      methods.CurrentStatus();
    });
    let prevent = 1;
    const methods = {
      //获取基本信息
      async getinfo() {
        let form = {
          from: 0,
        };
        let res: any = await registerbaseinfo(form);
        if (res.code == 1) {
          state.resumeid = res.data.id;
          state.basicForm = res.data.info;
          this.getCareerinfo(res.data.id);
        }
      },
      //获取求职意向
      async getCareerinfo(id: number) {
        let form = {
          resumeid: id,
        };
        let res: any = await registercareerinfo(form);
        if (res.code == 1) {
          // state.careerForm = res.data;
          state.careerForm.career = res.data.career;
          state.careerForm.careerId = res.data.careerId;
          state.careerForm.industry1 = res.data.industry1;
          state.careerForm.industry2 = res.data.industry2;
          state.careerForm.industry3 = res.data.industry3;
          state.careerForm.industryId1 = res.data.industryId1;
          state.careerForm.industryId2 = res.data.industryId2;
          state.careerForm.industryId3 = res.data.industryId3;
          state.careerForm.residency1 = res.data.residency1;
          state.careerForm.residency2 = res.data.residency2;
          state.careerForm.residency3 = res.data.residency3;
          state.careerForm.residencyId1 = res.data.residencyId1;
          state.careerForm.residencyId2 = res.data.residencyId2;
          state.careerForm.residencyId3 = res.data.residencyId3;
          state.careerForm.resumeId = res.data.resumeId;
          state.careerForm.workStatus = res.data.workStatus;
          state.careerForm.workStatusId = res.data.workStatusId;
          state.careerForm.salary = res.data.salary ? res.data.salary : "";

          fun.changeIndustry();
          fun.changePlace();
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //  保存
      async saveData() {
        if (prevent === 2) {
          return false;
        }
        state.loading = true;
        let form = state.basicForm;
        if (!state.haswork) {
          form.jobSeekerAttendWorkYear = 0;
        }
        let workId = state.haswork ? 1 : 0;
        form.jobSeekerTalentDegree = route.params.id;
        prevent = 2;
        let res: any = await saveregisterbaseinfo(form);
        let res2: any = await saveregistercareerinfo(state.careerForm);
        prevent = 1;
        if (res.code == 1 && res2.code == 1) {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          router.push({
            path: `/register/step2/${state.resumeid}/${workId}`,
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
        state.loading = false;
      },
      // 目前状态
      async CurrentStatus() {
        let result = store.state.workstatuoptions;
        if (result.length <= 0) {
          let res: any = await getWorkstatuoptions("");
          state.CurrentStatusList = res.data;
          store.commit("setWorkstatuoptions", res.data);
        } else {
          state.CurrentStatusList = result;
        }
      },
    };
    const fun = {
      onSubmit() {
        let a = 0;
        let b = 0;
        refBasicForm.value.validate((valid: boolean) => {
          a = valid ? 1 : 0;
        });
        refCareerForm.value.validate((valid: boolean) => {
          b = valid ? 1 : 0;
        });
        // if (state.isHaveWork=='有'&&(state.basicForm.jobSeekerBrithday >= state.basicForm.jobSeekerAttendWorkYear)) {
        //   ElMessage.warning("参加工作时间不得早于出生时间");
        //   return false
        // }

        if (a == 1 && b == 1) {
          methods.saveData();
        } else {
          ElMessage.warning("请填写完整信息");
        }
      },
      //接收子级传过来的----目前所在地
      currentCity(arr: any) {
        state.dialogVisibleA = false;

        if (!arr) return false;
        state.basicForm.residencyName = arr[0].keywordName;
        state.basicForm.jobSeekerResidency = arr[0].keywordID;
      },
      //接收子级传过来的---期望工作地
      confirmCity(p: any) {
        state.dialogVisibleB = false;
        if (!p) {
          return false;
        }
        state.careerForm.residencyId1 = p[0].keywordID;
        state.careerForm.residencyId2 = p.length >= 2 ? p[1].keywordID : 0;
        state.careerForm.residencyId3 = p.length == 3 ? p[2].keywordID : 0;

        state.careerForm.residency1 = p[0].keywordName;
        state.careerForm.residency2 = p.length >= 2 ? p[1].keywordName : "";
        state.careerForm.residency3 = p.length == 3 ? p[2].keywordName : "";
        state.careerForm.residency =
          state.careerForm.residency1 + "," + state.careerForm.residency1;
        fun.changePlace();
      },
      //接收从子集传过来的数据---职位
      confirmCareer(p: any) {
        state.dialogVisibleC = false;
        if (p) {
          state.careerForm.career = p.keywordName;
          state.careerForm.careerId = p.keywordID;
        }
      },
      //接收从子集传过来的数据--行业
      confirmIndustry(p: any) {
        state.dialogVisibleD = false;
        if (!p) {
          return false;
        }
        state.careerForm.industryId1 = p[0].keywordID;
        state.careerForm.industryId2 = p.length >= 2 ? p[1].keywordID : 0;
        state.careerForm.industryId3 = p.length == 3 ? p[2].keywordID : 0;

        state.careerForm.industry1 = p[0].keywordName;
        state.careerForm.industry2 = p.length >= 2 ? p[1].keywordName : "";
        state.careerForm.industry3 = p.length == 3 ? p[2].keywordName : "";
        fun.changeIndustry();
      },
      //组装行业
      changeIndustry() {
        var arr = [];
        if (state.careerForm.industry1) arr.push(state.careerForm.industry1);
        if (state.careerForm.industry2) arr.push(state.careerForm.industry2);
        if (state.careerForm.industry3) arr.push(state.careerForm.industry3);
        state.careerForm.expectIndustryNames = arr.join(",");
      },
      //组装地址
      changePlace() {
        var arr2 = [];
        if (state.careerForm.residency1) arr2.push(state.careerForm.residency1);
        if (state.careerForm.residency2) arr2.push(state.careerForm.residency2);
        if (state.careerForm.residency3) arr2.push(state.careerForm.residency3);
        state.careerForm.residency = arr2.join(",");
      },
      //接收子级传过来的籍贯 --像子级传值
      confirmDomicileNameCity(arr: any) {
        state.dialogVisibleE = false;
        if (arr.length > 0) {
          state.basicForm.domicileName = arr[0].keywordName;
          state.basicForm.domicile = arr[0].keywordID;
        }
      },
      //选择籍贯
      SelectDomicileNameCity() {
        state.dialogVisibleE = true;
      },
    };
    return {
      ...toRefs(state),
      ...fun,
      rulesA,
      rulesB,
      refBasicForm,
      refCareerForm,
    };
  },
});
</script>
<style lang="less">
.register-step-mn {
  background: #f4f5f9;
  padding-bottom: 50px;
  .content {
    background: #fff;
    .box {
      padding: 40px;
    }
    h1 {
      font-size: 24px;
      color: #333;
      padding-bottom: 40px;
    }
  }
  .sumBtn {
    background: #5f9efc;
    border-radius: 4px;
    width: 194px;
    height: 55px;
    padding: 0 0 0 0;
    line-height: 55px;
    margin: 80px 0 50px;
    font-size: 18px;
  }
  //每一项编辑弹窗的统一样式---star
  .edit-unify {
    .el-form-item__label {
      font-size: 15px;
      color: #666;
    }
    .el-input__inner {
      border: 1px solid #f2f2f2;
      height: 45px;
      font-size: 14px;
      color: #333;
    }

    .w240 {
      .el-input__inner {
        width: 240px;
      }
    }
    .w180 {
      .el-input__inner {
        width: 180px;
      }
    }
    .w520 {
      .el-input {
        width: 520px;
      }
      .el-input__inner {
        width: 520px;
      }
    }
    .w280 {
      .el-input__inner {
        width: 280px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .btn-end {
      text-align: right;
      .sub {
        font-size: 16px;
        width: 128px;
        height: 45px;
        background: #457ccf;
      }
      .cel {
        font-size: 16px;
        border: 1px solid #f2f2f2;
        width: 126px;
      }
    }
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label-wrap
      > .el-form-item__label:before,
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label::after {
      content: "*";
      color: var(--el-color-danger);
      margin-right: 4px;
    }
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label-wrap
      > .el-form-item__label:before,
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label:before {
      content: "" !important;
      color: var(--el-color-danger);
      margin-right: 0px;
    }
  }
  .radio-group {
    .el-radio {
      width: 96px;
      height: 43px;
      background: #ffffff;
      color: #999999;
      border: 1px solid #f2f2f2;
      text-align: center;
      font-size: 16px;
      .el-radio__input {
        opacity: 0;
      }
      span.el-radio__label {
        padding-left: 25px;
      }
    }
    .is-checked {
      background: #f2f7ff;
      color: #5f9efc;
      border: 1px solid #5f9efc;
    }
  }
  .yuan {
    color: #ddd;
    left: -70px;
  }
}
</style>