<template>
  <div class="register_radius_box register_basic_info">
    <div class="title">
      <img src="../../assets/register/icon_jy.png" alt="icon" />
      <h1>教育经历</h1>
      <p>请填写国家承认学历的教育经历，非培训经历</p>
    </div>

    <div class="form">
      <el-form style="width: 100%" :model="sizeForm" label-width="110px" :label-position="'left'" :rules="rules"
        ref="ruleForm">
        <el-form-item label="最高学历" prop="degreeID">
          <div style="display: flex; align-items: center">
            <el-select v-model="sizeForm.degreeID" @change="degreeChange" placeholder="请选择">
              <el-option :label="item.keywordName" v-for="item in degreeOptions" :value="item.keywordID" />
            </el-select>
            <div style="margin: 0 15px" v-if="showOther">-</div>
            <el-select v-model="sizeForm.fullTimeFlag" v-if="showOther" placeholder="选择类型">
              <el-option label="全日制" :value="true" />
              <el-option label="非全日制" :value="false" />
            </el-select>
          </div>
        </el-form-item>

        <el-form-item  label="学校名称" prop="university">
          <el-autocomplete   maxlength="30"
          show-word-limit v-model="sizeForm.university" :fetch-suggestions="querySearchAsyncSchoolName"
            placeholder="请输入内容" @select="handleSelectSchoolName">
            <template #default="data">
              <div class="positions_name" v-for="item in data">
                <p v-safe-html.relaxed="item"></p>
              </div>
            </template>
          </el-autocomplete>
        </el-form-item>

        <el-form-item v-if="showOther" label="所学专业" prop="majorID">
          <!-- <el-autocomplete v-model="sizeForm.majorIDName" :fetch-suggestions="querySearchAsyncMajorName"
            placeholder="请输入内容" @select="handleSelectMajorName" @input="sizeForm.majorID = 0">
            <template #default="data">
              <div class="major_name" v-for="item in data">
                <p v-html="item.name"></p>
              </div>
            </template>
          </el-autocomplete> -->


          <rc-cascader
              v-model="sizeForm.majorID"
              ref="industryCascader"
              width="532"
              :title="'所学专业'"
              height="40"
              clearVisible
              :options="specialtyOptions"
              :SearchEmptyPlaceholder="'暂无精准匹配专业，请在下方的类别中选择添加'"
              :valueId="'id'"
              :asyncCommit="false"
              :multiple="false"
              :label="'name'"
              :children="'children'"
              :placeholderSelect="'请选择'"
              :timelyClose="true"
              :deepFlex="3"
              :showApplySearchValue="false"
              @change="majorChange($event)"
              @clear="clear"
            ></rc-cascader>
        </el-form-item>

        <el-form-item  label="在校时间" prop="time">
          <div style="align-items: center; display: flex">
            <el-date-picker v-model="sizeForm.beginTime" value-format="YYYY-MM" format="YYYY-MM" type="month"
              placeholder="选择开始月份" @change="beginTimeChange" :disabled-date="disabledDateBegin">
            </el-date-picker>
            <div style="margin: 0 15px">-</div>
            <el-date-picker v-model="sizeForm.endTime" value-format="YYYY-MM" format="YYYY-MM" type="month"
              :disabled-date="disabledDateEnd" placeholder="选择结束月份" :disabled="!sizeForm.beginTime">
            </el-date-picker>
          </div>
        </el-form-item>
      </el-form>

      <div class="register_button_box">
        <div class="register_button previous_step" style="margin: 0 auto; margin-top: 3px; width: 180px"
          @click="previousStep">
          上一步
        </div>

        <div style="margin: 0 auto; margin-top: 3px">
          <el-button @click="onSubmit" class="register_button" style="width: 180px" type="primary"
            :loading="loading">下一步</el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from "vue";
import { ElForm } from "element-plus";
import { OptionsClass } from "../../http/app/Options";
import { AutocompeleteClass } from "../../http/app/Autocompelete";
import { EducationInfo } from "../../http/register/data-contracts";
import { KeywordSimpleOption,KeywordDto } from "../../http/app/data-contracts";
import { 简历注册相关PcClass } from "../../http/register/简历注册相关Pc";
import {所有端公共部分Class} from "../../http/register/所有端公共部分"
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
const route = useRoute();
const router = useRouter();

const resumeID = ref(0);
const jobSeekerTalentDegree = ref(0);

resumeID.value = Number(route.query.resumeID);
jobSeekerTalentDegree.value = Number(route.query.jobSeekerTalentDegree);

const showOther = computed(() => {
  return sizeForm.value.degreeID && sizeForm.value.degreeID > 353;
});

const sizeForm = ref<EducationInfo>({});

const getSaveInfo = async () => {
  const data = await 所有端公共部分Class.apiRegisterstepRegistereduinfoGet({
    resumeid: resumeID.value,
  });

  if (data.code == 1 && data && data.data) {
    for (let key in data.data) {
      sizeForm.value[key] = data.data[key];
      if(sizeForm.value.majorID === 0 ){
        sizeForm.value.majorID = ''
      }
    }

    if(sizeForm.value.majorPaths){
      sizeForm.value.majorID = sizeForm.value.majorPaths
    }

    if (sizeForm.value.degreeID == 0) sizeForm.value.degreeID = "";
  }
};

getSaveInfo();

const ruleForm = ref<InstanceType<typeof ElForm>>();

const handleSelectSchoolName = (item: string) => {
  sizeForm.value.university = item || "";
};

const degreeOptions = ref<KeywordSimpleOption[]>();
const getDegreeOptions = async () => {
  const data = await OptionsClass.apiOptionsDegreeoptionsGet({});
  degreeOptions.value = data.data;
};
getDegreeOptions();

const specialtyOptions = ref<KeywordDto[]>([]);
  type KeywordItemDtoS = KeywordDto & { children: KeywordItemDtoS[] };
function buildTree(data:KeywordDto[]): KeywordItemDtoS[] {
  const map = new Map<number, KeywordItemDtoS>();
  const tree: KeywordItemDtoS[] = [];

  // 先将所有节点放入 map 中
  data.forEach((item) => {
    map.set(item.id || 0, { ...item, children: [] });
  });

  // 再将子节点挂载到父节点的 children 字段下
  data.forEach((item) => {
    if (item.pid === 0) {
      tree.push(map.get(item.id || 0)!);
    } else {
      const parent = map.get(item.pid || 0);
      if (parent) {
        parent.children.push(map.get(item.id || 0)!);
      }
    }
  });

  return tree;
}
const getSpecialtyOptions = async () => {
  const data = await OptionsClass.apiOptionsSpecialtyGet({});
  specialtyOptions.value = buildTree(data.data)
  
};

getSpecialtyOptions();
const majorChange = (item)=>{
  sizeForm.value.majorIDName = item.name || "";
  sizeForm.value.majorID = item.id;
  sizeForm.value.major = item.name || "";

  nextTick(() => {
    ruleForm.value?.validateField("majorID", () => {});
   
  });
}

const clear = ()=>{
  sizeForm.value.majorIDName =''
  sizeForm.value.major = ''
  nextTick(() => {
    ruleForm.value?.validateField("majorID", () => {});
   
  });
}

const degreeChange = (e) => {
  if (e >= 353) {
    sizeForm.value.fullTimeFlag = true;
  } else {
    sizeForm.value.fullTimeFlag = false;
  }
};


const querySearchAsyncSchoolName = async (queryString: string, cb: any) => {
  if (!queryString) return cb([]);
  const data = await AutocompeleteClass.apiAutocompeleteSearchcollegePost({
    keyword: queryString,
    size: 10,
  });
  if (data.code == 1) {
    cb(data.data.result);
  } else {
    cb([]);
  }
};

const disabledDateBegin = (time: Date) => {
  return (
    time.getTime() >= Date.now() ||
    new Date("1925-01-01").getTime() > time.getTime()
  );
};

function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  return `${year}-${month}`;
}
const beginTimeChange = (time) => {
  const threeYears = [351, 352, 353, 354,355, 357, 358, 359, 360];
  const fourYears = [356];
  const endTime = new Date(time);

  if (threeYears.includes(sizeForm.value.degreeID || 0)) {
    endTime.setFullYear(endTime.getFullYear() + 3);
  }

  if (fourYears.includes(sizeForm.value.degreeID || 0)) {
    endTime.setFullYear(endTime.getFullYear() + 4);
  }
  sizeForm.value.endTime = formatDate(endTime);
};

const disabledDateEnd = (time: Date) => {
  if (!sizeForm.value.beginTime) {
    return new Date("1990-01-01").getTime() > time.getTime();
  } else {
    const begin = new Date(sizeForm.value.beginTime);
    begin.setFullYear(begin.getFullYear() + 1);

    const latest = new Date(sizeForm.value.beginTime);
    latest.setFullYear(latest.getFullYear() + 11);

    return !(
      latest.getTime() > time.getTime() && begin.getTime() < time.getTime()
    );
  }
};



const previousStep = () => {
  router.go(-1);
};

const loading = ref(false);
const onSubmit = () => {
  ruleForm.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      const obj = Object.assign({}, sizeForm.value);
      if(obj.majorID){
        const idsList = obj.majorID.split(",");
     
        obj.majorID = Number(idsList[idsList.length - 1]);
      }

      const data =
        await 所有端公共部分Class.apiRegisterstepSaveregistereduinfoPost({
          ...obj,
          resumeID: resumeID.value,
        });
      if (data.code == 1) {
        // ElMessage({
        //   type: "success",
        //   message: data.message || "保存成功",
        // });
        setTimeout(() => {
          loading.value = false;
          router.push(
            `/register/registerRouteView/registerIntention?resumeID=${resumeID.value}&jobSeekerTalentDegree=${jobSeekerTalentDegree.value}`
          );
        }, 50);
      }else{
        loading.value = false;
        ElMessage({
          type: "error",
          message: data.message || "保存失败",
        });
      }
    } else {
      console.log("error submit!!");
      return false;
    }
  });
};

const validateTime = (
  rule,
  value: string,
  callback: (error?: Error) => void
) => {
  nextTick(() => {
    if (!sizeForm.value.beginTime) {
      callback(new Error("请选择在校时间"));
    }
    if (!sizeForm.value.endTime) {
      callback(new Error("请选择在校时间"));
    }
    callback();
  });
};

const validateMajorIDName = async (
  rule,
  value: string,
  callback: (error?: Error) => void
) => {
  if (!value) {
    callback(new Error("请选择专业"));
  } else{

    callback();
  }
  // else if (!sizeForm.value.majorID) {

  //   callback(new Error("暂无精准匹配专业，请重试"));
  // } else {
  //   const data = await AutocompeleteClass.apiAutocompeleteSearchmajorPost({
  //     keyword: value,
  //     hightlight: true,
  //   });
  //   if (data.code == 1) {
  //     if (data.data[0].id != sizeForm.value.majorID) {
  //       callback(new Error("暂无精准匹配专业，请重试"));
  //     } else {
  //       callback();
  //     }
  //   }
  // }
};

const rules = {
  degreeID: [{ required: true, message: "请选择最高学历", trigger: "blur" }],
  university: [{ required: true, message: "请输入学校名称", trigger: "blur" }],
  majorID: [
    { required: true, validator: validateMajorIDName, trigger: "blur" },
  ],
  time: [{ required: true, validator: validateTime, trigger: "blur" }],
};
</script>
<style lang="less" scoped>
@import "./css/style.less";

.register_basic_info {
  padding: 43px 59px 50px 59px;

  .form {
    .register_button_box {
      .previous_step {
        background: #fff;
        border: 1px solid #d0d2d9;
        color: #666666;
      }
    }
  }

  ::v-deep(.el-date-editor) {
    width: 100%;
  }

  ::v-deep(.el-autocomplete) {
    width: 100%;
  }

  ::v-deep(.el-select) {
    width: 100%;
  }
}
</style>
