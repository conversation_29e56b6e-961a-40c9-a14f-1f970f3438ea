<template>
  <div class="register-step3-mn clearfix">
    <subheading></subheading>
    <div class="w1200">
      <stepBar :step="3"></stepBar>

      <div class="cont clearfix">
        <div class="sdl">
          <div class="circle">
            <el-progress
              type="circle"
              :percentage="wangzhengdu"
              status="exception"
              :width="150"
              :stroke-width="15"
              stroke-linecap="butt"
              :color="color"
            >
              <template #default="{ percentage }">
                <p class="percentage-value">{{ percentage }}<i>%</i></p>
                <span class="percentage-label">完整度</span>
              </template>
            </el-progress>
          </div>
        </div>
        <div class="sdr">
          <h1>简历填写完成！请等待审核</h1>
          <p>您的简历将在1-2个工作日内完成审核，请耐心等待</p>
          <el-button type="primary" @click="goMy()">前往个人中心</el-button>
        </div>
      </div>
      <div class="subheading">根据您的求职意向，以下岗位可能适合您</div>
      <!-- 搜索 -->
      <div class="search">
        <div class="search-bar clearfix">
          <div class="searchBox fl clearfix">
            <div class="searchType fl">
              <el-select v-model="searchValue" value-key="schType">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  @click="schType=item.value"
                >
                </el-option>
              </el-select>
            </div>
            <div class="inp fl">
                <el-input
                v-model="keyName"
                placeholder="请输入职位关键字"
              ></el-input>
            </div>
            <!-- 城市 -->
            <div class="pop fl">
              <el-input
                v-model="cityName"
                placeholder="选择城市"
                suffix-icon="el-icon-caret-bottom"
                @focus="dialogVisibleA = true"
                :readonly="true"
              ></el-input>
              <seleICity
                @confirmCity="confirmCity"
                v-if="dialogVisibleA"
                title="城市"
                :maxCount="1"
                :hideValue="[cityId]"
              ></seleICity>
            </div>
            <!-- 行业 -->
            <div class="pop fl">
              <el-input
                v-model="IndustryName"
                placeholder="选择行业"
                suffix-icon="el-icon-arrow-down"
                readonly
                @focus="dialogVisibleB = true"
              ></el-input>
              <seleIndustryThreeLevel
                :hideValue="Industryid"
                @confirm="confirmIndustry"
                :maxCount="1"
                v-if="dialogVisibleB"
              ></seleIndustryThreeLevel>
            </div>
            <!-- 职位 -->
            <div class="pop fl">
              <el-input
                v-model="CareerName"
                placeholder="选择职位"
                suffix-icon="el-icon-arrow-down"
                readonly
                @focus="dialogVisibleC=true"
              ></el-input>
              <seleCareer
                @confirm="confirmCareer"
                v-if="dialogVisibleC"
                :hideValue="CareerId"
              ></seleCareer>
            </div>
          </div>
          <div class="btn fl" @click="search()">
            <el-button type="primary">搜索</el-button>
          </div>

          <div class="line fl">
            <a href="//s.gxrc.com" target="_blank">高级搜索</a>
          </div>
        </div>
      </div>
      <div class="list">
        <el-row :gutter="12">
          <el-col :span="8" v-for="(item, index) in positionList" :key="index">
            <el-card class="box-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span
                    ><a
                      :href="`//www.gxrc.com/jobDetail/${item.positionGuid}`"
                      target="_blank"
                      :title="item.positionName"
                      class="tit"
                      >{{ item.positionName }}</a
                    ></span
                  >
                  <el-button class="button" type="text">{{
                    item.payPackage
                  }}</el-button>
                </div>
              </template>
              <div class="text item">
                <a
                  :href="`//www.gxrc.com/company/${item.enterpriseGuid}`"
                  target="_blank"
                  :title="item.enterpriseName"
                  >{{ item.enterpriseName }}</a
                >
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, onBeforeMount, ref, computed } from "vue";
import subheading from "@/views/register/head.vue";
import stepBar from "@/views/register/stepBar.vue";
import { getCompletestate } from "../../http/resumeApi";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { getPositionRecommendByCaree } from "@/http/mApiUrl";
import seleICity from "@/components/seleCity.vue";
import seleCareer from "@/components/seleCareer.vue";
import seleIndustryThreeLevel from "@/components/seleIndustryThreeLevel.vue";
import { getCookies } from "@/utils/common";
export default defineComponent({
  components: { subheading, stepBar, seleICity,seleCareer ,seleIndustryThreeLevel},
  setup() {
    const router = useRouter();
    const route = useRoute();
    const state = reactive({
      wangzhengdu: 0,
      positionList: [],
      dialogVisibleA: false,
      dialogVisibleB: false,
      dialogVisibleC: false,
      cityId: 0,
      cityName: "",
      IndustryName: "",
      Industryid: 0,
      CareerName: "",
      CareerId: 0,
      keyName:'',
      options: ref([
        {
          value: 1,
          label: "搜职位",
        },
        {
          value: 2,
          label: "搜公司",
        },
      ]),
      schType:1,
      searchValue: ref("搜职位"),
      color:"#3486FF"
    });
    onBeforeMount(() => {
      methods.getCompletestate(route.params.resumeId);
      methods.getPositionData();
    });
    const districtID = computed(() => {
      return getCookies("bid");
    });
    const methods = {
      async getCompletestate(id: number | any) {
        let data = {
          resumeid: id,
        };
        let res: any = await getCompletestate(id, null);
        if (res.code == 1) {
          state.wangzhengdu = res.data.totalScore;
          state.color=res.data.totalScore>=50?'#3486FF':'#FFB45D'
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //   获取推荐职位
      async getPositionData() {
        let parameter = {
          districtId: districtID.value,
          positionCareeID: 0,
          page: 1,
          pagesize: 15,
        };
        const res = await getPositionRecommendByCaree(parameter);
        if (res.code == 1) {
          state.positionList = res.data;
        }
      },
    };
    const fun = {
      //接收子级传过来的值--城市
      confirmCity(arr: any) {
        state.dialogVisibleA = false;
        if (!arr) return false;
        state.cityName = arr[0].keywordName;
        state.cityId = arr[0].keywordID;
      },
      //接收从子集传过来的数据
      confirmIndustry(p: any) {
        state.dialogVisibleB = false;
        if (!p) {
          return false;
        }
        state.Industryid = p[0].keywordID;
        state.IndustryName = p[0].keywordName;
      },
      //接收从子集传过来的数据---职位
      confirmCareer(p: any) {
        state.dialogVisibleC = false;
        if (p) {
          state.CareerName = p.keywordName;
          state.CareerId = p.keywordID;
        }
      },
      goMy(){
        router.push({ path: '/' });
      },
      // 搜索
      search(){
          let  url=`//s.gxrc.com/sJob?schType=${state.schType}&district=${state.cityId}&posType=${state.CareerId}&page=1&pageSize=20&keyword=${state.keyName}&industry=${state.Industryid}`;
          window.location.href=url;
      }
    };

    return { ...toRefs(state), ...fun,districtID };
  },
});
</script>
<style lang="less">
.register-step3-mn {
  .cont {
    padding: 48px 56px;
    background: #fff;
    .sdl {
      width: 150px;
      height: 150px;
      margin-right: -150px;
      float: left;
    }
    .sdr {
      margin-left: 200px;
      h1 {
        font-size: 24px;
        color: #333333;
      }
      p {
        font-size: 14px;
        color: #666666;
        padding: 14px 0 40px 0;
      }
    }
  }
  .subheading {
    text-align: center;
    font-size: 18px;
    padding: 24px 0;
    color: #333;
    font-weight: 600;
  }
  .list {
    padding: 0 0 100px 0;
    .el-card {
      border: none;
      margin-bottom: 12px;
    }
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .tit {
      color: #333;
      font-size: 17px;
      font-weight: 600;
    }
    .el-card__header {
      border-bottom: none;
      padding: 10px 24px 0 24px;
    }

    .el-button--text {
      span {
        color: #fc5c5b;
        font-size: 17px;
        font-weight: bold;
      }
      float: right;
    }
    .el-card__body {
      padding: 5px 24px 12px 24px;

      .text {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      a {
        font-size: 13px;
        color: #666666;
      }
    }
  }
  .search{
    padding: 20px 24px;
    background: #fff;
    margin-bottom: 12px;

    .searchBox{
        border: 1px solid #f2f2f2;
        border-right: none;
        height: 44px;
        border-radius: 4px 0 0 4px;
    }
    .searchType{
        width: 100px;
        line-height: 44px;
    }
    .el-input__inner{
            border: none;
        }
    .pop{
        width: 110px;
        cursor: pointer;
        
    }
    .inp{
        .el-input__inner{
            width: 500px;
        }
    }
    .btn{
       .el-button{
           width: 120px;
           height: 46px;
           padding: 0 0 0 0;
           line-height: 46px;
           text-align: center;
           background: #5F9EFC;
           border-radius: 0 4px 4px 0;
       } 
    }
    .line a{
        float: left;
        font-size: 14px;
        color: #5F9EFC;
        padding-left: 20px;
        line-height: 46px;
    }
  }
  .el-progress.is-exception .el-progress__text {
    .percentage-value{
      font-size: 32px;
      color: #333;
      font-weight: bolder;
      i{
        font-size: 16px;
      }
    }
    .percentage-label{
      font-size: 16px;
      color: #666;
    }
  }
}
</style>