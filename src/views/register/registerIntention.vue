<template>
  <div class="register_radius_box register_basic_info">
    <div class="title">
      <img src="../../assets/register/icon_jl.png" alt="icon" />
      <h1>求职意向</h1>
      <p>我们会根据你的意愿条件，推荐适合职位</p>
    </div>

    <div class="form">
      <el-form
        style="width: 100%"
        :model="sizeForm"
        label-width="110px"
        :label-position="'left'"
        :rules="rules"
        ref="ruleForm"
      >
        <el-form-item label="期望职位" prop="positionName">
          <div class="position_box">
            <rc-cascader
              v-model="sizeForm.positionName"
              ref="industryCascader"
              width="532"
              :title="'期望职位'"
              height="40"
              clearVisible
              :options="positionseList"
              :valueId="'keywordID'"
              :asyncCommit="true"
              :multiple="true"
              :showMultipleTag="true"
              :label="'keywordName'"
              :children="'children'"
              :placeholderSelect="'请选择'"
              :timelyClose="true"
              :deepFlex="3"
              @changeValue="positionChange($event)"
            ></rc-cascader>
            <div class="recommend" v-if="recommendOptions.length > 0">
              猜你想选：
              <el-tag
                v-for="tag in recommendOptions"
                size="small"
                style="margin-left: 8px; cursor: pointer"
                :key="tag.typeid"
                :type="haveSelection(tag) > -1 ? '' : 'info'"
                @click="quickSelection(tag)"
              >
                {{ tag.typename }}
              </el-tag>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="期望城市" prop="city">
          <rc-cascader
            v-model="sizeForm.city"
            width="532"
            :title="'期望工作地'"
            height="40"
            clearVisible
            :options="districtOptions"
            :valueId="'keywordID'"
            :multiple="true"
            :asyncCommit="true"
            :showMultipleTag="true"
            :label="'keywordName'"
            :children="'children'"
            :placeholderSelect="'请选择'"
            :timelyClose="true"
          ></rc-cascader>
        </el-form-item>

        <el-form-item label="薪资要求" prop="salary">
          <el-select v-model="sizeForm.salary" placeholder="请选择">
            <el-option
              :label="item.keywordName"
              v-for="item in SalaryOptions"
              :value="item.keywordID"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          label="个人优势"
          prop="desContent"
          v-if="!currentPositionsIsblueCollar"
        >
          <el-input
            v-model="sizeForm.desContent"
            type="textarea"
            :rows="4"
            maxlength="500"
            show-word-limit
            placeholder="请用1-2句话向HR介绍自己"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="register_button_box">
        <div
          class="register_button previous_step"
          style="margin: 0 auto; margin-top: 3px; width: 180px"
          @click="previousStep"
        >
          上一步
        </div>
        <div style="margin: 0 auto; margin-top: 3px; width: 180px">
          <el-button
            @click="onSubmit"
            class="register_button"
            style="width: 180px"
            type="primary"
            :loading="loading"
            >下一步</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from "vue";
import { ElForm } from "element-plus";
import { OptionsClass } from "../../http/app/Options";
import {
  KeywordItemDto,
  KeywordSimpleOption,
} from "../../http/app/data-contracts";
import rCascader from "gxrcw-ui";
import { 所有端公共部分Class } from "../../http/register/所有端公共部分";
import {
  RegisterCareerStepInputDto,
  Result101,
} from "../../http/register/data-contracts";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRoute, useRouter } from "vue-router";

const route = useRoute();
const router = useRouter();

const resumeID = ref(0);
const jobSeekerTalentDegree = ref(0);

resumeID.value = Number(route.query.resumeID);
jobSeekerTalentDegree.value = Number(route.query.jobSeekerTalentDegree);
const currentPositionsIsblueCollar = ref(false);
const getSaveInfo = async () => {
  const data = await 所有端公共部分Class.apiRegisterstepRegistercareerinfoGet({
    resumeid: resumeID.value,
  });
  if (data.code == 1 && data && data.data) {
    sizeForm.value = data.data;
    sizeForm.value.positionName = [];
    sizeForm.value.city = [];
    if (data.data.expectCareer1Paths)
      sizeForm.value.positionName.push(data.data.expectCareer1Paths.split(","));
    if (data.data.expectCareer2Paths)
      sizeForm.value.positionName.push(data.data.expectCareer2Paths.split(","));
    if (data.data.expectCareer3Paths)
      sizeForm.value.positionName.push(data.data.expectCareer3Paths.split(","));

    if (data.data.residencyId1Paths)
      sizeForm.value.city.push(data.data.residencyId1Paths.split(","));

    if (data.data.residencyId2Paths)
      sizeForm.value.city.push(data.data.residencyId2Paths.split(","));

    if (data.data.residencyId3Paths)
      sizeForm.value.city.push(data.data.residencyId3Paths.split(","));

      sizeForm.value.city.forEach((item)=>{
        if(item.length == 1){
          item.push(item[0])
        }
        if(item.length == 2){
          item.push(item[1])
        }
      })

    if (data.data.salary == 0) {
      data.data.salary = undefined;
    }
  }

  const data2 = await 所有端公共部分Class.apiRegisterstepDescriptionGet({
    resumeid: resumeID.value,
  });
  if (data2.code == 1 && data2 && data2.data) {
    sizeForm.value.desContent = data2.data.desContent;
    currentPositionsIsblueCollar.value = Boolean(data2.data.isBlue);
  }
};
getSaveInfo();

const sizeForm = ref<
  RegisterCareerStepInputDto & {
    city?: string[][];
    positionName?: string[][];
    desContent?: string;
  }
>({
  positionName: [],
  desContent: "",
  city: [],
});

const ruleForm = ref<InstanceType<typeof ElForm>>();

const positionseList = ref<KeywordItemDtoS[]>([]);

const positionChange = (event) => {
  nextTick(() => {
    ruleForm.value?.validateField("positionName", () => {});
  });
};

const previousStep = () => {
  router.go(-1);
};

const getOptionsPositions = async () => {
  const data = await OptionsClass.apiOptionsPositionGet({
    parentid: 0,
    withcache: false,
  });
  if (data.code == 1) {
    positionseList.value = buildTree(data.data);
  }
};

const districtOptions = ref<KeywordItemDtoS[]>([]);
const getDistrictOptions = async () => {
  const data = await OptionsClass.apiOptionsDistrictGet({
    parentid: 0,
    withcache: true,
  });
  districtOptions.value = buildTree(data.data);
  console.log(setSelftoChildren(districtOptions.value));
  
};
getDistrictOptions();

const SalaryOptions = ref<KeywordSimpleOption[]>([]);
const getSalaryOptions = async () => {
  const data = await OptionsClass.apiOptionsSalaryonlyvalueoptionsGet();

  SalaryOptions.value = data.data;
};
getSalaryOptions();

const haveSelection = (tag: Result101) => {
  const pathsString = tag.typeid?.toString() || "";
  if (sizeForm.value.positionName) {
    const index = sizeForm.value.positionName.findIndex((item) =>
      item.includes(pathsString)
    );
    return index;
  } else {
    return -1;
  }
};

const quickSelection = (tag: Result101) => {
  if (!sizeForm.value.positionName) return;

  const index = haveSelection(tag);
  if (index > -1) {
    sizeForm.value.positionName?.splice(index, 1);
    return;
  }

  if (sizeForm.value.positionName.length >= 3) {
    ElMessage.warning("最多选择3个期望职位");
    return;
  }

  const paths = tag.paths?.split(",");
  if (paths && paths?.length > 0) {
    sizeForm.value.positionName.push(paths);
  }
};

const recommendOptions = ref<Result101[]>([]);
const getRecommendOptions = async () => {
  const data =
    await 所有端公共部分Class.apiRegisterstepGerecommendcareerlistGet({
      resumeid: resumeID.value,
      count: 5,
    });

  recommendOptions.value = data.data || [];
};
getRecommendOptions();

type KeywordItemDtoS = KeywordItemDto & { children: KeywordItemDtoS[] };
function buildTree(data: KeywordItemDto[]): KeywordItemDtoS[] {
  const map = new Map<number, KeywordItemDtoS>();
  const tree: KeywordItemDtoS[] = [];

  // 先将所有节点放入 map 中
  data.forEach((item) => {
    map.set(item.keywordID || 0, { ...item, children: [] });
  });

  // 再将子节点挂载到父节点的 children 字段下
  data.forEach((item) => {
    if (item.parentID === 0 || item.parentID === -1) {
      tree.push(map.get(item.keywordID || 0)!);
    } else {
      const parent = map.get(item.parentID || 0);
      if (parent) {
        parent.children.push(map.get(item.keywordID || 0)!);
      }
    }
  });

  return tree;
}

function setSelftoChildren(data: KeywordItemDtoS[]): KeywordItemDtoS[] {
  data.forEach((item) => {
    if (item.children && item.children.length > 0) {
      item.children.unshift({
        keywordID: item.keywordID,
        keywordName: '全'+ item.keywordName,
        children: []
      });
      item.children = setSelftoChildren(item.children);
    } else {
      return data;
    }
  });
  return data;
}

getOptionsPositions();

const loading = ref(false);
const onSubmit = () => {
  ruleForm.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      const obj = Object.assign({}, sizeForm.value);

      obj.positionName.forEach((item, index) => {
        const key = "expectCareer";
        obj[key + (index + 1).toString()] = item[item.length - 1];
        obj[key + (index + 1).toString() + "Paths"] = item.join(",");
      });

      obj.city.forEach((item, index) => {
        const key = "residencyId";
        obj[key + (index + 1).toString()] = item[item.length - 1];
      });

      delete obj.city;
      delete obj.positionName;

      const data =
        await 所有端公共部分Class.apiRegisterstepSaveregistercareerinfoPost({
          ...obj,
          resumeId: resumeID.value,
        });
      let data2: any = null;
      if (!currentPositionsIsblueCollar.value) {
        data2 = await 所有端公共部分Class.apiRegisterstepSavedescriptionPost({
          desContent: obj.desContent || "",
          resumeId: resumeID.value,
        });
      }

      if (data.code == 1 && (data2 === null || data2.code == 1)) {
        // ElMessage({
        //   type: "success",
        //   message: data.message || "保存成功",
        // });
        setTimeout(() => {
          loading.value = false;
          router.push(
            `/register/registerRouteView/registerComplete?resumeID=${resumeID.value}&jobSeekerTalentDegree=${jobSeekerTalentDegree.value}&expectCareer=${obj.expectCareer1}`
          );
        }, 50);
      } else {
        loading.value = false;
        ElMessage({
          type: "error",
          message: data.message || "保存失败",
        });
      }
    } else {
      console.log("error submit!!");
      return false;
    }
  });
};

const rules = {
  positionName: [
    { required: true, message: "请选择期望职位", trigger: "change" },
  ],
  city: [{ required: true, message: "请选择期望城市", trigger: "change" }],
  salary: [{ required: true, message: "请选择薪资要求", trigger: "change" }],
  desContent: [{ required: true, message: "请填写个人优势", trigger: "blur" }],
};
</script>
<style lang="less" scoped>
@import "./css/style.less";
.register_basic_info {
  padding: 43px 59px 50px 59px;
  .form {
    .register_button_box {
      .previous_step {
        background: #fff;
        border: 1px solid #d0d2d9;
        color: #666666;
      }
    }
    .recommend {
      font-family:
        Source Han Sans CN,
        Source Han Sans CN;
      font-weight: 400;
      font-size: 13px;
      color: #9a9ea5;
      text-align: left;
    }

    .is-error {
      ::v-deep(.gxrc-cascader) {
        .gxrc-input__inner {
          border-color: var(--el-color-danger);
        }
      }
    }
  }

  ::v-deep(.el-date-editor) {
    width: 100%;
  }
  ::v-deep(.el-select) {
    width: 100%;
  }
}
</style>
