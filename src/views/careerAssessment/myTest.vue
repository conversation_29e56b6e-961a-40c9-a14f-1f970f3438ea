<template>
 <div class="interest-page">
<TopMenu :index=state.activeIndex :isAssessment=true />
 <div class="bg-white">
   <div class="con">
  <table>
      <thead>
          <tr>
<th class="w1">测评名称</th>
<th class="w2">测评序列号</th>
<th class="w3">状态</th>
<th class="w4">操作</th>
</tr>
      </thead>
      <tbody>
          <tr v-for="(item,index) in state.list" :key="index">
          <td class="w1">{{item.typeName}}</td>
          <td class="w2">{{item.serialNumber}}</td>
          <td class="w3" v-if="item.isCompleted">已完成</td>
          <td class="w3 wwc" v-else>未完成</td>
          <td class="w4" v-html="item.option"></td>
          </tr>
          <tr>
            <td colspan="4">{{state.message}}</td>
          </tr>
      </tbody>
  </table>
  </div>
    </div>
   </div>
</template>

<script lang="ts">
import { defineComponent,reactive,onMounted} from 'vue'
import TopMenu from '@/components/TopMenu.vue';
  import { testMytest } from "@/http/api";
  export default defineComponent({
       components: {TopMenu},
    setup() {
        const state = reactive({
    activeIndex: 1,
    list: [],
    message:''
        })
        onMounted(() => {
      methods.getTestMytest()
    })
const methods = {
     async getTestMytest(){
          const res=await testMytest()
          state.list=res.data.items

        state.list.forEach(element => {
            if(element.isCompleted){
              if(element.isAllowReport){
                element.option=`<a href="http://rccp.gxrc.com/Report/${element.reportGuid}">查看报告</a>`
              }
            }else{
                element.option=`<a href="http://rccp.gxrc.com/${element.serialNumber}">继续测评</a>`
            }
        });

if(state.list==''){
            state.message='没有数据'
          }else{
            if(state.message==null){
              state.message=''
            }else{
              state.message=res.message
            }
          }
        },
   }
   return {
       state
      }
    }
  })
</script>

<style lang="less">
.interest-page{
.bg-white{
    .con{
   table{width: 100%;border-spacing:0;
   thead{
       th{
color: #999999;
font-weight: normal;
font-size: 12px;
height: 48px;
line-height: 48px;
background: #FAFAFA;}
   }
   tbody{
       .w1{width: 180px;}
       .w2{width: 300px;}
       .w3{width: 150px;}
       .w4{width: 180px;}
       .wwc{color: #FDAA08;}
       td{line-height:54px;text-align: center;border-bottom: 1px solid #F2F2F2;
font-size: 14px;
a{color:#457CCF;}}
tr:last-child{
    td{border-bottom: 0;}
}
   }}
}
    }
}
</style>


