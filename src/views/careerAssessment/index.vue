<template>
  <div class="career-assessment-page">
    <TopMenu :index=state.activeIndex :isAssessment=true />
    <div class="bg-white">
      <div class="tit">
        <h2>您好！欢迎使用职业测评服务</h2>
      </div>
      <div class="con">
        <ul>
          <li>
            <div class="title">
              <el-image src="https://image.gxrc.com/gxrcsite/my/2021/zycp-zyxq.png" fit="fill"></el-image>
              <span>测评服务热线：0771-5582532</span>
            </div>
            <div class="info">
              <div class="summary">
                在国际上享誉盛名的职业兴趣测评，从你的兴趣出发，帮你找出适合你的工作类型，走出职业生涯规划的第一步。 欢迎免费使用！
              </div>
              <div class="btn-wrap clearfix">
                <router-link to="/careerAssessment/interest">
                  <el-button type="primary" round>免费试用</el-button>
                </router-link>
                <a href="javascript:;" @click="methods.chakanBaogao()">
                  <el-button round>查看报告</el-button>
                </a>
              </div>
            </div>
          </li>
          <!-- <li>
            <div class="title">
              <el-image src="https://image.gxrc.com/gxrcsite/my/2021/zycp-xsgw.png" fit="fill"></el-image>
              <span>测评服务热线：0771-5505030</span>
            </div>
            <div class="info">
              <div class="summary">
                以海氏胜任力素质模型为基础，广西被试为常模，打造第一款本土化测验；挖掘你身上的销售潜力 ，助力正确择业。欢迎免费使用。
              </div>
              <div class="btn-wrap clearfix">
                <a href="https://share.gxrc.com/Cp/NewCpAs" target="_blank">
                  <el-button type="primary" round>免费试用</el-button>
                </a>
              </div>
            </div>
          </li> -->
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted } from 'vue'
import TopMenu from '@/components/TopMenu.vue';
import { ElMessage } from 'element-plus';
import { testPositiontestnumber, testPositiontestreportstate, testPositiontestreport } from "@/http/api";
export default defineComponent({
  components: { TopMenu },
  setup() {
    const state = reactive({
      activeIndex: 0,
      chakan: false
    })
    onMounted(() => {
      methods.getTestPositiontestreportstate()
    })
    const methods = {
      chakanBaogao() {
        if (state.chakan) {
          methods.getTestPositiontestnumber()
        } else {
          ElMessage.error('未完成');
        }
      },
      async getTestPositiontestreportstate() {
        const res = await testPositiontestreportstate()
        state.chakan = res.data.state
      },
      async getTestPositiontestnumber() {
        const res = await testPositiontestnumber()
        methods.getTestPositiontestreport(res.data.number)
      },
      async getTestPositiontestreport(number: number) {
        const res = await testPositiontestreport(number)
        if (res.data.url) {
          window.open(res.data.url)
        }
      },
    }

    return {
      state,
      methods
    }
  }
})
</script>

<style lang="less">
.career-assessment-page {
  .bg-white {
    padding: 20px;

    .tit {
      padding-bottom: 30px;

      h2 {
        padding-bottom: 10px;
        font-size: 20px;
      }
    }

    .con {
      ul {
        li {
          margin-bottom: 30px;

          .title {
            height: 56px;
            line-height: 56px;
            background: #FAFAFA;
            font-size: 14px;
            border-radius: 6px;

            span {
              float: right;
              padding-right: 20px;
              color: #999999;
            }
          }

          .info {
            border-radius: 6px;
            border: 1px solid #F2F2F2;

            .summary {
              font-size: 14px;
              padding: 20px;
              line-height: 24px;
            }

            .btn-wrap {
              padding: 20px;

              a {
                float: right;
                margin-left: 20px;

                button {
                  width: 180px;
                }
              }
            }
          }
        }
      }
    }
  }
}</style>


