<template>
 <div class="interest-page">
<TopMenu :index=state.activeIndex :isAssessment=true />
 <div class="bg-white">
        <div class="tit">
        <h2>职业兴趣测评试用说明</h2>
  </div>
   <div class="con">
 <div class="shuoming">
<p>1.打开浏览器输入<a href="http://ceping.gxrc.com/" target="_blank">http://ceping.gxrc.com/</a>登录测评系统。</p>
<p>2.注册与登录：首次测试者，需先在测评系统注册个人基本信息；已注册者可直接凭已注册的电子邮箱及登录密码登录测评系统。</p>
<p>3.输入序列号：输入获取的序列号进行测试。</p>
<p>4.开始测试：明确指导语后请根据自己的实际情况答题。</p>
<p>5.完成：完成测验后即可在线查看或下载测评报告。</p>             
  </div>
   <div class="xulie">
<p>您的序列号是</p>
<span class="xlh">{{state.number}}</span>
  </div>
  </div>
    </div>
   </div>
</template>

<script lang="ts">
import { defineComponent,reactive,onMounted} from 'vue'
  import { testPositiontestnumber } from "@/http/api";
import TopMenu from '@/components/TopMenu.vue';
  export default defineComponent({
       components: {TopMenu},
    setup() {
        const state = reactive({
    activeIndex: 0,
    number:0
        })
    onMounted(() => {
      methods.getTestPositiontestnumber()
    })
const methods = {
     async getTestPositiontestnumber(){
          const res=await testPositiontestnumber()
          state.number=res.data.number
        },
   }
   return {
       state,
       methods
      }
    }
  })
</script>

<style lang="less">
.interest-page{
.bg-white{padding:20px 30px 50px;
    .tit{padding-bottom:30px;
        h2{padding-bottom: 10px;font-size: 20px;}
}
    .con{
      p{line-height: 28px;color: #666666;}
.shuoming{padding-bottom:40px;
a{display: inline-block; color: #457CCF;}
}
.xulie{
  p{padding-bottom: 20px;}
  .xlh{font-weight: bold;color: #333333;font-size: 22px;letter-spacing: 2px;background: #FAFAFA;padding: 10px 15px;border-radius: 2px;}}
}
    }
}
</style>


