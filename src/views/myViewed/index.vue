<template>
  <div class="my-viewed-page">
    <top-title>
      <div class="top-l"><strong>浏览记录</strong>(近一年)</div>
    </top-title>

    <div class="list-con">
      <div v-loading="state.loading" class="list-wrap clearfix">
        <el-space wrap>
          <Position v-for="(item, index) in state.list" :key="index" :item="item" :isMyViewed="true" />
        </el-space>
      </div>

      <div class="message" v-show="state.list == ''">{{ state.message }}</div>
    </div>
    <Pagination :pageSize="state.parameter.pagesize" :totalCount="state.totalCount"
      @handlePageChange="methods.handlePageChange" />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted } from 'vue'
import Position from '@/components/Position.vue';
import TopTitle from '@/components/TopTitle.vue';
import Pagination from '@/components/Pagination.vue';
import { myViewHistory } from "@/http/api";
export default defineComponent({
  components: { Position, TopTitle, Pagination },
  setup() {
    const state: object = reactive({
      parameter: {
        "page": 1,
        "pagesize": 15
      },
      totalCount: 0,
      message: '',
      list: [],
      loading: false,
    })

    onMounted(() => {
      methods.getData().catch(error => {
        console.error('Error fetching data:', error);
      });
    })

    const methods = {
      async getData() {
        state.loading = true
        // const res=await myViewHistory(state.parameter)
        // state.list=res.data.items
        const res = await myViewHistory(state.parameter);
        if (Array.isArray(res.data.items)) {
          state.list = res.data.items;
        } else {
          console.error('Invalid data received from server');
        }

        state.loading = false
        state.list.forEach(element => {
          element.viewTime = element.viewTime.substr(0, 10)
        });

        if (state.list == '') {
          state.message = '没有数据'
        } else {
          if (state.message == null) {
            state.message = ''
          } else {
            state.message = res.message
          }
        }
        state.totalCount = res.data.totalCount
      },
      handlePageChange(val: number) {
        state.parameter.page = val
        methods.getData()
      },
    }

    return {
      state,
      methods
    }
  }
})
</script>

<style lang="less">
.my-viewed-page {
  .list-wrap {
    width: 1020px;
  }
}
</style>
