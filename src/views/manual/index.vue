<template>
  <div class="manual-page">

    <TopMenu :index=state.activeIndex :list="state.menu" />

    <div v-loading="state.loading" class="list-wrap">
      <el-row class="title-wrap">
        <el-col :span="6">
          <div class="grid-content">购买项目</div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content">商品总额（元）</div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content">订单状态</div>
        </el-col>
        <el-col :span="6">
          <div class="grid-content">操作</div>
        </el-col>
      </el-row>
      <div class="list-body">
        <div class="one" v-for="(item, index) in state.list" :key="index">
          <div class="top"><span class="date">{{ item.orderTime }}</span><span class="bianhao">订单编号：{{ item.orderID }}</span>
          </div>
          <el-row class="body-wrap">
            <el-col :span="6">
              <div class="grid-content item">
                <el-image :src="item.manualPhotoUrl" fit="fill"></el-image>
                {{ item.manualName }}
              </div>
            </el-col>
            <el-col :span="6">
              <div class="grid-content price" v-html="item.contentOne"></div>
            </el-col>
            <el-col :span="6">
              <div class="grid-content state" v-if="item.paymentFlag">已支付</div>
              <div class="grid-content state no-pay" v-else>待支付</div>
            </el-col>
            <el-col :span="6">
              <div class="grid-content caozuo" v-if="item.paymentFlag"><el-button
                  @click="methods.goDown(item.orderID,item.orderDistinguish)">在线查看</el-button></div>


              <div class="grid-content caozuo" v-else><el-button type="primary"
                  @click="state.dialogVisible = true">支付购买</el-button></div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="message" v-show="state.list == ''">{{ state.message }}</div>
    </div>
    <Pagination :pageSize="state.parameter.pagesize" :totalCount="state.totalCount"
      @handlePageChange="methods.handlePageChange" />

    <el-dialog v-model="state.dialogVisible" title="提示" width="30%">
      <div class="manual-dialog-box">
        <p>个人中心服务产品，请下载广西人才网APP进行购买使用。</p>
        <p><img src="https://image.gxrc.com/gxrcsite/global/app_code.png" width="100" /></p>
        <p>扫描二维码下载APP</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="state.dialogVisible = false">知道了</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { myOrderList } from "@/http/api";
import { ElMessage } from 'element-plus';
import Pagination from '@/components/Pagination.vue';
import TopMenu from '@/components/TopMenu.vue';
export default defineComponent({
  components: { Pagination, TopMenu },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const state = reactive({
      menu: [{ "name": "全部", "path": "/manual/0", "selected": false }
        , { "name": "求职助手", "path": "/manual/1", "selected": false }
        , { "name": "成长课堂", "path": "/manual/2", "selected": false }
        , { "name": "薪酬报告", "path": "/manual/3", "selected": false }],
      activeIndex: route.params.id,
      parameter: {
        "orderdistinguish": route.params.id,
        "orderstatus": 0,
        "manualorderkeyid": 0,
        "page": 1,
        "pagesize": 10
      },
      totalCount: 0,
      message: '',
      list: [],
      dialogVisible: false,
      loading: false,
    })

    onMounted(() => {
      methods.getData()
    })

    watch(
      () => route.params.id, (newValue, oldValue) => {
        state.parameter.orderdistinguish = newValue
        state.activeIndex = newValue * 1
        methods.getData()
      }
    )

    const methods = {
      async getData() {
        state.loading = true
        const res = await myOrderList(state.parameter)
        state.list = res.data.items
        state.loading = false
        if (state.list == '') {
          state.message = '没有数据'
        } else {
          if (state.message == null) {
            state.message = ''
          } else {
            state.message = res.message
          }
        }
        state.totalCount = res.data.totalCount
      },
      handlePageChange(val: number) {
        state.parameter.page = val
        methods.getData()
      },
      goDown(id:string,orderDistinguish:number){
        let url=`/api/order/downloaddoc?orderno=${id}`;
        if(orderDistinguish==3){
          const link = document.createElement('a');
          link.target = '_blank';
          link.href = url;
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }else{
          state.dialogVisible = true
        }
       
        
      }
    }

    return {
      state,
      methods
    }
  }
})
</script>

<style lang="less">
.manual-page {
  .top-menu {
    display: flex;
    height: 52px;
    line-height: 52px;
    margin-bottom: 10px;
    background: #FFFFFF;
    color: #666;
    font-size: 14px;
    border-radius: 2px;

    li {
      flex: 1;
      line-height: 52px;
      border-right: 1px solid #F2F2F2;
      text-align: center;
      cursor: pointer;
    }

    li.current {
      color: #457CCF;
    }

    li:last-child {
      border-right: 0;
    }
  }

  .list-wrap {
    background: #fff;

    .list-body {
      padding: 15px;
    }

    .el-col {
      padding: 0 10px;
    }

    .title-wrap {
      height: 52px;
      line-height: 52px;
      margin: 0;
      background: #FAFAFA;

      .grid-content {
        text-align: center;
        font-size: 12px;
        color: #999999;
      }
    }

    .one {
      border: 1px solid #EEEEEE;
      margin-bottom: 15px;

      .top {
        height: 43px;
        line-height: 43px;
        background: #FAFAFA;
        color: #666666;
        padding: 0 15px;
        font-size: 14px;

        .date {
          padding-right: 30px;
        }
      }

      .body-wrap {
        font-size: 14px;
        padding: 10px;

        .grid-content {
          padding: 10px 10px;
          text-align: center;
          color: #666666;
        }

        .no-pay {
          color: #FD7008;
        }

        .item {
          text-align: left;

          .el-image {
            vertical-align: middle;
            margin-right: 10px;

            img {
              width: 50px;
              height: 50px;
              vertical-align: middle;
            }
          }
        }

        .price {
          color: #FC5C5B;
        }
      }
    }
  }

  .manual-dialog-box {
    p {
      text-align: center;
      padding-bottom: 10px;
    }
  }
}</style>


