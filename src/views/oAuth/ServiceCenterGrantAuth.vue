<template>
  <div class="empower">
    <Header name="用户授权" :link="true" />
    <div class="grant-container">
      <div class="grant-content">
        <div class="column">
          <div class="image-container">
            <div>
              <div style="text-align: center; padding: 0 30px">
                <div class="ruan-bg">
                  <img :src="agreementUrl" />
                </div>
                <p class="txt1 txt2">{{ signName }}</p>
              </div>
            </div>
            <div class="lr-trans">
              <div>
                <img src="//image.gxrc.com/gxrcsite/zt/新建文件夹/zh.png" />
              </div>
            </div>
            <div>
              <div style="text-align: center; padding: 0 30px">
                <div class="ruan-bg">
                  <img
                    src="//image.gxrc.com/gxrcsite/zt/新建文件夹/logo2.png"
                  />
                </div>
                <p class="txt1">广西人才网</p>
              </div>
            </div>
          </div>
          <div>
            <p class="policy">
              <el-checkbox label="勾选同意" v-model="automatism"></el-checkbox>
              <a
              :href="agreementUrl"
                target="_blank"
                style="color: #195bd4; font-size: 14px"
                >《账号绑定协议》</a
              >
            </p>
            <div class="elError" v-if="showTis">
              请您仔细阅读《用户服务协议》和《隐私权政策》后，<br />勾选同意，再进行授权
            </div>
            <p class="policy">
              温馨提示：<br />
              您正在绑定
              <a
                style="color: #195bd4; font-size: 14px"
                href="https://www.gxrc.com/"
                target="_blank"
                >广西人才网</a
              >
              平台账号，确认授权绑定即表明您已经知晓并同意
              <a
                :href="agreementUrl"
                target="_blank"
                style="color: #195bd4; font-size: 14px"
                >《账号绑定协议》</a
              >
            </p>
          </div>
        </div>
        <div
          style="
            width: 0px;
            height: 295px;
            opacity: 1;
            border: 1px solid #f2f2f2;
          "
        ></div>
        <div class="column">
          <div class="center-flex-row" v-if="islogin">
            <div style="text-align: center">
              <div>
                <svg
                  t="1656660286211"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="5165"
                  width="160"
                  height="160"
                >
                  <path
                    d="M768 853.333333c-69.393067 53.486933-149.333333 81.92-239.786667 85.333334H496.64c-90.453333-2.850133-170.376533-30.72-239.786667-83.626667v-88.746667C330.24 710.5536 415.2832 682.666667 512 682.666667c97.28 0 182.613333 28.450133 256 85.333333v85.333333z m-86.186667-426.666666c0 47.223467-16.776533 87.313067-50.346666 120.32-33.006933 33.5872-73.096533 50.346667-120.32 50.346666-47.223467 0-87.620267-16.759467-121.173334-50.346666-32.989867-33.006933-49.493333-73.096533-49.493333-120.32s16.503467-87.6032 49.493333-121.173334c33.553067-32.989867 73.949867-49.493333 121.173334-49.493333 47.223467 0 87.313067 16.503467 120.32 49.493333 33.570133 33.570133 50.346667 73.949867 50.346666 121.173334z"
                    p-id="5166"
                    fill="#1296db"
                  ></path>
                  <path
                    d="M496.64 955.733333c-94.208-2.9696-178.176-32.273067-250.146133-87.125333l-0.238934-0.187733a547.4304 547.4304 0 0 1-48.401066-43.1104C111.872 739.2768 68.266667 633.890133 68.266667 512c0-121.856 43.588267-227.549867 129.536-314.112C284.450133 111.854933 390.126933 68.266667 512 68.266667c121.9072 0 227.293867 43.605333 313.2928 129.5872C911.854933 284.416 955.733333 390.109867 955.733333 512c0 121.924267-43.895467 227.328-130.474666 313.326933a509.7472 509.7472 0 0 1-47.0016 41.642667c-71.953067 55.364267-155.835733 85.213867-249.3952 88.746667L496.64 955.733333z m-222.72-109.2608c64.699733 47.2576 139.758933 72.516267 223.266133 75.144534L528.213333 921.6c82.926933-3.157333 158.020267-28.962133 222.72-76.765867v-68.369066C682.376533 725.5552 602.026667 699.733333 512 699.733333c-89.480533 0-169.540267 25.258667-238.08 75.144534v71.594666zM512 102.4c-112.5376 0-210.1248 40.2432-290.065067 119.620267C142.6432 301.8752 102.4 399.479467 102.4 512c0 112.503467 40.226133 209.783467 119.586133 289.160533 5.888 5.870933 11.810133 11.554133 17.800534 16.9984V766.293333a17.066667 17.066667 0 0 1 6.741333-13.585066C322.6112 694.903467 411.938133 665.6 512 665.6c100.693333 0 190.344533 29.917867 266.4448 88.917333 4.181333 3.2256 6.621867 8.192 6.621867 13.482667v48.571733c5.410133-4.949333 10.8032-10.103467 16.093866-15.394133C881.1008 721.7664 921.6 624.4864 921.6 512c0-112.503467-40.516267-210.090667-120.439467-290.013867C721.800533 142.626133 624.520533 102.4 512 102.4z m-0.853333 512c-51.729067 0-96.5632-18.619733-133.256534-55.3472-36.1472-36.164267-54.4768-80.708267-54.4768-132.386133 0-51.6096 18.295467-96.4096 54.391467-133.137067C414.702933 257.2288 459.502933 238.933333 511.146667 238.933333c51.677867 0 96.221867 18.3296 132.386133 54.493867 36.727467 36.727467 55.3472 81.5616 55.3472 133.239467 0 51.746133-18.653867 96.324267-55.4496 132.488533C607.488 595.746133 562.8928 614.4 511.146667 614.4z m0-341.333333c-42.973867 0-78.6944 14.574933-109.2096 44.5952-29.7984 30.327467-44.3904 66.048-44.3904 109.0048 0 42.888533 14.557867 78.301867 44.4928 108.253866C432.5376 565.435733 468.224 580.266667 511.146667 580.266667c42.8544 0 78.2336-14.7968 108.151466-45.243734C649.949867 504.8832 664.746667 469.504 664.746667 426.666667c0-42.888533-14.830933-78.574933-45.346134-109.1072C589.448533 287.624533 554.052267 273.066667 511.146667 273.066667z"
                    p-id="5167"
                    fill="#1296db"
                  ></path>
                </svg>
              </div>
              <p>
                <span style="font-weight: bold">{{ userName }}</span>
              </p>
              <p style="font-weight: bold; margin-top: 1em">
                <el-button
                  type="primary"
                  @click="methods.authorization"
                  class="btn btn-blue"
                  :loading="loading"
                  :disabled="disabled"
                  >确认授权</el-button
                >
              </p>
            </div>
          </div>
          <div class="phone-sms-login" v-else>
            <el-form
              :model="FormPhone"
              label-width="0px"
              class="login-phone"
              ref="FormUserB"
              :rules="rulesphone"
            >
              <el-form-item prop="phone">
                <el-input
                  v-model.number="FormPhone.phone"
                  placeholder="手机号"
                  :clearable="true"
                ></el-input>
              </el-form-item>
              <el-form-item prop="code">
                <el-input
                  v-model="FormPhone.code"
                  placeholder="短信验证码"
                  :clearable="true"
                  class="code"
                >
                  <template #append>
                    <span class="gray" v-if="send"
                      >{{ countTime }}s重新获取</span
                    >
                    <span class="blue" v-else @click="sendSmsCode"
                      >发送验证码</span
                    >
                  </template>
                </el-input>
              </el-form-item>
              <el-button
                type="primary"
                @click="phoneLogin"
                class="submit-btn"
                :loading="loading"
                :disabled="disabled"
                >登录</el-button
              >
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  ref,
  toRefs,
  Ref,
  onBeforeMount,
  watch,
  onBeforeUnmount,
} from "vue";
import { computed, getCurrentInstance } from "@vue/runtime-core";
import { useRouter, useRoute } from "vue-router";
import { initGeet } from "@/mixins/geetestMixin";
import { useStore } from "vuex";
import {
  geetestSendCode,
  loginByPhone,
  Authpageinfo,
  ServiceCenter,
  myInfo,
} from "@/http/api";
import { ElMessage } from "element-plus";
import Footer from "@/components/Footer.vue";
import Header from "@/components/header.vue";
import { setCookies } from "@/utils/common";
export default defineComponent({
  components: { Footer, Header },
  setup() {
    const { proxy, ctx } = getCurrentInstance() as ComponentInternalInstance;
    const _this = ctx;
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    const FormUserB: Ref<any> = ref(null);
    const state = reactive({
      loading: false,
      disabled: false,
      msg: "", //后端返回的消息
      FormPhone: {
        device: 0,
        devToken: "",
        siteId: 0,
        phone: "",
        code: "",
      },
      send: false,
      showTis: false,
      clock: 0,
      countTime: 60,
      automatism: false,
      captcha: {} as any, //验证码实例
      userName: "",
      userlogo: "http://image.gxrc.com/app/person/mrtx/<EMAIL>",
      bussid: computed(() => {
        let url = window.location.href;
        if (url.indexOf("/gl/") != -1) {
          return 1;
        }
        if (url.indexOf("/lz/") != -1) {
          return 2;
        }
        if (url.indexOf("/wz/") != -1) {
          return 4;
        }
        if (url.indexOf("/gp/") != -1) {
          return 5;
        }
        if (url.indexOf("/bs/") != -1) {
          return 6;
        }
        if (url.indexOf("/qz/") != -1) {
          return 7;
        }
        if (url.indexOf("/hc/") != -1) {
          return 8;
        }
        if (url.indexOf("/bh/") != -1) {
          return 9;
        }
        if (url.indexOf("/fc/") != -1) {
          return 11;
        }
        if (url.indexOf("/yl/") != -1) {
          return 12;
        }
        if (url.indexOf("/cz/") != -1) {
          return 13;
        }
        if (url.indexOf("/gg/") != -1) {
          return 14;
        }
        if (url.indexOf("/lb/") != -1) {
          return 15;
        }
        if (url.indexOf("/hz/") != -1) {
          return 18;
        }
        if (url.indexOf("/pn/") != -1) {
          return 20;
        }
        if (
          url.indexOf(".gxrc.com/login") != -1 ||
          url.indexOf("tgxrc.com:8086/login") != -1
        ) {
          return 0;
        }
      }),
      check: "" as any,
      logoUrl: "//image.gxrc.com/gxrcsite/zt/新建文件夹/logo1.png",
      signName: "人才服务大厅",
      agreementUrl: "https://www.gxrc.com/About/Legal",
      islogin: false,
    });
    watch(
      () => state.automatism,
      (newValue, oldValue) => {
        state.showTis = !state.automatism;
      }
    );

    const validatephone = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输入手机号码"));
      } else {
        if (/^1[3456789]\d{9}$/.test(value)) {
        } else {
          callback(new Error("请输入正确的11位手机号码"));
        }
        callback();
      }
    };
    const validatecode = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输短信验证码"));
      } else {
        if (/^[0-9]\d{3,5}$/.test(value)) {
        } else {
          callback(new Error("请输入正确的短信验证码"));
        }
        callback();
      }
    };
    let rulesphone = {
      phone: [{ validator: validatephone, trigger: "blur" }],
      code: [{ validator: validatecode, trigger: "blur" }],
    };

    onBeforeMount(async () => {
      methods.info();
      state.captcha = await initGeet();
      let sign = route.query.sign || "0";
      let ticket = route.query.ticket || "0";
      methods.getData(sign, ticket);
      
    });
    onBeforeUnmount(() => {
      clearInterval(state.check);
    });
    const methods = {
      async initGeet() {
        state.captcha = await initGeet();
      },
      async info() {
        // const res = await myInfo();
        const res = store.state.userInfo;
        if (res) {
          state.userName = res.jobSeekerName;
          state.islogin = true;
          state.userlogo =
            res.avatar ||
            "http://image.gxrc.com/app/person/mrtx/<EMAIL>";
        } else {
          state.userName = "";
          state.islogin = false;
          state.userlogo = "http://image.gxrc.com/app/person/mrtx/<EMAIL>";
        }
      },
      //手机号登录
      async loginbyPhone() {
        state.loading = true;
        state.disabled = true;
        let form = state.FormPhone;
        form.siteId = state.bussid || 0;
        let data: any = await loginByPhone(form);
        state.loading = false;
        if (data.code == 1) {
          // methods.authorization()
          let data2 = await ServiceCenter({ sign: sign, ticket: ticket });
          ElMessage.success(data2.message);
          let domains =
            window.location.host.indexOf(".tgxrc.com") > -1
              ? ".tgxrc.com"
              : ".gxrc.com";
          setCookies("bid", state.bussid, 7 * 24 * 60 * 60, domains);

          window.clearInterval(state.clock);
          if (route.query.returnUrl) {
            window.location.href = route.query.returnUrl;
          } else {
            //新注册用户 跳转分布注册页
            if (data.data.step === "noresume") {
              router.replace({ name: "registerBasicInfo" });
            } else {
              router.push({ path: "/" });
            }
          }
        } else {
          ElMessage.error(data.message);
          state.disabled = false;
        }
      },
      //极验
      async geetestValidate() {
        state.captcha.verify();
        await state.captcha.onSuccess(async () => {
          const result = state.captcha.getValidate();
          if (result != undefined) {
            state.loading = true;
            let data = {
              geetest_challenge: result.geetest_challenge,
              geetest_validate: result.geetest_validate,
              geetest_seccode: result.geetest_seccode,
              phone: state.FormPhone.phone,
              site: 1,
            };

            let res: any = await geetestSendCode(data);
            state.loading = false;
            if (res.code == 1) {
              ElMessage.success(res.message);
              fun.countDown();
            } else if (res.code !== 1) {
              ElMessage.error(res.message);
              return;
            } else {
              ElMessage.error("验证失败，请稍后再试");

              return;
            }
          } else {
            ElMessage.error("验证失败，请稍后再试");
            return;
          }
        });
      },
      async getData(sign: string, ticket: string) {
        let res: any = await Authpageinfo({ sign: sign, ticket: ticket });
        if (res.code == 1) {
          let _url =
          res.data.logoUrl ||
            "//image.gxrc.com/gxrcsite/zt/MobileApp/images/logo-1.png";
          state.logoUrl = _url;
          state.signName = res.data.name || "人才服务大厅";
          state.agreementUrl =res.data.agreementUrl || "https://www.gxrc.com/About/Legal";
        } else {
          ElMessage.error(res.message);
        }
      },
      async authorization() {
        if (!state.automatism) {
          state.showTis = true;
          return false;
        }
        state.loading = true;
        state.disabled = true;
        let sign = route.query.sign;
        let ticket = route.query.ticket;
        let data2 = await ServiceCenter({ sign: sign, ticket: ticket });
        state.loading = false;
        state.disabled = false;
        if (data2.code == 1) {
          fun.goBack(data2.message);
        } else {
          ElMessage.error(data2.message);
          return false;
        }
      },
    };
    const fun = {
      handleClick(tab: any, event: any) {},
      //手机验证码登录
      phoneLogin() {
        if (!state.automatism) {
          state.showTis = true;
          return false;
        }
        FormUserB.value.validate((valid: boolean) => {
          if (valid) {
            methods.loginbyPhone();
          }
        });
      },
      sendSmsCode() {
        FormUserB.value.validateField("phone", (valid: boolean) => {
          if (!valid) {
            methods.geetestValidate();
          }
        });
      },
      countDown() {
        state.send = true;
        state.clock = window.setInterval(() => {
          state.countTime--;
          if (state.countTime < 0) {
            window.clearInterval(state.clock);
            state.countTime = 0;
            state.send = false;
            //重新初始化极验
            methods.initGeet();
          }
        }, 1000);
        state.countTime = 60;
      },
      goBack(msg: any) {
        ElMessage.success(msg);
        setTimeout(function () {
          if (route.query.returnUrl) {
            if (route.query.returnUrl.indexOf("login") > -1) {
              router.push({ path: "/" });
            } else {
              // router.push({ path: route.query.returnUrl });
              window.location.href = route.query.returnUrl;
            }
          } else {
            router.push({ path: "/" });
          }
        }, 1000);
      },
    };
    return {
      ...toRefs(state),
      ...fun,
      rulesphone,
      FormUserB,
      methods,
    };
  },
});
</script>
<style lang="less" scoped>
.empower {
  background: #fff;
  .submit-btn {
    width: 100%;
    background: rgb(25, 91, 212);
    margin-top: 30px;
  }
}

.container {
  width: 100%;
  /*min-height: 500px;*/
}

.grant-container {
  align-items: center;
  display: flex;
  justify-content: center;
  border-top: 1px solid #eee;
  width: 100%;
  background: #fff;
}

.grant-container .grant-content {
  align-items: center;
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  /*width: 1000px;*/
}

.grant-container .grant-content .column {
  padding: 50px;
  justify-content: center;
  align-items: center;
}

.image-container {
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
}

.vertical-divider {
  background: #cccccc;
  height: 25em;
  width: 1px;
}

.center-flex-row {
  align-items: center;
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
}

.horizontal-divider {
  background: #cccccc;
  height: 2px;
  margin: 2em 10%;
}

.lr-trans {
  display: block;
  margin-top: 28px;
}

.tb-trans {
  display: none;
}

.send-sms {
  background: #ffffff;
  border: 1px solid #374dc3;
  color: #374dc3;
  cursor: pointer;
  height: 42px;
  line-height: 42px;
  right: 0px;
  top: 0px;
  width: 120px;
  border-radius: 4px;
}

.policy {
  margin-top: 1em;
  width: 300px;
  color: #666666;
  font-size: 14px;
}

.site-nav a {
  color: #fff;
}

.zoomImage {
  -moz-background-size: cover;
  -webkit-background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  overflow: hidden;
  width: 100%;
}

.header .w1200 {
  width: 75%;
}
.txt1 {
  text-align: center;
  width: 95px;
  margin-top: 10px;
  font-size: 14px;
  color: #333333;
}
.txt2 {
  width: 100px;
}
@media screen and (max-width: 1366px) {
  .header {
    height: unset;
  }

  .header .w1200 {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .header .app-down {
    float: unset;
    padding-top: 0;
  }
}

@media screen and (max-width: 800px) {
  .header .w1200 {
    width: 100%;
    display: flex;
    flex-flow: column nowrap;
    align-items: center;
  }

  .header .title {
    float: left;
    font-size: 18px;
    color: #34495e;
    padding-left: unset;
    border-left: unset;
    margin-top: unset;
  }

  .grant-container {
    margin-top: 0;
  }

  .vertical-divider {
    display: none;
  }

  .horizontal-divider {
    margin: 0.5em;
  }

  .policy {
    margin-top: 0.5em;
  }

  #footer .site-nav .w1200 {
    width: 100%;
  }
}

@media screen and (max-width: 600px) {
  .image-container {
    align-items: center;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
  }

  .lr-trans {
    display: none;
  }

  .tb-trans {
    display: block;
  }
}

.ruan-bg {
  background: #ffffff;
  box-shadow: 0px 3px 18px 1px rgba(0, 0, 0, 0.16);
  opacity: 1;
  border: 1px solid #ebebeb;
  border-radius: 80px;
  width: 80px;
  height: 80px;
  line-height: 80px;
  img {
    padding-top: 15px;
  }
}
.btn {
  width: 160px;
  height: 48px;
  background: #195bd4;
  border: none;
  border-radius: 6px;
  text-align: center;
  font-size: 18px;
  color: #fff;
  cursor: pointer;
  box-sizing: border-box;
}
.elError {
  color: var(--el-color-danger);
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
</style>