<template>
  <div class="login-weixin">
    <el-container>
      <Header name="" :link="true" />
      <el-main>
        <div class="login-wrap w1200">
          <div class="big-title">
            <h1>
              {{ FormUser.nickName }}<span v-if="FormUser.nickName">，</span
              ><br />欢迎加入广西人才网，请绑定你的账号
            </h1>
          </div>
          <div class="login-box">
            <!-- 账号-短信登录 -->

            <div class="login-message-phone">
              <p class="tips">创建或登录已有账号，与您现在登录的微信绑定</p>
              <el-tabs
                v-model="activeName"
                @tab-click="handleClick"
                type="card"
              >
                <!-- //短信登录框 -->
                <el-tab-pane label="创建一个新账号" name="first">
                  <el-form
                    :model="FormPhone"
                    label-width="0px"
                    class="login-phone"
                    ref="FormUserB"
                    :rules="rulesphone"
                  >
                    <el-form-item prop="phone">
                      <el-input
                        v-model.number="FormPhone.phone"
                        placeholder="手机号"
                        :clearable="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item prop="code">
                      <el-input
                        v-model="FormPhone.code"
                        placeholder="短信验证码"
                        :clearable="true"
                        maxlength="6"
                        class="code"
                      >
                        <template #append>
                          <span class="gray" v-if="send"
                            >{{ countTime }}s重新获取</span
                          >
                          <span class="blue" v-else @click="sendSmsCode"
                            >发送验证码</span
                          >
                        </template>
                      </el-input>
                    </el-form-item>
                    <el-form-item>
                      <el-button
                        type="primary"
                        @click="phoneLogin"
                        class="submit-btn"
                        :loading="loading"
                        >注册并绑定微信</el-button
                      >
                    </el-form-item>
                    <el-form-item class="auto-login">
                      <el-checkbox
                        v-model="checked"
                        @click="showchecked = checked ? true : false"
                      >
                        我已阅读并同意遵守
                        <a
                          class="blue2"
                          href="//www.gxrc.com/HtmlView/Licence.html"
                          target="_blank"
                          >使用协议</a
                        ><a
                          class="blue2 ys"
                          href="https://app.gxrc.com/privacy.html"
                          target="_blank"
                          >隐私政策</a
                        >
                      </el-checkbox>
                      <div class="el-form-item__error" v-if="showchecked">
                        请在“我已阅读并同意遵守”前的复选框内划勾并继续!
                      </div>
                    </el-form-item>
                  </el-form>
                </el-tab-pane>
                <!-- end -->
                <!-- 账号登录start -->
                <el-tab-pane label="已有账号" name="second" class="first-box">
                  <el-form
                    ref="FormUserA"
                    :model="FormUser"
                    label-width="0px"
                    class="login-user"
                    :rules="rulesUser"
                  >
                    <el-form-item prop="userName">
                      <el-input
                        v-model="FormUser.userName"
                        placeholder="手机号/用户名/邮箱地址"
                        :clearable="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item prop="pwd">
                      <el-input
                        v-model="FormUser.pwd"
                        placeholder="请输入密码"
                        :clearable="true"
                        show-password
                      ></el-input>
                    </el-form-item>

                    <el-form-item v-if="showValid">
                      <el-input
                        v-model="FormUser.captchaCode"
                        placeholder="请输入右侧图片验证码"
                        :clearable="true"
                        class="code"
                      >
                        <template #append>
                          <img
                            :src="'data:image/jpeg;base64,' + security"
                            @click="methods.getcaptcha"
                          />
                        </template>
                      </el-input>
                    </el-form-item>
                    <el-form-item>
                      <el-button
                        type="primary"
                        @click="userLogin"
                        class="submit-btn"
                        >登录并绑定微信</el-button
                      >
                    </el-form-item>
                    <div class="foot-btn">
                      <el-link
                        class="mm fr"
                        href="/findpwd"
                        target="_blank"
                        :underline="false"
                        >忘记密码</el-link
                      >
                    </div>
                  </el-form>
                </el-tab-pane>
                <!-- end -->
              </el-tabs>
            </div>

            <!-- end -->
          </div>
        </div>
      </el-main>
      <el-footer>
        <Footer />
      </el-footer>
    </el-container>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  ref,
  toRefs,
  watch,
  Ref,
  onBeforeMount,
} from "vue";
import { computed, getCurrentInstance } from "@vue/runtime-core";
import { useRouter, useRoute } from "vue-router";
import { initGeet } from "@/mixins/geetestMixin";
import {
  accountLogin,
  getSecurityImageAsync,
  geetestSendCode,
  loginByPhone,
  getwxinfo,
  
} from "@/http/api";
import { ElMessage } from "element-plus";
import Footer from "@/components/Footer.vue";
import Header from "@/components/header.vue";
export default defineComponent({
  components: { Footer, Header },
  setup() {
    const { proxy, ctx } = getCurrentInstance() as ComponentInternalInstance;
    const _this = ctx;
    const router = useRouter();
    const route = useRoute();
    const FormUserA: Ref<any> = ref(null);
    const FormUserB: Ref<any> = ref(null);
    const state = reactive({
      loading: false,
      userName: ref(""),
      password: ref(""),
      FormUser: {
        device: 0,
        devToken: "",
        siteId: 0,
        userName: "",
        pwd: "",
        tokenId: "",
        captchaCode: "",
        unionId: "",
        type: 1,
        photo: "",
        nickName: "",
      },
      codeIMG: "https://image.gxrc.com/gxrcsite/global/app_code.png",
      activeName: "first", //tab切换
      msg: "", //后端返回的消息
      FormPhone: {
        device: 0,
        devToken: "",
        siteId: 0,
        phone: "",
        code: "",
        unionId: "",
        type: 1,
        photo: "",
        nickName: "",
      },
      send: false,
      clock: 0,
      countTime: 60,
      efficacy: false, //二维码失效
      automatism: false,
      security: "",
      showValid: false, //显示图形验证码
      captcha: {} as any, //验证码实例
      bussid: 0,
      checked: false,
      showchecked: false,
    });
    const validatephone = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输入手机号码"));
      } else {
        if (/^1[3456789]\d{9}$/.test(value)) {
        } else {
          callback(new Error("请输入正确的11位手机号码"));
        }
        callback();
      }
    };
    const validatecode = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输短信验证码"));
      } else {
        if (/^[0-9]\d{3,5}$/.test(value)) {
        } else {
          callback(new Error("请输入正确的短信验证码"));
        }
        callback();
      }
    };
    let rulesUser = {
      userName: [
        {
          required: true,
          message: "请输用户名",
          trigger: "blur",
        },
      ],
      pwd: [
        {
          required: true,
          message: "请输入密码",
          trigger: "blur",
        },
      ],
    };
    let rulesphone = {
      phone: [{ validator: validatephone, trigger: "blur" }],
      code: [{ validator: validatecode, trigger: "blur" }],
    };
    onBeforeMount(async () => {
      state.captcha = await initGeet();

      methods.getWeixin(route.params.state);
      state.bussid = computed(() => {
        let url = window.location.href;
        if (url.indexOf("/gl/") != -1) {
          return 1;
        }
        if (url.indexOf("/lz/") != -1) {
          return 2;
        }
        if (url.indexOf("/wz/") != -1) {
          return 4;
        }
        if (url.indexOf("/gp/") != -1) {
          return 5;
        }
        if (url.indexOf("/bs/") != -1) {
          return 6;
        }
        if (url.indexOf("/qz/") != -1) {
          return 7;
        }
        if (url.indexOf("/hc/") != -1) {
          return 8;
        }
        if (url.indexOf("/bh/") != -1) {
          return 9;
        }
        if (url.indexOf("/fcg/") != -1) {
          return 11;
        }
        if (url.indexOf("/yl/") != -1) {
          return 12;
        }
        if (url.indexOf("/cz/") != -1) {
          return 13;
        }
        if (url.indexOf("/gg/") != -1) {
          return 14;
        }
        if (url.indexOf("/lb/") != -1) {
          return 15;
        }
        if (url.indexOf("/hz/") != -1) {
          return 18;
        }
        if (url.indexOf("/pn/") != -1) {
          return 20;
        }
      });
    });
    const methods = {
      async initGeet() {
        state.captcha = await initGeet();
      },
      //获取微信信息
      async getWeixin(id: string) {
        const res: any = await getwxinfo(id, "");
        if (res.code == 1) {
          state.FormUser.unionId = res.data.unionid;
          state.FormUser.nickName = res.data.nickname;
          state.FormPhone.unionId = res.data.unionid;
          state.FormPhone.nickName = res.data.nickname;
        } else {
          ElMessage.error(res.message);
          //登录失效 重新扫码
        }
      },
      //密码登录
      async onLogin() {
        const res: any = await accountLogin(state.FormUser);

        if (res.code == 1) {
          if (state.automatism) {
            //7天自动登录
            localStorage.setItem("gxrcToken", res.data.accessToken);
          }
          ElMessage.success(res.message);
          setTimeout(function () {
            if (route.query.returnUrl) {
              if (route.query.returnUrl.indexOf("login") > -1) {
                router.push({ path: "/" });
              } else {
                router.push({ path: route.query.returnUrl });
              }
            } else {
              router.push({ path: "/" });
            }
          }, 1000);
        } else {
          var num = res.message.substring(1, 5);
          var msg = res.message.substring(6);
          if (num == 1002 || num == 1001) {
            methods.getcaptcha();
            state.showValid = true;
          }
          if (res.message.indexOf("重复登录") > -1) {
            setTimeout(function () {
              if (route.query.returnUrl) {
                router.push({ path: route.query.returnUrl });
              } else {
                router.push({ path: "/" });
              }
            }, 1000);
          }
          ElMessage.error(msg);
        }
      },
      //手机号登录
      async loginbyPhone() {
        state.loading = true;
        let form = state.FormPhone;
        form.siteId = state.bussid || 0;
        let data: any = await loginByPhone(form);
        state.loading = false;
        if (data.code == 1) {
          ElMessage.success(data.message);
          window.clearInterval(state.clock);
          if (data.data.step === "noresume") {
            //新注册用户 跳转分布注册页
            router.replace({ name: "registerBasicInfo" });
          } else {

            if (route.query.returnUrl) {
              router.push({ path: route.query.returnUrl });
            } else {
              router.push({ path: "/" });
            }
          }
        }else{
             ElMessage.error(data.message);
        }
      },
      //获取图形验证码
      async getcaptcha() {
        let data: any = await getSecurityImageAsync("");
        state.security = data.data.image;
        state.FormUser.tokenId = data.data.token;
      },
      //极验
      async geetestValidate() {
        state.captcha.verify();
        await state.captcha.onSuccess(async () => {
          const result = state.captcha.getValidate();
          if (result != undefined) {
            state.loading = true;
            let data = {
              geetest_challenge: result.geetest_challenge,
              geetest_validate: result.geetest_validate,
              geetest_seccode: result.geetest_seccode,
              phone: state.FormPhone.phone,
              site: 1,
            };

            let res: any = await geetestSendCode(data);
            state.loading = false;
            if (res.code == 1) {
              ElMessage.success(res.message);
              fun.countDown();
            } else if (res.code !== 1) {
              ElMessage.error(res.message);
              return;
            } else {
              ElMessage.error("验证失败，请稍后再试");

              return;
            }
          } else {
            ElMessage.error("验证失败，请稍后再试");
            return;
          }
        });
      },
    };
    const fun = {
      handleClick(tab: any, event: any) {
      },
      SignUp() {
      },
      //密码登录
      userLogin() {
        FormUserA.value.validate((valid: boolean) => {
          if (valid) {
            methods.onLogin();
          }
        });
      },
      //手机验证码登录
      phoneLogin() {
        if(!state.checked){
            state.showchecked=true;
        }
        FormUserB.value.validate((valid: boolean) => {
          if (valid&&state.checked) {
            methods.loginbyPhone();
          }
        });
      },
      sendSmsCode() {
        FormUserB.value.validateField("phone", (valid: boolean) => {
          if (!valid) {
            methods.geetestValidate();
          }
        });
      },
      countDown() {
        state.send = true;
        state.clock = window.setInterval(() => {
          state.countTime--;
          if (state.countTime < 0) {
            window.clearInterval(state.clock);
            state.countTime = 0;
            state.send = false;
            //重新初始化极验
            methods.initGeet();
          }
        }, 1000);
        state.countTime = 60;
      },
    };

    return {
      ...toRefs(state),
      ...fun,
      rulesUser,
      rulesphone,
      FormUserA,
      FormUserB,
      methods,
    };
  },
});
</script>
<style lang="less">
.login-weixin {
  .el-container.is-vertical {
    min-width: 1200px;
  }

  .el-main {
    padding: 0 0 0 0;
  }

  .focus {
    width: 100%;
    a {
      width: 100%;
      height: 600px;
      display: block;
    }
  }
  .login-message-phone {
    padding: 0px 36px 0 36px;
    min-height: 424px;
    background: #fff;
    border-radius: 8px;
    margin: auto;
    .el-tabs__nav {
      border: none;
      width: 100%;
      .el-tabs__item {
        width: 170px;
        text-align: center;
        font-size: 15px;
        color: #bbb;
        line-height: 44px;
        height: 44px;
        padding: 0 0 0 0;
        border: 1px solid #f2f2f2;
      }
      .is-active {
        color: #5f9efc;
        font-size: 16px;
      }
    }
    .el-tabs__nav :first-child {
      margin-right: 20px;
    }
    .el-tabs__nav-wrap::after {
      background-color: #fff;
    }
    .el-tabs__header {
      width: 360px;
      margin: auto;
      position: relative;
    }
    .el-tabs--card > .el-tabs__header {
      border-bottom: none;
    }
    .el-tabs__content {
      width: 360px;
      margin: auto;
      padding: 30px 0;
    }
    .login-user {
      .foot-btn {
        span.wx {
          color: #49be10;
          cursor: pointer;
        }
      }
      .code {
        .el-input-group__append {
          background: none;
          padding: 4px 0 0 0;
          border: 1px solid #f2f2f2;
          border-left: none;
        }
      }
    }
    .login-phone {
      .code {
        .el-input-group__append {
          background: #fff;
          border: 1px solid #f2f2f2;
          border-left: none;
        }
        span.blue {
          cursor: pointer;
        }
      }
    }
  }
  .login-box {
    margin-bottom: 20px;
  }
  .login-code {
    text-align: center;
    .tit {
      font-size: 22px;
      color: #333;
      padding: 30px 0 20px 0;
    }
    .sys {
      font-size: 16px;
      color: #999;
      padding: 0 0 25px 0;
    }
    .code-img {
      width: 160px;
      height: 160px;
      position: relative;
      margin: auto;
      .abate {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 160px;
        height: 160px;
        z-index: 9;
        background: #000000c4;
      }
    }
    .tip {
      color: #ffffff;
      padding: 50px 0 10px 0;
      font-size: 14px;
    }
    .el-button--primary {
      height: 25px;
      line-height: 25px;
      padding: 0 0 0 0;
      width: 80px;
      font-size: 12px;
      background: #5f9efc;
    }
  }
  .submit-btn {
    height: 48px;
    line-height: 48px;
    background: #5f9efc;
    color: #fff;
    font-size: 17px;
    width: 100%;
    border-radius: 2px;
    padding: 0 0 0 0;
  }
  .el-input__inner {
    height: 46px;
    line-height: 46px;
    border: 1px solid #f2f2f2;
  }
  .big-title {
    padding: 28px 0 24px 0;
    line-height: 40px;
    h1 {
      font-size: 24px;
      color: #333;
    }
  }
  .tips {
    font-size: 14px;
    color: #666;
    text-align: center;
    padding: 30px 0;
  }
  //   .ico {
  //     position: absolute;
  //     top: 20px;
  //     right: 20px;
  //     cursor: pointer;
  //     z-index: 99;
  //     i {
  //       color: #5f9efc;
  //       font-size: 28px;
  //     }
  //   }
  .el-footer {
    background: #fff;
  }
  .ys {
    padding-left: 10px;
  }
}
</style>
