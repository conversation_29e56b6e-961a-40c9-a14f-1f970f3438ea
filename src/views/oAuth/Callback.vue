<template v-loading="loading">
   <div class="load" v-loading="loading" element-loading-text="马上跳转" element-loading-body="true"></div> 
</template>
<script lang="ts">
import { reactive, toRefs, defineComponent,onBeforeMount } from "vue";
import { useRouter, useRoute } from "vue-router";
import {oauthweixin,} from "@/http/api";
import { fstat } from "fs";
export default defineComponent({
   setup() {
    const router = useRouter();
    const route = useRoute();
    const state = reactive({
      code: "0" as any,
      state:"",
      loading: true,
    });
    onBeforeMount(async () => {
     methods.getData(route.query.code)
     })
    const methods = {
      async  getData(code:any){
           let res=await oauthweixin(code)
           if (res.code === 0 || !state.code) {
                // 确保目标URL是安全的
                const safeUrl = "/login"; // 或者使用一个函数来检查URL的安全性
                window.location.href = safeUrl;
                return false
            }
          if(res.code==1){//type==2没绑，type==1，已经绑定账号了
              if(res.data.type==1){
                  router.push({ path: "/" });   
              }else{
                    // router.push(`/oAuth/Login/${res.data.state}`);
                    const cleanedState = encodeURIComponent(res.data.state);
                router.push(`/oAuth/Login/${cleanedState}`);
              }

          }
        }

    }
    return { ...toRefs(state)};
  },
})
</script>
<style lang="less" scoped>
    .load{
        text-align: center;
        padding: 100px;
    }
</style>