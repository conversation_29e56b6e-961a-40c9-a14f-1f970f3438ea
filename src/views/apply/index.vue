<template>
  <div class="apply-page">
    <el-menu
      :default-active="activeIndex"
      class="el-menu-demo"
      mode="horizontal"
      @select="methods.handleSelect"
      :ellipsis="false"
    >
      <el-menu-item index="0">全部</el-menu-item>
      <el-menu-item index="1">
        被查看
        <!-- <el-badge is-dot class="item">被查看</el-badge> -->
      </el-menu-item>
      <el-sub-menu index="2">
        <template #title>邀面试</template>
        <el-menu-item index="2">全部</el-menu-item>
        <el-menu-item index="21">待确认</el-menu-item>
        <el-menu-item index="22">已接受</el-menu-item>
        <el-menu-item index="23">已拒绝</el-menu-item>
      </el-sub-menu>
      <el-menu-item index="3">不合适</el-menu-item>
      <el-menu-item index="4">已撤销投递</el-menu-item>
    </el-menu>

    <!-- <div v-loading="loading" class="list-wrap">
      <el-row class="title-wrap">
        <el-col :span="5">
          <div class="grid-content">职位信息</div>
        </el-col>
        <el-col :span="5">
          <div class="grid-content">简历名称</div>
        </el-col>
        <el-col :span="5">
          <div
            :class="`grid-content toudishijian ${deliverySortStyle}`"
            @click="methods.deliverySort"
          >
            投递时间
            <i class="iconfont icon-arrowUp"></i>
            <i class="iconfont icon-arrowDown"></i>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="grid-content">当前状态</div>
        </el-col>
        <el-col :span="4">
          <div class="grid-content">职位竞争力</div>
        </el-col>
      </el-row>

      <el-row class="body-wrap" v-for="(item,index) in list" :key="index">
        <el-col :span="5">
          <div class="grid-content position">
            <p class="pos">
              <a
                :href="`//www.gxrc.com/jobDetail/${item.positionGuid}`"
                target="_blank"
                :title="item.positionName"
              >{{ item.positionName }}</a>
            </p>
            <p class="salary">{{ item.paypackage }}</p>
            <p class="ent">
              <a
                :href="`//www.gxrc.com/company/${item.enterpriseGuid}`"
                target="_blank"
                :title="item.enterpriseName"
              >{{ item.enterpriseName }}</a>
            </p>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="grid-content resume">
            {{ item.resumeName }}
          </div>
        </el-col>
        <el-col :span="5">
          <div class="grid-content td-date">{{ item.deliverTime }}</div>
        </el-col>
        <el-col :span="5">
          <div class="grid-content state">
            <el-popover
              v-model:visible="item.showInterview"
              placement="bottom"
              :width="364"
              v-if="item.statusType == 2"
              popper-class="pop-box-wrap daiqueren-box-wrap"
              :show-arrow="false"
            >
              <template #reference>
                <div class="zhuangtai daiqueren" @click="methods.daiqueren(index, item.deliveryID)">
                  <p :class="item.statusStyle">{{ item.status }}</p>
                  <p class="date">{{ item.statusTime }}</p>
                </div>
              </template>
              <div class="daiqueren-box">
                <div class="tit">
                  邀面试-待确认
                  <span>{{ interviewDetail.data.createTime }}</span>
                </div>
                <div class="con">
                  <div class="hr clearfix">
                    <el-avatar size="small" :src="interviewDetail.data.interviewEnteAvatar"></el-avatar>
                    {{ interviewDetail.data.interviewEnteContacts }}
                  </div>
                  <div class="xiangxi">
                    <el-row>
                      <el-col :span="3">
                        <div class="grid-content bt">时间</div>
                      </el-col>
                      <el-col :span="21">
                        <div class="grid-content nr">{{ interviewDetail.data.jobInterviewTime }}</div>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="3">
                        <div class="grid-content bt">地点</div>
                      </el-col>
                      <el-col :span="21">
                        <div class="grid-content nr">{{ interviewDetail.data.interviewAddress }}</div>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="3">
                        <div class="grid-content bt">备注</div>
                      </el-col>
                      <el-col :span="21">
                        <div class="grid-content nr">{{ interviewDetail.data.interviewAddress }}</div>
                      </el-col>
                    </el-row>
                  </div>
                  <div class="btn-wrap">
                    <el-button
                      class="btn cancel"
                      @click="methods.refuseInterviewInvitation(index, item.deliveryID)"
                    >拒绝邀请</el-button>
                    <el-button
                      type="primary"
                      class="btn sure"
                      @click="methods.confirmInterviewInvitation(index, item.deliveryID)"
                    >接受邀请</el-button>
                  </div>
                </div>
              </div>
            </el-popover>

            <div class="zhuangtai" v-else>
              <p :class="item.statusStyle">{{ item.status }}</p>
              <p class="date">{{ item.statusTime }}</p>
            </div>

            <div v-if="item.topResumePointAll > 0">
              <el-popover
                v-model:visible="item.showTopResume"
                placement="bottom"
                :width="300"
                v-if="!item.isTop"
                popper-class="pop-box-wrap zhiding-box-wrap"
                :show-arrow="false"
              >
                <template #reference>
                  <div
                    class="zd tdzd"
                    @click="item.showTopResume = true"
                  >设置投递置顶(余{{ item.topResumePointAll }}次)</div>
                </template>
                <div class="zhiding-box">
                  <div class="tit">
                    当前职位：
                    <span>{{ item.positionName }}</span>
                  </div>
                  <div class="con">
                    <div class="shengyu">投递置顶：剩余{{ item.topResumePointAll }}次</div>
                    <div class="shifou">是否使用1次投递置顶</div>
                    <div class="btn-wrap">
                      <el-button class="btn cancel" @click="item.showTopResume = false">取消</el-button>
                      <el-button
                        type="primary"
                        class="btn sure"
                        @click="methods.confirmSetTopResume(index, item.deliveryID)"
                      >确定</el-button>
                    </div>
                  </div>
                </div>
              </el-popover>
              <div class="zd ysy" v-else>已使用投递置顶</div>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          
        </el-col>
      </el-row>
      <div class="message" v-show="list == ''">
        <el-empty :description="message"></el-empty>
      </div>
    </div>-->
    <div v-loading="loading" class="list-wrap">
      <el-table :data="list" style="width: 100%" align="center" type="index">
        <el-table-column
          label="职位信息"
          width="198"
          header-align="center"
          align="left"
        >
          <template #default="scope">
            <div class="grid-content position">
              <p class="pos">
                <a
                  :href="`//www.gxrc.com/jobDetail/${scope.row.positionGuid}`"
                  target="_blank"
                  :title="scope.row.positionName"
                  :style="scope.row.statusType == 7 ? 'color: #BBBBBB;' : ''"
                  >{{ scope.row.positionName }}</a
                >
              </p>
              <p
                class="salary"
                :style="scope.row.statusType == 7 ? 'color: #BBBBBB;' : ''"
              >
                {{ scope.row.paypackage }}
              </p>
              <p class="ent">
                <a
                  :href="`//www.gxrc.com/company/${scope.row.enterpriseGuid}`"
                  target="_blank"
                  :title="scope.row.enterpriseName"
                  :style="scope.row.statusType == 7 ? 'color: #BBBBBB;' : ''"
                  >{{ scope.row.enterpriseName }}</a
                >
              </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="简历名称" header-align="center" align="center">
          <template #default="scope">
            <a
              :href="`/resume/${scope.row.resumeId}`"
              target="_blank"
              class="resume"
              :title="scope.row.enterpriseName"
              :style="scope.row.statusType == 7 ? 'color: #BBBBBB;' : ''"
              >{{ scope.row.resumeName }}</a
            >
          </template>
        </el-table-column>
        <el-table-column header-align="center" width="198" align="center">
          <template #header>
            <div
              :class="`grid-content toudishijian ${deliverySortStyle}`"
              @click="methods.deliverySort"
            >
              投递时间
              <i class="iconfont icon-arrowUp"></i>
              <i class="iconfont icon-arrowDown"></i>
            </div>
          </template>

          <template #default="scope">
            <div
              class="grid-content td-date"
              :style="scope.row.statusType == 7 ? 'color: #BBBBBB;' : ''"
            >
              {{ scope.row.deliverTime }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="当前状态"
          header-align="center"
          align="center"
          width="198"
        >
          <template #default="scope">
            <div class="grid-content state">
              <el-popover
                v-model:visible="scope.row.showInterview"
                placement="bottom"
                :width="364"
                v-if="scope.row.statusType == 2"
                popper-class="pop-box-wrap daiqueren-box-wrap"
                :show-arrow="false"
              >
                <template #reference>
                  <div
                    class="zhuangtai daiqueren"
                    @click="
                      methods.daiqueren(scope.$index, scope.row.deliveryID)
                    "
                  >
                    <p :class="scope.row.statusStyle">{{ scope.row.status }}</p>

                    <p class="date">{{ scope.row.statusTime }}</p>
                  </div>
                </template>
                <div class="daiqueren-box">
                  <div class="tit">
                    邀面试-待确认
                    <span>{{ interviewDetail.data.createTime }}</span>
                  </div>
                  <div class="con">
                    <div class="hr clearfix">
                      <el-avatar
                        size="small"
                        :src="interviewDetail.data.interviewEnteAvatar"
                      ></el-avatar>
                      {{ interviewDetail.data.interviewEnteContacts }}
                    </div>
                    <div class="xiangxi">
                      <el-row>
                        <el-col :span="3">
                          <div class="grid-content bt">时间</div>
                        </el-col>
                        <el-col :span="21">
                          <div class="grid-content nr">
                            {{ interviewDetail.data.jobInterviewTime }}
                          </div>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="3">
                          <div class="grid-content bt">地点</div>
                        </el-col>
                        <el-col :span="21">
                          <div class="grid-content nr">
                            {{ interviewDetail.data.interviewAddress }}
                          </div>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="3">
                          <div class="grid-content bt">备注</div>
                        </el-col>
                        <el-col :span="21">
                          <div class="grid-content nr">
                            {{ interviewDetail.data.interviewAddress }}
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                    <div class="btn-wrap">
                      <el-button
                        class="btn cancel"
                        @click="
                          methods.refuseInterviewInvitation(
                            scope.$index,
                            scope.row.deliveryID
                          )
                        "
                        >拒绝邀请</el-button
                      >
                      <el-button
                        type="primary"
                        class="btn sure"
                        @click="
                          methods.confirmInterviewInvitation(
                            scope.$index,
                            scope.row.deliveryID
                          )
                        "
                        >接受邀请</el-button
                      >
                    </div>
                  </div>
                </div>
              </el-popover>

              <div class="zhuangtai" v-else>
                <p v-if="scope.row.statusType == 1">
                  被查看({{ scope.row.enterpriseViewCount }}次)
                </p>
                <p :class="scope.row.statusStyle" v-else>
                  {{ scope.row.status }}
                </p>

                <p
                  class="date"
                  v-if="scope.row.statusType > 0 && scope.row.statusType != 7"
                >
                  {{ scope.row.statusTime }}
                </p>
              </div>

              <div
                v-if="
                  scope.row.topResumePointAll > 0 && scope.row.statusType != 7
                "
              >
                <el-popover
                  v-model:visible="scope.row.showTopResume"
                  placement="bottom"
                  :width="364"
                  v-if="!scope.row.isTop"
                  popper-class="pop-box-wrap zhiding-box-wrap"
                  :show-arrow="false"
                >
                  <template #reference>
                    <span
                      class="zd tdzd"
                      @click="scope.row.showTopResume = true"
                      >设置投递置顶(余{{ scope.row.topResumePointAll }}次)</span
                    >
                  </template>
                  <div class="zhiding-box">
                    <div class="tit">
                      当前职位：
                      <span>{{ scope.row.positionName }}</span>
                    </div>
                    <div class="con">
                      <div class="shengyu">
                        投递置顶：剩余{{ scope.row.topResumePointAll }}次
                      </div>
                      <div class="shifou">是否使用1次投递置顶</div>
                      <div class="btn-wrap">
                        <el-button
                          class="btn cancel"
                          @click="scope.row.showTopResume = false"
                          >取消</el-button
                        >
                        <el-button
                          type="primary"
                          class="btn sure"
                          @click="
                            methods.confirmSetTopResume(
                              scope.$index,
                              scope.row.deliveryID
                            )
                          "
                          >确定</el-button
                        >
                      </div>
                    </div>
                  </div>
                </el-popover>
                <div class="zd ysy" v-else>已使用投递置顶</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="职位竞争力" header-align="center" width="195">
          <template #default="scope">
            <div
              class="grid-content jzl"
              v-if="![7, 11].includes(scope.row.statusType)"
            >
              <el-button
                round
                v-if="!scope.row.isCompetion"
                @click="
                  methods.showCompetionAnalysis(
                    scope.row.positionGuid,
                    scope.row.deliveryID,
                    scope.row.positionName
                  )
                "
                >立即分析</el-button
              >
              <div
                v-else
                class="ckjg"
                @click="
                  methods.popCompetionAnalysis(
                    scope.row.positionGuid,
                    scope.row.resumeId
                  )
                "
              >
                <p>已分析</p>
                <p>查看结果</p>
              </div>
            </div>
            <div class="cx" v-if="![7, 11].includes(scope.row.statusType)">
              <el-popover :width="60" trigger="hover">
                <template #reference>
                  <i class="icon-gengduo iconfont"></i>
                </template>
                <template #default>
                  <div
                    class="demo-rich-conent"
                    @click="methods.CancelDelivery(scope.row.deliveryID)"
                  >
                    撤销投递
                  </div>
                </template>
              </el-popover>
            </div>
            <div v-if="scope.row.statusType == 7" style="text-align: center">
              -
            </div>
          </template>
        </el-table-column>
        <template #empty>
          <el-empty :description="message"></el-empty>
        </template>
      </el-table>
    </div>
    <!-- 使用竞争力分析弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      width="364px"
      custom-class="POPanalyse"
      :showClose="false"
    >
      <template #title>
        <h3 class="title">
          <span class="a1">当前职位 </span>
          <span class="a2">{{ analyseName }}</span>
        </h3>
      </template>
      <div class="box">
        <span class="f1">竞争力分析：剩余{{ analysenumber }}次</span>
        <p class="f2">是否使用1次竞争力分析</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false" class="cancel"
            >取 消</el-button
          >
          <el-button type="primary" @click="methods.ensure" class="sur"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <Pagination
      :pageSize="parameter.pagesize"
      :totalCount="totalCount"
      @handlePageChange="methods.handlePageChange"
    />

    <MymallCompetionAnalysis
      :drawer="caDrawer"
      :positionGuid="positionGuid"
      :resumeId="resumeId"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted, watch, ref, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  myDelivery,
  mySetDeliverTopResume,
  myInterviewdetail,
  agreeInterview,
  refuseInterview,
  cancelapply,
  deliverdetailcompetepower,
  competionservice,
  getMyServices,
} from "@/http/api";
import { ElMessage, ElMessageBox } from "element-plus";
import Pagination from "@/components/Pagination.vue";
import MymallCompetionAnalysis from "@/components/MymallCompetionAnalysis.vue";
export default defineComponent({
  components: { Pagination, MymallCompetionAnalysis },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const state = reactive({
      // 状态数值型statusType -1:强行加入的虚拟企业待查看记录 // 0:未查看, 1:已查看, 2:已邀请, 3:面试邀请已接受, 4:面试邀请已拒绝, 5:被拒绝 6:面试邀请-待确认 7：投递撤销
      parameter: {
        state: route.params.id,
        ordertype: 0,
        page: 1,
        pagesize: 10,
      },
      activeIndex: route.params.id.toString(),
      totalCount: 0,
      message: "",
      tdSort: 0,
      deliverySortStyle: "",
      list: [],
      loading: false,
      interviewDetail: {
        code: 0,
        data: {
          jobInterviewTime: "string",
          createTime: "string",
          askToken: "string",
          interviewEnteAvatar: "string",
          interviewEnteContacts: "string",
          interviewType: 0,
          interviewAddress: "string",
          jobInterviewEntePhone: "string",
          statusType: 0,
          status: "string",
          enterpriseYXId: "string",
        },
        message: "string",
        now: "2021-10-22T01:44:38.313Z",
      },
      caDrawer: { value: false },
      positionGuid: 0,
      visible: false,
      dialogVisible: false,
      analyseName: "",
      analyseGuid: 0,
      analysenumber: 0,
      resumeId: 0,
    });

    onMounted(() => {
      methods.getData();
    });

    watch(
      () => route.params.id,
      (newValue, oldValue) => {
        state.parameter.state = newValue;
        state.activeIndex = newValue;
        methods.getData();
      }
    );

    const methods = {
      async getData() {
        state.loading = true;
        const res = await myDelivery(state.parameter);
        state.list = res.data.items;
        state.loading = false;
        state.list?.forEach((element: any) => {
          element.showTopResume = false;
          element.showInterview = false;
          if (element.statusType == 2 || element.statusType == 3) {
            element.statusStyle = "yms";
          } else if (element.statusType == 4) {
            element.statusStyle = "yjj";
          } else if (element.statusType == 5) {
            element.statusStyle = "bhs";
          } else if (element.statusType == 7) {
            element.statusStyle = "chexiao";
          } else {
            element.statusStyle = "";
          }
        });
        if (state.list == "") {
          state.message = "没有数据";
        } else {
          if (state.message == null) {
            state.message = "";
          } else {
            state.message = res.message;
          }
        }
        state.totalCount = res.data.totalCount;
      },
      handleSelect(key: number, keyPath: string) {
        router.push({ name: "apply", params: { id: key } });
      },
      handlePageChange(val: number) {
        state.parameter.page = val;
        methods.getData();
      },
      deliverySort() {
        if (state.tdSort < 2) {
          state.tdSort++;
        } else {
          state.tdSort = 0;
        }
        if (state.tdSort == 0) {
          state.parameter.ordertype = 0;
          state.deliverySortStyle = "";
        } else if (state.tdSort == 1) {
          state.parameter.ordertype = 1;
          state.deliverySortStyle = "jiang";
        } else {
          state.parameter.ordertype = 2;
          state.deliverySortStyle = "sheng";
        }
        methods.getData();
      },
      async confirmSetTopResume(index: number, deliveryid: number) {
        state.list[index].showTopResume = false;
        const res = await mySetDeliverTopResume(deliveryid);
        if (res.code == 1) {
          ElMessage.success(res.message);
          methods.getData();
        } else {
          ElMessage.error(res.message);
        }
      },
      async daiqueren(index: number, deliveryid: number) {
        state.list[index].showInterview = true;
        const res = await myInterviewdetail({ deliveryid: deliveryid });
        if (res.code == 1) {
          state.interviewDetail = res;
        }
      },
      async confirmInterviewInvitation(index: number) {
        const res = await agreeInterview({
          enterId: state.interviewDetail.data.enterpriseYXId,
          askToken: state.interviewDetail.data.askToken,
        });
        if (res.code == 1) {
          ElMessage.success(res.message);
        } else {
          ElMessage.error(res.message);
        }
        state.list[index].showInterview = false;
      },
      async refuseInterviewInvitation(index: number) {
        const res = await refuseInterview({
          enterId: state.interviewDetail.data.enterpriseYXId,
          askToken: state.interviewDetail.data.askToken,
          refuseReason: 0,
        });
        if (res.code == 1) {
          ElMessage.success(res.message);
        } else {
          ElMessage.error(res.message);
        }
        state.list[index].showInterview = false;
      },
      //撤销投递
      async CancelDelivery(id: string) {
        ElMessageBox.confirm("是否撤销投递?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          center: true,
          showClose: false,
          customClass: "cancel-box",
        })
          .then(async () => {
            const res = await cancelapply(id);
            if (res.code == 1) {
              ElMessage.success(res.message);
              methods.getData();
            } else {
              ElMessage.error(res.message);
            }
          })
          .catch(() => {});
      },
      async ensure(type:number = 0) {
        const res = await competionservice(state.analyseGuid);

        if (res.code == 1) {
          methods.getData();
          state.dialogVisible = false;
          if(type == 0){
            ElMessage.success(res.message);
          }
          
          setTimeout(() => {
            state.caDrawer.value = true;
            state.positionGuid = state.analyseGuid;
            state.analysenumber--;
          }, 1000);
        } else {
          state.caDrawer.value = true;
          state.positionGuid = 0;
          ElMessage.error(res.message);
        }
      },
      popCompetionAnalysis(guid: number, resumeId: number) {
        state.caDrawer.value = true;
        state.positionGuid = guid;
        state.resumeId = resumeId;
        console.log("简历id", resumeId);
      },
      async showCompetionAnalysis(
        guid: number,
        deliveryID: number,
        name: string
      ) {
        state.analyseGuid = guid;
        const data = await getMyServices(100001);
        console.log("res", data);
        if (data.code != 1) {
          ElMessage.error(data.message);
          return false;
        }
        const topResult = data.data;
        if (
          topResult?.businessInfoList &&
          (topResult?.businessInfoList?.length || 0) > 0
        ) {
          const item = topResult?.businessInfoList.find(
            (item: any) => item.businessType == 100001
          );
          state.analysenumber = item?.leftCount || 0;
          if (item && [-1, 1, 2].includes(item?.competionAnalysisFlag || 0)) {
            if ([1,2].includes(item?.competionAnalysisFlag || 0)) {
              await this.ensure(1);
            } else if ([-1].includes(item?.competionAnalysisFlag || 0)) {
              state.dialogVisible = true;
              state.analyseName = name;
            } else {
              state.caDrawer.value = true;
              state.positionGuid = 0;
              state.analysenumber = 0;
            }
          } else {
            state.caDrawer.value = true;
            state.positionGuid = 0;
            state.analysenumber = 0;
          }
        } else {
          state.caDrawer.value = true;
          state.positionGuid = 0;
          state.analysenumber = 0;
        }
        // const res = await deliverdetailcompetepower({deliverid:deliveryID});
        // if(res.code!=1){
        //   ElMessage.error(res.message);
        //   return false
        // }
        // if(res.data.availableNumber>0){
        //   state.dialogVisible = true;
        //   state.analyseName = name;
        //   state.analysenumber=res.data.availableNumber
        // }else{
        //   ElMessage.error(res.data.surplusSentence);
        //   setTimeout(()=>{
        //     state.caDrawer.value = true;
        //     state.positionGuid = 0;
        //     state.analysenumber=0
        //   },1000)
        // }
      },
    };

    return {
      ...toRefs(state),
      methods,
    };
  },
});
</script>

<style lang="less">
.apply-page {
  .el-menu.el-menu--horizontal {
    border-bottom: 0;
    margin-bottom: 10px;
  }
  .el-menu--horizontal > li {
    width: 20%;
    height: 52px;
    line-height: 52px;
    text-align: center;
    border-right: 1px solid #f2f2f2;
  }
  .el-menu--horizontal > li:last-child {
    border-right: 0;
  }
  .el-menu--horizontal > .el-sub-menu .el-sub-menu__title {
    height: 52px;
    line-height: 52px;
  }
  .el-menu--horizontal > .el-menu-item,
  .el-menu--horizontal > .el-sub-menu .el-sub-menu__title {
    color: #666;
  }

  .el-menu--horizontal > .el-menu-item.is-active {
    border-bottom: 0;
    color: #457ccf;
  }
  .el-menu-item {
    padding: 0 0;
    transition: none;
  }
  .el-menu--horizontal > .el-sub-menu.is-active .el-sub-menu__title {
    border-bottom: 0;
    color: #457ccf;
  }
  .el-badge__content.is-fixed {
    top: 15px;
    right: -5px;
  }
  .list-wrap {
    .el-table__header {
      .cell {
        font-size: 12px;
        color: #999;
        font-weight: normal;
      }
    }
    .grid-content {
      font-size: 12px;
      color: #999999;
    }
    .position {
      text-align: left;
      padding-left: 20px;
      .pos a,
      .ent a {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        display: inline-block;
        width: 170px;
      }
      .pos {
        a {
          font-size: 16px;
          color: #333333;
        }
        a:hover {
          color: #457ccf;
        }
      }
      .salary {
        font-size: 16px;
      }
      .ent {
        a {
          font-size: 12px;
          color: #999;
        }
        a:hover {
          color: #457ccf;
        }
      }
    }
    .td-date {
      text-align: center;
      font-size: 14px;
      color: #666666;
    }
    .resume {
      text-align: center;
      font-size: 14px;
      color: #666666;
    }
    .jzl {
      float: left;
      padding-left: 20px;
    }
    .cx {
      float: right;
      padding-right: 30px;
      cursor: pointer;
    }
    .jzl {
      .el-button.is-round {
        border-color: #5f9efc;
        color: #5f9efc;
        padding: 5px 10px;
        font-size: 14px;
      }
      .el-button.is-round:hover {
        background: #5f9efc;
        color: #fff;
      }
      .ckjg {
        cursor: pointer;
        p {
          color: #457ccf;
          font-size: 14px;
        }
      }
    }
    .state {
      p {
        font-size: 14px;
        color: #666;
      }
      .date {
        font-size: 12px;
        color: #999999;
      }
      .zd {
        font-size: 12px;
        color: #9d6623;
        margin: 0 auto;
        border-radius: 12px;
      }
      .tdzd {
        padding: 2px 10px 2px 20px;
        height: 22px;
        line-height: 22px;
        cursor: pointer;
        border: 1px solid #9d6623;
        background: url(/src/assets/img/resume-top.png) no-repeat 5px 4px;
      }
      .ysy {
        width: 126px;
        height: 24px;
        line-height: 24px;
        background: #ffefd1;
      }
      .yms {
        color: #457ccf;
      }
      .yjj {
        color: #999999;
      }
      .bhs {
        color: #fc5c5b;
      }
      .chexiao {
        color: #bbbbbb;
      }
    }
    .toudishijian {
      position: relative;
      cursor: pointer;
      i {
        position: absolute;
        right: 50px;
        font-size: 12px;
        transform: scale(0.7);
      }
      .icon-arrowUp {
        top: -3px;
      }
      .icon-arrowDown {
        top: 4px;
      }
    }
    .sheng .icon-arrowUp,
    .jiang .icon-arrowDown {
      color: #457ccf;
      font-weight: bold;
    }
  }

  // .list-wrap {
  //   background: #fff;
  //   .el-col {
  //     padding: 0 10px;
  //   }
  //   .title-wrap {
  //     height: 52px;
  //     line-height: 52px;
  //     border-bottom: 1px solid #f2f2f2;
  //     margin: 0;
  //     .grid-content {
  //       text-align: center;
  //       font-size: 12px;
  //       color: #999999;
  //     }
  //     .toudishijian {
  //       position: relative;
  //       cursor: pointer;
  //       i {
  //         position: absolute;
  //         right: 48px;
  //         font-size: 12px;
  //       }
  //       .icon-arrowUp {
  //         top: -5px;
  //       }
  //       .icon-arrowDown {
  //         top: 6px;
  //       }
  //     }
  //     .sheng .icon-arrowUp,
  //     .jiang .icon-arrowDown {
  //       color: #457ccf;
  //       font-weight: bold;
  //     }
  //   }
  //   .body-wrap {
  //     font-size: 14px;
  //     padding: 0 10px;
  //     .el-col {
  //       border-bottom: 1px solid #f2f2f2;
  //     }
  //     .grid-content {
  //       padding: 10px 10px;
  //       text-align: center;
  //       color: #666666;
  //     }
  //     .position {
  //       text-align: left;
  //       .pos a,
  //       .ent a {
  //         white-space: nowrap;
  //         text-overflow: ellipsis;
  //         overflow: hidden;
  //       }
  //       .pos {
  //         a {
  //           font-size: 16px;
  //           color: #333333;
  //         }
  //         a:hover {
  //           color: #457ccf;
  //         }
  //       }
  //       .salary {
  //         font-size: 16px;
  //       }
  //       .ent {
  //         a {
  //           font-size: 12px;
  //           color: #999;
  //         }
  //         a:hover {
  //           color: #457ccf;
  //         }
  //       }
  //     }
  //     .resume {
  //       a {
  //         color: #666666;
  //       }
  //       a:hover {
  //         color: #457ccf;
  //       }
  //     }
  //     .td-date {
  //     }
  //     .daiqueren {
  //       cursor: pointer;
  //     }
  //     .state {
  //       p {
  //         padding-bottom: 5px;
  //       }
  //       .date {
  //         font-size: 12px;
  //         color: #999999;
  //       }
  //       .zd {
  //         font-size: 12px;
  //         color: #9d6623;
  //         margin: 0 auto;
  //         border-radius: 12px;
  //       }
  //       .tdzd {
  //         width: 140px;
  //         padding-left: 16px;
  //         height: 22px;
  //         line-height: 22px;
  //         cursor: pointer;
  //         border: 1px solid #9d6623;
  //         background: url(@/assets/img/resume-top.png) no-repeat 10px 6px;
  //       }
  //       .ysy {
  //         width: 126px;
  //         height: 24px;
  //         line-height: 24px;
  //         background: #ffefd1;
  //       }
  //       .yms {
  //         color: #457ccf;
  //       }
  //       .yjj {
  //         color: #999999;
  //       }
  //       .bhs {
  //         color: #fc5c5b;
  //       }
  //     }
  //     .jzl {
  //       .el-button.is-round {
  //         border-color: #5f9efc;
  //         color: #5f9efc;
  //         padding: 5px 10px;
  //         font-size: 14px;
  //       }
  //       .el-button.is-round:hover {
  //         background: #5f9efc;
  //         color: #fff;
  //       }
  //       .ckjg {
  //         cursor: pointer;
  //         p {
  //           color: #457ccf;
  //         }
  //       }
  //     }
  //   }
  // }
}
.el-menu--horizontal > .el-menu .el-menu-item.is-active,
.el-menu--horizontal .el-menu .el-sub-menu.is-active > .el-sub-menu__title {
  color: #457ccf;
}
.pop-box-wrap {
  .tit {
    height: 50px;
    line-height: 50px;
    padding: 0 10px;
  }
  .btn-wrap {
    text-align: center;
    .btn {
      border: none;
      border-radius: 0;
    }
    .cancel {
      border: 1px solid #f2f2f2;
      background: #fff;
      span {
        color: #999999;
      }
    }
    .sure {
      background: #5e9efc;
      span {
        color: #fff;
      }
    }
  }
}

.el-popover.el-popper.zhiding-box-wrap {
  border-radius: 0;
  background: #fff;
  padding: 0;
  .zhiding-box {
    .tit {
      background: #fafafa;
      color: #999999;
      font-size: 16px;
      padding: 0 15px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      span {
        color: #333333;
      }
    }
    .con {
      padding: 20px 10px 10px;
      text-align: center;
      .shengyu {
        color: #457ccf;
        font-size: 16px;
        font-weight: bold;
        padding: 10px 0;
      }
      .shifou {
        color: #666666;
        padding: 0px 0 20px;
      }
    }
    .btn-wrap {
      .cancel {
        width: 100px;
      }
      .sure {
        width: 160px;
      }
    }
  }
}
.el-popover.el-popper.daiqueren-box-wrap {
  .daiqueren-box {
    .tit {
      color: #fff;
      background: #4187f2;
      span {
        float: right;
      }
    }
    .con {
      padding: 10px 15px;
      .hr {
        height: 32px;
        line-height: 32px;
        .el-avatar {
          vertical-align: middle;
          margin-right: 5px;
        }
      }
      .xiangxi {
        padding: 15px 0;
        .el-row {
          padding-bottom: 10px;
        }
        .bt {
          color: #999999;
        }
      }
    }
    .btn-wrap {
      .cancel {
        width: 120px;
      }
      .sure {
        width: 200px;
      }
    }
  }
}
.cancel-box {
  width: 300px;
  padding: 0 0 0 0;
  border: none;
  .el-message-box__btns {
    padding: 0 0 0 0;
    border-top: 1px solid #f2f2f2;
    .el-button {
      width: 50%;
      border: none;
      float: left;
      margin: 0 0 0 0;
      height: 40px;
      line-height: 40px;
      padding: 0 0 0 0;
      border-radius: 0;
    }
  }
  .el-message-box__content {
    padding-bottom: 30px;
  }
  .el-button--primary {
    background: #fff;
    color: #5e9efc;
    position: relative;
    &:hover {
      background: #cee2ff;
    }
  }
  .el-button--primary:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: 24px;
    bottom: 0;
    left: 0px;
    border-left: 1px solid #f2f2f2;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    height: 40px;
    width: 1px;
  }
}
.demo-rich-conent {
  cursor: pointer;
}
.POPanalyse {
  .el-dialog__header {
    background: #fafafa;
  }
  .title {
    font-size: 16px;
  }
  .a1 {
    color: #999999;
  }
  .a2 {
    color: #333333;
  }
  .box {
    text-align: center;
  }
  .f1 {
    color: #457ccf;
    font-size: 16px;
  }
  .f2 {
    color: #666666;
    font-size: 16px;
    padding-top: 10px;
  }
  .cancel {
    width: 96px;
    height: 38px;
    font-size: 16px;
    border: 1px solid #f2f2f2;
  }
  .sur {
    background: #4187f2;
    color: #fff;
    width: 224px;
    height: 40px;
    font-size: 16px;
  }
}
</style>
