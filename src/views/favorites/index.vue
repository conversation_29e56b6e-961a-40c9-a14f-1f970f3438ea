<template>
  <div class="my-viewed-page">
    <top-title>
      <div :class="state.typenum==1?'top-l pitch-on':'top-l'" @click="state.typenum=1">
        <span>职位收藏</span><span class="zs">{{ state.totalCount }}</span>
      </div>
      <div :class="state.typenum==2?'top-l pitch-on':'top-l'" @click="state.typenum=2">
        公司收藏<span class="zs">{{ state.totalCountCom }}</span>
      </div>
      <div class="top-r delete" @click="methods.deleteAllOverdueCollection" v-if="state.typenum==1">
        <i class="iconfont icon-trash4"></i>清除所有过期职位
      </div>
    </top-title>

    <div class="list-con" v-if="state.typenum==1">
      <div v-loading="state.loading" class="list-wrap clearfix">
        <el-space wrap>
          <Position
            v-for="(item, index) in state.list"
            :key="index"
            :item="item"
            :isFavorites="true"
            @refreshList="methods.getData"
          />
        </el-space>
      </div>

      <div class="message" v-show="state.list == ''">{{ state.message }}</div>
      <Pagination
      :pageSize="state.parameter.pagesize"
      :totalCount="state.totalCount"
      @handlePageChange="methods.handlePageChange"
    />
    </div>
    <!-- //公司收藏 -->
    <div class="list-con" v-else>
      <div v-loading="state.loading" class="list-wrap clearfix">
        <el-space wrap>
          <Company
            v-for="(item, index) in state.listCom"
            :key="index"
            :item="item"
            :isFavorites="true"
            @refreshList="methods.getCompanyData"
          />
        </el-space>
      </div>

      <div class="message" v-show="state.listCom == ''">{{ state.messageCom }}</div>
      <Pagination
      :pageSize="state.parameter.pagesizeCom"
      :totalCount="state.totalCountCom"
      @handlePageChange="methods.handlePageChangeCom"
    />
    </div>

    
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, onMounted } from "vue";
import Position from "@/components/Position.vue";
import Company from "@/components/Company.vue";
import TopTitle from "@/components/TopTitle.vue";
import Pagination from "@/components/Pagination.vue";
import { myCollection, myOverdueCollection,companyCollectionction,companyCollection } from "@/http/api";
import { ElMessage } from "element-plus";
export default defineComponent({
  components: { Position, TopTitle, Pagination ,Company},
  setup() {
    const state = reactive({
      parameter: {
        page: 1,
        pagesize: 15,
      },
      parameterCom: {
        pageCom: 1,
        pagesizeCom: 15,
      },
      totalCount: 0,
      totalCountCom:0,//公司个数
      message: "",
      messageCom: "",
      list: [],
      listCom: [],
      loading: false,
      typenum:1,//1  职位 2公司
    });

    onMounted(() => {
      methods.getData();
      methods.getCompanyData();
    });

    const methods = {
      async getData() {
        const res = await myCollection(state.parameter);
        state.loading = true;
        state.list = res.data.items;
        state.loading = false;

        if (state.list == "") {
          state.message = "没有数据";
        } else {
          if (state.message == null) {
            state.message = "";
          } else {
            state.message = res.message;
          }
        }
        state.totalCount = res.data.totalCount;
      },
      async getCompanyData() { //获取公司数据
        const res = await companyCollectionction(state.parameterCom);
        state.loading = true;
        state.listCom = res.data.items;
        state.loading = false;

        if (state.listCom == "") {
          state.message = "没有数据";
        } else {
          if (state.messageCom == null) {
            state.messageCom = "";
          } else {
            state.messageCom = res.message;
          }
        }
        state.totalCountCom = res.data.totalCount;
      },

      handlePageChange(val: number) {
        state.parameter.page = val;
        methods.getData();
      },
      handlePageChangeCom(val: number) {
        state.parameter.pageCom = val;
        methods.getCompanyData();
      },
      async deleteAllOverdueCollection() {
        const res = await myOverdueCollection();
        if (res.code == 1) {
          ElMessage.success(res.message);
        } else {
          ElMessage.error(res.message);
        }
      },
    };

    return {
      state,
      methods,
    };
  },
});
</script>

<style lang="less">
.my-viewed-page {
  .list-wrap {
    width: 1020px;
  }
}
</style>


