<template>
  <div class="index-mn clearfix">
    <div class="middle">
      <!-- 广告 -->
      <Carousel :type="1" :list="state.adList1" v-if="state.adList1" />
      <div class="Default-picture" @click="methods.popVip()" v-else>
        <img src="https://image.gxrc.com/gxrcsite/ad/2022/pc/banner1.png" />
      </div>
      <Carousel :type="1" :list="state.adList2" v-if="state.adList2" />
      <div class="Default-picture" v-else>
        <a
          href="//share.gxrc.com/HtmlView/2021/huodong/jianliyouhua.html"
          target="_blank"
          rel="noopener noreferrer"
        >
          <img src="https://image.gxrc.com/gxrcsite/ad/2022/pc/banner2.png"
        /></a>
      </div>
      <!-- 记录 -->
      <div class="cell">
        <ul class="clearfix">
          <li>
            <router-link to="/apply/0">
              <p>投递记录</p>
              <span>{{ userInfo.deliverCount }}</span>
              <el-divider
                direction="vertical"
                content-position="right"
              ></el-divider>
            </router-link>
          </li>
          <li @click="methods.getNewViewMe">
            <router-link to="/enterpriseView/0">
              <p>谁看过我</p>
              <span class="elbadge">
                {{ userInfo.enterpriseViewMeCount }}
                <sup
                  class="
                    el-badge__content el-badge__content--danger
                    is-fixed is-dot
                  "
                  v-if="userInfo.isNewViewMe"
                ></sup>
              </span>
              <el-divider direction="vertical"></el-divider>
            </router-link>
          </li>
          <li>
            <router-link to="/myViewed">
              <p>浏览记录</p>
              <span>{{ userInfo.lookCount }}</span>
              <el-divider direction="vertical"></el-divider>
            </router-link>
          </li>
          <li>
            <router-link to="/favorites">
              <p>职位收藏</p>
              <span>{{ userInfo.favoriteCount }}</span>
            </router-link>
          </li>
        </ul>
      </div>
      <!-- 卡片 -->
      <div class="tabs-wrap">
        <el-tabs v-model="state.activeName" @tab-click="methods.handleClick">
          <el-tab-pane
            v-loading="state.loading"
            v-for="(item, index) in state.positionRecommend"
            :key="index"
            :label="item.label"
            :name="item.name"
          >
            <div class="noList" v-if="item.list.length < 1">
              <div class="box clearfix">
                <div class="noImg"></div>
                <div class="doc">
                  <p>没有找到合适您的职位</p>
                  <router-link
                    class=""
                    target="_blank"
                    :to="
                      '/resume/' +
                      userInfo.defaultResumeId +
                      '#iseditCarrer'
                    "
                  >
                    <i class="iconfont icon-add2"></i>
                    添加意向 >
                  </router-link>
                </div>
              </div>
            </div>
            <div class="list-box" v-else>
              <el-space wrap :size="0">
                <Position
                  v-for="(item2, index2) in item.list"
                  :key="index2"
                  :item="item2"
                />
              </el-space>
            </div>
          </el-tab-pane>
        </el-tabs>
        <router-link
          class="btn-add"
          target="_blank"
          :to="'/resume/' + userInfo.defaultResumeId + '#iseditCarrer'"
        >
          <i class="iconfont icon-add2"></i>
          添加意向 <i class="iconfont icon-warn" v-if="state.hasQueren"></i>
        </router-link>
        <div class="huanyipi" @click="methods.huanyipi(state.activeindex)">
          <i class="iconfont icon-reset2"></i>换一批
        </div>
      </div>
    </div>
    <div class="rightSide">
      <rightSide></rightSide>
    </div>
    <div class="clear"></div>
    <div class="news-list">
      <el-space wrap :size="15">
        <NewsList
          v-for="(item, index) in state.newsList"
          :key="index"
          :title="item.title"
          :list="item.list"
          :url="item.url"
        />
      </el-space>
    </div>
  </div>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  onMounted,
  watch,
  ref,
  onBeforeMount,
  onUpdated,
  computed,
} from "vue";
import Carousel from "@/components/Carousel.vue";
import rightSide from "@/components/rightSide.vue";
import Position from "@/components/Position.vue";
import NewsList from "@/components/NewsList.vue";
import { getCareer, getNewsList, NewViewMe, myInfo } from "@/http/api";
import { getPositionRecommend ,getPositionRecommendByCaree} from "@/http/mApiUrl";
import { useStore } from "vuex";
import { getCookies } from "../utils/common";
export default defineComponent({
  name: "index",
  components: { Carousel, rightSide, Position, NewsList },
  setup() {
    const store = useStore();
    const state = reactive({
      activeName: "0",
      activeindex: 0,
      
      // positionRecommend: [
      //   {
      //     label: "推荐",
      //     name: "0",
      //     parameter: {
      //       districtId: 0,//地市
      //       positionCareeID: 0,
      //       page: 1,
      //       pagesize: 12,
      //     },
      //     list: [],
      //     message: "",
      //   },
      // ],
      positionRecommend: [],
      newsList: [
        {
          title: "职场风云",
          list: [],
          url: "https://news.gxrc.com/Article/List/75",
        },
        {
          title: "简历制作",
          list: [],
          url: "https://news.gxrc.com/Article/List/81",
        },
        {
          title: "成功面试",
          list: {},
          url: "https://news.gxrc.com/Article/List/82",
        },
      ],
      loading: false,
      isViewMe: false,
      hasQueren: false,
    });
    let districtID = computed(() => {
      return getCookies("bid");
    });
    onBeforeMount(() => {
      //methods.getUserInfo();
      methods.getgetCareerData(store.state.userInfo.defaultResumeId);
      methods.getNewsListData();
    });
    watch(
      () => store.state.hasQueren,
      (newValue, oldValue) => {
        state.hasQueren=newValue
      }
    );

    watch(
      () => store.state.adList,
      (newValue, oldValue) => {
        if (newValue.ad1 != null) {
          state.adList1 = newValue.ad1.length < 1 ? null : newValue.ad1;
        } else {
          state.adList1 = newValue.ad1;
        }
        if (newValue.ad2 != null) {
          state.adList2 = newValue.ad2.length < 1 ? null : newValue.ad2;
        } else {
          state.adList2 = newValue.ad2;
        }
      }
    );
    watch(()=> store.state.userInfo,(val)=>{
      if(val.defaultResumeId) methods.getgetCareerData(val.defaultResumeId);
    })
    const userInfo = computed(()=> store.state.userInfo)

    const methods = {
      async getPositionRecommendData(index: number, CareeID: any) {
        state.loading = true;
        let Arr = {
          districtId: districtID.value,
          positionCareeID: CareeID,
          page: state.positionRecommend[index].parameter.page||1,
          pagesize: state.positionRecommend[index].parameter.pagesize||20,
          payment:[] 
        };
        state.positionRecommend[index].parameter = Arr;
        const res = await getPositionRecommendByCaree(Arr);

        state.positionRecommend[index].list = res.data;
        state.loading = false;

        if (state.positionRecommend[index].list == "") {
          if (CareeID > 0) {
            methods.getPositionRecommendData(index, 0);
          }
        }
        if (state.positionRecommend[index].list == "") {
          state.positionRecommend[index].message = "没有数据";
        } else {
          if (state.positionRecommend[index].message == null) {
            state.positionRecommend[index].message = "";
          } else {
            state.positionRecommend[index].message = res.message;
          }
        }
      },
      async getgetCareerData(resumeid: number) {
        const res = await getCareer({ resumeid: resumeid });
        if (res.data.expectCareer1Name && res.data.expectCareer1Name != "") {
          let arr = {
            label: res.data.expectCareer1Name,
            name: "0",
            parameter: {
              districtId: districtID.value,
              positionCareeID: res.data.expectCareer1,
              page: 1,
              pagesize: 12,
            },
            list: [],
            message: "",
          };
          state.positionRecommend.push(arr);
        }
        if (res.data.expectCareer2Name && res.data.expectCareer2Name != "") {
          let arr = {
            label: res.data.expectCareer2Name,
            name: "1",
            parameter: {
              districtId: districtID.value,
              positionCareeID: res.data.expectCareer2,
              page: 1,
              pagesize: 12,
            },
            list: [],
            message: "",
          };
          state.positionRecommend.push(arr);
        }
        if (res.data.expectCareer3Name && res.data.expectCareer3Name != "") {
          let arr = {
            label: res.data.expectCareer3Name,
            name: "2",
            parameter: {
              districtId: districtID.value,
              positionCareeID: res.data.expectCareer3,
              page: 1,
              pagesize: 12,
            },
            list: [],
            message: "",
          };
          state.positionRecommend.push(arr);
        }
        if(res.data.expectCareer1&&res.data.expectCareer1Name){
          methods.getPositionRecommendData(0, res.data.expectCareer1);
        }
        
      },
      async getNewsListData() {
        const res = await getNewsList();

        state.newsList[0].list = res.data.job;
        state.newsList[1].list = res.data.resume;
        state.newsList[2].list = res.data.interview;
      },
      async getNewViewMe() {
        const res = await NewViewMe(); //一旦点击，就让红点消失
        userInfo.isNewViewMe = false;
      },
      huanyipi(index: number) {
        if (
          state.positionRecommend[index].list.length <
          state.positionRecommend[index].parameter.pagesize
        ) {
          state.positionRecommend[index].parameter.page = 1;
        } else {
          state.positionRecommend[index].parameter.page += 1;
        }
        methods.getPositionRecommendData(
          index,
          state.positionRecommend[index].parameter.positionCareeID
        );
      },
      handleClick(tab: any, event: any) {
        let index = tab.props.name * 1;
        methods.getPositionRecommendData(
          index,
          state.positionRecommend[index].parameter.positionCareeID
        );
        state.activeindex = index;
      },
      popVip() {
        let a = store.state.showVipPOP ? false : true;
        store.commit("setVipPOP", a);
      },
    };
    return {
      state,
      methods,
      districtID,
      userInfo
    };
  },
});
</script>

<style lang="less">
.index-mn {
  .middle {
    width: 681px;
    float: left;
    .Default-picture {
      cursor: pointer;
    }
  }
  .cell {
    padding: 20px 0;
    border-bottom: 1px solid #f2f2f2;
    width: 100%;
    background: #fff;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    li {
      text-align: center;
      width: 25%;
      float: left;
      position: relative;
      .el-divider {
        height: 32px;
        background-color: #f2f2f2;
        position: absolute;
        right: 0px;
        top: 25px;
      }
    }
    span.elbadge {
      position: relative;
      .el-badge__content.is-fixed.is-dot {
        right: 0px;
        top: 5px;
      }
    }
    a {
      display: block;
    }
    p {
      font-size: 14px;
      color: #999999;
      padding: 0 0 10px 0;
    }
    span {
      font-size: 34px;
      color: #457ccf;
      font-family: "微软雅黑";
    }
  }
  .tabs-wrap {
    position: relative;
    .el-tabs__nav-wrap {
      background: #fff;
      height: 59px;
      line-height: 59px;
      padding: 0 15px 0 24px;
      border-bottom-left-radius: 2px;
      border-bottom-right-radius: 2px;
    }
    .el-tabs__nav-wrap::after {
      height: 0;
    }
    .el-tabs__active-bar {
      background-color: #457ccf;
      height: 3px;
    }
    .el-tabs__nav {
      height: 59px;
    }
    .el-tabs__item {
      max-width: 135px;
      height: 59px;
      line-height: 59px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      padding: 0 8px;
    }
    .el-tabs__item.is-active,
    .el-tabs__item:hover {
      color: #457ccf;
    }
    .btn-add {
      height: 29px;
      line-height: 29px;
      padding: 0 0;
      color: #457ccf;
      position: absolute;
      right: 130px;
      top: 15px;
      z-index: 9;
      display: inline-block;
      border-radius: 20px;
      font-size: 14px;
      i {
        font-weight: bold;
        font-size: 12px;
      }
      i.icon-warn {
        color: #fdaa08;
      }
    }
    .list-box {
      width: 680px;
      overflow: hidden;
      .el-space .el-space__item {
        margin-bottom: 12px;
        .pos-li {
          width: 304px;
        }
      }
      .el-space .el-space__item:nth-child(2n) {
        margin-left: 12px;
      }
    }
    .huanyipi {
      width: 96px;
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      cursor: pointer;
      color: #5f9efc;
      text-align: center;
      position: absolute;
      top: 14px;
      right: 15px;
      background: #f0f7fc;
      border-radius: 15px;
      i {
        font-size: 13px;
        margin-right: 5px;
      }
    }
    .tag-box {
      .el-tag--plain.el-tag--info {
        background-color: #fff;
        border-color: #eee;
        color: #666;
        font-size: 12px;
        border-radius: 2px;
        margin-right: 5px;
      }
    }
    .noList {
      background: #fff;
      padding: 20px 0;
      margin-bottom: 12px;
      .box {
        margin: auto;
        width: 260px;
      }
      .noImg {
        width: 94px;
        height: 44px;
        background: url(@/assets/img/nonejob_img.png) no-repeat center center;
        padding-right: 10px;
        float: left;
      }
      .doc {
        p {
          font-size: 14px;
          color: #333;
          font-weight: bold;
        }
        a {
          font-size: 12px;
          color: #5f9efc;
        }
        i.iconfont {
          font-size: 12px;
        }
      }
    }
  }
  .el-pagination {
    text-align: center;
    padding: 20px 5px;
  }
  .el-pagination.is-background .btn-next,
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li {
    background-color: #fff;
  }
  .rightSide {
    width: 300px;
    float: right;
  }
  .news-list {
    padding-top: 20px;
    width: 1020px;
  }
}
</style>
