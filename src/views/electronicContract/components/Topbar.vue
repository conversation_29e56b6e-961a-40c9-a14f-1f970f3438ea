<template>
 <div class="topbar"><h3 v-html="props.title"></h3><div class="func"><slot></slot></div></div>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount } from "vue";
export default defineComponent({
    props:['title'],
  setup(props) {
    const state = reactive({
      
    })

  onBeforeMount(() => {
    method.getCascaderData()
  })

    const method = {
      async getCascaderData(){
        
      }
    }
   

    return {
      ...method,
      ...toRefs(state),
      props
    }
  }
})

</script>
<style lang="less">
.topbar{
  .el-button{width:120px;
    margin-left:25px;
  }
  }
</style>
<style lang="less" scoped>
.topbar{text-align: left;font-size: 16px;padding-bottom: 20px;display:flex;
  h3{flex:1;font-weight: normal;}
  .func{flex:1;text-align: right;
  }
  }
</style>