<template>
 <div id="date" class="date-wrap" v-show="dateShow">
<el-popover popper-class="date-popover" placement="top" :width="90" trigger="hover" :show-arrow="false">
      <template #reference>
        <div class="date">{{nowDate}}</div>
      </template>
       <div class="tool clearfix">
          <div class="icon edit" @click="editDateFormat()"><i class="iconfont icon-bianji2"></i></div>
          <div class="vl">|</div>
          <div class="icon delete" @click="deleteDate()"><i class="iconfont icon-shanchu"></i></div>
      <ul class="date-format" v-show="editDateShow">
        <li>选择日期格式</li>
        <li v-for="(item,index) in dateFormat" :key="index" @click="chooseDateFormat(index)">{{item}}</li>
      </ul>
      </div>
    </el-popover>
    </div>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount,onMounted } from "vue";
import useMove from "../hooks/useMove";
export default defineComponent({
    props:[''],
  setup(props) {
    const state = reactive({
      nowDate:'2022-03-03',
      dateFormat:[],
      editDateShow:false,
      dateShow:true
    })

onMounted(() => {
      const el = document.getElementById("date");
      useMove(el);
    });

    const method = {
      insertDate(){
        state.dateShow=true
      },
      deleteDate(){
        state.dateShow=false
      },
      initDateFormat(){
        if(state.dateFormat==''){
          let date=new Date()
          let year=date.getFullYear()
          let month=(date.getMonth()+1).toString().padStart(2,'0')
          let day=date.getDate().toString().padStart(2,'0')

          state.dateFormat=[]
          let str=`${year}/${month}/${day}`
          state.dateFormat.push(str)
          str=`${year}-${month}-${day}`
          state.dateFormat.push(str)
          str=`${year}年${month}月${day}日`
          state.dateFormat.push(str)
        }
      },
      editDateFormat(){
        method.initDateFormat()
        state.editDateShow=!state.editDateShow
      },
      chooseDateFormat(index:any){
        state.nowDate=state.dateFormat[index]
        state.editDateShow=false
      },
    }
   

    return {
      ...method,
      ...toRefs(state),
      props
    }
  }
})

</script>
<style lang="less">
.date-popover.el-popover.el-popper{padding: 0;background: none;box-shadow:none;border: none;}
</style>
<style lang="less" scoped>
.clearfix:after{content:"";display:block;height:0;clear:both}
ul,li{margin: 0; padding: 0;list-style: none;}
.date-wrap{color: #575962;text-align: center;position: absolute;top:100px;left:100px;
  .date{width: 160px;height: 50px;line-height: 50px;font-size: 18px; border: 1px dashed #e61717;background: rgba(255,217,217,.46);cursor: move;}
  }
  .tool{width:90px;height: 32px;line-height: 32px;border-radius: 2px;background: rgba(0,0,0,0.6);color: #fff;text-align: center;margin:0 auto;
div{height: 32px;line-height: 32px;box-sizing: border-box;}
  .edit{float: left;padding-left:8px;}
  .delete{float: right;padding-right:8px;}
  .icon{width:40px;cursor: pointer;
  .iconfont{font-size: 14px;}}
  .vl{float: left;color: #575962;padding-left:2px;}
  .date-format{width: 130px; text-align: left;background: #fff;color: #666;font-size: 12px; position: absolute;top:20px;left:56px;
  li{padding-left: 10px;cursor: default;}
  li:first-child{color: #999;}
  li:not(:first-child):hover{background:#f1f1f1;}}
  }
</style>