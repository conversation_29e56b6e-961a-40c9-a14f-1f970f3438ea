<template>
  <el-upload 
  ref="upload" 
  class="ele-upload-demo" 
  :before-upload="beforeUpload" 
  :accept="type" 
  :limit="1" 
  :action="''"
  :file-list="fileList" 
  :on-remove="handleRemove" 
  list-type="picture">
  <i class="el-icon-upload"></i>
  <div class="el-upload__text"><em>点击上传文件</em></div>
  <div class="el-upload__tip">每次最多可上传<span class="red">1</span>份文件，只能上传{{type}}格式的文件</div>
</el-upload>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount } from "vue";
import { ElMessage, ElMessageBox } from 'element-plus'
export default defineComponent({
  props:['type'],
  setup(props,{ emit }: any) {
    interface RawFile {
      name: string
      url: string
    }
    const upload = ref(null)
    const state = reactive({
      fileList: ref<RawFile[]>([]),
    })

  onBeforeMount(() => {
    
  })

    const methods = {
      async beforeUpload(file:any) {
        const param = new FormData();
          param.append("file", file);

        if(props.type.indexOf('doc')>-1){
          
        }else if(props.type.indexOf('pdf')>-1){
          state.fileList.push({name:file.name,url: 'https://image.gxrc.com/gxrcsite/vip/dzht/pdf.png'})
          emit('callbackFile',param);
        }
    },
      handleRemove(file: UploadFile, fileList: UploadFile[]){
        state.fileList=fileList
      },
    }
   

    return {
      ...methods,
      ...toRefs(state),
      ...toRefs(props),
      upload
    }
  }
})

</script>
<style lang="less">
.ele-upload-demo {
.el-upload,.el-upload-dragger{width: 100%;}
 .el-upload{
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    box-sizing: border-box;
    width:  100%;
    height: 180px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
   .el-icon-upload {
    font-size: 67px;
    color: #c0c4cc;
    margin: 40px 0 16px;
    line-height: 50px;
}
.el-upload__text {
    color: #606266;
    font-size: 14px;
    text-align: center;
  em {
    color: #374dc3;
    font-style: normal;
}
}
.el-upload__tip {
    font-size: 12px;
    color: #606266;
    margin-top: 7px;
}
}
.el-upload:hover {
    border-color: #374dc3;
}
.el-upload-list__item-name{text-align: left;}
}
</style>
<style lang="less" scoped>
.ele-upload-demo {width: 100%;line-height:20px;}
.red{color:red;padding:0 2px;font-size:16px;}
</style>