<template>

<el-dialog custom-class="auth-box" v-model="showAuthBox.value" title="系统提示" width="30%" center>
    <div class="tips">
      <div class="icon"></div>
      <p>请先完成实名认证</p>
 </div>
    <template #footer>
      <span class="dialog-footer btn-wrap">
        <el-button type="primary" @click="goAuth">实名认证</el-button>
        <el-button @click="closeAuthBox">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount,onMounted } from "vue";
import { useRouter } from 'vue-router'

export default defineComponent({
    props:['showAuthBox'],
    emits: ['closeAuthBox'],
  setup(props: any, { emit }) {
    const router = useRouter();
    const state = reactive({
    })

onMounted(() => {
      
    });

    const method = {
      goAuth(){
        router.push({ name: 'certified' });
      },
      closeAuthBox(){
        emit('closeAuthBox', false)
      }
    }
   

    return {
      ...method,
      ...toRefs(state),
      props
    }
  }
})

</script>
<style lang="less">
.auth-box{
  .el-dialog__footer {
    text-align: center;
}
.dialog-footer{display: block;}
}
</style>
<style lang="less" scoped>
.auth-box{
  .tips{text-align: center;
    .icon{height:100px;background:url(https://image.gxrc.com/gxrcsite/vip/dzht/warn.png) no-repeat center top;}
    p{font-size: 16px;}
  }
}
</style>