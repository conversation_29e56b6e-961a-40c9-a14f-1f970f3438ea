<template>
  <div class="certified-left-con">
    <ul>
      <li v-for="(item,index) in data" :key='index' :class="item.link.indexOf(route.name)>-1?'current':''">
        <router-link :to="item.link">
          <div class="icon"><i class="iconfont icon-shenqing"></i></div>
          <div class="info">
          {{item.name}}
          <p>{{item.des}}</p>
          </div>
        </router-link>
        <div class="arrow"></div>
      </li>
    </ul>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRef, toRefs, ref, watch, computed, getCurrentInstance, onBeforeMount } from "vue";
import { useRoute } from "vue-router";
export default defineComponent({
  setup() {
    const state = reactive({
      data:[
      {name:'个人认证',des:'个人刷脸3分钟完成认证',link:'./corporate'}
      ]
    })
    const route = useRoute()

    onBeforeMount(() => {
      
    })

    const method = {
      
    }


    return {
      ...method,
      ...toRefs(state),
      route
    }
  }
})

</script>
<style lang="less" scoped>
.certified-left-con {
  text-align: left;
  padding: 30px;
  ul,li{list-style: none;padding: 0;margin: 0;}
  li {padding: 20px 15px;margin-bottom: 30px;
    a{text-decoration: none;color: #333;display: flex;
    .icon{flex:1;padding-top:18px;
    .iconfont{font-size: 24px;}
    }
    .info{flex:4;
      font-size: 18px;
      p{font-size: 14px;color: #ccc;padding-top: 10px;line-height: 22px;}
    }
    
    }
    
  }
  li.current{background: #F4F6F9;position: relative;
  .iconfont{color: #4b71f0;}
    .arrow{width: 0;height: 0;border: 16px solid transparent;border-left:18px solid #F4F6F9;position: absolute;top:40px;right:-30px;}
  }
}
</style>