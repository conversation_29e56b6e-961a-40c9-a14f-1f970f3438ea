<template>
  <div :id="finalPosition.signId" class="sign-wrap"
    :style="{ top: finalPosition.y + 'px', left: finalPosition.x + 'px' }" v-show="signShow" ondragstart="return false">
    <el-image :src="props.signSrc" fit="fill"></el-image>
    <div class="delete" @click="deleteSign">×</div>
  </div>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  toRef,
  toRefs,
  ref,
  watch,
  computed,
  getCurrentInstance,
  onBeforeMount,
  onMounted,
} from 'vue';
export default defineComponent({
  props: ['id', 'page', "signSrc", 'x', 'y', 'parentDiv'],
  setup(props, { emit }: any) {
    const state = reactive({
      signShow: true,
      finalPosition: { signId: props.id, page: props.page, x: props.x, y: props.y, realx: 0, realy: 0 },
    });
    onMounted(() => {
      const el = document.getElementById(props.id);
      if (el) {
        method.useMove(el);
      }
    });

    watch(() => props.x, (val) => {
      state.finalPosition.x = val
    }
    );
    watch(() => props.y, (val) => {
      state.finalPosition.y = val
    }
    );
    watch(() => props.id, (val) => {
      state.finalPosition.signId = val
    }
    );

    const method = {
      useMove(el: any) {
        el.style.position = 'absolute';
        let elH = el.clientHeight + 30,
          elW = el.clientWidth;

        let offsetX: number,
          offsetY: number,
          oL: number,
          oT: number,
          oLeft: number,
          oTop: number;

        if (el != null) {
          el.addEventListener('mousedown', function (event: any) {
            if (event.button == 0 && el != null) {
              const lexObj: any = getComputedStyle(el);
              offsetX = event.pageX - el.offsetLeft + parseInt(lexObj['margin-left']);
              offsetY = event.pageY - el.offsetTop + parseInt(lexObj['margin-right']);
              const move = function (event: any) {
                if (el != null) {
                  let x = event.pageX - offsetX;
                  let y = event.pageY - offsetY;

                  if (x < 0) {
                    x = 0;
                  } else if (x > props.parentDiv.w - elW) {
                    x = props.parentDiv.w - elW;
                  }

                  if (y < 0) {
                    y = 0;
                  } else if (y > props.parentDiv.h - elH) {
                    y = props.parentDiv.h - elH;
                  }

                  el.style.left = x + 'px';
                  el.style.top = y + 'px';
                  state.finalPosition.x = x;
                  state.finalPosition.y = y;
                  state.finalPosition.realx = x;
                  state.finalPosition.realy = props.parentDiv.h - elH - y;

                  emit('getFinalPosition', state.finalPosition);

                }
                return false;
              };
              document.addEventListener('mousemove', move);
              const stop = function () {
                document.removeEventListener('mousemove', move);
                document.removeEventListener('mouseup', stop);
              };
              document.addEventListener('mouseup', stop);
            }
            return false;
          });
        }
      },
      deleteSign() {
        emit('deleteSign', state.finalPosition);
      },
    };

    return {
      ...method,
      ...toRefs(state),
      props,
    };
  },
});
</script>

<style lang="less" scoped>
.sign-wrap {
  width: 118px;
  height: 118px;
  background: rgba(217, 237, 255, 0.5);
  border: 1px dashed rgba(115, 190, 255, 0.5);
  position: absolute;
  top: 0px;
  left: 0px;
  cursor: move;

  .delete {
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 26px;
    text-align: center;
    font-weight: normal;
    color: #73BEE4;
    border: 1px solid rgba(115, 190, 255, 0.8);
    border-radius: 50%;
    cursor: pointer;
    position: absolute;
    top: -10px;
    right: -10px;
  }
}
</style>
