<template>
  <div class="certified-form">
   <el-form
    ref="ruleForm"
    :model="state.ruleForm"
    :rules="state.rules"
    label-width="130px"
    class="demo-ruleForm"
  >

      <div class="title">
        个人信息填写
      </div>

    <el-form-item label="个人姓名" prop="name">
      <el-input v-model="state.ruleForm.name"></el-input>
    </el-form-item>

    <el-form-item label="身份证号码：" prop="idNumber">
      <el-input v-model="state.ruleForm.idNumber"></el-input>
    </el-form-item>

    <el-form-item prop="checkbox">
        <el-checkbox v-model="state.ruleForm.checkbox" label="" name="checkbox">我已阅读并同意<span class="xieyi" @click="seeXieyi">《实名认证授权协议》</span></el-checkbox>
    </el-form-item>
  
     <el-form-item class="btn-wrap">
      <el-button type="primary" class="btn-submit" @click="submitForm('ruleForm')">下一步</el-button>
      <el-button @click="resetForm('ruleForm')">取消</el-button>
    </el-form-item>
  
  </el-form>

  <el-dialog v-model="state.dialogXieyiVisible" title="实名认证授权协议">
    <div class="xieyi-con">
      <p>认证服务协议（以下简称“本协议”）是广西人才网科技有限公司（以下简称“广西人才网”或“我们”）与用户（以下尊称“您”）就认证服务（以下简称“本服务”）的使用等相关事项所订立的有效合约。
</p>
<p>本协议有助于您理解我们为您提供的服务内容及您的权利和义务，请您仔细阅读。
</p>
<p>为便于您使用本服务，您通过网络页面点击或以其他形式确认本协议的，即视为您已充分理解本协议所有条款，同意与广西人才网订立本协议并接受本协议的约束。
</p>
<h3>一、定义
</h3>
<p>用户：是指使用广西人才网认证服务的主体，用户可以是个人，也可以是企业。
</p>
<p>认证服务：指广西人才网提供的，通过信息核验、人脸比对、校验码回填、人工审核等方式确认您身份的真实性，并将认证结果反馈给您或向您提供产品或服务的供应商（以下简称“供应商”）的服务。认证服务在广西人才网产品体系内的具体应用包括实名认证及意愿认证。
</p>
<p>实名认证：指广西人才网提供的，通过信息核验、人脸比对、人工审核等方式确认您身份的真实性，并将认证结果反馈给您或供应商的服务。
</p>
<p>意愿认证：指广西人才网提供的，通过人脸比对、校验码回填等方式确认用户在签署发生时身份的真实性，并将认证结果反馈给您或供应商的服务。用户在首次签署发生时尚未进行实名认证的，则仅需完成实名认证即可同时作为意愿认证的认证结果。
</p>
<p>认证结果：认证结果包括是否通过认证服务的结果，以及您提供的用来进行身份认证的个人信息或企业信息。个人信息和企业信息的具体范围根据您使用的认证方式确定。
</p>
<h3>二、授权与许可
</h3>
<p>2.1 为向您提供本服务，供应商需要将您在申请服务过程中所填写或留存的信息（例如：姓名、身份证号码、手机号码等）提供给广西人才网。
</p>
<p>2.2 为准确核验您的身份，尽量防止您的身份被冒用，您授权广西人才网采集您的设备信息以验证您在进行认证时是否处于可信环境，并根据不同的认证方式按需收集您的人脸照片、身份证件、银行卡号、手机号、组织机构统一社会信用代码、企业对公账户等信息。
</p>
<p>2.3 为准确验证您的身份，广西人才网需在必要时将前款载明的信息提供给合法存有您信息的第三方机构进行比对核验，以便广西人才网核验您的身份真实性。
</p>
<p>2.4 为便于供应商评估是否向您提供产品或服务，您授权供应商可向广西人才网调取广西人才网的认证结果。
</p>
<h3>三、您的权利义务
</h3>
<p>3.1 广西人才网会尽力提升核验结果的准确性，但由于广西人才网能收集到的信息范围有限，以及行业技术水平及核验手段仍在完善过程中，广西人才网无法确保核验结果完全准确无误。如您发现核验结果不准确，您可通过客服电话400-0771-056告知我们，以便我们重新核验并逐步提升核验能力。
</p>
<p>3.2 您不得将本服务用于中国法律法规所限制或禁止，以及违背道德风俗的领域，否则因此导致广西人才网或第三方遭受损失的，您应承担相应赔偿责任。
</p>
<p>3.3 如您发现我们收集、使用您个人信息的行为，违反了法律、行政法规规定或违反了与您的约定，或您发现我们采集、储存的您的个人信息有错误的，您可通过客服电话021-61984561告知我们，要求删除或更正。
</p>
<p>3.4 您应当知悉，本服务仅对个人或企业身份在实名阶段或意愿认证阶段的真实性进行校验。
</p>
<p>3.5您认可广西人才网通过本服务所提供的认证方式进行对您的身份进行认证，您应当妥善保存能够对您身份进行认证的信息、材料以及设备。如相关信息、材料或设备发生泄露、遗失等失去您控制的情形，您应当及时通知广西人才网，否则广西人才网有权利认定相应认证行为由您完成。
</p>
<h3>四、广西人才网的权利义务
</h3>
<p>4.1 广西人才网应按本协议约定向您提供本服务。
</p>
<p>4.2 广西人才网会根据认证技术的发展及市场风险环境的需要，不断调整完善相应认证服务的内容及形式。
</p>
<p>4.3 广西人才网提供认证服务所处理的个人信息仅限于履行认证服务所必须获取的信息，该情形符合《个人信息保护法》第十三条之规定，如您使用认证服务，则广西人才网有权处理您的个人信息。
</p>
<p>4.4 除根据法律法规规定及本协议对您的个人信息予以保护外，在处理您的信息时，我们将依据广西人才网《用户隐私政策》严格保护您的信息。请您在广西人才网官网仔细阅读该政策，如有任何疑问，请随时联系我们。
</p>
<p>4.5 向供应商提供您的认证结果如涉及向境外提供个人信息的情形，广西人才网应要求供应商根据《个人信息保护法》的规定处理。
</p>
<h3>五、数字证书服务
</h3>
<p>5.1 您的供应商购买广西人才网的认证服务仅用于身份认证用途的，广西人才网不会为您申请数字证书。
</p>
<p>5.2 您的供应商购买广西人才网的认证服务同时购买电子签名服务的，广西人才网将在您完成实名认证后为您申请数字证书，并在您签署时为您调用数字证书。您使用广西人才网的电子签名服务视为您同意授权广西人才网为您生成并调用数字证书，否则您应当停止使用广西人才网的电子签名服务。
</p>
<p>5.3 我们将依据广西人才网《数字证书服务协议》向您提供数字证书服务。请您在广西人才网官网仔细阅读该协议，如有任何疑问，请随时联系我们。
</p>
<h3>六、除外责任
</h3>
<p>您理解并同意，鉴于计算机、互联网的特殊性，下述情况不属于广西人才网违约：
</p>
<p>1）因自然灾害如洪水、地震、瘟疫流行等以及社会事件如战争、动乱、政府行为、电信主干线路中断、网路堵塞、电信部门技术调整和政府管制等导致本服务不能提供或中断；
</p>
<p>2）黑客攻击造成服务中断、信息数据泄露；但我们应按照本协议约定做好应急处理。
</p>
<p>3）广西人才网在进行服务器配置、维护时，需要短时间中断服务；
</p>
<p>4）由于Internet上的通路阻塞造成您网站访问速度下降；
</p>
<p>5）因国家法律法规及政策的相关要求，中断或终止本服务。
</p>
<h3>七、协议变更
</h3>
<p>为了满足您多样化和专业化的服务需求，我们将根据国家法律、法规及其他规范性文件及业务需要，可能 增加、变更服务内容或服务方式，我们可能对本协议内容进行变更，并以在广西人才网官方网站 （www.gxrc.com）发布公告的方式提前予以公布。若您不接受本协议的变更，您有权自主决定选择停止使用本服务。若您在公告约定的生效时间后继续使用本服务的，表示您已充分阅读、理解并接受变更修改后的协议内容。
</p>
<h3>八、适用法律及管辖条款</h3>

<p>本协议适用中华人民共和国法律（仅为本协议之目的，不包括香港特别行政区、澳门特别行政区及台湾地区法律）。因广西人才网与您就本协议的签订、履行或解释发生争议，双方应努力友好协商解决。如协商不成的，广西人才网和您均同意向被告住所地人民法院提起诉讼。 
</p>
      </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button class="btn-confirm" type="primary" @click="confirmXieyiBox">我已阅读并同意</el-button>
      </div>
    </template>
  </el-dialog>

  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRef, toRefs, ref, watch, computed, getCurrentInstance, onBeforeMount } from "vue";
import { useRouter } from 'vue-router'
import { useStore } from 'vuex';
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAuthInfo,postAuthAdd,postAuthLegalfaceqrcode } from "@/http/dzhtApi";
export default defineComponent({
  setup(props,{emit}:any) {
    const ruleForm = ref(null)
    const router = useRouter()
    const store = useStore();

    onBeforeMount(() => {
      method.getAuthStatus()
     })

    const method = {
      async getAuthStatus(){
        let data={}
        if(store.state.accountAuth.authStatus!=undefined){
          data =store.state.accountAuth
        }else{
          const result = await getAuthInfo();
          data=result.data
          store.commit('setAccountAuth',data)
        }

        if(data.name!=''){
          state.ruleForm.name=data.name
        }
        if(data.idNumber!=''){
          state.ruleForm.idNumber=data.idNumber
        }
      },
      seeXieyi(){
        state.dialogXieyiVisible = true
      },
      confirmXieyiBox(){
        state.ruleForm.checkbox=true
        method.closeXieyiBox()
      },
      closeXieyiBox(){
        state.dialogXieyiVisible = false
      },
      submitForm(formName){
        ruleForm.value.validate((valid) => {
            if (valid) {
              state.parameter.name=state.ruleForm.name
              state.parameter.idNumber=state.ruleForm.idNumber

              postAuthLegalfaceqrcode(state.parameter).then((result:any)=>{
                if (result.code == 1) {
                  emit('certifiedSuccessImg',result.data);
                }else{
                  ElMessage.warning(result.message)
                }
              })
          } else {
              return false
            }
          })
      },
      resetForm(formName) {
        ruleForm.value.resetFields()
        router.push({ name: `certified` })
      },
    validateName(rule, value, callback){
      if (value === '') {
        callback(new Error('请输入个人姓名'))
      } else {
        if (value.trim() !== ''&&value.length>10) {
          callback(new Error('不能超过10字'))
        }
        callback()
      }
    },
    validateIdNumber(rule, value, callback){
      if (value === '') {
        callback(new Error('请输入身份证号码'))
      } else {
        if (value.trim() !== ''&&value.length>18) {
          callback(new Error('不能超过18位数'))
        }
        callback()
      }
    },
    validateCheckbox(rule, value, callback){
      if (value == '') {
        callback(new Error('请阅读并同意《实名认证授权协议》'))
      } else {
        callback()
      }
    },
    }
    
    const state = reactive({
      dialogXieyiVisible:false,
      parameter:{
  "name": "",
  "idNumber": "",
  "from": "0"
},
      ruleForm:{
        name:'',
        idNumber:'',
        checkbox: false,
      },
      rules: {
        name: [{ validator: method.validateName, trigger: 'blur' }],
        idNumber: [{ validator: method.validateIdNumber, trigger: 'blur' }],
        checkbox: [{ validator: method.validateCheckbox, trigger: 'change' }],
      },
    })

    return {
      ...method,
      state,
      props,
      ruleForm
    }
  }
})

</script>
<style lang="less">
.certified-form {
.el-dialog__header{text-align:center;}
.el-dialog__footer{text-align:center;
.btn-confirm{width:160px;}}
}
</style>
<style lang="less" scoped>
.certified-form {
  padding:20px 40px;
  .title{font-size: 18px;padding: 20px 0;}
  .xieyi{color:cornflowerblue;cursor: pointer;}
  .btn-wrap{padding-top: 30px;}
  .btn-submit{width:130px;}
  .xieyi-con{height:350px;line-height:26px; overflow-y:auto;border:1px solid #ddd;padding:20px;
  h3,p{padding-bottom:15px;}
  p{text-indent:2em;}}
}
</style>