<template>
  <div class="dzht-content">
  <div class="certified-children">
    <div class="leftbar box-shadow">
      <LeftMenu></LeftMenu>
    </div>
     <div class="center"></div>
    <div class="right-wrap box-shadow">
    <div class="top-title"><h3>个人授权认证</h3></div>
    <div class="con-wrap">
      <router-view></router-view>
</div>
    </div>
  </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRef, toRefs, ref, watch, computed, getCurrentInstance, onBeforeMount } from "vue";
import LeftMenu from "../components/CertifiedLeftMenu.vue";
import { useRoute } from "vue-router";
export default defineComponent({
  components:{
    LeftMenu
  },
  setup() {
    const state = reactive({

    })
    const route = useRoute()

    onBeforeMount(() => {
    })

    const method = {
    }


    return {
      ...method,
      ...toRefs(state),
      route
    }
  }
})

</script>
<style lang="less" scoped>
.certified-children{
  text-align: left;
  width: 100%;
  display: flex;
.box-shadow{background: #fff;box-shadow: 0 0 8px #ddd;}
.leftbar{
  flex: 2;
}
.center{width:20px;background:	#F5F5F5;}
.right-wrap{
  flex: 5;padding:30px;
.top-title{height: 42px;line-height: 40px; text-align: left;font-size: 16px;border-bottom:1px solid #eaeaea;
h3{float: left;line-height: 40px;border-bottom:2px solid #4b71f0;padding: 0 20px;}}
.con-wrap{padding-top: 20px;}
}
}
</style>