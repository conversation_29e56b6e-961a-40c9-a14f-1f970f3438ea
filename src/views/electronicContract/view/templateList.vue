<template>
  <div class="dzht-content">
  <div class="template-list" v-if="route.name == 'templateList'">
    <Topbar title="模板列表"></Topbar>
<div class="con-wrap">
<ul>
  <li>连带保证契约书<span>+</span></li>
</ul>
</div>
  </div>
  <router-view v-else></router-view>
  </div>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount } from "vue";
import { useRoute } from "vue-router";
import Topbar from "../components/Topbar.vue";
export default defineComponent({
   components:{
    Topbar
  },
  setup() {
    const route = useRoute()
    const state = reactive({
      list:[
        {"code":"3434234234234","name":"体验签署电子合同（无需实名认证）","state":"正常","code2":"15177169240","date":"2021-09-07 16:50","type":"企业"},
      ]
    })

  onBeforeMount(() => {
    method.getCascaderData()
  })

    const method = {
      async getCascaderData(){
        
      }
    }
   

    return {
      ...method,
      ...toRefs(state),
      route
    }
  }
})

</script>

<style lang="less" scoped>
.template-list{padding:30px;
.con-wrap{
  ul,li{list-style: none;padding: 0;}
  li{height: 63px;line-height:63px;background:rgb(247,248,250);border-radius:3px;padding: 0 30px;text-align:left;
    span{float: right;color: #1787FB;}
  }
  
}
}
</style>