<template>
  <div class="dzht-content">
  <div class="certified-children-right-con">
   <CertifiedForm v-if="!erweimaShow" @certifiedSuccessImg="certifiedSuccessImg"></CertifiedForm>
   <div v-else class="erweima-wrap">
     <h3>第一步：扫码认证</h3>
      <div class="erweima">
     <el-image :src="erweima" fit="fill"></el-image>
     <p>请打开微信扫一扫，进行识别认证</p>
   </div>
   <h3>第二步：认证结果</h3>
   <div class="btn-wrap">
    <p><el-button type="primary" class="btn-submit" @click="goback">已认证成功，点击获取认证结果</el-button></p>
    <p><el-button @click="goback">认证失败，返回</el-button></p>
   </div>
   </div>
  </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRef, toRefs, ref, watch, computed, getCurrentInstance, onBeforeMount } from "vue";
import { useRoute,useRouter } from 'vue-router';
import CertifiedForm from "../components/CertifiedForm.vue";
export default defineComponent({
   components:{
    CertifiedForm
  },
  setup() {
    const {proxy}:any = getCurrentInstance()
    const router = useRouter();
const state = reactive({
  erweima:'https://image.gxrc.com/gxrcsite/global/app_code.png',
  erweimaShow:false,
    })
    onBeforeMount(() => {
    })

    const method = {
      certifiedSuccessImg(value){
        if(value!=null){
          state.erweimaShow=true
          state.erweima=`data:image/png;base64,${value}`
        }
      },
      goback(){
        router.push({ name: 'certified' });
      }
    }


    return {
      ...method,
      ...toRefs(state),
    }
  }
})

</script>

<style lang="less" scoped>
.certified-children-right-con {
  .erweima-wrap{text-align:center;
  h3{line-height:40px;padding:0 15px; text-align:left;background:#F4F6F9;margin-bottom:30px;font-size:16px;}
    .erweima{padding-bottom:50px;
      .el-image{width:200px;}
    }
    .btn-wrap{
      p{padding-bottom:30px;
      .el-button{width:260px;}}
    }
  }
}
</style>