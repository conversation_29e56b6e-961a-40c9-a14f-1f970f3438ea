<template>
  <div class="dzht-content">
  <div class="template-sign">
<Topbar title="添加印章">
        <el-button class="btn-data" type="info" plain @click="goBack">&lt; 返回签名列表</el-button>
    </Topbar>
<div class="con-wrap">
<div class="preview">
<div class="kuang">

</div>
</div>
<div class="con details">
  <div class="form-wrap">
<div class="label">名称：</div>
<div class="label-con"><el-input v-model="input" placeholder="必填，便于后期管理，支持2-15个字符" /></div>
<div class="label">创建方式：</div>
<div class="label-con"><el-radio v-model="radio1" label="1" size="large">使用模板</el-radio></div>
<div class="label">横向文：</div>
<div class="label-con"><el-input v-model="input" placeholder="请输入横向文，如 合同专用章" /></div>
<div class="label">下弦文：</div>
<div class="label-con"><el-input v-model="input" placeholder="请输入下弦文，一般为数字" /></div>
<div class="label">颜色：</div>
<div class="label-con"></div>
<div class="label">印章样式：</div>
<div class="label-con"></div>
  </div>
  <div class="btn-wrap">
  <el-button type="primary"><i class="iconfont icon-fuzhi"></i>创建印章</el-button>
  <el-button type="info" @click="goBack"><i class="iconfont icon-fuzhi"></i>取消</el-button>
  </div>
  </div>

</div>
 </div>
  </div>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount } from "vue";
import Upload from "../components/Upload.vue";
import Topbar from "../components/Topbar.vue";
import { useRouter } from 'vue-router'
export default defineComponent({
   components:{
    Upload,Topbar
  },
  setup() {
    const state = reactive({
      input:'',
      radio1:ref('1')
    })
    const router = useRouter()

  onBeforeMount(() => {
  })

    const method = {
      goBack(){
        router.push({ name: 'sealManagement' })
      }
    }
   

    return {
      ...method,
      ...toRefs(state),
    }
  }
})

</script>
<style lang="less" scoped>
.template-sign {padding:30px;text-align: left;
    .con-wrap{display: flex;
      .preview{flex:1;
      .kuang{width: 297px;height: 408px;border: 1px solid #ddd;}}
      .details{flex:2;
      .form-wrap{
        .label{padding-bottom: 10px;font-size: 14px;}
        .label-con{padding-bottom: 30px;}
      }}
    }
    .btn-wrap{padding-top: 30px;}
}
</style>