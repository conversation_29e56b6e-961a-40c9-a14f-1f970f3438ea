<template>
  <div class="dzht-content">
  <div class="signed-inspection">
    <Topbar title="合同验签"></Topbar>
<div class="con-wrap" v-loading="loading">
<div class="tips">请在此处上传您的电子合同，点击【合同验签】按钮即可获得验签结果。</div>
<div class="pdf-upload">
<Upload :type="'.pdf'" @callbackFile="callbackFile"></Upload>
</div>
<div class="btn-wrap">
  <el-button type="primary" class="btn btn-yanqian" @click="signedInspection()">合同验签</el-button>
  <el-button class="btn btn-yanqian" @click="cancel">返回合同列表</el-button>
</div>
</div>

<el-dialog v-model="dialogResultVisible" :close-on-click-modal="false">
    <div class="yanqian-box">
      <div class="result">
      {{data.pdfModify==true?"该文件未被修改，验证通过":"合同验签失败"}}
</div>
<div v-if="data.pdfModify==true">
<div class="one">
  <table class="table">
        <thead>
          <tr><th class="tit" colspan="3">文件信息</th></tr>
        </thead>
        <tbody>
          <tr><td class="w1 label">文件名：</td><td class="w2">{{data.fileName}}</td><td class="w3" rowspan="3"><div class="img-success"></div></td></tr>
          <tr><td class="w1 label">签章数量：</td><td class="w2">{{data.num}}个</td></tr>
          <tr><td class="w1 label">验证结果：</td><td class="w2">{{data.pdfModify==true?"该文件未被修改过，验证通过":"合同验签失败"}}</td></tr>
        </tbody>
      </table>
</div>

<div class="one" v-for="(item,index) in data.signers" :key="index">
   <table class="table">
        <thead>
          <tr><th class="tit" colspan="3">签章详情</th></tr>
        </thead>
        <tbody>
          <tr><td class="w1 label">签署人：</td><td class="w2">{{item.signerName}}</td><td class="w3"><p>签名有效</p></td></tr>
          <tr><td class="w1 label">签署时间：</td><td class="w2">{{item.dateTime}}</td><td class="w3"><p>时间戳有效</p></td></tr>
          <tr><td class="w1 label">颁发机构：</td><td class="w2">{{item.issuingAuthority}}</td><td class="w3"><p>未被篡改</p></td></tr>
        </tbody>
      </table>
</div>
      </div>

    </div>
  </el-dialog>
</div>
  </div>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount } from "vue";
import { ElMessage, ElMessageBox } from 'element-plus'
import Topbar from "../components/Topbar.vue";
import { useRouter } from 'vue-router'
import Upload from "../components/Upload.vue";
import { postContractSignatureCheck} from "@/http/dzhtApi";
export default defineComponent({
  components:{
    Topbar,Upload
  },
  setup() {
    const state = reactive({
      file:'',
      loading:ref(false),
      dialogResultVisible:ref(false),
      data:{
    "fileName": "",
    "pdfModify": false,
    "num": 0,
    "signers": [
      {
        "signerName": "",
        "dateTime": "",
        "timeValidity": true,
        "isVerify": true
      }
    ]
  }
    })
    const router = useRouter()

  onBeforeMount(() => {
  })

    const method = {
      callbackFile(value){
        state.file=value
      },
      async signedInspection(){
        if(state.file!=''){
        state.loading=true
          const data = await postContractSignatureCheck(state.file)
        state.loading=false
          state.dialogResultVisible=true
          if (data&&data.code == 1) {
            state.data=data.data
          }else{
            state.data.pdfModify=false
          }
           
        }else{
          ElMessage.warning('请上传pdf合同文件')
        }
      },
      cancel(){
        router.push({ name: 'contractManagement' });
      },
    }
   

    return {
      ...method,
      ...toRefs(state),
    }
  }
})

</script>
<style lang="less" scoped>
.clearfix:after {
  content: '';
  display: block;
  height: 0;
  clear: both;
}
.signed-inspection {padding:30px;
.con-wrap{
  .tips{text-align: left;font-size: 14px;padding-bottom: 20px;}
  .pdf-upload{padding-bottom: 20px;}
  .btn{display:block; width:200px;margin:0 auto 20px;}
}
.yanqian-box{
  .result{text-align: center;font-size: 22px;font-weight: bold;color:#444; padding-bottom: 40px;}
  .one{text-align: left;font-size: 16px;
    padding: 15px 0;
    border-top: 1px solid #DDDDDD;padding-left:10px;
  }
  .table{width:100%;
      th,td{line-height:30px;font-size: 14px;}
      .tit{font-size: 16px;font-weight: bold;color: #2557A5;padding-bottom: 10px;}
      .w1 {width:80px;vertical-align: top;}
       .w3{width:225px;padding-left:40px;
          p{padding-left:28px;background:url(https://image.gxrc.com/gxrcsite/vip/dzht/yanqian-success.png) no-repeat left center;background-size:20px auto;}
      }
      .img-success{height:120px; background:url(https://image.gxrc.com/gxrcsite/vip/dzht/yanqian-res.png) no-repeat left center;background-size:auto 90px;}
      }
}
}
</style>