<template>
  <div class="dzht-content">
  <div class="add-sign">
    <Topbar title="添加手写签名">
        <el-button class="btn-data" type="info" plain @click="goBack">&lt; 返回签名列表</el-button>
    </Topbar>
<div class="con-wrap">

</div>
  </div>
  </div>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount } from "vue";
import Topbar from "../components/Topbar.vue";
import { useRouter } from 'vue-router'
export default defineComponent({
  components:{
    Topbar
  },
  setup() {
    const state = reactive({
      
    })
    const router = useRouter()

  onBeforeMount(() => {
  })

    const method = {
      goBack(){
        router.push({ name: 'sealManagement' })
      }
    }
   

    return {
      ...method,
      ...toRefs(state),
    }
  }
})

</script>
<style lang="less" scoped>
.add-sign {padding:30px;
  .topbar{text-align: left;font-size: 16px;padding-bottom: 20px;}
.con-wrap{
  
}
}
</style>