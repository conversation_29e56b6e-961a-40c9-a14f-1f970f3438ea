<template>
  <div class="dzht-content">
  <div class="certified-children-right-con">
   <CertifiedForm type='public'></CertifiedForm>
  </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRef, toRefs, ref, watch, computed, getCurrentInstance, onBeforeMount } from "vue";
import CertifiedForm from "../components/CertifiedForm.vue";
export default defineComponent({
   components:{
    CertifiedForm
  },
  setup() {
const state = reactive({
    })
    onBeforeMount(() => {
    })

    const method = {}


    return {
      ...method,
      state,
    }
  }
})

</script>

<style lang="less" scoped>
.certified-children-right-con {
  
}
</style>