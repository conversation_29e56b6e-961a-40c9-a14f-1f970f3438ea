<template>
  <div class="dzht-content">
  <div class="template-management" v-if="route.name == 'templateManagement'">
    <Topbar title="我的模板">
        <el-button class="btn-data"><i class="iconfont icon-wodejianli"></i>数据导出</el-button>
    <el-button type="primary" @click="gotoTemplateList">+新建模板</el-button>
    </Topbar>
<div class="con-wrap">
 <el-row class="tit">
    <el-col :span="4"><div class="grid-content">序号</div></el-col>
    <el-col :span="4"><div class="grid-content">状态</div></el-col>
    <el-col :span="4"><div class="grid-content">版本</div></el-col>
    <el-col :span="4"><div class="grid-content">模板名称</div></el-col>
    <el-col :span="4"><div class="grid-content">更新时间</div></el-col>
    <el-col :span="4"><div class="grid-content">操作</div></el-col>
  </el-row>
 <el-row class="con" v-for="(item,index) in list" :key="index">
    <el-col :span="4"><div class="grid-content">{{item.code}}</div></el-col>
    <el-col :span="4"><div class="grid-content">{{item.state}}</div></el-col>
    <el-col :span="4"><div class="grid-content"><span class="state">{{item.code2}}</span></div></el-col>
    <el-col :span="4"><div class="grid-content">{{item.type}}</div></el-col>
    <el-col :span="4"><div class="grid-content">{{item.date}}</div></el-col>
    <el-col :span="4"><div class="grid-content">
      <el-dropdown class="btn-fqht">
    <el-button type="primary" class="btn-use">使用模板</el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item>使用模板</el-dropdown-item>
        <el-dropdown-item>修改模板</el-dropdown-item>
        <el-dropdown-item>下载模板</el-dropdown-item>
        <el-dropdown-item>删除</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
      </div></el-col>
  </el-row>
</div>
  </div>
  <router-view v-else></router-view>
  </div>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount } from "vue";
import { useRoute,useRouter } from "vue-router";
import Topbar from "../components/Topbar.vue";
export default defineComponent({
   components:{
    Topbar
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const state = reactive({
      list:[
        {"code":"3434234234234","name":"体验签署电子合同（无需实名认证）","state":"正常","code2":"15177169240","date":"2021-09-07 16:50","type":"企业"},
      ]
    })

  onBeforeMount(() => {
    method.getCascaderData()
  })

    const method = {
      async getCascaderData(){
        
      },
      gotoTemplateList(){
        router.push({ name: 'templateList' })
      }
    }
   

    return {
      ...method,
      ...toRefs(state),
      route
    }
  }
})

</script>
<style lang="less">
.template-management {
.el-row{
  .grid-content{text-align: left;font-size: 14px;}
}
}
 .add-sign-dropdown-menu{
    a{color: #333; text-decoration: none;}
  }
</style>
<style lang="less" scoped>
.template-management {padding:30px;
 .el-dropdown-menu{background: olivedrab;
    a{color: #333; text-decoration: none;}
  }
.btn-data{margin-right: 15px;}
.con-wrap{
  .tit{line-height:50px;border-bottom: solid 2px #e6e6e6;
  .grid-content{font-size: 16px;font-weight: bold;}}
  .con{line-height:50px;
  .state{font-size: 12px;background: #f1f1f1;color: #1787FB;padding:5px 8px;}}
  .btn-use{font-size: 12px;}
}
}
</style>