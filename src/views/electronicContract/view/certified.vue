<template>
  <div class="dzht-content">
  <div class="certified" v-if="route.name == 'certified'">
    <Topbar title="账户认证"></Topbar>
    <div class="con-wrap">
        <p v-if="person.authStatus==0">
        <router-link class="kuang" to="./certified/certifiedChildren/corporate"><el-button type="primary">点击添加账户认证</el-button></router-link>
      </p>
        <div v-else class="kuang">
           <div class="more">
             <el-dropdown class="btn-edit">
                  <i @click="editCertified()" class="iconfont icon-bianji2"></i>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="editCertified()">编辑认证</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
        </div>
           <div class="info">
            <el-row>
            <el-col :span="6"><div class="grid-content"><el-avatar :size="60" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"></el-avatar></div></el-col>
            <el-col :span="18"><div class="grid-content"><h3>{{person.name}}</h3><p>证件号：{{person.idNumber}}</p></div></el-col>
          </el-row>
        </div>
         <div class="list">
          <el-row>
            <el-col :span="12"><div class="tl">认证类型：</div></el-col>
            <el-col :span="12"><div class="tr">个人认证</div></el-col>
          </el-row>
          <el-row>
            <el-col :span="12"><div class="tl">认证状态：</div></el-col>
            <el-col :span="12"><div class="tr">{{person.authStatusTip}}</div></el-col>
          </el-row>
          <el-row>
            <el-col :span="12"><div class="tl">认证方式：</div></el-col>
            <el-col :span="12"><div class="tr">个人授权认证</div></el-col>
          </el-row>
          <el-row>
            <el-col :span="12"><div class="tl">认证时间：</div></el-col>
            <el-col :span="12"><div class="tr">{{person.passAuthTime}}</div></el-col>
          </el-row>
        </div>
        </div>
    </div>
  </div>
  <router-view v-else></router-view>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRef, toRefs, ref, watch, computed, getCurrentInstance, onBeforeMount } from "vue";
import { useRoute,useRouter } from 'vue-router';
import { useStore } from 'vuex';
import Topbar from "../components/Topbar.vue";
import { getAuthInfo } from "@/http/dzhtApi";
export default defineComponent({
   components:{
    Topbar
  },
  setup() {
    const { proxy }: any = getCurrentInstance();
    const router = useRouter();
    const route = useRoute()
    const store = useStore();
    const state = reactive({
      person:{
    authMsg: "",
authStatus: 3,
authStatusTip: "",
authType: 14,
idNumber: "",
name: "",
passAuthTime: "",
submitAuthTime: ""
  }
    })

    onBeforeMount(() => {
      method.getAuthStatus()
    })

    const method = {
      async getAuthStatus(){
        let data={}
        if(store.state.accountAuth.authStatus!=undefined){
          data =store.state.accountAuth
        }else{
          const result = await getAuthInfo();
          data=result.data
          store.commit('setAccountAuth',data)
        }

        if(data.name){
          state.person.name=data.name
        }
        if(data.idNumber){
          state.person.idNumber=data.idNumber
        }
        if(data.authStatus){
          state.person.authStatus=data.authStatus
        }
        if(data.authStatusTip){
          state.person.authStatusTip=data.authStatusTip
        }
        if(data.passAuthTime){
          state.person.passAuthTime=data.passAuthTime
        }
      },
      editCertified(){
        router.push({'name':'corporate'})
      }
    }


    return {
      ...method,
      ...toRefs(state),
      route
    }
  }
})

</script>
<style lang="less">
.certified {
  .btn-edit{cursor:pointer;}
}
</style>
<style lang="less" scoped>
.certified {
  padding: 30px;
  .tl{text-align: left;}
  .tr{text-align: right;}
  .topbar {
    text-align: left;
    font-size: 16px;
    padding-bottom: 20px;
  }
  .con-wrap {
      .kuang{width: 400px;height: 400px;box-sizing:border-box;text-align: center;}
      a.kuang {
        display: block;
        line-height: 400px;
        background: #fff;
        border: 1px dashed #ddd;
        border-radius: 4px;
      }
      div.kuang{padding:30px 30px 0;text-align: left; border: 1px solid #ddd;
        .more{text-align: right;padding-bottom:20px;
        i{font-size: 22px;}}
        .info{padding-bottom:50px;
        h3{font-weight: normal;padding-bottom:10px;}}
        .list{line-height: 30px;}
      }
  }
}
</style>