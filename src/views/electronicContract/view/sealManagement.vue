<template>
  <div class="dzht-content">
  <div class="seal-management" v-if="route.name == 'sealManagement'">
    <Topbar title="印章管理"></Topbar>
<div class="con-wrap">


<el-table
                :data="tableData"
                tooltip-effect="dark"
                style="width: 100%"
              >
                <el-table-column label="序号">
                  <template #default="scope">
<div class="grid-content">{{scope.row.id}}</div>
                  </template>
                </el-table-column>
<el-table-column label="名称">
                  <template #default="scope">
<div class="grid-content">{{scope.row.sealName}}</div>
                  </template>
                </el-table-column>
<el-table-column label="预览">
                  <template #default="scope">
<div class="grid-content"><el-image style="width: 100px; height: 100px" :src="scope.row.sealUrl" fit="fill"></el-image></div>
                  </template>
                </el-table-column>
<el-table-column label="创建时间">
                  <template #default="scope">
<div class="grid-content">{{scope.row.createTime}}</div>
                  </template>
                </el-table-column>
<el-table-column label="印章编号">
                  <template #default="scope">
<div class="grid-content">{{scope.row.sealCode}}</div>
                  </template>
                </el-table-column>
              </el-table>


<div class="message" v-show="message!=''&&message!=null">{{message}}</div>
</div>
  <Pagination :pageSize="parameter.PageSize" :totalCount="total" @handlePageChange="handlePageChange" />

  </div>
  <router-view v-else></router-view>
  </div>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount } from "vue";
import { useRoute } from "vue-router";
import Topbar from "../components/Topbar.vue";
import Pagination from '@/components/Pagination.vue';
import { getSealList} from "@/http/dzhtApi";
export default defineComponent({
   components:{
    Topbar,Pagination
  },
  setup() {
    const route = useRoute()
    const state = reactive({
      tableData:[],
      parameter:{
        Page:1,
        PageSize:10,
      },
      total:0,
      message:'',
    })

  onBeforeMount(() => {
    method.getSealListData()
  })

    const method = {
      async getSealListData(){
        const {data} = await getSealList(state.parameter)
        state.total=data.total
        state.tableData=data.data
        state.message=data.message
      },
      handlePageChange(val:number) {
        state.parameter.Page=val
        method.getSealListData()
      },
    }
   

    return {
      ...method,
      ...toRefs(state),
      route
    }
  }
})

</script>
<style lang="less">
.seal-management {
  .el-table th.is-leaf{
    border-bottom: solid 2px #e6e6e6;font-size: 16px;
    font-weight: bold;color: #333;
}
}
 .add-sign-dropdown-menu{
    a{color: #333; text-decoration: none;}
  }
</style>
<style lang="less" scoped>
.seal-management {padding:30px;
 .el-dropdown-menu{background: olivedrab;
    a{color: #333; text-decoration: none;}
  }
.btn-data{margin-right: 15px;}
}
</style>