<template>
  <div class="dzht-content">
  <div class="contract-management" v-if="route.name == 'contractManagement'">
    <div class="topbar">
      <el-menu
        :default-active="activeIndex"
        class="el-menu-demo"
        mode="horizontal"
        @select="handleSelect"
      >
        <el-menu-item index="-1">全部合同</el-menu-item>
        <el-menu-item index="6">待签署</el-menu-item>
        <el-menu-item index="2">已签署</el-menu-item>
        <el-menu-item index="3">已拒签</el-menu-item>
        <el-menu-item index="5">已过期</el-menu-item>
      </el-menu>
    </div>
 
    <div class="search-wrap">
      <el-row>
        <el-col :span="2"><div class="grid-content">合同主题：</div></el-col>
        <el-col :span="9">
          <div class="input-content">
            <el-input v-model="input_name" placeholder="E.g:xx文件" /></div>
            </el-col>
            <el-col :span="2"><div class="grid-content">发起方：</div></el-col>
        <el-col :span="7">
          <div class="input-content">
            <el-input v-model="input_senderName" placeholder="E.g:某某公司" /></div>
            </el-col>
        <el-col :span="4">
          <div class="grid-content">
            <el-button type="primary" class="btn-search" @click="search">搜索</el-button>
          </div>
          </el-col>
      </el-row>
    </div>
    <div class="con-wrap">
<el-table :data="tableData" tooltip-effect="dark" style="width: 100%">
            <el-table-column label="合同主题">
              <template #default="scope">
                <div class="grid-content">{{ scope.row.name }}</div>
              </template>
            </el-table-column>
            <el-table-column label="发起方">
              <template #default="scope">
                <div class="grid-content">{{ scope.row.senderName }}</div>
              </template>
            </el-table-column>
            <el-table-column label="签署方">
              <template #default="scope">
                <div class="grid-content">{{ scope.row.signUsers }}</div>
              </template>
            </el-table-column>
            <el-table-column width="180" label="发起时间">
              <template #default="scope">
                <div class="grid-content">{{ scope.row.createTime }}</div>
              </template>
            </el-table-column>
            <el-table-column label="签署状态">
              <template #default="scope">
                <div class="grid-content">
                  <span class="state">
                    {{ scope.row.status }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-dropdown class="btn-fqht">
                  <el-button
                    type="primary"
                    class="btn-check"
                    @click="gotoSignContract(scope.row.contractCode)"
                    >查看合同<i class="jt"></i></el-button
                  >
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        @click="gotoSignContract(scope.row.contractCode)"
                        >查看合同</el-dropdown-item
                      >
                      <el-dropdown-item v-if="scope.row.statusCode==2"
                        @click="downloadContract(scope.row.name,scope.row.contractCode)"
                        >下载合同</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

<div class="message" v-show="message!=''&&message!=null">{{message}}</div>
</div>
  <Pagination :pageSize="parameter.PageSize" :totalCount="total" @handlePageChange="handlePageChange" />

  </div>
  <router-view v-else></router-view>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRef, toRefs, ref, watch,onUnmounted,getCurrentInstance,onBeforeMount,} from 'vue';
import { useRoute ,useRouter} from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import Pagination from '@/components/Pagination.vue';
import { getContractList} from "@/http/dzhtApi";
import axios from 'axios'
export default defineComponent({
  components:{
    Pagination
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const { proxy }: any = getCurrentInstance();
    const contractPagination: Ref = ref(null);
    const state = reactive({
      activeIndex: ref('-1'),
      input_name: ref(''),
      input_senderName: ref(''),
      tableData:[],
      message:'',
      parameter:{
        PersonStatus:'',
        Name:'',
        SenderName:'',
        Page:1,
        PageSize:10,
      },
      total:0
    });
 
    onBeforeMount(() => {
      method.getContractListData()
    });
    onUnmounted(() => { 
      state.input_name = '';
      state.input_senderName = '';

     });

    const method = {
      async getContractListData(){
        const {data} = await getContractList(state.parameter)
        state.total=data.total
        state.tableData=data.data
        state.message=data.message
      },
      handlePageChange(val:number) {
        state.parameter.Page=val
        method.getContractListData()
      },
      handleSelect(key: string, keyPath: string[]) {
        state.input_name = ''
        state.parameter.Name=''
        state.input_senderName = ''
        state.parameter.SenderName=''
        state.activeIndex = key;
        state.parameter.PersonStatus = key;
        if(key=='-1'){
          state.parameter.PersonStatus = ''
        }
        method.getContractListData()
      },
      search() {
        if (state.input_name == ''&&state.input_senderName == '') {
          ElMessage.warning('请输入合同主题或发起方');
          return;
        }
        if (state.input_name != ''){
          state.parameter.Name = state.input_name;
        }
        if (state.input_senderName != ''){
          state.parameter.SenderName = state.input_senderName;
        }
        method.getContractListData()
      },
      gotoSignContract(contractCode: string) {
        router.push({
          name: 'signContract',
          query: { id: contractCode },
        });
      },
      async downloadContract(fileName: string,contractCode: string) {
        await axios({
          method: 'GET',
          url: `/api/sign/contract/download/${contractCode}`,
          responseType: 'blob'
        }).then(res => {
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(res);
          link.download = `${fileName}.pdf`;
          document.body.appendChild(link);
          link.click();
          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);
        }).catch(err => {
          ElMessage.warning(err);
        })
      },
    };

    return {
      ...method,
      ...toRefs(state),
      route,
      contractPagination,
    };
  },
});
</script>
 
<style lang="less">
.contract-management {
  .el-menu--horizontal > .el-menu-item.is-active {
    border-bottom: 2px solid #1787fb;
    color: #303133;
  }
  li.el-menu-item.is-active {
    background: none !important;
  }
  .el-menu--horizontal > .el-menu-item:hover {
    background: none !important;
  }
  .topbar {
    .btn-fqht {
      position: absolute;
      bottom: 15px;
      right: 0;
    }
  }
  .el-table th.is-leaf {
    border-bottom: solid 2px #e6e6e6;
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }
}
</style>
<style lang="less" scoped>
.contract-management {
  padding: 30px;
  .topbar {
    position: relative;
    margin-bottom: 30px;
  }
  .search-wrap {text-align: left;font-size: 14px;
    padding-bottom: 30px;
    .el-row {
      line-height: 44px;
    }
    .input-content {
      padding-right: 20px;
    }
    .btn-search {
      width: 100px;
    }
  }
  .con-wrap {
    .state {
      font-size: 12px;
      background: #f1f1f1;
      color: #1787fb;
      padding: 5px 8px;
    }
    .btn-check {
      font-size: 12px;
      .jt{display:inline-block;width:6px;height:6px;border-width:1px;border-style:dashed dashed solid solid; border-color:transparent transparent #fff #fff;transform:rotate(-45deg);margin:0px 0 0 5px;}
    }
  }
}
</style>