<template>
  <div class="dzht-content">
  <div class="img-sign">
<Topbar title="从图片导入签名">
        <el-button class="btn-data" type="info" plain @click="goBack">&lt; 返回签名列表</el-button>
    </Topbar>
<div class="con-wrap">
<div class="tit">基本资料</div>
<div class="con mingcheng">
  <p>*签名名称：</p>
   <el-input v-model="input" placeholder="取一个名字方便后期管理" />
  </div>
<div class="tit">签名图片<i class="doubt">?</i></div>
<div class="con">
   <Upload></Upload>
  </div>
</div>
  <div class="btn-wrap">
  <el-button type="primary"><i class="iconfont icon-fuzhi"></i>确认提交（需审核）</el-button>
  <el-button type="info" @click="goBack"><i class="iconfont icon-fuzhi"></i>取消</el-button>
  </div>
  </div>
  </div>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount } from "vue";
import Upload from "../components/Upload.vue";
import Topbar from "../components/Topbar.vue";
import { useRouter } from 'vue-router'
export default defineComponent({
   components:{
    Upload,Topbar
  },
  setup() {
    const state = reactive({
      input:'',
      
    })
    const router = useRouter()

  onBeforeMount(() => {
  })

    const method = {
      goBack(){
        router.push({ name: 'sealManagement' })
      }
    }
   

    return {
      ...method,
      ...toRefs(state),
    }
  }
})

</script>
<style lang="less" scoped>
.img-sign {padding:30px;text-align: left;
    .doubt{display: inline-block;width: 18px;height: 18px;line-height: 18px;text-align: center;border: 1px solid #666;border-radius:50%;font-style:normal;margin-left: 5px;}
    .con-wrap{
      .tit{height:20px;line-height:20px; border-left: 3px solid #374DC3;padding-left: 10px;margin-bottom: 20px;}
      .mingcheng{padding-bottom: 40px;
      p{font-size: 14px;padding-bottom:10px;}}
    }
    .btn-wrap{text-align: center;padding-top: 30px;}
}
</style>