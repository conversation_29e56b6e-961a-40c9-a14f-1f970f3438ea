<template>
  <div class="dzht-content">
    <div class="sign-contract">
      <div class="topbar-wrap">
        <Topbar title="签署合同">
          <el-button v-if="details.statusCode == 6" type="danger" class="btn btn-refuse"
            @click="askRefuseContract">拒签合同</el-button>
          <el-button v-if="details.statusCode == 6" type="primary" class="btn btn-data" @click="pushSign">提交签署</el-button>
        </Topbar>
      </div>

      <div class="con-wrap" v-loading="loading">
        <div class="left-con">
          <div class="pdf-wrap">
            <div class="parent-wrap" v-for="(item, index) in details.contracts" :class="`parent-wrap-${index}`"
              :key="index" :index="index" @drop="drop" @dragover="allowDrop">
              <el-image class="img-pdf" :src="item" fit="fill">
                <template #placeholder>
                  <div class="image-slot">图片加载中<span class="dot">...</span></div>
                </template>
              </el-image>

              <Sign v-for="(sign, signIndex) in signObj[`contract${index}`]" :id="sign.signId" :page="index"
                :signSrc="signList[0].sealUrl" :x="sign.x" :y="sign.y" :parentDiv="parentDiv"
                @getFinalPosition="getFinalPosition" @deleteSign="deleteSign"></Sign>

            </div>
          </div>
        </div>

        <div class="right-con">
          <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane label="我的印章" name="first">
              <div class="yinzhang">
                <p class="tips">提示：可将签章拖拽到合同内任意位置</p>

                <ul class="clearfix">
                  <li>
                    <div class="bg">
                      <el-image id="drag1" draggable="true" @dragstart="drag($event, signList[0].sealId)" class="img-yz"
                        :src="signList[0].sealUrl" fit="fill"></el-image>
                      <p>{{ signList[0].sealName }}</p>
                    </div>
                  </li>
                </ul>
              </div>
            </el-tab-pane>
            <el-tab-pane label="合同信息" name="second">
              <div class="hetong">
                <h3 class="tit2">主题名称</h3>
                <p>{{ details.name }}</p>

                <h3 class="tit2">合同id</h3>
                <p>{{ details.contractCode }}</p>

                <h3 class="tit2">文件状态</h3>
                <p>{{ details.status }}</p>

                <h3 class="tit2">发起方</h3>
                <p>{{ details.senderName }}</p>

                <h3 class="tit2">文件发起日期</h3>
                <p>{{ details.createTime }}</p>

                <h3 class="tit2">签署截止日期</h3>
                <p>{{ details.signEndDate }}</p>

                <h3 class="tit2">签署人</h3>
                <p>
                  {{ details.signers[1].signName }}（{{
            details.signers[1].signPhone
          }}）
                </p>

                <h3 class="tit2">流程信息</h3>
                <ul class="flow">
                  <li v-for="(item, index) in details.flows" :key="index">
                    <p class="content">{{ item.content }}</p>
                    <p class="time">{{ item.addTime }}</p>
                  </li>
                </ul>
              </div>
            </el-tab-pane>
          </el-tabs>

        </div>
      </div>

      <el-dialog v-model="dialogMsgVisible" title="获取验证码" width="500px">
        <div class="send-msg-wrap">
          <table>
            <tr>
              <td class="w1">手机号码：</td>
              <td class="w2">{{ details.signers[1].signPhone }}</td>
              <td class="w3"></td>
            </tr>
            <tr class="nopad">
              <td class="w1"><span class="red">*</span>验证码：</td>
              <td class="w2">
                <el-input v-model="msgInput" placeholder="您收到的验证码" />
              </td>
              <td class="w3">
                <el-button v-if="sendMsgBtnShow" type="primary" class="btn-fasong" @click="sendMsg">发送验证码</el-button>
                <el-button v-else type="info" disabled class="btn-fasong">发送成功 {{ count }}</el-button>
              </td>
            </tr>
            <tr class="nopad">
              <td class="w1"></td>
              <td class="w2 yanzheng red">
                {{ yanzheng }}
              </td>
              <td class="w3">
              </td>
            </tr>
            <tr>
              <td class="w1"></td>
              <td class="w2">
                <el-button v-if="btnSureShow" type="primary" class="btn-sure" @click="confirmSign">确定</el-button>
                <el-button v-else type="info" disabled class="btn-sure">正在提交，请稍后</el-button>
              </td>
              <td class="w3"></td>
            </tr>
          </table>
        </div>
      </el-dialog>

      <el-dialog custom-class="success-box" v-model="dialogSuccessVisible" title width="410px" center>
        <div class="tips">
          <div class="icon success"></div>
          <p>签署成功</p>
        </div>
        <template #footer>
          <span class="dialog-footer btn-wrap">
            <el-button type="primary" @click="goBack">返回文件列表</el-button>
            <el-button @click="checkContract">查看文件</el-button>
          </span>
        </template>
      </el-dialog>

      <el-dialog custom-class="success-box" v-model="dialogRefuseVisible" title width="410px" center>
        <div class="tips">
          <div class="icon warn"></div>
          <p>确定要拒签该合同吗？</p>
        </div>
        <template #footer>
          <span class="dialog-footer btn-wrap">
            <el-button type="danger" @click="refuseContract">确定</el-button>
            <el-button @click="dialogRefuseVisible = false">取消</el-button>
          </span>
        </template>
      </el-dialog>

      <Auth :showAuthBox="showAuthBox" @closeAuthBox="closeAuthBox"></Auth>
    </div>
  </div>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  toRef,
  toRefs,
  ref,
  watch,
  computed,
  getCurrentInstance,
  onBeforeMount,
  onUnmounted,
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Topbar from '../components/Topbar.vue';
import Sign from '../components/Sign.vue';
import Auth from '../components/Auth.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useStore } from 'vuex';
import { getContractDetail, getContractSealList, getAuthInfo, getContractWillCheck, postContractSign, postContractRefuse } from "@/http/dzhtApi";
export default defineComponent({
  components: {
    Topbar,
    Sign,
    Auth,
  },
  setup() {
    const store = useStore();
    const router = useRouter();
    const route = useRoute();
    const state = reactive({
      loading: ref(false),
      showAuthBox: { value: false },
      dialogMsgVisible: ref(false),
      dialogSuccessVisible: ref(false),
      dialogRefuseVisible: ref(false),
      msgInput: '',
      activeName: ref('first'),
      yanzheng: '',
      details: {
        id: 0,
        contractCode: '',
        name: '',
        senderName: '',
        signEndDate: '',
        createTime: '',
        updateTime: '',
        startTime: '',
        status: '',
        signers: [
          {
            signName: '',
            signPhone: '',
          },
          {
            signName: '',
            signPhone: '',
          },
        ],
        flows: [],
        contracts: [],
      },
      signList: [
        {
          sealId: '',
          sealName: '',
          sealUrl: '',
        },
      ],
      signData: {
        messageCode: '',
        sealId: '',
        areas: [
          {
            x: 0,
            y: 0,
            page: 0,
          },
        ],
      },
      sendMsgBtnShow: true,
      btnSureShow: true,
      count: 60,
      finalPosition: { page: 0, x: 0, y: 0 },
      parentDiv: { w: 744.14, h: 1052 },
      signDiv: { w: 120, h: 120 },
    });
    let timer = null;
    onUnmounted(() => {
      if (timer) {
        clearInterval(timer);
      }
    });
    onBeforeMount(() => {
      methods.getContractDtails();
      methods.getSignList();
    });
    const signObj = reactive({});
    const DragFun = {
      signNum: 0,
      allowDrop(ev) {
        ev.preventDefault();
      },
      drop(ev) {
        let signId = ev.dataTransfer.getData('signId');
        if (!signId) return
        // ev.target.appendChild(document.getElementById(data));
        let index = ev.target.parentNode.parentNode.getAttribute('index');
        let x = ev.offsetX - state.signDiv.w / 2;
        let y = ev.offsetY - state.signDiv.h / 2;
        ev.preventDefault();
        let item = state.signList.find((item) => item.sealId == signId);
        let realx = x, realy = state.parentDiv.h - state.signDiv.h / 2 - y
        let obj = {
          index, //第几页
          signId, //印章id
          x,
          y,
          realx, realy
        };
        obj.signId = `sign${obj.signId}${DragFun.signNum}${index}`;
        if (signObj.hasOwnProperty(`contract${index}`)) {
          signObj[`contract${index}`].push(obj);
        } else {
          signObj[`contract${index}`] = [];
          signObj[`contract${index}`].push(obj);
        }
        DragFun.signNum++;
      },
      drag(e: any, id: number) {
        e.dataTransfer.setData('signId', id);
      },
      getFinalPosition(value) {
        signObj[`contract${value.page}`].forEach((element, index) => {
          if (element.signId == value.signId) {
            element.x = value.x
            element.y = value.y
            element.realx = value.realx
            element.realy = value.realy
          }
        });
      },
      deleteSign(value) {
        signObj[`contract${value.page}`].forEach((element, key) => {
          if (element.signId == value.signId) {
            signObj[`contract${value.page}`].splice(key, 1);
          }
        });
      }
    };

    const methods = {
      async getContractDtails() {
        state.loading = true
        let { data } = await getContractDetail(route.query.id)
        state.loading = false
        state.details = data;
      },
      async getSignList() {
        let { data } = await getContractSealList()
        if (data == '') {
          const result = await methods.accountAuth()
          if (result.authStatus != 3) {
            state.showAuthBox.value = true;
          } else {
            state.showAuthBox.value = false;
            ElMessage.warning('没有生成印章图片，请联系客服');
          }
        } else {
          state.signList = data;
          state.signData.sealId = data[0].sealId;
        }

      },
      async accountAuth() {
        let data = {};
        if (store.state.accountAuth.authStatus != undefined) {
          data = store.state.accountAuth;
        } else {
          const result = await getAuthInfo();
          data = result.data;
          store.commit('setAccountAuth', data);
        }
        return data
      },
      async askRefuseContract() {
        state.dialogRefuseVisible = true
      },
      async refuseContract() {
        const data = await postContractRefuse(route.query.id);
        if (data.code == 1) {
          ElMessage.success('拒签该合同成功');
          methods.goBack();
        } else {
          ElMessage.error(data.message);
        }
      },
      async pushSign() {
        const result = await methods.accountAuth()
        if (result.authStatus != 3) {
          state.showAuthBox.value = true;
        } else {
          state.showAuthBox.value = false;
          if (JSON.stringify(signObj) == "{}") {
            ElMessage.warning('请将签章拖拽到合同内需要签署的位置');
            return
          }
          state.dialogMsgVisible = true;
        }
      },
      sendMsg() {
        getContractWillCheck(route.query.id).then((data: any) => {
          if (data.code == 1) {
            ElMessage.success('短信验证码发送成功');

            state.count = 60;


            timer = setInterval(function () {
              state.count--;
              state.sendMsgBtnShow = false;
              if (state.count <= 0) {
                clearInterval(timer);
                state.sendMsgBtnShow = true;
                timer = null;
              }
            }, 1000);
          } else {
            ElMessage.warning(data.message);
          }
        });
      },
      confirmSign() {
        if (state.msgInput == '') {
          state.yanzheng = "请输入验证码"
          return
        } else {
          state.yanzheng = ""
        }
        state.btnSureShow = false;
        state.loading = true
        state.signData.areas = [];
        for (let key in signObj) {
          signObj[key].forEach(element => {
            state.signData.areas.push({ x: element.realx * 0.8291, y: element.realy * 0.8212, page: element.index * 1 + 1 })
          });
        }
        state.signData.messageCode = state.msgInput

        postContractSign(route.query.id, state.signData).then((data: any) => {
          state.loading = false
          state.btnSureShow = true;
          if (data.code == 1) {
            state.dialogMsgVisible = false;
            state.dialogSuccessVisible = true;
          } else {
            ElMessage.warning(data.message);
          }
        });
      },
      goBack() {
        router.push({ name: 'contractManagement' });
      },
      switchRight() {
        if (state.right == 0) {
          state.right = -280;
        } else {
          state.right = 0;
        }
      },
      checkContract() {
        location.reload();
      },
      closeAuthBox(value: boolean) {
        state.showAuthBox.value = value;
      },
    };

    return {
      ...methods,
      ...toRefs(state),
      ...DragFun, signObj
    };
  },
});
</script>
<style lang="less">
.sign-contract {
  .el-dialog__footer {
    text-align: center;
  }

  .dialog-footer {
    display: block;
  }
}
</style>
<style lang="less">
.sign-contract {
  .topbar {
    h3 {
      line-height: 44px;
    }
  }

  .doubt {
    display: inline-block;
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 50%;
    font-style: normal;
    margin-left: 5px;
  }

  .el-tabs__header,
  .el-tabs__nav-wrap {
    height: 50px;
    background: #fff;
    padding: 0;
  }

  .el-tabs__nav {
    width: 100%;
    width: 220px;
    height: 50px;
    background: #fff;

    .el-tabs__item {
      width: 50%;
      height: 50px;
      line-height: 50px;
      padding: 0;
      text-align: center;
      border-bottom: 2px solid #E4E7ED;
    }
  }
}
</style>
<style lang="less" scoped>
.clearfix:after {
  content: '';
  display: block;
  height: 0;
  clear: both;
}

ul,
li {
  margin: 0;
  padding: 0;
  list-style: none;
}

.sign-contract {
  text-align: left;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .topbar-wrap {
    position: relative;

    .topbar {
      border-bottom: 1px solid #ebedf2;
      padding: 10px 20px;
    }
  }

  .con-wrap {
    flex: 1;
    background: rgb(232, 232, 232);
    position: relative;
    height: 0;

    .left-con {
      width: 770px;
      height: 750px;
      margin: 0 220px 0 0px;
      overflow: auto;

      .pdf-wrap {
        width: 744.14px;
        margin: 0 auto;

        .parent-wrap {
          position: relative;

          .img-pdf {
            width: 100%;
            height: 1052px;
            display: block;
          }
        }
      }
    }

    .right-con {
      width: 220px;
      height: 100%;
      background: #fff;
      border-left: 1px solid #e1e1e1;
      position: absolute;
      top: 0;
      right: 0;

      .yinzhang {
        padding: 10px;

        .tips {
          font-size: 12px;
          padding: 0 0 15px 10px;
          color: #999;
        }

        ul {
          padding-bottom: 20px;

          li {
            cursor: pointer;

            .bg {
              width: 160px;
              border: 1px solid #e1e1e1;
              text-align: center;
              font-size: 14px;
              color: #666;
              padding: 10px 0;
              margin: 0 auto;

              .img-yz {
                width: 120px;
                height: 120px;
              }
            }
          }
        }
      }

      .hetong {
        height: 670px;
        overflow-y: auto;
        padding: 10px;

        h3.tit2 {
          height: 18px;
          line-height: 18px;
          font-size: 14px;
          font-weight: normal;
          border-left: 3px solid #09f;
          padding-left: 7px;
          margin-bottom: 10px;
          color: #333;
        }

        p {
          padding: 0 0 20px 10px;
          color: #888;
          font-size: 14px;
          word-break: break-all;
        }
      }

      .flow {
        p {
          width: 100%;
          word-break: break-all;
          margin: 0;
        }

        .content {
          padding-bottom: 5px;
          color: #888;
        }

        .time {
          font-size: 12px;
          padding-bottom: 10px;
          color: #ccc;
        }
      }
    }
  }

  .send-msg-wrap {
    table {
      width: 100%;

      td {
        padding-bottom: 20px;
      }

      .nopad td {
        padding-bottom: 0px;
      }

      .yanzheng {
        height: 20px;
        line-height: 20px;
      }

      .w1 {
        width: 83px;
      }

      .w2 {
        width: 220px;
      }

      .w3 {
        padding-left: 10px;
      }

      .red {
        color: red;
      }

      .btn-sure {
        width: 155px;
      }
    }
  }

  .success-box {
    .tips {
      text-align: center;

      .icon {
        height: 100px;
      }

      .success {
        background: url(https://image.gxrc.com/gxrcsite/vip/dzht/hook.png) no-repeat center top;
      }

      .warn {
        background: url(https://image.gxrc.com/gxrcsite/vip/dzht/warn.png) no-repeat center top;
      }

      p {
        font-size: 16px;
      }
    }
  }
}
</style>
