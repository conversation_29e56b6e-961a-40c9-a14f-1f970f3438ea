<template>
  <div class="dzht-content">
  <div class="initiate-contract" v-if="route.name == 'initiateContract'">
    <el-form
    ref="ruleForm"
    :model="state.parameters"
    :rules="state.rules"
    class="demo-ruleForm"
    label-position="top"
  >
  <el-form-item label="合同名称" prop="name">
    <div class="form-con input"><el-input v-model="state.parameters.name" placeholder="取一个名字方便后期管理"></el-input></div>
    </el-form-item>

  <el-form-item label="签署的文件" prop="sendFile">
    <div class="form-con input">
        <Upload @sendFileId="sendFileId"></Upload>
    </div>
 </el-form-item>

  <el-form-item label="签署方" prop="signers">
<div class="form-con qianshufang">
      <el-table :data="state.parameters.signers" border style="width: 100%">
    <el-table-column label="角色" width="90">
      <template #default="scope">
        <div class="role">
          {{scope.$index==0?'甲方':'乙方'}}
        </div>
      </template>
    </el-table-column>

    <el-table-column label="" width="280">
       <template #header>
        姓名<i class="doubt">?</i>
      </template>
      <template #default="scope">
        <p v-if="scope.row.signClass=='2'">{{scope.row.signName}}</p>
        <el-input v-else v-model="scope.row.signName" placeholder="请输入求职者姓名"></el-input>
      </template>
    </el-table-column>

    <el-table-column label="" width="220">
       <template #header>
        手机号<i class="doubt">?</i>
      </template>
      <template #default="scope">
        <p v-if="scope.row.signClass=='2'">{{scope.row.signPhone}}</p>
        <el-input type="number" v-else v-model="scope.row.signPhone" placeholder="请输入求职者联系号码"></el-input>
      </template>
    </el-table-column>

    <el-table-column label="签署主体" width="100">
      <template #default="scope">
{{ scope.row.signClass==1?'个人':'企业' }}
      </template>
    </el-table-column>

    <el-table-column label="操作">
      <template #default="scope">
        <p v-if="scope.row.signClass=='2'"></p>
        <el-button v-else type="primary" @click="verifyPhone">验证手机号码</el-button>
        <span v-if="scope.row.signClass=='1'" :class="`verify-tips ${state.verifyTips.style}`">{{state.verifyTips.tips}}</span>
      </template>
    </el-table-column>

  </el-table>
</div>
 </el-form-item>
 
  <el-form-item label="其他设置" prop="signEndDate">
<div class="form-con other">
<el-row class="tit">
    <el-col :span="12"><div class="grid-content">签署截止日期：</div></el-col>
    <el-col :span="12"><div class="grid-content"></div></el-col>
  </el-row>
  <el-row>
    <el-col :span="12">
      <div class="grid-content">
      <el-date-picker v-model="state.parameters.signEndDate" type="datetime" placeholder="选择日期">
      </el-date-picker>
      </div>
      </el-col>
    <el-col :span="12"><div class="grid-content">自动通知签署人：<el-switch v-model="state.sendMsg" disabled /></div></el-col>
  </el-row>
  <el-row class="tips">
    <el-col :span="12"><div class="grid-content">所有签署方须在截止日期前完成签署</div></el-col>
    <el-col :span="12"><div class="grid-content"></div></el-col>
  </el-row>
  </div>
 </el-form-item>

   <div class="btn-wrap">
  <el-button type="primary" class="btn-faqi" @click="initiateContract()">发起合同</el-button>
  <el-button @click="cancel">取消</el-button>
  </div>
    </el-form>
  <Auth :showAuthBox="state.showAuthBox" @closeAuthBox="closeAuthBox"></Auth>
  </div>
  <router-view v-else></router-view>
  </div>
</template>
<script lang="ts">
import { defineComponent,reactive, toRef, toRefs, ref,watch,computed, getCurrentInstance, onBeforeMount } from "vue";
import { useRouter,useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import Upload from "../components/Upload.vue";
import Auth from "../components/Auth.vue";
import { getAuthInfo,postContractCreate,getContractCheckuserexists } from "@/http/dzhtApi";
import { useStore } from 'vuex';
export default defineComponent({
   components:{
    Upload,Auth
  },
  setup() {
    const route = useRoute()
    const router = useRouter();
    const store = useStore();
    const ruleForm = ref(null)

const validateName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('内容不能为空'))
      } else {
        if (value.trim() !== ''&&value.length>50) {
          callback(new Error('不能超过50字'))
        }
        callback()
      }
    }
    const validateSendFile = (rule, value, callback) => {
      if (state.parameters.fileId === '') {
        callback(new Error('请上传一个签署文件'))
      } else {
        callback()
      }
    }
    const validateSigners = (rule, value, callback) => {
      if (value[1].signName === '') {
        callback(new Error('请输入求职者姓名'))
      } else if (value[1].signPhone === '') {
        callback(new Error('请输入求职者联系号码'))
      } else if (value[1].signPhone.length!=11) {
        callback(new Error('请输入11位数的手机号码'))
      } else {
        callback()
      }
    }
    const validateSignEndDate = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请选择签署截止日期'))
      } else {
        callback()
      }
    }

    const state = reactive({
      showAuthBox:{value:false},
      sendMsg:ref(true),
      parameters:{
  "name": "",
  "signEndDate": "",
  "fileId": "",
  "signers": [
    {
      "signClass": 2,
      "signName": "",
      "signPhone": ""
    },
    {
      "signClass": 1,
      "signName": "",
      "signPhone": ""
    },
  ],
  "isStart": true
},
  verifyTips:{"style":"","tips":""},
rules: {
        name: [{ validator: validateName, trigger: 'blur' }],
        sendFile: [{ validator: validateSendFile, trigger: 'blur' }],
        signers: [{ validator: validateSigners, trigger: 'blur' }],
        signEndDate: [{ validator: validateSignEndDate, trigger: 'blur' }],
      },
    })

  onBeforeMount(() => {
    method.getAuthStatus()
  })

watch(() => state.parameters.signers[1].signName,(newValue, oldValue) => {
      if(newValue==''){
        state.verifyTips.style=""
        state.verifyTips.tips=""
      }
    })
watch(() => state.parameters.signers[1].signPhone,(newValue, oldValue) => {
      if(newValue==''){
        state.verifyTips.style=""
        state.verifyTips.tips=""
      }
    })

    const method = {
      async getAuthStatus(){
        let data={}
        if(store.state.accountAuth.authStatus!=undefined){
          data =store.state.accountAuth
        }else{
          const result = await getAuthInfo();
          data=result.data
          store.commit('setAccountAuth',data)
        }

        if(data.enterpriseName){
          state.parameters.signers[0].signName=data.enterpriseName
        }
        if(data.phone){
          state.parameters.signers[0].signPhone=data.phone
        }

        if(data.authStatus!=3){
          state.showAuthBox.value=true
        }else{
          state.showAuthBox.value=false
        }
      },
      async initiateContract(){
        method.getAuthStatus()

        ruleForm.value.validate((valid) => {
            if (valid) {
            if(state.verifyTips.style==""){
              ElMessage.warning('请验证求职者的手机号码')
              return
            }
            if(state.verifyTips.style=="fail"){
              ElMessage.warning('该求职者没有绑定手机号码，不能发起合同')
              return
            }

           postContractCreate(state.parameters).then((data: any) => {
              if (data.code == 1) {
                ElMessage.success('创建成功')
                method.cancel()
              }else{
                ElMessage.warning(data.message)
              }
            })

          } else {
              return false
            }
          })
      },
      closeAuthBox(value:boolean){
        state.showAuthBox.value=value
      },
      cancel(){
        router.push({ name: 'contractManagement' });
      },
      sendFileId(fileId){
        state.parameters.fileId=fileId
      },
      async verifyPhone(){
        if(state.parameters.signers[1].signName==''){
          ElMessage.warning('请填写求职者姓名')
          return
        }
        if(state.parameters.signers[1].signPhone==''){
          ElMessage.warning('请填写求职者联系号码')
          return
        }

        let { data } = await getContractCheckuserexists({"signName":state.parameters.signers[1].signName,"signPhone":state.parameters.signers[1].signPhone})
        if(data==false){
          state.verifyTips.style="fail"
          state.verifyTips.tips="该求职者没有绑定手机号码，请等待绑定"
        }else{
          state.verifyTips.style="success"
          state.verifyTips.tips="验证成功"
        }
      }
    }
   

    return {
      ...method,
      state,
      route,
      ruleForm
    }
  }
})

</script>
<style lang="less">
.initiate-contract {
.qianshufang{
  .el-dropdown{display: block;}
}
.el-row{font-size: 14px;}
.other{
  .el-date-editor.el-input{width: 350px;}
}
.el-form-item{margin-bottom:35px;}
.el-form--label-top .el-form-item__label{height:20px;line-height:20px;font-size:16px;font-weight:bold;color:#333; border-left: 3px solid #374DC3;padding-left: 10px;margin-bottom:15px;}
}
</style>
<style lang="less" scoped>
.initiate-contract {padding: 30px;text-align: left;
.doubt{display: inline-block;width: 18px;height: 18px;line-height: 18px;text-align: center;border: 1px solid #ddd;border-radius:50%;font-style:normal;margin-left: 5px;}
.qianshufang{
  .role{width: 100%;height: 40px;line-height: 40px;padding: 0 15px;border-radius: 4px;background-color: #F5F7FA;border: 1px solid #E4E7ED;color: #C0C4CC;box-sizing: border-box;text-align: center;}
.yaoqiu{list-style: none;padding: 0;}
}
.btn-wrap{text-align: center;padding: 20px 0;
.btn-faqi{width:150px;}}
.other{
  .tit{padding-bottom:5px;}
  .tips{color: #ccc;padding-top:5px;}
  }
  .arrow-down{width: 0;height: 0;border: 5px solid transparent;border-top: 5px solid #999;}
  .verify-tips{padding-left:10px;}
  .verify-tips.success{color:#2ECC71;}
  .verify-tips.fail{color:#F53128;}
}
</style>
