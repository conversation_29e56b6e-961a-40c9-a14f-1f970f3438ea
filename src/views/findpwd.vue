<template>
  <div class="find-password">
    <el-container>
      <Header name="找回密码" :link="true" :bid="bid"/>
      <el-main>
        <div class="w1200 bg-white content">
          <h1>找回密码</h1>
          <div class="form">
            <el-form
              :model="Form"
              label-width="0px"
              class="login-phone"
              ref="Formpwd"
              :rules="rules"
            >
              <el-form-item prop="account">
                <el-input
                  v-model="Form.account"
                  placeholder="输入手机号/邮箱"
                  :clearable="true"
                  class="w360"
                ></el-input>
              </el-form-item>
              <el-form-item prop="code">
                <el-input
                  v-model="Form.code"
                  placeholder="输入验证码"
                  :clearable="true"
                  class="code"
                >
                  <template #append>
                    <span class="gray" v-if="send"
                      >{{ countTime }}s重新获取</span
                    >
                    <span class="lightblue" v-else @click="sendSmsCode"
                      >发送验证码</span
                    >
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  v-model="Form.password"
                  placeholder="设置密码(6-20位字母数字符号组合)"
                  :clearable="true"
                  show-password
                  class="w360"
                ></el-input>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  @click="setPassword"
                  class="submit-btn"
                  :loading="loading"
                  >重置密码</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-main>
      <el-footer>
        <Footer />
      </el-footer>
    </el-container>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  ref,
  toRefs,
  Ref,
  onBeforeMount,
} from "vue";
import { computed, getCurrentInstance } from "@vue/runtime-core";
import { useRouter, useRoute } from "vue-router";
import { initGeet } from "../mixins/geetestMixin";
import { getCookies } from "@/utils/common"
import {
  phonePassword,
  emailPassword,
  geetestSendCode,
  emailSendcode,
} from "../http/api";
import { ElMessage, ElMessageBox } from "element-plus";
import Footer from "@/components/Footer.vue";
import Header from "@/components/header.vue";

export default defineComponent({
  components: { Footer, Header },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const Formpwd: Ref<any> = ref(null);
    const state = reactive({
      Form: {
        password: "",
        account: "",
        code: "",
      },
      send: false,
      loading: false,
      captcha: {} as any, //验证码实例
      clock: 0,
      countTime: 60,
      bid: computed(() => {
                return parseInt(getCookies("bid"));
            })
    });
    const validateaccount = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输入手机号码或邮箱"));
      } else {
        if (
          /^1[3456789]\d{9}$/.test(value) ||
          /\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/.test(value)
        ) {
        } else {
          callback(new Error("请输入正确手机号码或邮箱"));
        }
        callback();
      }
    };
    const validatecode = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输验证码"));
      } else {
        if (/^[A-Za-z0-9]{3,6}$/.test(value)) {
        } else {
          callback(new Error("请输入正确的验证码"));
        }
        callback();
      }
    };
    let textPass =/^(?:(?=.*[0-9].*)(?=.*[A-Za-z].*)(?=.*[,\.#@%'!&\+\$\*\=\|\\\?\(\)/<>~\{\}\[\]"\-:;^_`].*))[,\.#@%'!&\+\$\*\=\|\\\?\(\)/<>~\{\}\[\]\-:;^_`0-9A-Za-z]{6,20}$/;
    // 密码不能为相同数字或者连续数字！
    const validatepassword = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输入新密码"));
      } else {
        if (!textPass.test(value)) {callback(new Error("请输入密码长度为6到16个字符由英文字母、数字和符号组合"));
        } else {
          callback();
        }
        
      }
    };
    let rules = {
      account: [{ validator: validateaccount, trigger: "blur" }],
      code: [{ validator: validatecode, trigger: "blur" }],
      password: [{ validator: validatepassword, trigger: "blur" }],
    };
    onBeforeMount(async () => {
      state.captcha = await initGeet();
    });
    const methods = {
      async initGeet() {
        state.captcha = await initGeet();
      },
      // 提交数据
      async setPassword() {
        let value = state.Form.account;
        let res: any;
        state.loading = true;
        if (/^1[3456789]\d{9}$/.test(value)) {
          res = await phonePassword(state.Form);
        } else {
          res = await emailPassword(state.Form);
        }
        state.loading = false;
        if (res.code == 1) {
          ElMessageBox.confirm("密码修改完成!", "", {
            customClass: "mimaxiugai",
            confirmButtonText: "立即登录",
            type: "success",
            center: true,
            showCancelButton: false,
          }).then(() => {
            router.push({ path: "/login" });
          });
        } else {
          ElMessage.error(res.message);
        }
      },
      //极验
      async geetestValidate() {
        state.captcha.verify();
        await state.captcha.onSuccess(async () => {
          const result = state.captcha.getValidate();
          if (result != undefined) {
            // state.loading = true;
            let data = {
              geetest_challenge: result.geetest_challenge,
              geetest_validate: result.geetest_validate,
              geetest_seccode: result.geetest_seccode,
              phone: state.Form.account,
              site: 1,
            };
            let data2 = {
              geetest_challenge: result.geetest_challenge,
              geetest_validate: result.geetest_validate,
              geetest_seccode: result.geetest_seccode,
              email: state.Form.account,
            };
            let value = state.Form.account;
            let res: any;
            if (/^1[3456789]\d{9}$/.test(value)) {
              res = await geetestSendCode(data);
            } else {
              res = await emailSendcode(data2);
            }

            // state.loading = false;
            if (res.code == 1) {
              ElMessage.success(res.message);
              fun.countDown();
            } else if (res.code !== 1) {
              ElMessage.error(res.message);
              return;
            } else {
              ElMessage.error("验证失败，请稍后再试");

              return;
            }
          } else {
            ElMessage.error("验证失败，请稍后再试");
            return;
          }
        });
      },
    };
    const fun = {
      //重置密码
      setPassword() {
        Formpwd.value.validate((valid: boolean) => {
          if (valid) {
            methods.setPassword();
          }
        });
      },
      //发送验证码
      sendSmsCode() {
        Formpwd.value.validateField("account", (valid: boolean) => {
          if (!valid) {
            methods.geetestValidate();
          }
        });
      },
      countDown() {
        state.send = true;
        state.clock = window.setInterval(() => {
          state.countTime--;
          if (state.countTime < 0) {
            window.clearInterval(state.clock);
            state.countTime = 0;
            state.send = false;
            //重新初始化极验
            methods.initGeet();
          }
        }, 1000);
        state.countTime = 60;
      },
    };

    return { ...toRefs(state), Formpwd, ...fun, rules };
  },
});
</script>
<style lang="less">
.find-password {
  h1 {
    font-size: 22px;
    color: #333;
    padding-bottom: 40px;
  }
  .content {
    padding: 50px 0;
    text-align: center;
    .form {
      width: 360px;
      margin: auto;
    }
    .el-input__inner {
      border: 1px solid #f2f2f2;
      height: 44px;
      line-height: 44px;
    }
    .submit-btn {
      width: 360px;
      background: #5f9efc;
      font-size: 18px;
    }
    .code {
      border: 1px solid #f2f2f2;
      .el-input__inner {
        border: none;
      }
      .el-input-group__append {
        border: none;
        background: #fff;
        cursor: pointer;
      }
    }
    .el-form-item {
      margin-bottom: 28px;
    }
  }
  .el-footer {
    overflow: hidden;
    width: 100%;
    background: #fff;
    height: auto;
  }
  .lightblue {
    color: #5f9efc;
    font-size: 14px;
  }
}
.mimaxiugai {
  width: 370px;
  .el-button--primary {
    background: #4187f2;
    width: 160px;
    height: 40px;
    line-height: 40px;
    padding: 0 0 0 0;
    font-size: 14px;
  }
  .el-message-box__message {
    font-size: 20px;
    color: #333;
  }
  .el-message-box__btns {
    margin-top: 30px;
  }
  .el-icon-success:before {
    font-size: 45px;
    color: #4187f2;
  }
}
</style>