<template>
  <BaseConfirmationStep
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <el-form-item label="个人描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="6"
          :maxlength="1000"
          show-word-limit
          placeholder="请简要描述您的个人特点、优势、职业目标等"
          class="field-input"
        />
      </el-form-item>
    </template>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="handleConfirm" type="primary">
        确认
      </el-button>
      <el-button @click="$emit('skip')">
        跳过
      </el-button>
    </div>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import BaseConfirmationStep from './BaseConfirmationStep.vue';
import { PersonalDescriptionData } from '../types';

export default defineComponent({
  name: 'PersonalDescriptionStep',
  components: {
    BaseConfirmationStep
  },
  props: {
    data: {
      type: Object as () => PersonalDescriptionData,
      default: () => ({})
    },
    step: {
      type: Number,
      required: true
    },
    recordIndex: {
      type: Number,
      default: 0
    }
  },
  emits: ['confirm', 'skip', 'edit', 'data-change', 'save-success'],
  setup(props, { emit }) {
    // 表单数据
    const formData = reactive<PersonalDescriptionData>({} as PersonalDescriptionData);
    
    // 表单验证规则
    const formRules = computed(() => ({
      description: [
        { max: 1000, message: '描述不能超过1000个字符', trigger: 'blur' }
      ]
    }));

    // 监听数据变化
    watch(() => props.data, (newData) => {
      if (newData) {
        Object.assign(formData, newData);
      }
    }, { immediate: true, deep: true });

    // 监听编辑数据变化，实时同步给父组件
    watch(() => formData, (newFormData) => {
      emit('data-change', { ...newFormData });
    }, { deep: true });

    // 处理确认（由BaseConfirmationStep的表单验证触发）
    const handleConfirm = () => {
      // 表单验证已通过，提交数据
      emit('edit', { ...formData });
      emit('confirm');
    };

    // 处理保存
    const handleSave = async () => {
      try {
        emit('edit', { ...formData });
        console.log('PersonalDescriptionStep 保存数据:', formData);
        emit('save-success');
      } catch (error) {
        console.error('保存失败:', error);
        ElMessage.error('保存失败，请重试');
      }
    };

    return {
      formData,
      formRules,
      handleConfirm,
      handleSave
    };
  }
});
</script>
