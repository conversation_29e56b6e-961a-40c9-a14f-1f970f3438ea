<template>
  <BaseConfirmationStep
    :errors="errors"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <div class="field-item">
        <label class="field-label">外语语种</label>
        <div class="field-value">
          <el-select
            v-model="editData.language"
            placeholder="请选择外语语种"
            class="field-input"
          >
            <el-option label="英语" value="英语" />
            <el-option label="日语" value="日语" />
            <el-option label="韩语" value="韩语" />
            <el-option label="法语" value="法语" />
            <el-option label="德语" value="德语" />
            <el-option label="俄语" value="俄语" />
            <el-option label="西班牙语" value="西班牙语" />
            <el-option label="阿拉伯语" value="阿拉伯语" />
            <el-option label="其他" value="其他" />
          </el-select>
        </div>
      </div>

      <div class="field-item">
        <label class="field-label">综合能力</label>
        <div class="field-value">
          <el-select
            v-model="editData.comprehensiveAbility"
            placeholder="请选择综合能力水平"
            class="field-input"
          >
            <el-option label="初级" value="初级" />
            <el-option label="中级" value="中级" />
            <el-option label="中高级" value="中高级" />
            <el-option label="高级" value="高级" />
            <el-option label="专业级" value="专业级" />
            <el-option label="母语水平" value="母语水平" />
          </el-select>
        </div>
      </div>

      <div class="field-item">
        <label class="field-label">听说能力</label>
        <div class="field-value">
          <el-select
            v-model="editData.listeningAndSpeaking"
            placeholder="请选择听说能力水平"
            class="field-input"
          >
            <el-option label="一般" value="一般" />
            <el-option label="良好" value="良好" />
            <el-option label="熟练" value="熟练" />
            <el-option label="精通" value="精通" />
          </el-select>
        </div>
      </div>

      <div class="field-item">
        <label class="field-label">读写能力</label>
        <div class="field-value">
          <el-select
            v-model="editData.readingAndWriting"
            placeholder="请选择读写能力水平"
            class="field-input"
          >
            <el-option label="一般" value="一般" />
            <el-option label="良好" value="良好" />
            <el-option label="熟练" value="熟练" />
            <el-option label="精通" value="精通" />
          </el-select>
        </div>
      </div>
    </template>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="handleConfirm" type="primary" :disabled="hasErrors">
        确认
      </el-button>
      <el-button @click="$emit('skip')">
        跳过
      </el-button>
    </div>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import BaseConfirmationStep from './BaseConfirmationStep.vue';
import { LanguageSkillsData } from '../types';

export default defineComponent({
  name: 'LanguageSkillsStep',
  components: {
    BaseConfirmationStep
  },
  props: {
    data: {
      type: Object as () => LanguageSkillsData,
      default: () => ({})
    },
    step: {
      type: Number,
      required: true
    },
    recordIndex: {
      type: Number,
      default: 0
    }
  },
  emits: ['confirm', 'skip', 'edit', 'data-change', 'save-success'],
  setup(props, { emit }) {
    const state = reactive({
      editData: {} as LanguageSkillsData,
      errors: [] as string[]
    });

    // 监听数据变化
    watch(() => props.data, (newData) => {
      if (newData) {
        state.editData = { ...newData };
      }
    }, { immediate: true, deep: true });

    // 监听编辑数据变化，实时同步给父组件
    watch(() => state.editData, (newEditData) => {
      emit('data-change', { ...newEditData });
    }, { deep: true });

    // 计算属性
    const errors = computed(() => state.errors);
    const hasErrors = computed(() => state.errors.length > 0);

    // 数据验证
    const validateData = () => {
      const errors: string[] = [];

      if (!state.editData.language || state.editData.language.trim() === '') {
        errors.push('外语语种不能为空');
      }

      state.errors = errors;
      return errors.length === 0;
    };

    // 处理确认
    const handleConfirm = () => {
      if (validateData()) {
        emit('edit', { ...state.editData });
        emit('confirm');
      }
    };

    // 处理保存
    const handleSave = async () => {
      if (validateData()) {
        try {
          // 保存数据到父组件
          emit('edit', { ...state.editData });
          
          // 这里可以添加具体的API保存逻辑
          // 目前先直接emit保存成功事件
          console.log('LanguageSkillsStep 保存数据:', state.editData);
          
          // 保存成功，通知父组件进行下一步
          emit('save-success');
        } catch (error) {
          console.error('保存失败:', error);
          // 可以显示错误提示
          ElMessage.error('保存失败，请重试');
        }
      }
    };

    return {
      editData: computed(() => state.editData),
      errors,
      hasErrors,
      handleConfirm,
      handleSave
    };
  }
});
</script>
