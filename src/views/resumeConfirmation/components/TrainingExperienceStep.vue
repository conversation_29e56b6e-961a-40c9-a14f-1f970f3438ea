<template>
  <BaseConfirmationStep
    :errors="errors"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <div class="field-item">
        <label class="field-label">培训课程</label>
        <div class="field-value">
          <el-input
            v-model="editData.trainCourse"
            placeholder="请输入培训课程名称"
            class="field-input"
          />
        </div>
      </div>

      <div class="field-item">
        <label class="field-label">培训机构</label>
        <div class="field-value">
          <el-input
            v-model="editData.trainInstitution"
            placeholder="请输入培训机构名称"
            class="field-input"
          />
        </div>
      </div>

      <div class="field-item">
        <label class="field-label">培训时间</label>
        <div class="field-value">
          <el-input
            v-model="editData.trainBeginTime"
            placeholder="如：2022-03 至 2022-06"
            class="field-input"
          />
          <el-input
            v-model="editData.trainEndTime"
            placeholder="如：2022-03 至 2022-06"
            class="field-input"
          />
        </div>
      </div>

      <div class="field-item">
        <label class="field-label">培训内容</label>
        <div class="field-value">
          <el-input
            v-model="editData.trainingDescription"
            type="textarea"
            :rows="4"
            :maxlength="800"
            show-word-limit
            placeholder="请描述培训的主要内容和收获"
            class="field-input"
          />
        </div>
      </div>
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import BaseConfirmationStep from './BaseConfirmationStep.vue';
import { TrainingExperienceData } from '../types';

export default defineComponent({
  name: 'TrainingExperienceStep',
  components: {
    BaseConfirmationStep
  },
  props: {
    data: {
      type: Object as () => TrainingExperienceData,
      default: () => ({})
    },
    step: {
      type: Number,
      required: true
    },
    recordIndex: {
      type: Number,
      default: 0
    }
  },
  emits: ['confirm', 'skip', 'edit', 'data-change', 'save-success'],
  setup(props, { emit }) {
    const state = reactive({
      editData: {} as TrainingExperienceData,
      errors: [] as string[]
    });

    // 监听数据变化
    watch(() => props.data, (newData) => {
      if (newData) {
        state.editData = { ...newData };
      }
    }, { immediate: true, deep: true });

    // 监听编辑数据变化，实时同步给父组件
    watch(() => state.editData, (newEditData) => {
      emit('data-change', { ...newEditData });
    }, { deep: true });

    // 计算属性
    const errors = computed(() => state.errors);
    const hasErrors = computed(() => state.errors.length > 0);

    // 数据验证
    const validateData = () => {
      const errors: string[] = [];

      if (!state.editData.trainingCourse || state.editData.trainingCourse.trim() === '') {
        errors.push('培训课程不能为空');
      }

      if (!state.editData.trainingInstitution || state.editData.trainingInstitution.trim() === '') {
        errors.push('培训机构不能为空');
      }

      if (!state.editData.trainingTime || state.editData.trainingTime.trim() === '') {
        errors.push('培训时间不能为空');
      }

      // 验证培训内容长度
      if (state.editData.trainingContent && state.editData.trainingContent.length > 800) {
        errors.push('培训内容不能超过800个字符');
      }

      state.errors = errors;
      return errors.length === 0;
    };

    // 处理确认
    const handleConfirm = () => {
      if (validateData()) {
        emit('edit', { ...state.editData });
        emit('confirm');
      }
    };

    // 处理保存
    const handleSave = async () => {
      if (validateData()) {
        try {
          // 保存数据到父组件
          emit('edit', { ...state.editData });
          
          // 这里可以添加具体的API保存逻辑
          // 目前先直接emit保存成功事件
          console.log('TrainingExperienceStep 保存数据:', state.editData);
          
          // 保存成功，通知父组件进行下一步
          emit('save-success');
        } catch (error) {
          console.error('保存失败:', error);
          // 可以显示错误提示
          ElMessage.error('保存失败，请重试');
        }
      }
    };

    return {
      editData: computed(() => state.editData),
      errors,
      hasErrors,
      handleConfirm,
      handleSave
    };
  }
});
</script>
