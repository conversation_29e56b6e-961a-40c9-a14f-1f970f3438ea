<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <!-- 技能名称 -->
      <el-form-item label="技能名称" prop="techName">
        <el-input
          v-model="formData.techName"
          placeholder="请输入技能名称，如：Java、Python等"
          class="field-input"
        />
      </el-form-item>

      <!-- 使用时间 -->
      <el-form-item label="使用时间" prop="monthUsed">
        <el-input
          v-model="formData.monthUsed"
          placeholder="请输入使用时间"
          type="number"
        >
          <template #suffix>
            <i class="yue">月</i>
          </template>
        </el-input>
      </el-form-item>

      <!-- 掌握程度 -->
      <el-form-item label="掌握程度" prop="techLevel">
        <el-select
          v-model="formData.techLevel"
          placeholder="请选择掌握程度"
          class="field-input"
        >
          <el-option
            v-for="item in technologylevelList"
            :key="item.keywordID"
            :label="item.keywordName"
            :value="item.keywordID"
            @click="formData.techLevelId = item.keywordID"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  computed,
  watch,
  ref,
  onBeforeMount,
  toRefs,
} from "vue";
import BaseConfirmationStep from "./BaseConfirmationStep.vue";
import { TechnicalSkillsData } from "../types";
import { ResumepartClass } from "../../../http/app/Resumepart";
import { ResumeEditTechnologyDto } from "../../../http/app/data-contracts";
import { useSaveStep } from "../composables/useSaveStep";
import { getTechnologylevel } from "../../../http/dictionary";

export default defineComponent({
  name: "TechnicalSkillsStep",
  components: {
    BaseConfirmationStep,
  },
  props: {
    data: {
      type: Object as () => TechnicalSkillsData,
      default: () => ({}),
    },
    step: {
      type: Number,
      required: true,
    },
    recordIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ["confirm", "skip", "edit", "data-change", "save-success"],
  setup(props, { emit }) {
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();

    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<TechnicalSkillsData>({} as TechnicalSkillsData);

    // 表单验证规则
    const formRules = computed(() => ({
      techName: [
        {
          required: true,
          message: "请输入技能名称",
          trigger: "blur",
        },
        {
          min: 1,
          max: 25,
          message: "长度1-25个字符",
          trigger: "blur",
        },
      ],
      monthUsed: [
        {
          required: true,
          message: "请输入使用时间",
          trigger: "blur",
        },
      ],
      techLevel: [
        {
          required: true,
          message: "请选择掌握程度",
          trigger: "change",
        },
      ],
    }));

    const state = reactive({
      technologylevelList: [] as any[],
    });

    // 监听数据变化
    watch(
      () => props.data,
      (newData) => {
        if (newData) {
          Object.assign(formData, newData);
          console.log("TechnicalSkillsStep 接收到数据:", formData);
        }
      },
      { immediate: true, deep: true }
    );

    // 掌握程度映射函数
    const masteryLevelToId = (level: string): number => {
      const levelMap: { [key: string]: number } = {
        了解: 1,
        熟悉: 2,
        熟练: 3,
        精通: 4,
        专家: 5,
      };
      return levelMap[level] || 1;
    };

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      console.log("🔥 TechnicalSkillsStep handleConfirm 被调用了！");

      // 创建API调用包装函数
      const apiCall = (data: ResumeEditTechnologyDto) => {
        return ResumepartClass.apiResumepartSavetechnologyPost(
          { logtoken: "" },
          data
        );
      };

      await executeSave(formData, apiCall, baseStepRef, emit, {
        onSaveSuccess: (result) => {
          console.log("技术能力保存成功回调:", result);
        },
        onSaveError: (error) => {
          console.error("技术能力保存失败回调:", error);
        },
      });
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log("BaseConfirmationStep 验证通过");

      // 表单验证已通过，提交数据
      emit("edit", { ...formData });
      emit("confirm");
    };
    const getTechnologylevelList = async () => {
      let form = {
        resumeid: formData.resumeId,
      };
      let res: any = await getTechnologylevel(form);
      state.technologylevelList = res.data;
    };
    onBeforeMount(async () => {
      await getTechnologylevelList();
    });
    return {
      baseStepRef,
      formData,
      formRules,
      saving,
      handleConfirm,
      handleBaseConfirm,
      ...toRefs(state),
    };
  },
});
</script>
