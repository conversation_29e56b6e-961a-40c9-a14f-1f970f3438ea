<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <!-- 技能名称 -->
      <el-form-item label="技能名称" prop="skillName">
        <el-input
          v-model="formData.skillName"
          placeholder="请输入技能名称，如：Java、Python等"
          class="field-input"
        />
      </el-form-item>

      <!-- 使用时间 -->
      <el-form-item label="使用时间" prop="useTime">
        <el-input
          v-model="formData.useTime"
          placeholder="如：3年、1-2年等"
          class="field-input"
        />
      </el-form-item>

      <!-- 掌握程度 -->
      <el-form-item label="掌握程度" prop="masteryLevel">
        <el-select
          v-model="formData.masteryLevel"
          placeholder="请选择掌握程度"
          class="field-input"
        >
          <el-option label="了解" value="了解" />
          <el-option label="熟悉" value="熟悉" />
          <el-option label="熟练" value="熟练" />
          <el-option label="精通" value="精通" />
          <el-option label="专家" value="专家" />
        </el-select>
      </el-form-item>
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, watch, ref } from 'vue';
import BaseConfirmationStep from './BaseConfirmationStep.vue';
import { TechnicalSkillsData } from '../types';
import { ResumepartClass } from '../../../http/app/Resumepart';
import { ResumeEditTechnologyDto } from '../../../http/app/data-contracts';
import { useSaveStep } from '../composables/useSaveStep';

export default defineComponent({
  name: 'TechnicalSkillsStep',
  components: {
    BaseConfirmationStep
  },
  props: {
    data: {
      type: Object as () => TechnicalSkillsData,
      default: () => ({})
    },
    step: {
      type: Number,
      required: true
    },
    recordIndex: {
      type: Number,
      default: 0
    }
  },
  emits: ['confirm', 'skip', 'edit', 'data-change', 'save-success'],
  setup(props, { emit }) {
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();

    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<TechnicalSkillsData>({} as TechnicalSkillsData);

    // 表单验证规则
    const formRules = computed(() => ({
      skillName: [
        { required: true, message: '请输入技能名称', trigger: 'blur' }
      ],
      masteryLevel: [
        { required: true, message: '请选择掌握程度', trigger: 'change' }
      ]
    }));

    // 监听数据变化
    watch(() => props.data, (newData) => {
      if (newData) {
        Object.assign(formData, newData);
        console.log("TechnicalSkillsStep 接收到数据:", formData);
      }
    }, { immediate: true, deep: true });

    // 监听编辑数据变化，实时同步给父组件
    watch(() => formData, (newFormData) => {
      emit('data-change', { ...newFormData });
    }, { deep: true });

    // 掌握程度映射函数
    const masteryLevelToId = (level: string): number => {
      const levelMap: { [key: string]: number } = {
        '了解': 1,
        '熟悉': 2,
        '熟练': 3,
        '精通': 4,
        '专家': 5
      };
      return levelMap[level] || 1;
    };

    // 使用时间转换函数（将字符串转换为月数）
    const useTimeToMonths = (timeStr: string): number => {
      if (!timeStr) return 0;

      // 提取数字
      const match = timeStr.match(/(\d+)/);
      if (!match) return 0;

      const num = parseInt(match[1]);

      // 如果包含"年"，转换为月
      if (timeStr.includes('年')) {
        return num * 12;
      }

      // 如果包含"月"，直接返回
      if (timeStr.includes('月')) {
        return num;
      }

      // 默认按年处理
      return num * 12;
    };

    // 数据转换函数：将 TechnicalSkillsData 转换为 ResumeEditTechnologyDto
    const transformDataForAPI = (data: TechnicalSkillsData): ResumeEditTechnologyDto => {
      return {
        techId: undefined, // 新增时为空
        resumeId: undefined, // 由API调用时设置
        techName: data.skillName || '',
        monthUsed: useTimeToMonths(data.useTime || ''),
        techLevelId: masteryLevelToId(data.masteryLevel || '了解')
      };
    };

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      console.log('🔥 TechnicalSkillsStep handleConfirm 被调用了！');

      // 创建API调用包装函数
      const apiCall = (data: ResumeEditTechnologyDto) => {
        return ResumepartClass.apiResumepartSavetechnologyPost(
          { logtoken: '' },
          data
        );
      };

      await executeSave(
        formData,
        apiCall,
        baseStepRef,
        emit,
        {
          transformData: transformDataForAPI,
          onSaveSuccess: (result) => {
            console.log('技术能力保存成功回调:', result);
          },
          onSaveError: (error) => {
            console.error('技术能力保存失败回调:', error);
          }
        }
      );
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log('BaseConfirmationStep 验证通过');

      // 表单验证已通过，提交数据
      emit('edit', { ...formData });
      emit('confirm');
    };

    return {
      baseStepRef,
      formData,
      formRules,
      saving,
      handleConfirm,
      handleBaseConfirm
    };
  }
});
</script>
