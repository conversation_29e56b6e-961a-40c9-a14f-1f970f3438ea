<template>
  <BaseConfirmationStep
    ref="baseStepRef"
    :form-data="formData"
    :form-rules="formRules"
    :step="step"
    :recordIndex="recordIndex"
    @confirm="handleBaseConfirm"
    @skip="$emit('skip')"
  >
    <template #fields>
      <!-- 证书名称 -->
      <el-form-item label="证书名称" prop="certificateName">
        <el-input
          v-model="formData.certificateName"
          placeholder="请输入证书名称"
          class="field-input"
          maxlength="30"
          show-word-limit
        />
      </el-form-item>

      <!-- 证书类型 -->
      <el-form-item label="证书类型" prop="certificateType">
        <el-select
          v-model="formData.certificateType"
          placeholder="请选择证书类型"
          class="field-input"
        >
          <el-option label="职业资格证书" value="职业资格证书" />
          <el-option label="技能证书" value="技能证书" />
          <el-option label="专业技术资格证书" value="专业技术资格证书" />
          <el-option label="学历证书" value="学历证书" />
          <el-option label="培训证书" value="培训证书" />
          <el-option label="语言证书" value="语言证书" />
          <el-option label="计算机证书" value="计算机证书" />
          <el-option label="其他" value="其他" />
        </el-select>
      </el-form-item>

      <!-- 获得时间 -->
      <el-form-item label="获得时间" prop="obtainTime">
        <el-date-picker
          v-model="formData.obtainTime"
          type="month"
          placeholder="请选择获得时间"
          format="YYYY-MM"
          value-format="YYYY-MM"
          class="field-input"
          :disabled-date="disabledDate"
        />
      </el-form-item>

      <!-- 证书等级 -->
      <el-form-item label="证书等级" prop="certificateLevel">
        <el-select
          v-model="formData.certificateLevel"
          placeholder="请选择证书等级"
          class="field-input"
        >
          <el-option label="初级" value="初级" />
          <el-option label="中级" value="中级" />
          <el-option label="高级" value="高级" />
          <el-option label="技师" value="技师" />
          <el-option label="高级技师" value="高级技师" />
          <el-option label="专家级" value="专家级" />
          <el-option label="无等级" value="无等级" />
        </el-select>
      </el-form-item>
    </template>
  </BaseConfirmationStep>
</template>

<script lang="ts">
import { defineComponent, reactive, computed, watch, ref } from 'vue';
import BaseConfirmationStep from './BaseConfirmationStep.vue';
import { CertificateData } from '../types';
import { ResumepartClass } from '../../../http/app/Resumepart';
import { ResumeEditCertDto } from '../../../http/app/data-contracts';
import { useSaveStep } from '../composables/useSaveStep';

export default defineComponent({
  name: 'CertificatesStep',
  components: {
    BaseConfirmationStep
  },
  props: {
    data: {
      type: Object as () => CertificateData,
      default: () => ({})
    },
    step: {
      type: Number,
      required: true
    },
    recordIndex: {
      type: Number,
      default: 0
    }
  },
  emits: ['confirm', 'skip', 'edit', 'data-change', 'save-success'],
  setup(props, { emit }) {
    // BaseConfirmationStep 的引用
    const baseStepRef = ref();

    // 使用通用保存组合式函数
    const { saving, executeSave } = useSaveStep();

    // 表单数据
    const formData = reactive<CertificateData>({} as CertificateData);

    // 表单验证规则
    const formRules = computed(() => ({
      certificateName: [
        { required: true, message: '请输入证书名称', trigger: 'blur' },
        { max: 30, message: '证书名称不能超过30个字符', trigger: 'blur' }
      ],
      obtainTime: [
        { required: true, message: '请选择获得时间', trigger: 'change' }
      ]
    }));

    // 监听数据变化
    watch(() => props.data, (newData) => {
      if (newData) {
        Object.assign(formData, newData);
        console.log("CertificatesStep 接收到数据:", formData);
      }
    }, { immediate: true, deep: true });

    // 监听编辑数据变化，实时同步给父组件
    watch(() => formData, (newFormData) => {
      emit('data-change', { ...newFormData });
    }, { deep: true });

    // 禁用未来日期
    const disabledDate = (date: Date) => {
      return date && date.getTime() > Date.now();
    };

    // 数据转换函数：将 CertificateData 转换为 ResumeEditCertDto
    const transformDataForAPI = (data: CertificateData): ResumeEditCertDto => {
      // 证书类型映射（这里简化处理，实际项目中可能需要从字典获取）
      const certificateTypeMap: Record<string, number> = {
        '职业资格证书': 1,
        '技能证书': 2,
        '专业技术资格证书': 3,
        '学历证书': 4,
        '培训证书': 5,
        '语言证书': 6,
        '计算机证书': 7,
        '其他': 8
      };

      // 证书等级映射
      const certificateLevelMap: Record<string, number> = {
        '初级': 1,
        '中级': 2,
        '高级': 3,
        '技师': 4,
        '高级技师': 5,
        '专家级': 6,
        '无等级': 0
      };

      return {
        certName: data.certificateName || '',
        getTime: data.obtainTime || '',
        certificateType: data.certificateType ? certificateTypeMap[data.certificateType] : undefined,
        certTypeLevel: data.certificateLevel ? certificateLevelMap[data.certificateLevel] : undefined,
        // resumeId 和 certId 会在API调用时设置
      };
    };

    // 外部调用的确认处理方法（主动触发BaseConfirmationStep验证）
    const handleConfirm = async () => {
      console.log('🔥 CertificatesStep handleConfirm 被调用了！');

      await executeSave(
        formData,
        ResumepartClass.apiResumepartSavecertificatePost,
        baseStepRef,
        emit,
        {
          transformData: transformDataForAPI,
          onSaveSuccess: (result) => {
            console.log('证书保存成功回调:', result);
          },
          onSaveError: (error) => {
            console.error('证书保存失败回调:', error);
          }
        }
      );
    };

    // BaseConfirmationStep内部验证成功后的回调（保留用于其他组件兼容性）
    const handleBaseConfirm = () => {
      console.log('BaseConfirmationStep 验证通过');

      // 表单验证已通过，提交数据
      emit('edit', { ...formData });
      emit('confirm');
    };

    return {
      formData,
      formRules,
      saving,
      baseStepRef,
      disabledDate,
      handleConfirm,
      handleBaseConfirm
    };
  }
});
</script>
