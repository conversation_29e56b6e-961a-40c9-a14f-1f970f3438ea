<template>
  <div class="base-confirmation-step">
    <!-- el-form 表单包装 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="confirmation-form"
      require-asterisk-position="right"
    >
      <!-- 数据展示区域 - 通过插槽让子组件自定义字段内容 -->
      <div class="data-display">
        <div class="field-list">
          <slot name="fields" :form-data="formData" :form-ref="formRef"></slot>
        </div>
      </div>
    </el-form>

    <!-- 错误提示 -->
    <div v-if="hasErrors" class="error-tips">
      <div class="error-title">请检查以下信息</div>
      <div class="error-list">
        <div v-for="error in errors" :key="error" class="error-item">
          {{ error }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, PropType, ref, provide } from "vue";
import type { FormInstance } from "element-plus";

export default defineComponent({
  name: "BaseConfirmationStep",
  props: {
    // 错误信息数组，由子组件传入
    errors: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    // 表单数据，由子组件传入
    formData: {
      type: Object,
      default: () => ({}),
    },
    // 表单验证规则，由子组件传入
    formRules: {
      type: Object,
      default: () => ({}),
    },
    // 步骤信息，保留用于扩展
    step: {
      type: Number,
      required: true,
    },
    recordIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ["confirm", "skip", "validate-success", "validate-error"],
  setup(props, { emit, expose }) {
    const formRef = ref<FormInstance>();

    // 计算属性
    const hasErrors = computed(() => {
      return props.errors.length > 0;
    });

    // 表单验证方法
    const validateForm = async (): Promise<boolean> => {
      if (!formRef.value) {
        console.warn("表单引用不存在");
        return false;
      }

      try {
        console.log(555);
        await formRef.value.validate();
        emit("validate-success");
        return true;
      } catch (error) {
        emit("validate-error", error);
        return false;
      }
    };

    // 清除验证
    const clearValidate = (props?: string | string[]) => {
      if (formRef.value) {
        formRef.value.clearValidate(props);
      }
    };

    // 重置表单
    const resetFields = () => {
      if (formRef.value) {
        formRef.value.resetFields();
      }
    };

    // 方法
    const handleConfirm = async () => {
      const isValid = await validateForm();
      if (isValid) {
        emit("confirm");
      }
    };

    const handleSkip = () => {
      emit("skip");
    };

    // 提供表单实例给子组件
    provide("formInstance", formRef);

    // 暴露方法给父组件
    expose({
      validateForm,
      clearValidate,
      resetFields,
      formRef,
    });

    return {
      formRef,
      hasErrors,
      handleConfirm,
      handleSkip,
      validateForm,
      clearValidate,
      resetFields,
    };
  },
});
</script>

<style lang="less" >
@import "../styles.less";
.el-form-item.is-required:not(.is-no-asterisk)
  > .el-form-item__label-wrap
  > .el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label::after {
  content: "*";
  color: var(--el-color-danger);
  margin-right: 4px;
}
.el-form-item.is-required:not(.is-no-asterisk)
  > .el-form-item__label-wrap
  > .el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
  content: "" !important;
  color: var(--el-color-danger);
  margin-right: 0px;
}
</style>
