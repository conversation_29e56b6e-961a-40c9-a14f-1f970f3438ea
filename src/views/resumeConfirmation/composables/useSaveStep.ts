// 通用步骤保存组合式函数
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';

export interface SaveResult {
  success: boolean;
  data?: any;
  error?: string;
}

export interface SaveStepOptions {
  validateBusinessLogic?: () => boolean;
  transformData?: (data: any) => any;
  onSaveSuccess?: (result: any) => void;
  onSaveError?: (error: string) => void;
}

export function useSaveStep() {
  // 保存状态
  const saving = ref(false);
  
  // 通用保存方法
  const executeSave = async (
    formData: any,
    apiCall: (data: any) => Promise<any>,
    baseStepRef: any,
    emit: any,
    options: SaveStepOptions = {}
  ) => {
    const {
      validateBusinessLogic,
      transformData,
      onSaveSuccess,
      onSaveError
    } = options;

    if (saving.value) {
      console.log('正在保存中，请稍候...');
      return false;
    }

    try {
      // 1. 表单验证
      if (baseStepRef?.value?.validateForm) {
        const isValid = await baseStepRef.value.validateForm();
        if (!isValid) {
          console.log('表单验证失败');
          return false;
        }
      }

      // 2. 业务逻辑验证
      if (validateBusinessLogic && !validateBusinessLogic()) {
        return false;
      }

      // 3. 开始保存
      console.log('所有验证通过，开始保存数据', formData);
      saving.value = true;

      // 4. 数据转换
      const dataToSave = transformData ? transformData(formData) : formData;

      // 5. 调用API
      const result = await saveDataWithAPI(dataToSave, apiCall);

      if (result.success) {
        console.log('保存成功');
        emit('edit', { ...formData });
        emit('confirm');
        ElMessage.success('保存成功！');
        
        if (onSaveSuccess) {
          onSaveSuccess(result.data);
        }
        return true;
      } else {
        console.error('保存失败:', result.error);
        ElMessage.error(result.error || '保存失败，请重试');
        
        if (onSaveError) {
          onSaveError(result.error || '保存失败');
        }
        return false;
      }

    } catch (error: any) {
      console.error('保存过程中出错:', error);
      const errorMsg = error?.message || error?.msg || '保存失败，请重试';
      ElMessage.error(errorMsg);
      
      if (onSaveError) {
        onSaveError(errorMsg);
      }
      return false;
    } finally {
      saving.value = false;
    }
  };

  // 通用API调用包装
  const saveDataWithAPI = async (data: any, apiCall: (data: any) => Promise<any>): Promise<SaveResult> => {
    try {
      console.log('调用保存API，数据:', data);
      const res = await apiCall(data);
      console.log('API响应:', res);

      // 检查API响应是否成功
      if (res && (res.code === 200 || res.code === 1 || res.success)) {
        return { success: true, data: res };
      } else {
        return {
          success: false,
          error: res?.message || res?.msg || '保存失败'
        };
      }
    } catch (error: any) {
      console.error('API调用失败:', error);
      return {
        success: false,
        error: error?.message || error?.msg || '网络请求失败，请重试'
      };
    }
  };

  return {
    saving,
    executeSave,
    saveDataWithAPI
  };
}