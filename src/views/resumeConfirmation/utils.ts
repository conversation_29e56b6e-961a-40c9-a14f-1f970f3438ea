// 简历确认功能的工具函数

import { 
  ResumeData, 
  ConfirmationStep, 
  FieldValidationResult,
  DynamicStepConfig,
  CareerIntentionData,
  WorkExperienceData,
  EducationExperienceData,
  ProjectExperienceData,
  TrainingExperienceData,
  TechnicalSkillsData,
  LanguageSkillsData,
  CertificateData,
  PersonalDescriptionData
} from './types';
import { STEP_CONFIGS } from './constants';
import { 
  ResumeAnalysisResultDto,
  CareerPartDto,
  WorkPartDto,
  EducationPartDto,
  ResumeEditProjectDto,
  ResumeEditTrainDto,
  TechnologyDto,
  CertificateDto,
  LanguageDto,
  ResumeEditDesDto
} from '../../http/app/data-contracts';

/**
 * 验证普通字段是否有值
 * @param value 要验证的值
 * @returns 是否有有效值
 */
export const hasValue = (value: any): boolean => {
  if (value === null || value === undefined) return false;
  if (typeof value === 'string' && value.trim() === '') return false;
  if (typeof value === 'object' && Object.keys(value).length === 0) return false;
  return true;
};

/**
 * 验证数组字段是否有数据
 * @param array 要验证的数组
 * @returns 是否有有效数据
 */
export const hasArrayData = (array: any[] | null | undefined): boolean => {
  return Array.isArray(array) && array.length > 0;
};

/**
 * 验证接口返回的各个字段
 * @param apiData 接口返回的数据
 * @returns 字段验证结果数组
 */
export const validateResumeFields = (apiData: ResumeAnalysisResultDto): FieldValidationResult[] => {
  const results: FieldValidationResult[] = [
    {
      hasData: hasValue(apiData.career),
      step: ConfirmationStep.CAREER_INTENTION,
      fieldName: 'career'
    },
    {
      hasData: hasArrayData(apiData.workList),
      step: ConfirmationStep.WORK_EXPERIENCE,
      fieldName: 'workList'
    },
    {
      hasData: hasArrayData(apiData.educationList),
      step: ConfirmationStep.EDUCATION_EXPERIENCE,
      fieldName: 'educationList'
    },
    {
      hasData: hasArrayData(apiData.projectList),
      step: ConfirmationStep.PROJECT_EXPERIENCE,
      fieldName: 'projectList'
    },
    {
      hasData: hasArrayData(apiData.trainList),
      step: ConfirmationStep.TRAINING_EXPERIENCE,
      fieldName: 'trainList'
    },
    {
      hasData: hasArrayData(apiData.technologyList),
      step: ConfirmationStep.TECHNICAL_SKILLS,
      fieldName: 'technologyList'
    },
    {
      hasData: hasArrayData(apiData.languageList),
      step: ConfirmationStep.LANGUAGE_SKILLS,
      fieldName: 'languageList'
    },
    {
      hasData: hasArrayData(apiData.certificateList),
      step: ConfirmationStep.CERTIFICATES,
      fieldName: 'certificateList'
    },
    {
      hasData: hasArrayData(apiData.descriptionList),
      step: ConfirmationStep.PERSONAL_DESCRIPTION,
      fieldName: 'descriptionList'
    }
  ];

  return results;
};

/**
 * 计算有效的步骤列表
 * @param apiData 接口返回的数据
 * @returns 有效步骤数组
 */
export const calculateValidSteps = (apiData: ResumeAnalysisResultDto): ConfirmationStep[] => {
  const validationResults = validateResumeFields(apiData);
  return validationResults
    .filter(result => result.hasData)
    .map(result => result.step);
};

/**
 * 生成动态步骤配置
 * @param apiData 接口返回的数据
 * @returns 动态步骤配置数组
 */
export const generateDynamicStepConfigs = (apiData: ResumeAnalysisResultDto): DynamicStepConfig[] => {
  const validationResults = validateResumeFields(apiData);
  
  return STEP_CONFIGS.map(config => {
    const validation = validationResults.find(v => v.step === config.step);
    let dataCount = 0;
    
    // 计算数据数量
    switch (config.step) {
      case ConfirmationStep.CAREER_INTENTION:
        dataCount = hasValue(apiData.career) ? 1 : 0;
        break;
      case ConfirmationStep.WORK_EXPERIENCE:
        dataCount = apiData.workList?.length || 0;
        break;
      case ConfirmationStep.EDUCATION_EXPERIENCE:
        dataCount = apiData.educationList?.length || 0;
        break;
      case ConfirmationStep.PROJECT_EXPERIENCE:
        dataCount = apiData.projectList?.length || 0;
        break;
      case ConfirmationStep.TRAINING_EXPERIENCE:
        dataCount = apiData.trainList?.length || 0;
        break;
      case ConfirmationStep.TECHNICAL_SKILLS:
        dataCount = apiData.technologyList?.length || 0;
        break;
      case ConfirmationStep.LANGUAGE_SKILLS:
        dataCount = apiData.languageList?.length || 0;
        break;
      case ConfirmationStep.CERTIFICATES:
        dataCount = apiData.certificateList?.length || 0;
        break;
      case ConfirmationStep.PERSONAL_DESCRIPTION:
        dataCount = apiData.descriptionList?.length || 0;
        break;
    }
    
    return {
      ...config,
      isValid: validation?.hasData || false,
      dataCount
    };
  });
};

/**
 * 映射求职意向数据
 */
const mapCareerData = (career: CareerPartDto | undefined): CareerIntentionData | undefined => {
  if (!career) return undefined;

  return {
    workStatusId: career.workStatusId || undefined,
    workingState: career.workingState || undefined,
    expectWorkPlace: career.expectWorkPlaceName?.join(', ') || undefined,
    expectWorkPlaceIds: career.expectWorkPlaceIds || undefined,
    expectWorkPlaceName: career.expectWorkPlaceName || undefined,
    currentSalary: career.currentSalary || undefined,
    expectSalary: career.expectSalary ? parseInt(career.expectSalary) : undefined,
    expectCareer: career.expectCareer1Name || undefined,
    expectIndustry: career.expectIndustry1Names?.join(', ') || undefined,
    
    // 多个期望职位
    expectCareer1: career.expectCareer1 || undefined,
    expectCareer1Name: career.expectCareer1Name || undefined,
    expectCareer2: career.expectCareer2 || undefined,
    expectCareer2Name: career.expectCareer2Name || undefined,
    expectCareer3: career.expectCareer3 || undefined,
    expectCareer3Name: career.expectCareer3Name || undefined,
    
    // 多个期望行业
    expectIndustry1: career.expectIndustry1 || undefined,
    expectIndustry1Names: career.expectIndustry1Names || undefined,
    expectIndustry2: career.expectIndustry2 || undefined,
    expectIndustry2Names: career.expectIndustry2Names || undefined,
    expectIndustry3: career.expectIndustry3 || undefined,
    expectIndustry3Names: career.expectIndustry3Names || undefined,
    
    // 完善状态标识
    isExpectCareer1: career.isExpectCareer1 || undefined,
    isExpectCareer2: career.isExpectCareer2 || undefined,
    isExpectCareer3: career.isExpectCareer3 || undefined,

    resumeId: career.resumeId || undefined,
  };
};

/**
 * 映射工作经历数据
 */
const mapWorkExperiences = (workList: WorkPartDto[] | null | undefined): WorkExperienceData[] => {
  if (!workList) return [];

  return workList.map(work => ({
    id: work.workId,
    entName: work.entName || '',
    department: work.department || undefined,
    experienceStartTime: work.experienceStartTime || '',
    experienceFinishTime: work.experienceFinishTime || '',
    positionDescription: work.positionDescription || undefined,
    workIndustryName: work.workIndustryName || undefined,
    workIndustryId: work.workIndustryId || undefined,
    positionTypeId: work.positionTypeId || undefined,
    positionTypeName: work.positionTypeName || undefined,
    workPlace: work.workPlace || undefined,
    isToThisDay: work.isToThisDay || undefined,
  }));
};

/**
 * 映射教育经历数据
 */
const mapEducationExperiences = (educationList: EducationPartDto[] | null | undefined): EducationExperienceData[] => {
  if (!educationList) return [];
  
  return educationList.map(edu => ({
    id: edu.id,
    school: edu.school || '',
    specialityInputName: edu.specialityInputName || undefined,
    specialityId: edu.specialityId || undefined,
    specialityName: edu.specialityName || undefined,
    education: edu.education || undefined,
    experienceStartTime: edu.experienceStartTime || '',
    experienceFinishTime: edu.experienceFinishTime || '',
    degreeId: edu.degreeId || undefined,
    majorDescription: edu.specialityDescription || undefined,
    resumeId: edu.resumeId || undefined,
  }));
};

/**
 * 映射项目经历数据
 */
const mapProjectExperiences = (projectList: ResumeEditProjectDto[] | null | undefined): ProjectExperienceData[] => {
  if (!projectList) return [];
  
  return projectList.map(project => ({
    id: project.id,
    projectName: project.projectName || '',
    beginTime: project.beginTime || '',
    endTime: project.endTime || '',
    resumeId: project.resumeId || undefined,
    playRole: project.playRole || undefined,
    playRoleStr: project.playRoleStr || undefined,
    projectDescription: project.projectDescription || undefined
  }));
};

/**
 * 映射培训经历数据
 */
const mapTrainingExperiences = (trainList: ResumeEditTrainDto[] | null | undefined): TrainingExperienceData[] => {
  if (!trainList) return [];
  
  return trainList.map(train => ({
    trainId: train.trainId || 0,
    trainCourse: train.trainCourse || '',
    trainInstitution: train.trainInstitution || '',
    trainBeginTime: train.trainBeginTime || '',
    trainEndTime: train.trainEndTime || '',
    resumeId: train.resumeId || undefined,
    trainingDescription: train.trainingDescription || undefined
  }));
};

/**
 * 映射技术能力数据
 */
const mapTechnicalSkills = (technologyList: TechnologyDto[] | null | undefined): TechnicalSkillsData[] => {
  if (!technologyList) return [];
  
  return technologyList.map(tech => ({
    techId: tech.techId || 0,
    techName: tech.techName || undefined,
    monthUsed: tech.monthUsed || undefined,
    techLevel: tech.techLevel || undefined,
    techLevelId: tech.techLevelId || 0,
    resumeId: tech.resumeId || undefined,
  }));
};

/**
 * 映射语言技能数据
 */
const mapLanguageSkills = (languageList: LanguageDto[] | null | undefined): LanguageSkillsData[] => {
  if (!languageList) return [];
  
  return languageList.map(lang => ({
    language: lang.langName || undefined,
    comprehensiveAbility: lang.langLevel || undefined,
    listeningAndSpeaking: lang.lsLevel || undefined,
    readingAndWriting: lang.rwLevel || undefined
  }));
};

/**
 * 映射证书职称数据
 */
const mapCertificates = (certificateList: CertificateDto[] | null | undefined): CertificateData[] => {
  if (!certificateList) return [];
  
  return certificateList.map(cert => ({
    certId: cert.certId || 0,
    certName: cert.certName || undefined,
    certTypeTitle: cert.certTypeTitle || undefined,
    certificateType: cert.certificateType || 0,
    getTime: cert.getTime || undefined,
    resumeId: cert.resumeId || 0,
    levelVisible: cert.levelVisible || false,
    certTypeLevel: cert.certTypeLevel || 0,
    certTypeLevelName: cert.certTypeLevelName || undefined,
  }));
};

/**
 * 映射个人描述数据
 */
const mapPersonalDescription = (descriptionList: ResumeEditDesDto[] | null | undefined): PersonalDescriptionData[] => {
  if (!descriptionList) return [];
  
  return descriptionList.map(desc => ({
    description: desc.desContent || undefined
  }));
};

/**
 * 将API数据映射为组件数据格式
 * @param apiData 接口返回的数据
 * @returns 组件使用的数据格式
 */
export const mapApiDataToResumeData = (apiData: ResumeAnalysisResultDto): ResumeData => {
  return {
    careerIntention: mapCareerData(apiData.career),
    workExperiences: mapWorkExperiences(apiData.workList),
    educationExperiences: mapEducationExperiences(apiData.educationList),
    projectExperiences: mapProjectExperiences(apiData.projectList),
    trainingExperiences: mapTrainingExperiences(apiData.trainList),
    technicalSkills: mapTechnicalSkills(apiData.technologyList),
    languageSkills: mapLanguageSkills(apiData.languageList),
    certificates: mapCertificates(apiData.certificateList),
    personalDescription: mapPersonalDescription(apiData.descriptionList)
  };
};
