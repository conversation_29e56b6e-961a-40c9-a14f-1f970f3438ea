// 简历解析确认功能的样式文件

.resume-confirmation {
  max-width: 690px;
  margin: 0 auto;
  padding-top: 100px;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", Arial, sans-serif;

  .confirmation-container {
    background: #fff;
    position: relative;
    padding: 70px;
  }
  .confirmation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;

    .exit-link {
      color: #999;
      text-decoration: none;
      font-size: 14px;
      transition: color 0.3s;

      &:hover {
        color: #5999f8;
      }
    }

    .progress-text {
      font-size: 16px;
      color: #333;
      font-weight: 500;
    }
  }
  .quit-btn {
    font-size: 16px;
    color: #999;
    position: absolute;
    right: 30px;
    top: 30px;
    cursor: pointer;
    a {
      color: #999;
    }
  }
  .confirmation-title {
    margin-bottom: 40px;
    .title-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 24px;
      color: #333;
    }
    h1 {
      font-size: 24px;
      color: #333;
      margin-bottom: 10px;
      font-weight: 500;
    }

    .subtitle {
      font-size: 16px;
      color: #ccc;
    }
  }

  .confirmation-content {
    margin-bottom: 40px;
  }

  .confirmation-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding-top: 20px;

    .skip-btn {
      min-width: 100px;
      height: 40px;
      border: 1px solid #dcdfe6;
      color: #606266;
      background: #fff;

      &:hover {
        color: #5999f8;
        border-color: #5999f8;
      }
    }

    .confirm-btn {
      min-width: 120px;
      height: 40px;
      background: #5999f8;
      border-color: #5999f8;

      &:hover {
        background: #4a8cf7;
        border-color: #4a8cf7;
      }
    }
  }
}

// 基础确认步骤组件样式
.base-confirmation-step {
  .data-display {
    border-radius: 8px;
    margin-bottom: 20px;

    .field-list {
      .field-input{
        width: 100%;
      }
      .salary-unit {
        color: #909399;
        font-size: 14px;
        position: absolute;
        left: 175px;
        
      }
      .field-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .field-label {
          width: 120px;
          flex-shrink: 0;
          font-size: 14px;
          color: #666;
          line-height: 32px;

          margin-right: 16px;
          
        }
        .required{
          &::after{
            content: '*';
            color: #f56c6c;
          }
        }
        .field-value {
          flex: 1;
          display: flex;
          align-items: flex-start;
          position: relative;
          .value-text {
            font-size: 14px;
            color: #333;
            line-height: 32px;
            min-height: 32px;
            padding: 0 12px;
            background: #fff;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            flex: 1;
            word-break: break-all;
            white-space: pre-wrap;

            &:empty::before {
              content: "未填写";
              color: #c0c4cc;
            }
          }

          .field-input {
            flex: 1;
          }

          .el-input,
          .el-select,
          .el-textarea {
            flex: 1;
          }

          .el-textarea .el-textarea__inner {
            min-height: 80px;
          }

          .el-select {
            width: 100%;
          }

          .el-input-number {
            width: 200px;
          }

          
        }
      }
    }
  }
  :deep(.el-form-item__label) {
    color: #666;
    text-align: left;

  }
  .error-tips {
    background: #fef0f0;
    border: 1px solid #fbc4c4;
    border-radius: 4px;
    padding: 12px 16px;
    margin-bottom: 20px;

    .error-title {
      font-size: 14px;
      color: #f56c6c;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .error-list {
      .error-item {
        font-size: 12px;
        color: #f56c6c;
        line-height: 20px;

        &:before {
          content: "• ";
          margin-right: 4px;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding-top: 20px;
    margin-top: 20px;

    .el-button {
      min-width: 100px;
      height: 40px;

      &.el-button--primary {
        background: #5999f8;
        border-color: #5999f8;

        &:hover {
          background: #4a8cf7;
          border-color: #4a8cf7;
        }

        &:disabled {
          background: #c0c4cc;
          border-color: #c0c4cc;
          cursor: not-allowed;
        }
      }

      &.el-button--default {
        border: 1px solid #dcdfe6;
        color: #606266;
        background: #fff;

        &:hover {
          color: #5999f8;
          border-color: #5999f8;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .resume-confirmation {
    padding: 15px;

    .confirmation-title h1 {
      font-size: 20px;
    }

    .confirmation-actions {
      flex-direction: column;
      align-items: center;

      .skip-btn,
      .confirm-btn {
        width: 100%;
        max-width: 200px;
      }
    }
  }

  
}
