# 简历解析确认功能

## 功能概述

这是一个单页面简历解析确认功能，用于在单页面内逐项确认从接口返回的简历数据。支持9个模块的数据确认，包括多条记录的逐条确认逻辑。

## 功能特性

- ✅ 单页面流程控制，不分页
- ✅ 按顺序进行9个模块的数据确认
- ✅ 每个模块支持"确认"和"跳过"操作
- ✅ 多条记录模块的逐条确认逻辑
- ✅ 支持数据编辑和验证
- ✅ 响应式设计，支持移动端
- ✅ 统一的组件架构

## 确认模块

1. **求职意向** - 目前状态、期望工作地、目前薪资、期望薪资、期望职能、期望行业
2. **工作经历** - 职位类型、工作行业、职位名称、职位等级、在职时间、公司名称等（支持多条记录）
3. **教育经历** - 就读院校、在校时间、学历、专业类别、专业名称、专业描述（支持多条记录）
4. **项目经历** - 项目名称、项目时间、参与身份、项目描述（支持多条记录）
5. **培训经历** - 培训课程、培训机构、培训时间、培训内容（支持多条记录）
6. **技术能力** - 技能名称、使用时间、掌握程度
7. **语言技能** - 外语语种、综合能力、听说能力、读写能力
8. **证书职称** - 证书名称、证书类型、获得时间、证书等级
9. **个人描述** - 个人描述

## 技术架构

### 技术栈
- Vue 3 + TypeScript
- Element Plus UI框架
- Vue Router 4
- Less样式预处理器

### 目录结构
```
src/views/resumeConfirmation/
├── ResumeConfirmation.vue          # 主确认页面
├── types.ts                        # TypeScript类型定义
├── constants.ts                    # 常量配置
├── styles.less                     # 样式文件
├── README.md                       # 说明文档
└── components/                     # 组件目录
    ├── BaseConfirmationStep.vue    # 基础确认步骤组件
    ├── CareerIntentionStep.vue     # 求职意向确认
    ├── WorkExperienceStep.vue      # 工作经历确认
    ├── EducationExperienceStep.vue # 教育经历确认
    ├── ProjectExperienceStep.vue   # 项目经历确认
    ├── TrainingExperienceStep.vue  # 培训经历确认
    ├── TechnicalSkillsStep.vue     # 技术能力确认
    ├── LanguageSkillsStep.vue      # 语言技能确认
    ├── CertificatesStep.vue        # 证书职称确认
    └── PersonalDescriptionStep.vue # 个人描述确认
```

## 使用方法

### 1. 路由访问
```
/resumeConfirmation
```

### 2. 组件使用
```vue
<template>
  <ResumeConfirmation />
</template>

<script>
import ResumeConfirmation from '@/views/resumeConfirmation/ResumeConfirmation.vue';

export default {
  components: {
    ResumeConfirmation
  }
}
</script>
```

### 3. 数据格式
```typescript
interface ResumeData {
  careerIntention?: CareerIntentionData;
  workExperiences?: WorkExperienceData[];
  educationExperiences?: EducationExperienceData[];
  projectExperiences?: ProjectExperienceData[];
  trainingExperiences?: TrainingExperienceData[];
  technicalSkills?: TechnicalSkillsData;
  languageSkills?: LanguageSkillsData;
  certificates?: CertificateData;
  personalDescription?: PersonalDescriptionData;
}
```

## 核心组件说明

### ResumeConfirmation.vue
主确认页面，负责：
- 流程控制和步骤切换
- 进度显示
- 数据管理
- 组件动态加载

### BaseConfirmationStep.vue
基础确认步骤组件，提供：
- 统一的布局容器
- 错误提示显示
- 插槽支持自定义字段内容

### 各步骤组件
每个步骤组件独立实现：
- 直接在模板中定义字段
- 独立的数据验证逻辑
- 组件内部状态管理
- 实时数据同步

## 动态步骤逻辑实现

### 核心功能
- ✅ 根据接口返回数据动态计算总步骤数
- ✅ 自动跳过无数据的步骤
- ✅ 支持9个字段的数据验证和映射
- ✅ 完整的字段验证函数
- ✅ API数据到组件数据的自动映射

### 接口数据结构分析
`ResumeAnalysisResultDto` 包含9个关键字段：
1. **career** (CareerPartDto) - 求职意向（单个对象）
2. **workList** (WorkPartDto[]) - 工作经历（数组）
3. **educationList** (EducationPartDto[]) - 教育经历（数组）
4. **projectList** (ResumeEditProjectDto[]) - 项目经历（数组）
5. **trainList** (ResumeEditTrainDto[]) - 培训经历（数组）
6. **technologyList** (TechnologyDto[]) - 技术能力（数组）
7. **certificateList** (CertificateDto[]) - 证书职称（数组）
8. **languageList** (LanguageDto[]) - 语言能力（数组）
9. **descriptionList** (ResumeEditDesDto[]) - 个人描述（数组）

### 字段验证规则
- **普通字段**：检查是否有值（非null、非undefined、非空字符串）
- **数组字段**：检查数组长度是否大于0
- **跳过逻辑**：如果某个字段无值，则在简历确认流程中跳过对应步骤

### 新增文件说明
- `utils.ts` - 工具函数集合，包含字段验证、数据映射、动态步骤计算
- `test-utils.ts` - 测试工具和验证函数

### 使用方法
```typescript
// 在组件中使用
import { calculateValidSteps, mapApiDataToResumeData } from './utils';

// 计算有效步骤
const validSteps = calculateValidSteps(apiData);

// 映射数据
const mappedData = mapApiDataToResumeData(apiData);
```

## 扩展开发

### 添加新的确认步骤
1. 在`types.ts`中定义数据类型
2. 在`constants.ts`中添加步骤配置
3. 创建新的步骤组件，继承BaseConfirmationStep
4. 在主组件中注册和使用
5. 在`utils.ts`中添加对应的验证和映射逻辑

### 创建步骤组件示例
```vue
<template>
  <BaseConfirmationStep @confirm="handleConfirm" @skip="$emit('skip')">
    <template #fields>
      <div class="field-item">
        <label class="field-label">字段名称</label>
        <div class="field-value">
          <el-input v-model="editData.fieldName" placeholder="请输入" />
        </div>
      </div>
    </template>

    <div class="action-buttons">
      <el-button @click="handleConfirm" type="primary">确认</el-button>
      <el-button @click="$emit('skip')">跳过</el-button>
    </div>
  </BaseConfirmationStep>
</template>
```

### 组件内验证逻辑
```typescript
const validateData = () => {
  const errors: string[] = [];
  if (!editData.fieldName) {
    errors.push('字段名称不能为空');
  }
  return errors.length === 0;
};
```

### 自定义样式
修改`styles.less`文件或在组件中添加scoped样式。

## API集成

目前使用模拟数据，实际使用时需要：

1. 替换模拟数据为API调用
2. 添加数据保存接口
3. 处理加载状态和错误状态

```typescript
// 示例API集成
const loadResumeData = async () => {
  try {
    const response = await fetch('/api/resume/parse');
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('加载简历数据失败:', error);
  }
};

const saveConfirmation = async (data: ResumeData) => {
  try {
    const response = await fetch('/api/resume/confirm', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    return await response.json();
  } catch (error) {
    console.error('保存确认数据失败:', error);
  }
};
```

## 注意事项

1. 确保Element Plus已正确安装和配置
2. 多条记录的模块会逐条确认，确认完所有记录后才进入下一个模块
3. 编辑功能支持实时验证和错误提示
4. 响应式设计在移动端会调整布局

## 后续优化

- [ ] 添加数据持久化
- [ ] 优化加载性能
- [ ] 添加更多验证规则
- [ ] 支持批量操作
- [ ] 添加撤销功能
- [ ] 国际化支持
