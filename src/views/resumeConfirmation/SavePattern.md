# 简历确认步骤保存模式

## 概述

已实现统一的保存API模式，验证通过后调用保存接口，成功后才进入下一步。

## 架构设计

### 1. 通用保存组合式函数 (`useSaveStep.ts`)

提供标准化的保存流程：
- **表单验证** → **业务逻辑验证** → **API调用** → **状态管理** → **成功反馈**

### 2. 保存流程

```
用户点击"确认添加" 
→ handleConfirmClick (ResumeConfirmation.vue)
→ handleConfirm (子组件)
→ executeSave (useSaveStep)
  → 1. BaseConfirmationStep表单验证
  → 2. 自定义业务逻辑验证
  → 3. 调用保存API
  → 4. 成功后emit('confirm')进入下一步
```

## 已实现示例：CareerIntentionStep.vue

```typescript
import { useSaveStep } from '../composables/useSaveStep';
import { ResumepartClass } from '../../../http/app/Resumepart';

export default defineComponent({
  setup(props, { emit }) {
    const baseStepRef = ref();
    const { saving, executeSave } = useSaveStep();
    const formData = reactive<CareerIntentionData>({});

    const handleConfirm = async () => {
      await executeSave(
        formData,
        ResumepartClass.apiResumepartSavecareernewPost, // API调用方法
        baseStepRef,
        emit,
        {
          validateBusinessLogic, // 自定义业务验证
          onSaveSuccess: (result) => {
            console.log('保存成功回调:', result);
          },
          onSaveError: (error) => {
            console.error('保存失败回调:', error);
          }
        }
      );
    };

    return {
      saving,        // 保存状态，供模板使用
      handleConfirm, // 确认处理方法
      baseStepRef,   // BaseConfirmationStep引用
      // ... 其他返回值
    };
  }
});
```

## 其他步骤复用模式

### 1. WorkExperienceStep.vue 示例

```typescript
import { useSaveStep } from '../composables/useSaveStep';
import { WorkExperienceClass } from '../../../http/app/WorkExperience';

export default defineComponent({
  setup(props, { emit }) {
    const baseStepRef = ref();
    const { saving, executeSave } = useSaveStep();
    const formData = reactive<WorkExperienceData>({});

    // 自定义业务验证
    const validateWorkExperience = () => {
      // 工作经历特有的验证逻辑
      if (!formData.companyName || formData.companyName.trim() === '') {
        ElMessage.error('请填写公司名称');
        return false;
      }
      return true;
    };

    const handleConfirm = async () => {
      await executeSave(
        formData,
        WorkExperienceClass.apiWorkExperienceSavePost, // 对应的API
        baseStepRef,
        emit,
        {
          validateBusinessLogic: validateWorkExperience,
          transformData: (data) => {
            // 数据转换逻辑（如果需要）
            return {
              ...data,
              startDate: formatDate(data.startDate),
              endDate: formatDate(data.endDate)
            };
          }
        }
      );
    };

    return {
      saving,
      handleConfirm,
      baseStepRef,
      formData
    };
  }
});
```

### 2. EducationExperienceStep.vue 示例

```typescript
import { useSaveStep } from '../composables/useSaveStep';
import { EducationClass } from '../../../http/app/Education';

export default defineComponent({
  setup(props, { emit }) {
    const baseStepRef = ref();
    const { saving, executeSave } = useSaveStep();
    const formData = reactive<EducationData>({});

    const validateEducation = () => {
      // 教育经历验证逻辑
      if (!formData.schoolName) {
        ElMessage.error('请填写学校名称');
        return false;
      }
      if (!formData.major) {
        ElMessage.error('请填写专业');
        return false;
      }
      return true;
    };

    const handleConfirm = async () => {
      await executeSave(
        formData,
        EducationClass.apiEducationSavePost,
        baseStepRef,
        emit,
        {
          validateBusinessLogic: validateEducation
        }
      );
    };

    return {
      saving,
      handleConfirm,
      baseStepRef,
      formData
    };
  }
});
```

## UI状态管理

### 主页面按钮状态 (ResumeConfirmation.vue)

```vue
<el-button
  type="primary"
  @click="handleConfirmClick"
  :loading="currentStepSaving"
  :disabled="currentStepSaving"
>
  {{ currentStepSaving ? "保存中..." : (isLastStep ? "完成确认" : "确认添加") }}
</el-button>
```

```typescript
// 获取当前步骤组件的保存状态
const currentStepSaving = computed(() => {
  return currentStepRef.value?.saving || false;
});
```

## API适配说明

### 标准响应格式
useSaveStep会自动处理以下响应格式：
- `{ code: 200, data: {...} }`
- `{ code: 0, data: {...} }`  
- `{ success: true, data: {...} }`

### 错误处理
自动提取错误信息：
- `res.message`
- `res.msg`
- 默认错误信息

## 关键特性

### ✅ 已实现
- **统一保存流程**：表单验证 → 业务验证 → API调用 → 状态管理
- **Loading状态管理**：按钮显示"保存中..."，防止重复提交
- **错误处理**：统一的错误提示和处理逻辑
- **成功反馈**：保存成功提示，自动进入下一步
- **可扩展性**：支持自定义验证、数据转换、成功/失败回调

### 🔄 使用步骤
1. 导入 `useSaveStep` 组合式函数
2. 创建对应的API调用方法
3. 定义自定义业务验证逻辑（可选）
4. 在 `handleConfirm` 中调用 `executeSave`
5. 在模板中使用 `saving` 状态

### 📋 标准化流程
所有步骤组件都应遵循相同的模式，确保一致的用户体验和代码维护性。

## 验证测试

现在求职意向步骤已实现完整的保存流程：
1. 点击"确认添加"按钮
2. 表单验证（必填字段、格式验证）
3. 业务逻辑验证（期望职位分类层级）
4. 调用 `ResumepartClass.apiResumepartSavecareernewPost` API
5. 保存成功后进入下一步，失败则显示错误提示