// 简历解析确认功能的常量定义

import { StepConfig, ConfirmationStep } from './types';

// 步骤配置
export const STEP_CONFIGS: StepConfig[] = [
  {
    step: ConfirmationStep.CAREER_INTENTION,
    title: '同步新的求职意向',
    description: '检测到有新的意向职位，同步后将添加至在线简历中',
    hasMultipleRecords: false
  },
  {
    step: ConfirmationStep.WORK_EXPERIENCE,
    title: '同步新的工作经历',
    description: '这是一项新的工作经历，同步后将添加至在线简历中',
    hasMultipleRecords: true
  },
  {
    step: ConfirmationStep.EDUCATION_EXPERIENCE,
    title: '同步新的教育经历',
    description: '这是一项新的教育经历，同步后将添加至在线简历中',
    hasMultipleRecords: true
  },
  {
    step: ConfirmationStep.PROJECT_EXPERIENCE,
    title: '同步新的项目经历',
    description: '这是一项新的项目经历，同步后将添加至在线简历中',
    hasMultipleRecords: true
  },
  {
    step: ConfirmationStep.TRAINING_EXPERIENCE,
    title: '同步新的培训经历',
    description: '这是一项新的培训经历，同步后将添加至在线简历中',
    hasMultipleRecords: true
  },
  {
    step: ConfirmationStep.TECHNICAL_SKILLS,
    title: '同步新的技术能力',
    description: '这是一项新的技术能力，同步后将添加至在线简历中',
    hasMultipleRecords: false
  },
  {
    step: ConfirmationStep.LANGUAGE_SKILLS,
    title: '同步新的语言技能',
    description: '这是一项新的语言技能，同步后将添加至在线简历中',
    hasMultipleRecords: false
  },
  {
    step: ConfirmationStep.CERTIFICATES,
    title: '同步新的证书职称',
    description: '这是一项新的证书职称，同步后将添加至在线简历中',
    hasMultipleRecords: false
  },
  {
    step: ConfirmationStep.PERSONAL_DESCRIPTION,
    title: '同步个人描述',
    description: '附件中的个人描述更为详细，建议更新至在线简历中',
    hasMultipleRecords: false
  }
];


