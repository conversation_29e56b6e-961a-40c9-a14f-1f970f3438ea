<template>
  <div class="left-menu">
    <div class="tit">
      消息通知
    </div>
    <el-row class="tac">
      <el-col>
        <el-menu :uniqueOpened="false" :default-active="activeIndex" class="el-menu-vertical-demo" @open="handleOpen"
          @close="handleClose" background-color="#FFFFFF" text-color="#666666" active-text-color="#333333">
          <el-menu-item index="0" @click="golink('/message/commonPhrases')">
            常用语（{{ commonPhrasesCount }}条）
          </el-menu-item>

          <el-menu-item index="1" @click="golink('/message/setGreet')">
            设置打招呼语
          </el-menu-item>

          <el-menu-item index="2" @click="golink('/message/messageReminders')">
            消息与提醒
          </el-menu-item>

        </el-menu>
      </el-col>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch, computed, } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import { commonexpression } from "@/IM/api/index";

const router = useRouter();
const route = useRoute();
const store = useStore();
const commonPhrasesCount = computed(() => store.state.commonPhraseslist.length)

onMounted(async () => {
  await init();
});

const init = async () => {
  let data: any = await commonexpression();
  store.state.commonPhraseslist = data.data;
}
const activeIndex = computed(() => {
  let url = route.fullPath;
  if (url == "/message/commonPhrases") {
    return "0";
  }
  if (url == "/message/setGreet") {
    return "1";
  }
  if (url == "/message/messageReminders") {
    return "2";
  }
})



const handleOpen = (key: any, keyPath: any) => { }
const handleClose = (key: any, keyPath: any) => { }
const golink = (url: string, type: number) => {
  if (type) {
    //新页面打开
    window.open(url);
    return false;
  }
  router.push({
    path: url,
  });
}

</script>
<style lang="less" scoped>
.left-menu {
  margin-bottom: 12px;
  height: auto;

  .tit {
    height: 70px;
    line-height: 70px;
    padding: 10px 0 0;
    font-weight: bold;
    font-size: 20px;
    color: #333333;
    background: #F9FCFF;
    border-radius: 12px 12px 0px 0px;
    text-align: center;
  }

  .el-menu {
    border: 0;
  }

  .el-menu-item {
    font-size: 16px;
    padding-left: 50px !important;
  }

  .el-menu-item.is-active {
    background: #E5EFFF;
    border-left: 4px solid #5F9EFC;
    font-weight: bold;
  }

}
</style>