<template>
  <el-dialog class="add-common-phrases-dialog" v-model="show1" :title="dialogTitle" width="600px" @close="handleClose">
    <div class="textarea-con">
      <el-input v-model="textarea" style="width: 100%" :rows="5" maxlength="200" show-word-limit type="textarea"
        placeholder="请输入您的常用回复语，不要带有QQ、微信号、手机号、广告等敏感信息，以免被系统封禁账号" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="confirmAdd">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch, computed } from "vue";
import { ElMessage } from 'element-plus'
import { useStore } from "vuex";
const store = useStore();
const commonPhrasesCount= computed(() => store.state.commonPhraseslist.length)

interface Props {
  show: boolean
  text: string
  type: number
}
const props = withDefaults(defineProps<Props>(), {
  show: false,
  text: '',
  type: -1
})

const emits = defineEmits<{
  (e: 'update:show', show: boolean, closeType: number): void  //closeType：0关闭，1保存
  (e: 'update:text', text: string): void
}>()

const show1 = computed({
  get: () => props.show,
  set: (value) => emits('update:show', value)
})
const textarea = computed({
  get: () => props.text,
  set: (value) => emits('update:text', value)
})
const dialogTitle = computed(() => {
  return props.type == -1 ? '添加常用语' : '修改常用语'
})

const confirmAdd = () => {
  if (commonPhrasesCount.value >= 15) {
    ElMessage.closeAll()
    ElMessage({
      message: '常用语个数已达到上限',
      type: 'error'
    })
    return
  }

  emits('update:text', textarea.value)
  emits('update:show', false, 1)
}
const handleClose = () => {
  emits('update:show', false, 0)
}

</script>
<style lang="less" scoped>
::v-deep .el-dialog__title{
  color: #414A60!important;
  font-weight: bold!important;
}
::v-deep .el-textarea__inner{
  background: #F8F8F8!important;
border-radius: 8px 8px 8px 8px!important;
border: 1px solid #E3E7ED!important;
}
</style>