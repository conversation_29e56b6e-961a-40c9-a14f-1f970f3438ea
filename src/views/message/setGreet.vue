<template>
    <div class="container set-greet-page">
        <div class="top-title">
            聊天的招呼语
        </div>
        <div class="qiyong">
            <div class="nei">
                启用招呼语
                <el-switch v-model="switchValue" class="ml-2" style="--el-switch-on-color: #247BFF;"
                    @change="changeSwitch" />
            </div>
            <div class="shuoming">
                启用后，将在沟通时自动发送招呼语
            </div>
        </div>

        <div class="msg-content" v-show="showContent">
            <div class="dangqian">
                <div class="nei">
                    当前
                </div>
                <div class="shuoming">
                    {{ currentText }}
                </div>
            </div>
            <div class="changgui" :style="`height:${changguiHeight}px`">
                <el-tabs v-model="tabActiveName" class="demo-tabs" @tab-click="clickTab">
                    <el-tab-pane label="常规" name="常规">
                        <ul class="list">
                            <li v-for="(item, index) in list0" :key="index"
                                :class="{ 'selected': selectedIndex === index }" @click="clickLi(index)">
                                <div class="text">{{ item.content }}</div>
                                <div class="gou">
                                    <div class="iconfont icon-tick"></div>
                                </div>
                            </li>
                        </ul>
                    </el-tab-pane>
                    <el-tab-pane label="幽默" name="幽默">
                        <ul class="list">
                            <li v-for="(item, index) in list1" :key="index"
                                :class="{ 'selected': selectedIndex === index }" @click="clickLi(index)">
                                <div class="text">{{ item.content }}</div>
                                <div class="gou">
                                    <div class="iconfont icon-tick"></div>
                                </div>
                            </li>
                        </ul>
                    </el-tab-pane>
                    <el-tab-pane label="礼貌" name="礼貌">
                        <ul class="list">
                            <li v-for="(item, index) in list2" :key="index"
                                :class="{ 'selected': selectedIndex === index }" @click="clickLi(index)">
                                <div class="text">{{ item.content }}</div>
                                <div class="gou">
                                    <div class="iconfont icon-tick"></div>
                                </div>
                            </li>
                        </ul>
                    </el-tab-pane>
                    <el-tab-pane label="诚恳" name="诚恳">
                        <ul class="list">
                            <li v-for="(item, index) in list3" :key="index"
                                :class="{ 'selected': selectedIndex === index }" @click="clickLi(index)">
                                <div class="text">{{ item.content }}</div>
                                <div class="gou">
                                    <div class="iconfont icon-tick"></div>
                                </div>
                            </li>
                        </ul>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from "vue";
import { TabsPaneContext, ElMessage } from 'element-plus'
import { allgreetings, jobseekergreeting, changegreetingsendstate, setdefaultgreeting } from "@/IM/api/index";

const switchValue = ref(true)
const showContent = ref(true)
const tabActiveName = ref('常规')
const storageIndex = ref(["常规", -1])
const selectedIndex = ref(-1)
const currentText = ref('')
const changguiHeight = ref(500)
const list = ref([])
const list0 = ref([])
const list1 = ref([])
const list2 = ref([])
const list3 = ref([])

onMounted(async () => {
    await init();
})
const init = async () => {
    let data: any = await allgreetings();
    list.value = data.data;
    list0.value = list.value[0].greetings;
    list1.value = list.value[1].greetings;
    list2.value = list.value[2].greetings;
    list3.value = list.value[3].greetings;

    let data1: any = await jobseekergreeting();
    currentText.value = data1.data.currentGreeting;

    list.value.forEach(item => {
        item.greetings.forEach((item2, index2) => {
            if (item2.id == data1.data.greetingId) {
                tabActiveName.value = storageIndex.value[0] = item.categoryName;
                selectedIndex.value = storageIndex.value[1] = index2
            }
        });
    });

    switchValue.value = data1.data.isAutoSend;
    if (switchValue.value == false) {
        showContent.value = false
    } else {
        showContent.value = true
    }
}
const changeSwitch = async () => {
    if (switchValue.value == false) {
        showContent.value = false
    } else {
        showContent.value = true
    }
    let data: any = await changegreetingsendstate({
        "state": switchValue.value
    });

    ElMessage.closeAll()
    ElMessage({
        message: '设置成功',
        type: 'success'
    })
}
const clickTab = (tab: TabsPaneContext, event: Event) => {
    if (storageIndex.value[0] == tab.props.label && storageIndex.value[1] != -1) {
        selectedIndex.value = storageIndex.value[1]
    } else {
        selectedIndex.value = -1
    }
}
const clickLi = async (index: number) => {
    storageIndex.value[1] = selectedIndex.value = index
    ElMessage.closeAll()
    ElMessage({
        message: '设置成功',
        type: 'success'
    })

    let id = 0;
    if (tabActiveName.value == "常规") {
        id = list0.value[selectedIndex.value].id
        currentText.value = list0.value[selectedIndex.value].content
    } else if (tabActiveName.value == "幽默") {
        id = list1.value[selectedIndex.value].id
        currentText.value = list1.value[selectedIndex.value].content
    } else if (tabActiveName.value == "礼貌") {
        id = list2.value[selectedIndex.value].id
        currentText.value = list2.value[selectedIndex.value].content
    } else {
        id = list3.value[selectedIndex.value].id
        currentText.value = list3.value[selectedIndex.value].content
    }

    await setdefaultgreeting({
        "greetingId": id,
        "content": currentText.value
    });
}

</script>
<style lang="less" scoped>
/* 自定义滚动条样式 */
&::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

&::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
}

&::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
}

&::-webkit-scrollbar-thumb:hover {
    background: #909399;
}

.set-greet-page {
    padding: 30px 0;

    .top-title {
        font-weight: bold;
        font-size: 20px;
        color: #333333;
        line-height: 30px;
        text-align: left;
        font-style: normal;
        padding: 0px 30px 20px;
    }

    .qiyong {
        line-height: 30px;
        padding: 0px 30px 10px;

        .nei {
            font-size: 16px;
            color: #333333;
        }

        .el-switch {
            margin-left: 10px;
        }

        .shuoming {
            font-weight: 400;
            font-size: 14px;
            color: #8D8D8D;
            padding-bottom: 10px;
        }
    }

    .dangqian {
        margin: 0px 30px 0px;
        line-height: 24px;
        padding-bottom: 20px;
        border-bottom: 1px solid #F5F5F5;
        display: flex;

        .nei {
            flex: 1;
            color: #333333;
        }

        .shuoming {
            flex: 20;
            font-size: 14px;
            color: #333333;
            padding: 0 10px;
            background: #F4F5F9;
        }
    }

    .changgui {
        padding: 10px 30px 15px;
        overflow-y: auto;

        ::v-deep .el-tabs__item.is-active {
            color: #247BFF;
            font-weight: bold;
        }

        ::v-deep .el-tabs__active-bar {
            height: 3px;
            background-color: #247BFF;
        }

        .list {
            list-style: none;
            margin: 0;
            padding: 0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px 20px;
        }

        .list li {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 16px 20px;
            font-size: 15px;
            color: #333;
            cursor: pointer;
            background: #FFFFFF;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #F4F5F9;
            transition: background 0.2s, border 0.2s;
            position: relative;
            min-height: 40px;
            text-align: left;
            position: relative;
        }

        .list li.selected {
            background: #F9FCFF;
            border: 1px solid #247BFF;
        }

        .list li:hover {
            background: #F9FCFF;
        }

        .text {
            flex: 1;
            word-break: break-all;
            text-align: left;
        }

        .gou {
            width: 24px;
            height: 24px;
            line-height: 24px;
            background: #247BFF;
            border-radius: 0px 8px 0px 8px;
            text-align: center;
            margin-left: auto;
            font-weight: bold;
            visibility: hidden;
            position: absolute;
            top: 0;
            right: 0;

            .iconfont {
                font-size: 16px;
                color: #fff;
            }
        }

        .list li.selected .gou {
            visibility: visible;
        }
    }
}
</style>