<template>
  <div class="container common-phrases-page">
    <div class="top-title">
      常用语
    </div>
    <ul class="list">
      <li v-for="(item, index) in list" :key="index" draggable="true" @dragstart="handleDragStart($event, index)"
        @dragover.prevent @dragenter.prevent="handleDragEnter($event, index)" @dragend="handleDragEnd"
        :class="{ 'dragging': draggingIndex === index }">
        <div class="dian"></div>
        <div class="text">{{ item.content }}</div>
        <div class="fun">
          <!-- <div v-if="!item.editable" class="icon-edit-noable iconfont icon-a-bianji1"></div> -->
          <div class="icon-edit-able iconfont icon-a-bianji1" @click="openCommonPhrasesBox(index)"></div>

          <el-popconfirm title="确认删除该条常用语吗？" placement="left-start" :hide-icon="true" @confirm="confirmDelete(index)">
            <template #reference>
              <div class="iconfont icon-a-shanchu1"></div>
            </template>
          </el-popconfirm>

          <div class="iconfont icon-paixu"></div>
        </div>
      </li>
    </ul>
    <div class="add" @click="openCommonPhrasesBox(-1)">
      <div class="iconfont icon-tianjia"></div>添加常用语
    </div>
  </div>
  <AddCommonPhrases :type="currentEditIndex" v-model:show="showCommonPhrasesBox" v-model:text="commonPhraseText"
    @update:show="(val: boolean, closeType: number) => {
      if (!val) { // 只在对话框关闭时处理
        handleCommonPhrasesChange(val, closeType, commonPhraseText)
      }
    }" />
</template>

<script lang="ts" setup>
import { onMounted, ref, computed, watch } from "vue";
import AddCommonPhrases from "@/views/message/components/AddCommonPhrases.vue";
import { commonexpression,operatecommonexpression, sortchatgreeting } from "@/IM/api/index";
import { useStore } from "vuex";
const store = useStore();

const list = computed(() => store.state.commonPhraseslist)
const showCommonPhrasesBox = ref(false)
const commonPhraseText = ref("")
const currentEditIndex = ref(-1) // 当前编辑的索引，-1表示新增
const draggingIndex = ref(-1)
const dragOverIndex = ref(-1)

const post = ref({
  "msgId": 0,
  "content": "",
  "operateType": ""
})

onMounted(async () => {
})
const init = async () => {
  let data: any = await commonexpression();
  store.state.commonPhraseslist = data.data;
}
const openCommonPhrasesBox = (index: number) => {
  showCommonPhrasesBox.value = true
  currentEditIndex.value = index
  if (index !== -1) {
    commonPhraseText.value = list.value[index].content
  } else {
    commonPhraseText.value = ""
  }
}
const confirmDelete = async (index: number) => {
  post.value.msgId = list.value[index].msgID
  post.value.content = list.value[index].content
  post.value.operateType = "Delete"
  await operatecommonexpression(post.value);
  await init();
}
const handleCommonPhrasesChange = async (show: boolean, closeType: number, text: string) => {
  if (text && closeType == 1) { // 对话框关闭且有文本内容时,closeType：0关闭，1保存
    post.value.content = text
    if (currentEditIndex.value === -1) {// 新增
      post.value.operateType = "Add"
    } else {// 编辑
      post.value.msgId = list.value[currentEditIndex.value].msgID
      post.value.operateType = "Update"
    }
    await operatecommonexpression(post.value);
    await init();
    // 重置状态
    commonPhraseText.value = ""
    currentEditIndex.value = -1
  }
}
const handleDragStart = (e: DragEvent, index: number) => {
  draggingIndex.value = index
  if (e.dataTransfer) {
    e.dataTransfer.effectAllowed = 'move'
  }
}
const handleDragEnter = (e: DragEvent, index: number) => {
  dragOverIndex.value = index
}
const handleDragEnd = async () => {
  if (draggingIndex !== dragOverIndex) {
    // 获取拖拽项
    const draggedItem = store.state.commonPhraseslist[draggingIndex.value]
    // 删除拖拽项
    store.state.commonPhraseslist.splice(draggingIndex.value, 1)
    // 在目标位置插入
    store.state.commonPhraseslist.splice(dragOverIndex.value, 0, draggedItem)
    let newList = store.state.commonPhraseslist.map((item: any) => {
      return{
        "msgId":item.msgID,
        "edit":item.editable
      }
    })
    await sortchatgreeting({ "greetings": newList })
  }
  // 重置状态
  draggingIndex.value = -1
  dragOverIndex.value = -1
}

</script>

<style lang="less" scoped>
.common-phrases-page {
  padding: 30px;

  .top-title {
    font-weight: bold;
    font-size: 20px;
    color: #333333;
    line-height: 30px;
    text-align: left;
    font-style: normal;
    border-bottom: 1px solid #F5F5F5;
    padding-bottom: 20px;
  }

  .list {
    padding: 20px 0px 30px;

    li {
      line-height: 28px;
      overflow: hidden;
      padding: 8px 20px;
      transition: all 0.3s ease;
      cursor: move;

      &.dragging {
        opacity: 0.5;
        background: #f5f5f5;
      }

      .dian {
        width: 6px;
        height: 6px;
        background: #C1D6F9;
        border-radius: 50%;
        float: left;
        margin: 12px 15px 0 0;
      }

      .text {
        float: left;
        width: 700px;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }

      .fun {
        float: right;

        .iconfont {
          display: inline-block;
          font-size: 18px;
          color: #B7B7B7;
          margin-left: 20px;
          cursor: pointer;
        }

        .iconfont:hover {
          color: #247BFF;
        }

        .icon-edit-noable {
          color: #eee;
        }

        .icon-edit-noable:hover {
          color: #eee;
        }

        .icon-list3 {
          cursor: move;
        }
      }

    }

    li:hover {
      background: #FFFFFF;
      box-shadow: 2px 2px 4px 4px rgba(0, 0, 0, 0.03);
      border-radius: 12px 12px 12px 12px;
    }
  }

  .add {
    width: 128px;
    height: 36px;
    line-height: 36px;
    padding: 0 10px;
    background: #E8F1FF;
    border-radius: 100px 100px 100px 100px;
    font-weight: bold;
    font-size: 14px;
    color: #247BFF;
    cursor: pointer;

    .iconfont {
      float: left;
      font-size: 18px;
      color: #247BFF;
      margin: 0px 7px 0 14px;
    }
  }
}
</style>

<style lang="less">
.el-popconfirm__action {
  padding-top: 10px;
}

.el-popconfirm__action .el-button--text {
  padding-left: 15px;
  padding-right: 15px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #3399FF !important;
  color: #3399FF !important;
}

.el-popconfirm__action .el-button--primary {
  background: #3399FF !important;
  border-color: #3399FF !important;
}
</style>