<template>
    <div class="container message-reminders-page">
        <div class="top-title">
            消息与提醒
        </div>
        <div class="qiyong">
            <div class="nei">
                允许新消息提醒
                <el-switch v-model="switchValue" class="ml-2" style="--el-switch-on-color: #247BFF;"
                    @change="changeSwitch" />
            </div>
            <div class="shuoming">
                在浏览网页过程中，右上角可以收到来自HR的新消息提醒
            </div>
            <div class="msg-content" v-show="showContent">
                <div class="img-wrap">
                    <img src="//image.gxrc.com/gxrcsite/vip/message/消息示例.png" />
                </div>
            </div>
        </div>
    </div>

</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from "vue";
import { ElMessage, ElMessageBox } from 'element-plus'
import { chatsettings, changeisacceptchatting } from "@/IM/api/index";
import { useStore } from "vuex";

const store = useStore();
const switchValue = ref(true)
const showContent = ref(true)

onMounted(async () => {
    await init();
})

const init = async () => {
    let data: any = await chatsettings();
    switchValue.value = data.data.isAcceptChatting;
}
const changeSwitch = async () => {
    if (switchValue.value == false) {
        showContent.value = false
        ElMessageBox.alert('新消息提醒关闭成功！', {
            confirmButtonText: '知道了',
            type: 'success'
        })
    } else {
        showContent.value = true
        ElMessageBox.alert('新消息提醒开启成功！', {
            confirmButtonText: '知道了',
            type: 'success'
        })
    }
    await changeisacceptchatting({
        "isAcceptChatting": switchValue.value
    });
    store.commit("setAllowNewMessageReminders", switchValue.value)
}

</script>
<style lang="less" scoped>
.message-reminders-page {
    padding: 30px;

    .top-title {
        font-weight: bold;
        font-size: 20px;
        color: #333333;
        line-height: 30px;
        text-align: left;
        font-style: normal;
        padding-bottom: 20px;
    }

    .qiyong {
        line-height: 30px;

        .nei {
            font-size: 16px;
            color: #333333;
        }

        .el-switch {
            margin-left: 10px;
        }

        .shuoming {
            font-weight: 400;
            font-size: 14px;
            color: #8D8D8D;
            padding-bottom: 10px;
        }
    }


}
</style>