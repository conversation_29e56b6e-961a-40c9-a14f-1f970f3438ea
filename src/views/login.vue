<template>
  <div class="login-page">
    <el-container>
      <Header name="个人会员登录" :link="true"  :bid='bussid'/>
      <el-main>
        <div class="login-wrap w1200">
          <div class="login-box">
            <span v-if="mode == 0" class="ico">
              <i class="iconfont icon-computer" @click="methods.loginCode(1)"></i>
            </span>
            <span v-else class="ico">
              <i class="iconfont icon-code1" @click="methods.loginCode(0)"></i>
            </span>
            <!-- 账号-短信登录 -->
            <div class="login-message-phone" v-if="mode == 1">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <!-- //短信登录框 -->
                <el-tab-pane label="短信登录" name="first">
                  <el-form
                    :model="FormPhone"
                    label-width="0px"
                    class="login-phone"
                    ref="FormUserB"
                    :rules="rulesphone"
                  >
                    <el-form-item prop="phone">
                      <el-input
                        v-model.number="FormPhone.phone"
                        placeholder="手机号"
                        :clearable="true"
                      ></el-input>
                    </el-form-item>
                    <el-form-item prop="code">
                      <el-input
                        v-model="FormPhone.code"
                        placeholder="短信验证码"
                        :clearable="true"
                        class="code"
                      >
                        <template #append>
                          <span class="gray" v-if="send">{{ countTime }}s重新获取</span>
                          <span class="blue" v-else @click="sendSmsCode">发送验证码</span>
                        </template>
                      </el-input>
                    </el-form-item>
                  </el-form>
                </el-tab-pane>
                <!-- end -->
                <!-- 账号登录start -->
                <el-tab-pane label="账号登录" name="second">
                  <el-form
                    ref="FormUserA"
                    :model="FormUser"
                    label-width="0px"
                    class="login-user"
                    :rules="rulesUser"
                  >
                    <el-form-item prop="userName">
                      <el-input
                        v-model="FormUser.userName"
                        placeholder="手机号/用户名/邮箱地址"
                        :clearable="true"
                        @blur="FormUser.userName=FormUser.userName.replace(/^\s+|\s+$/g,'')"
                      ></el-input>
                    </el-form-item>
                    <el-form-item prop="pwd">
                      <el-input
                        v-model="FormUser.pwd"
                        placeholder="请输入密码"
                        :clearable="true"
                        @blur="FormUser.pwd=FormUser.pwd.replace(/^\s+|\s+$/g,'')"
                        show-password
                      ></el-input>
                    </el-form-item>
                    <el-form-item v-if="showValid">
                      <el-input
                        v-model="FormUser.captchaCode"
                        placeholder="请输入右侧图片验证码"
                        :clearable="true"
                        class="code"
                      >
                        <template #append>
                          <img
                            :src="'data:image/jpeg;base64,' + security"
                            @click="methods.getcaptcha"
                          />
                        </template>
                      </el-input>
                    </el-form-item>
                  </el-form>
                </el-tab-pane>
                <!-- end -->
              </el-tabs>
              <div>
                <!-- 共用 -->
                <div class="checkbox-Seven clearfix">
                  <el-checkbox label="我已阅读并同意遵守" v-model="automatism"></el-checkbox>
                  <a
                    id="Agreement"
                    href="//www.gxrc.com/HtmlView/Licence.html"
                    target="_blank"
                    class="xy"
                  >使用协议</a>
                  <a
                    id="Agreement"
                    href="https://app.gxrc.com/privacy.html"
                    target="_blank"
                    class="xy"
                  >隐私政策</a>
                  <div class="elError" v-if="showTis">请阅读并同意使用协议和隐私政策</div>
                  <!-- <div
                    class="el-popper is-light el-popover--plain el-popover"
                    role="tooltip"
                    aria-hidden="true"
                    data-popper-placement="top-start"
                    v-if="showTis"
                  >
                    请阅读并同意使用协议和隐私政策
                    <span class="el-popper__arrow" data-popper-arrow></span>
                  </div>-->
                </div>
                <div class="btn-box">
                  <el-button
                    type="primary"
                    @click="phoneLogin"
                    class="submit-btn"
                    :loading="loading"
                    :disabled="disabled"
                    v-if="activeName == 'first'"
                  >登录/注册</el-button>
                  <el-button
                    type="primary"
                    @click="userLogin"
                    class="submit-btn"
                    :loading="loading"
                    :disabled="disabled"
                    v-else
                  >登录</el-button>
                </div>
                <div class="foot-btn">
                  <span class="wx fl" @click="weixinLogin">
                    <i class="iconfont icon-weixin2"></i>微信登录
                  </span>
                  <el-link
                    class="mm fr"
                    href="/findpwd"
                    target="_blank"
                    :underline="false"
                    v-if="activeName == 'second'"
                  >忘记密码</el-link>
                </div>
              </div>
            </div>
            <!-- 扫码登录 -start-->
            <div class="login-code" v-else>
              <h1 class="tit">打开广西人才网APP</h1>
              <p class="sys">扫一扫登录</p>
              <div class="code-img">
                <el-image style="width: 161px; height: 161px" :src="codeIMG" fit="cover"></el-image>
                <div class="abate" v-if="efficacy">
                  <p class="tip">{{ efficacyTXT }}</p>
                  <el-button type="primary" @click="methods.loginCode(0)">刷新</el-button>
                </div>
                <div class="abate"  v-if="isScan">
                  <p class="tip">扫描成功<br/>请在手机上点击"登录"</p>
                </div>
              </div>
            </div>
            <!-- end -->
          </div>
        </div>
        <div class="focus">
          <el-carousel height="600px" arrow="never">
            <el-carousel-item v-for="(item, index) in listAd" :key="index" @click="methods.countLogo(item.logoID)">
              <a
                :href="item.linkUrl"
                :style="
                  'background:url(' + item.logoSrc + ') no-repeat center top;'
                "
                target="_blank"
              ></a>
            </el-carousel-item>
          </el-carousel>
        </div>
      </el-main>
      <el-footer>
        <Footer />
      </el-footer>
    </el-container>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  ref,
  toRefs,
  Ref,
  onBeforeMount,
  watch,
  onBeforeUnmount
} from "vue";
import { computed, getCurrentInstance } from "@vue/runtime-core";
import { useRouter, useRoute } from "vue-router";
import { initGeet } from "../mixins/geetestMixin";
import {
  accountLogin,
  getSecurityImageAsync,
  geetestSendCode,
  loginByPhone,
  getAdlist,
  randKey,
  getqrcode,
  checksScan,
  
} from "../http/api";


import { ClickCount } from "@/utils/utils";

import { ElMessage } from "element-plus";
import Footer from "@/components/Footer.vue";
import Header from "@/components/header.vue";
import { useStore } from "vuex";
// import { stringify } from "querystring";
import {setCookies} from "@/utils/common";
export default defineComponent({
  components: { Footer, Header },
  setup() {
    const { proxy, ctx } = getCurrentInstance() as ComponentInternalInstance;
    const _this = ctx;
    const router = useRouter();
    const route = useRoute();
    const FormUserA: Ref<any> = ref(null);
    const FormUserB: Ref<any> = ref(null);
    const state = reactive({
      loading: false,
      disabled: false,
      userName: ref(""),
      password: ref(""),
      FormUser: {
        device: 0,
        devToken: "",
        siteId: 0,
        userName: "",
        pwd: "",
        tokenId: "",
        captchaCode: "",
        unionId: "",
        type: 0,
        photo: "",
        nickName: "",
      },
      codeIMG: "",
      mode: 1, //切换登录方式
      activeName: "first", //tab切换
      msg: "", //后端返回的消息
      FormPhone: {
        device: 0,
        devToken: "",
        siteId: 0,
        phone: "",
        code: "",
      },
      send: false,
      showTis: false,
      clock: 0,
      countTime: 60,
      efficacy: false, //二维码失效
      isScan:false,
      efficacyTXT: "",
      automatism: false,
      security: "",
      showValid: false, //显示图形验证码
      captcha: {} as any, //验证码实例
      bussid:computed(()=>{
        let url = window.location.href;
      if (url.indexOf("/gl/") != -1) {
        return 1
      }
      if (url.indexOf("/lz/") != -1) {
        return 2;
      }
      if (url.indexOf("/wz/") != -1) {
        return 4;
      }
      if (url.indexOf("/gp/") != -1) {
        return 5;
      }
      if (url.indexOf("/bs/") != -1) {
        return 6;
      }
      if (url.indexOf("/qz/") != -1) {
        return 7;
      }
      if (url.indexOf("/hc/") != -1) {
        return 8;
      }
      if (url.indexOf("/bh/") != -1) {
        return 9;
      }
      if (url.indexOf("/fc/") != -1) {
        return 11;
      }
      if (url.indexOf("/yl/") != -1) {
        return 12;
      }
      if (url.indexOf("/cz/") != -1) {
        return 13;
      }
      if (url.indexOf("/gg/") != -1) {
        return 14;
      }
      if (url.indexOf("/lb/") != -1) {
        return 15;
      }
      if (url.indexOf("/hz/") != -1) {
        return 18;
      }
      if (url.indexOf("/pn/") != -1) {
        return 20;
      }
      if (url.indexOf(".gxrc.com/login") != -1||url.indexOf("tgxrc.com:8086/login") != -1) {
        return 0;
      }
      }),
      
      check: "" as any,
      weixinAppid: "wx4badbe4df2d5a02e" as string,
      weixinRedirectURI: "http%3a%2f%2fmy.gxrc.com%2foAuth%2fCallback",
      //weixinRedirectURI: "http%3a%2f%2fmy.gxrc.com%2flogin",
      listAd: [
        {
          linkUrl:
            "https://image.gxrc.com/gxrcsite/zt/MobileApp/index.htm?tdsourcetag=s_pctim_aiomsg",
          logoSrc:
            "https://logo.gxrc.com/Logo/81046651-e3d9-41c5-a87c-02c6a4e4fa4a/grdl0902.jpg",
        },
      ], 
      //焦点图背景--横幅
    });
    watch(() => state.automatism, (newValue, oldValue) => { state.showTis = !state.automatism});

    const validatephone = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输入手机号码"));
      } else {
        if (/^1[3456789]\d{9}$/.test(value)) {
        } else {
          callback(new Error("请输入正确的11位手机号码"));
        }
        callback();
      }
    };
    const validatecode = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输短信验证码"));
      } else {
        if (/^[0-9]\d{3,5}$/.test(value)) {
        } else {
          callback(new Error("请输入正确的短信验证码"));
        }
        callback();
      }
    };
    let rulesUser = {
      userName: [
        {
          required: true,
          message: "请输用户名",
          trigger: "blur",
        },
      ],
      pwd: [
        {
          required: true,
          message: "请输入密码",
          trigger: "blur",
        },
      ],
    };
    let rulesphone = {
      phone: [{ validator: validatephone, trigger: "blur" }],
      code: [{ validator: validatecode, trigger: "blur" }],
    };
    const store = useStore();
    onBeforeMount(async () => {
      methods.getAD(state.bussid);
      state.captcha = await initGeet();
      store.commit("setLogoPagBid", state.bussid);
    });
    onBeforeUnmount(()=>{
      clearInterval(state.check);
    })
    const methods = {
      async initGeet() {
        state.captcha = await initGeet();
      },
      async getAD(id: any) {
        let form = {
          districtid: id,
        };
        const res: any = await getAdlist(form);
        if (res.data.length > 0) {
          state.listAd = res.data;
        }
      },
      //密码登录
      async onLogin() {
        state.loading = true;
        state.disabled = true;
        const res: any = await accountLogin(state.FormUser);
        if (res.code == 1) {
          fun.goBack(res.message);
          let domains=(window.location.host).indexOf(".tgxrc.com")> -1?".tgxrc.com":".gxrc.com";
          setCookies('bid',state.bussid,7*24*60*60,domains)
        } else {
          var num = res.message.substring(1, 5);
          var msg = res.message.substring(6);
          if (num == 1002 || num == 1001) {
            methods.getcaptcha();
            state.showValid = true;

          }
          if (res.message.indexOf("重复登录") > -1) {
            setTimeout(function () {
              if (route.query.returnUrl) {
                // router.push({ path: route.query.returnUrl });
                window.location.href = route.query.returnUrl;
              } else {
                router.push({ path: "/" });
              }
            }, 500);
          }
          state.disabled = false;
          state.loading = false;
          ElMessage.error(msg);
        }
      },
      //手机号登录
      async loginbyPhone() {
        state.loading = true;
        state.disabled = true;
        let form = state.FormPhone;
        form.siteId = state.bussid || 0;
        let data: any = await loginByPhone(form);
        state.loading = false;
        if (data.code == 1) {
          let domains=(window.location.host).indexOf(".tgxrc.com")> -1?".tgxrc.com":".gxrc.com";
          setCookies('bid',state.bussid,7*24*60*60,domains)
          ElMessage.success(data.message);
          window.clearInterval(state.clock);
          if (route.query.returnUrl) {
            // router.push({ path: route.query.returnUrl });
            window.location.href = route.query.returnUrl;
          } else {
            //新注册用户 跳转分布注册页
            if (data.data.step === "noresume") {
              router.replace({ name: "registerBasicInfo" });
            } else {
              router.push({ path: "/" });
            }
          }
        } else {
          ElMessage.error(data.message);
          state.disabled = false;
        }
      },
      //获取图形验证码
      async getcaptcha() {
        let data: any = await getSecurityImageAsync("");
        state.security = data.data.image;
        state.FormUser.tokenId = data.data.token;
      },
      //极验
      async geetestValidate() {
        state.captcha.verify();
        await state.captcha.onSuccess(async () => {
          const result = state.captcha.getValidate();
          if (result != undefined) {
            state.loading = true;
            let data = {
              geetest_challenge: result.geetest_challenge,
              geetest_validate: result.geetest_validate,
              geetest_seccode: result.geetest_seccode,
              phone: state.FormPhone.phone,
              site: 1,
            };

            let res: any = await geetestSendCode(data);
            state.loading = false;
            if (res.code == 1) {
              ElMessage.success(res.message);
              fun.countDown();
            } else if (res.code !== 1) {
              ElMessage.error(res.message);
              return;
            } else {
              ElMessage.error("验证失败，请稍后再试");

              return;
            }
          } else {
            ElMessage.error("验证失败，请稍后再试");
            return;
          }
        });
      },
      async loginCode(a: number) {
        state.mode = a;
        if (a == 0) {
          state.efficacy = false;
          let resKey: any = await randKey("");
          if (resKey.code == 1) {
            let form = {
              content: resKey.data.key,
            };
            let res: any = await getqrcode(form);
            let url = "/api/qrcode/getqrcode?content=" + resKey.data.key;
            state.codeIMG = url;
            fun.chronography(resKey.data.key);
          }
        } else {
          clearInterval(state.check);
        }
      },

      async ISchecksScan(tokenid: string) {
        let form = {
          tokenid: tokenid,
          siteid: state.bussid,
        };
        const res: any = await checksScan(form);
        // status  2000 扫码登录成功  2001-未扫码 2002 已扫码  2004 二维码过期  2005 扫码登录失败  2006 取消登录
        if (res.code == 1) {
          // if (res.data.status == 2001||res.data.status == 2002) {

          // }
          if (res.data.status == 2000) {
            fun.goBack("扫码登录成功");
            state.isScan = false;
            state.efficacy = false;
            let domains=(window.location.host).indexOf(".tgxrc.com")> -1?".tgxrc.com":".gxrc.com";
            setCookies('bid',state.bussid,7*24*60*60,domains)
            clearInterval(state.check);
            return false;
          }
          if (res.data.status == 2004) {
            state.efficacy = true;
            state.isScan = false;
            state.efficacyTXT = "二维码已过期";
            clearInterval(state.check);
          }
          if (res.data.status == 2005) {
            state.efficacy = true;
            state.isScan = false;
            state.efficacyTXT = "扫码登录失败";
            clearInterval(state.check);
          }
          if (res.data.status == 2006) {
            state.efficacy = true;
            state.isScan = false;
            state.efficacyTXT = "取消登录";
            clearInterval(state.check);
          }
          //扫码成功但是还没登录-接口还要继续请求
          if (res.data.status == 2002) {
            state.isScan = true;
            state.efficacy = false;
          }
        } else {
          state.efficacy = true;
            state.isScan = false;
          state.efficacyTXT = res.message;
          clearInterval(state.check);
        }
      },
      //广告点击统计
      async countLogo(logoID:number){
        let form = {
          logoID: logoID,
          from:0
        };
        ClickCount(form)
      }
    };
    const fun = {
      handleClick(tab: any, event: any) {
      },
      SignUp() {
      },
      //密码登录
      userLogin() {
        if (!state.automatism) {
          state.showTis = true;
          return false;
        }
        FormUserA.value.validate((valid: boolean) => {
          if (valid) {
            methods.onLogin();
          }
        });
      },
      //手机验证码登录
      phoneLogin() {
        if (!state.automatism) {
          state.showTis = true;
          return false;
        }
        FormUserB.value.validate((valid: boolean) => {
          if (valid) {
            methods.loginbyPhone();
          }
        });
      },
      sendSmsCode() {
        FormUserB.value.validateField("phone", (valid: boolean) => {
          if (!valid) {
            methods.geetestValidate();
          }
        });
      },
      countDown() {
        state.send = true;
        state.clock = window.setInterval(() => {
          state.countTime--;
          if (state.countTime < 0) {
            window.clearInterval(state.clock);
            state.countTime = 0;
            state.send = false;
            //重新初始化极验
            methods.initGeet();
          }
        }, 1000);
        state.countTime = 60;
      },
      weixinLogin() {
        //微信号
        function randomString(e: number) {
          e = e || 32;
          var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
            a = t.length,
            n = "";
          for (let i = 0; i < e; i++)
            n += t.charAt(Math.floor(Math.random() * a));
          return n;
        }
        let stated = randomString(32);

        window.location.href = `https://open.weixin.qq.com/connect/qrconnect?response_type=code&appid=${state.weixinAppid}&redirect_uri=${state.weixinRedirectURI}&state=${stated}&scope=snsapi_login`;
      },
      //一分钟请求二维码接口
      chronography(tokenid: any) {
        state.check = setInterval(() => {
          methods.ISchecksScan(tokenid);
        }, 1000);
      },
      goBack(msg: any) {
        ElMessage.success(msg);
        setTimeout(function () {
          if (route.query.returnUrl) {
            if (route.query.returnUrl.indexOf("login") > -1) {
              router.push({ path: "/" });
            } else {
              // router.push({ path: route.query.returnUrl });
              window.location.href = route.query.returnUrl;
            }
          } else {
            router.push({ path: "/" });
          }
        }, 1000);
      },
    };
    return {
      ...toRefs(state),
      ...fun,
      rulesUser,
      rulesphone,
      FormUserA,
      FormUserB,
      methods,
    };
  },
});
</script>
<style lang="less">
.login-page {
  .el-container.is-vertical {
    min-width: 1200px;
  }

  .el-main {
    padding: 0 0 0 0;
  }

  .focus {
    width: 100%;
    a {
      width: 100%;
      height: 600px;
      display: block;
    }
  }
  .login-message-phone {
    padding: 25px 0 0px 0;
    .el-tabs__nav {
      width: 100%;
      .el-tabs__item {
        width: 50%;
        padding: 0 0 45px 0;
        text-align: center;
        font-size: 22px;
        color: #999;
      }
      .is-active {
        color: #333;
        font-weight: bold;
      }
    }
    .el-tabs__active-bar {
      width: 32px !important;
      left: 70px;
      height: 4px;
      border-radius: 4px;
    }
    .el-tabs__nav-wrap::after {
      background-color: #fff;
    }
    .el-tabs__header {
      padding-bottom: 25px;
    }
    .checkbox-Seven {
      position: relative;
      a.xy {
        font-size: 12px;
        color: #5f9efc;
        line-height: 40px;
        float: left;
      }
      .el-checkbox {
        float: left;
      }
      .el-checkbox__label {
        font-size: 12px;
        color: #666666;
      }
      .elError {
        color: var(--el-color-danger);
        font-size: 12px;
        line-height: 1;
        padding-top: 4px;
        position: absolute;
        top: 23px;
        left: 0px;
      }
      // .el-popover {
      //   z-index: 2216;
      //   width: 181px;
      //   position: absolute;
      //   left: -66px;
      //   top: -40px;
      //   margin: 0px;
      //   font-size: 12px;
      //   color: #f00;
      // }
      // .el-popper__arrow {
      //   position: absolute;
      //   left: 0px;
      //   transform: translate3d(67.5px, 0px, 0px);
      // }
    }
    .login-user {
      .code {
        .el-input-group__append {
          background: none;
          padding: 4px 0 0 0;
          border: 1px solid #f2f2f2;
          border-left: none;
        }
      }
    }
    .foot-btn {
      font-size: 14px;
      span.wx {
        color: #49be10;
        cursor: pointer;
        i {
          font-size: 17px;
          padding-right: 5px;
        }
      }
    }
    .login-phone {
      .code {
        .el-input-group__append {
          background: #fff;
          border: 1px solid #f2f2f2;
          border-left: none;
        }
        span.blue {
          cursor: pointer;
        }
      }
    }
  }

  .login-wrap {
    position: relative;
  }
  .login-box {
    padding: 36px 36px 0 36px;
    width: 350px;
    min-height: 424px;
    position: absolute;
    right: 0;
    top: 70px;
    background: #fff;
    border-radius: 8px;
    z-index: 99;
  }
  .login-code {
    text-align: center;
    .tit {
      font-size: 22px;
      color: #333;
      padding: 30px 0 20px 0;
    }
    .sys {
      font-size: 16px;
      color: #999;
      padding: 0 0 25px 0;
    }
    .code-img {
      width: 160px;
      height: 160px;
      position: relative;
      margin: auto;
      .abate {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 160px;
        height: 160px;
        z-index: 9;
        background: #000000c4;
      }
      img {
        width: 100%;
        height: 100%;
      }
    }
    .tip {
      color: #ffffff;
      padding: 50px 0 10px 0;
      font-size: 14px;
    }
    .el-button--primary {
      height: 25px;
      line-height: 25px;
      padding: 0 0 0 0;
      width: 80px;
      font-size: 12px;
      background: #5f9efc;
    }
  }
  .submit-btn {
    height: 44px;
    line-height: 44px;
    background: #5f9efc;
    color: #fff;
    font-size: 18px;
    margin-bottom: 15px;
    width: 100%;
    border-radius: 2px;
    padding: 0 0 0 0;
  }
  .el-input__inner {
    height: 46px;
    line-height: 46px;
    border: 1px solid #f2f2f2;
  }
  .ico {
    position: absolute;
    top: 20px;
    right: 20px;
    cursor: pointer;
    z-index: 99;
    i {
      color: #5f9efc;
      font-size: 28px;
    }
  }
  .el-footer {
    background: #fff;
  }
}
</style>
