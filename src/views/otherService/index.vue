<template>
 <div class="other-service-page bg-white">
        <div class="tit">
        <h2>人才服务</h2>
        <p>中国广西人才市场是国家人事部同意广西壮族自治区人民政府建立的区域性、综合性国家级人才市场。而广西人才网是中国广西人才市场的网上服务窗口，竭诚为求职者提供求职中介、人才培训、人才测评、人事代理、人才租赁等一系列高端服务。</p>
  </div>
   <div class="con">
<ul>
    <li>
        <div class="info">
 <div class="title">人才培训（广西人才学习城）</div>
 <div class="summary">
广西人才学习城下属于中国广西人才市场，开设认证考试、考前培训、资格考试、学历学位教育、卫星远程教育、企业内训等培训课程。部分品牌课程：注册国际人力资源师、全国营销、物流管理、国家公务员录用考试、建造师、神州数码工程师、普通话水平测试培训…
  </div>
 <div class="btn-wrap">
   <a href="http://www.gxrcpx.com/" target="_blank">
            <el-button type="primary" round>广西人才培训网
                <i class="iconfont icon-arrowRight9"></i>
            </el-button>
   </a>
    </div>
        </div>
        <div class="pic">
<el-image
        src="https://image.gxrc.com/gxrcsite/my/2021/rcfw-img1.png"
        fit="fill"
      ></el-image>
        </div>
    </li>
      <li>
        <div class="info">
 <div class="title">人才测评与职业生涯规划</div>
 <div class="summary">
为什么我老是觉得找不到合适我的工作？到底什么行业适合我？我的特长到底在哪里，我有哪些潜质？我觉得我能力还可以，为什么对我手头的工作老是做不好？如果您总是有以上问题烦心，我们建议您可以试做一下个人的职业生涯规划，解决您心头的职业忧郁和苦恼！
  </div>
 <div class="btn-wrap">
   <a href="https://cp.gxrc.com/" target="_blank">
            <el-button type="primary" round>人才评荐中心
                <i class="iconfont icon-arrowRight9"></i>
            </el-button>
            </a>
    </div>
        </div>
        <div class="pic">
<el-image
        src="https://image.gxrc.com/gxrcsite/my/2021/rcfw-img2.png"
        fit="fill"
      ></el-image>
        </div>
    </li>
      <li>
        <div class="info">
 <div class="title">人事代理</div>
 <div class="summary">
中国广西人才市场人事代理部是广西最权威的人事代理机构，我们运用社会化服务方式和现代科学的管理手段，为各类用人单位和人才提供：接收保管人事档案、落户南宁市户口、办理专业技术职称评审、代办学历认证、代办社会保险、接转党团关系等专业化的人事代理服务。
  </div>
 <div class="btn-wrap">
   <a href="http://www.gxrcda.com/" target="_blank">
            <el-button type="primary" round>广西人事代理网
                <i class="iconfont icon-arrowRight9"></i>
            </el-button>
            </a>
    </div>
        </div>
        <div class="pic">
<el-image
        src="https://image.gxrc.com/gxrcsite/my/2021/rcfw-img3.png"
        fit="fill"
      ></el-image>
        </div>
    </li>
</ul>
  </div>
    </div>

</template>

<script lang="ts">
import { defineComponent,reactive} from 'vue'
  export default defineComponent({
    setup() {
        const state = reactive({
        })

   return {
       state
      }
    }
  })
</script>

<style lang="less">
.other-service-page{padding:20px;
    .tit{padding-bottom:30px;
        h2{padding-bottom: 10px;
font-size: 22px;}
p{
line-height: 24px;
font-size: 14px;}}
    .con{
ul{
  li{display: flex;
background: #F4F8FD;padding:30px;
border-radius: 8px;margin-bottom: 20px;
.info{flex:3;
.title{padding-bottom: 20px;font-weight: bold;
font-size: 22px;}
.summary{font-size: 14px;padding-bottom: 20px;
line-height: 24px;}
.btn-wrap{.iconfont{font-size: 12px;margin-left:5px;}}}
.pic{flex:1;text-align: right;
img{width: 140px;height: 140px;}}
}
}
    }
}
</style>


