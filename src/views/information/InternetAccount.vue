<template>
  <div class="account-page">
    <TopMenu :index="2" :isInformation="true" />
    <div class="bg-white">
      <div class="tit">账号绑定</div>

      <div class="con">
        <ul>
          <li v-for="(item, index) in list" :key="index">
            <div class="le"><i :class="`iconfont icon-${item.icon}`"></i></div>
            <div class="mi">
              <p>{{ item.name }}</p>
              <p class="zh" v-if="item.bind">{{ item.account }}</p>
            </div>
            <div class="ri" v-if="!item.bind">
              <el-button type="primary" @click="bind(item.type)"
                >立即绑定</el-button
              >
            </div>
            <div class="ri" v-else>
              <el-button @click="unbind(item.type)">解绑</el-button>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!-- 修改手机弹窗 -->
    <Changephone
      @revisePhone="revisePhone"
      :phone="list[1].account"
      v-if="dialogVisiblePhone"
      :phoneIsAuth="phoneIsAuth"
    />
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, onMounted, watch, ref, toRefs,Ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  accountBind,
  weixinBind,
  weixinUNbind,
} from "@/http/api";
import { ElMessage } from "element-plus";
import Changephone from "@/views/information/components/changephone.vue";
import TopMenu from "@/components/TopMenu.vue";

export default defineComponent({
  components: { TopMenu, Changephone },
  setup() {
    const route = useRoute();
    const state = reactive({
      
      list: [
        //{"type": 2,"name":"QQ账号","icon":"QQ","account":"","bind": false},
        { type: 3, name: "微信号", icon: "weixin4", account: "", bind: false },
        // {"type": 4,"name":"新浪微博","icon":"weibo","account":"","bind": false},
        { type: 1, name: "手机号", icon: "shouji", account: "", bind: false },
      ],
      //qqAppid:'**********',
      //qqRedirectURI:'http://my2.tgxrc.com:3000/InternetAccount',
       weixinAppid:"wx4badbe4df2d5a02e" as string,
      // weixinRedirectURI: "http%3A%2F%2Fmy.gxrc.com%3A3000%2FInternetAccount",
       weixinRedirectURI: "http%3a%2f%2fmy.gxrc.com%2fInternetAccount",
      phoneIsAuth: true,
      dialogVisiblePhone: false,
    });
    onMounted(() => {
      methods.getData();
      methods.getWeixinBind();
    });
    const methods = {
      async getWeixinBind() {
        if (route.query.code && route.query.state) {
          const res = await weixinBind(route.query.code);
          if (res.code == 1) {
            ElMessage.success(res.message);
            methods.getData();
          } else {
            ElMessage.error(res.message);
          }
        }
      },
      async getData() {
        const res = await accountBind({ device: 0 });
        res.data.forEach((element: any) => {
          state.list.forEach((item) => {
            if (element.type == item.type) {
              item.account = element.value;
              item.bind = element.bind;
            }
          });
        });
      },
      async wxunbind() {
        const res = await weixinUNbind();
        if (res.code == 1) {
          ElMessage.success(res.message);
          methods.getData();
        } else {
          ElMessage.error(res.message);
        }
      },
      
    };
    const fun = {
      // 解绑
      unbind(type: number) {
        if (type == 1) {
          state.dialogVisiblePhone = true;
          //手机号
        } else if (type == 2) {
          //QQ账号
        } else if (type == 3) {
          //微信
          methods.wxunbind();
        }
      },
      // 修改手机号
      revisePhone(phone: number) {
        state.dialogVisiblePhone = false;
        state.phoneIsAuth=true
        if (phone) {
          let start = phone.toString().slice(0, 3);
          let end = phone.toString().slice(-4);
          let a = `${start}****${end}`;
          state.list[1].account = a;
        }
      },
      bind(type: number) {
        if (type == 1) {
          //手机号绑定
          state.dialogVisiblePhone = true;
          state.phoneIsAuth=false
        } else if (type == 2) {
          //QQ账号
          // QC.Login.showPopup({
          //     appId:state.qqAppid,
          //     redirectURI:state.qqRedirectURI
          // })
        } else if (type == 3) {
          //微信号
          function randomString(e: number) {
            e = e || 32;
            var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
              a = t.length,
              n = "";
            for (let i = 0; i < e; i++)
              n += t.charAt(Math.floor(Math.random() * a));
            return n;
          }
          let stateID = randomString(32);
           window.location.href = `https://open.weixin.qq.com/connect/qrconnect?response_type=code&appid=${state.weixinAppid}&redirect_uri=${state.weixinRedirectURI}&state=${stateID}&scope=snsapi_login`;
        }
      },
    };

    return {
      ...toRefs(state),
      methods,
      ...fun,
    };
  },
});
</script>
<style lang="less" >
.account-page {
  .bg-white {
    padding: 20px;
    .tit {
      height: 30px;
      line-height: 30px;
      padding-bottom: 30px;
      font-size: 18px;
      font-weight: bold;
    }
    .con {
      ul {
        li {
          display: table;
          width: 100%;
          vertical-align: middle;
          height: 80px;
          padding: 0 20px;
          box-sizing: border-box;
          border-bottom: 1px solid #f2f2f2;
          > div {
            display: table-cell;
            vertical-align: middle;
          }
          .le {
            width: 60px;
            .iconfont {
              font-size: 40px;
              color: rgb(65, 175, 254);
            }
            .icon-QQ {
              color: rgb(65, 175, 254);
            }
            .icon-weixin4 {
              color: rgb(73, 190, 16);
            }
            .icon-weibo {
              color: rgb(251, 85, 85);
            }
            .icon-shouji {
              color: rgb(0, 177, 253);
            }
          }
          .mi {
            width: 400px;
            p {
              padding-bottom: 5px;
              font-size: 16px;
            }
            p.zh {
              font-size: 14px;
              color: #bbbbbb;
            }
          }
          .ri {
            text-align: right;
            .el-button {
              width: 100px;
            }
          }
        }
        li:last-child {
          border-bottom: 0;
        }
      }
    }
  }
}
</style>