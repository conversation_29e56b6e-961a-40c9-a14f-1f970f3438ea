<template>
  <div class="workComponent borderLine2">
    <subheading @addItem="addItem" :hasbtn="hasbtn" title="工作经历"></subheading>
    <div class="list-work list-unify">
      <ul class>
        <li v-if="!readOnly">
          <workEdit @handleRtn="handleRtn" :resumeid="resumeid" :workid="0"></workEdit>
        </li>
        <li
          v-for="(item, index) in passData"
          :key="index"
          @mouseover="showShadow = index"
          @mouseout="showShadow = 900"
          :class="showShadow == index ? 'item bar-shadow-unify' : 'item'"
        >
          <div class="li-readOnly" v-if="addshowindex !== index">
            <h1>
              {{ item.entName || '未填写公司名称' }}
              <label>{{ item.timeRang }}</label>
            </h1>
            <div class="info">
              <span>{{ item.enterprisePropertyName }}</span>
              <el-divider direction="vertical" v-if="item.enterpriseEmployeeNumberName"></el-divider>
              <span>{{ item.enterpriseEmployeeNumberName }}</span>
              <el-divider direction="vertical" v-if="item.workIndustryName"></el-divider>
              <span>{{ item.workIndustryName }}</span>
              <el-divider direction="vertical" v-if="item.workPlace"></el-divider>
              <span>{{ item.workPlace }}</span>
            </div>
            <h2>{{ item.positionName || item.positionTypeName }}</h2>
            <div class="info">
              <span>{{ item.department }}</span>
              <el-divider direction="vertical" v-if="item.workPropertyName"></el-divider>
              <span>{{ item.workPropertyName }}</span>
              <el-divider direction="vertical" v-if="item.positionLevelName"></el-divider>
              <span>{{ item.positionLevelName }}</span>
            </div>
            <p class="doc" v-safe-html.relaxed="item.positionDescription"></p>
            <div class="workSkills">
              <ul><li v-for="(i, index) in item.keywordIds" :key="index">{{i.keywordName}}</li>
               </ul>
            </div>
            <div class="advanced-talent" v-if="item.higherUp||item.jobPerformance||item.leavingReason||item.underlingNum">
              <div class="bar" v-if="item.higherUp||item.underlingNum">
                <span class="sub" v-if="item.higherUp">汇报对象</span>
                <label v-if="item.higherUp">{{ item.higherUp }}</label>
                <span :class="item.higherUp?'sub sub2':'sub'" v-if="item.underlingNum">下属人数</span>
                <label v-if="item.underlingNum">{{ item.underlingNum }}</label>
              </div>
              <div class="bar" v-if="item.leavingReason">
                <span class="sub" >离职原因</span>
                <label>{{ item.leavingReason }}</label>
              </div>
              <div class="bar bar-end" v-if="item.jobPerformance">
                <span class="sub">工作业绩</span>
              </div>

              <p class="doc" v-safe-html.relaxed="item.jobPerformance"></p>
            </div>
            <div class="projects" v-if="item.hasProject > 0">
              <el-collapse v-model="item.workId">
                <el-collapse-item name="1" :title="'参与' + item.hasProject + '个项目'">
                  <div>
                    <ul class="projectsList">
                      <li v-for="(it, index) in projectData" :key="index">
                        <a
                          class="clearfix li"
                          v-if="it.playRole == item.workId[0]"
                          :href="'#' + it.playRole"
                        >
                          <span>{{ it.projectName }}</span>
                          <label>
                            详情
                            <i class="iconfont icon-arrowDown5"></i>
                          </label>
                        </a>
                      </li>
                    </ul>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
            <btnBox @deleteItem="deleteItem" @edit="edit" :passData="item" :ind="index"></btnBox>
          </div>
          <workEdit @handleRtn="handleRtn" v-else :resumeid="item.resumeId" :workid="item.workId"></workEdit>
        </li>
      </ul>
    </div>
    <!-- 为空时的状态 -->
    <emptyBox v-if="isempty" :resumeid="resumeid" mold="1" @handleBox="CopyDefaultesume" />
  </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  computed,
  ref,
  onBeforeMount,
} from "vue";
import subheading from "@/views/information/components/subheading.vue";
import workEdit from "./workEdit.vue";
import emptyBox from "./emptyBox.vue";
import btnBox from "./btnBox.vue";
// import { useStore } from "vuex";
import { ElMessage, ElMessageBox } from "element-plus";
import { deleteWork } from "../../../http/resumeApi";
import { runMain } from "module";

export default defineComponent({
  components: { subheading, workEdit, emptyBox, btnBox },
  emits: ["ReloadData"],
  props: {
    passData: {
      type: Array,
      default: () => [],
    },
    resumeid: {
      type: Number,
      default: 0,
    },
    projectData: {
      type: Array,
      default: () => [],
    },
  },

  setup(props: any, { emit }) {
    const state = reactive({
      readOnly: true,
      showShadow: 900,
      addshowindex: 999, //打开的窗口
      resumeid: props.resumeid,
      workid: 0,
      hasbtn: computed(() => {
        if (props.passData.length > 0) {
          return true;
        } else {
          return false;
        }
      }),
      projectsNum: 3,
      activeNames: ref(["1"]),
      isempty: computed(() => {
        if (props.passData.length > 0) {
          return false;
        } else if (props.passData.length < 0 || state.readOnly) {
          return true;
        } else {
          return false;
        }
      }),
      
    });
    // const store = useStore();

    const methods = {
      async deleteItem(p: any) {
        let form = {
          resumeid: p.resumeId,
          workid: p.workId,
        };
        let res: any = await deleteWork(form);
        if (res.code == 1) {
          // 删除成功 重新加载数据
          ElMessage.success(res.message);
          emit("ReloadData");
        } else {
          ElMessage.error(res.message);
        }
      },
    };
    const fun = {
      //删除--操作
      deleteItem(p: any) {
        ElMessageBox.confirm("确定删除该项工作经历吗", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            methods.deleteItem(p);
          })
          .catch(() => {
            return;
          });
      },
      //添加项目
      addItem() {
        state.resumeid = props.resumeid;
        state.workid = 0;
        state.readOnly = false;
      },
      //编辑
      edit(data: any, ind: number) {
        state.addshowindex = ind;
      },
      // 接收子组件传过来的值
      handleRtn(type: number) {
        if (type == 1) {
          emit("ReloadData");
        }
        state.addshowindex = 900;
        state.readOnly = true;
      },
      // 从默认简历复制
      CopyDefaultesume(type: string) {
        if (type == "1") {
          emit("ReloadData");
        } else {
          state.readOnly = false;
        }
      },
      //计算项目个数
      count(id: Number) {
        let a = 0;
        props.projectData.forEach((p: any) => {
          if (p.playRole == id) {
            a++
          }
        });
        return a || '*'
      }
    };

    return {
      ...toRefs(state),
      ...fun,
    };
  },
});
</script>
<style lang="less" >
.workComponent {
      padding-bottom: 20px;
  .advanced-talent {
    padding: 24px;
    background: #fafafa;
    margin: 24px 0 0 0;
    .bar {
      padding-bottom: 20px;
      font-size: 14px;
      .sub {
        color: #999;
      }
      label {
        color: #333;
        padding-left: 16px;
      }
      .sub2 {
        padding-left: 50px;
      }
    }
    .bar-end {
      padding-bottom: 10px;
    }
  }
  .workSkills{
    ul{
      display: flex;
      flex-wrap: wrap;
    }
    li{
      background: #FAFAFA;
      color: #666666;
      font-size: 14px;
      padding: 3px 12px;
      margin-right: 5px;
      margin-top: 5px;
    }
  }
  .projects {
    padding: 20px 0 0 0;
    .el-collapse {
      border-top: none;
      border-bottom: none;
    }
    .el-collapse-item__header {
      width: 110px;
      color: #5f9efc;
      font-size: 14px;
      border: none;
    }
    .projectsList {
      width: 300px;
      height: auto;
      border-left: 4px solid #f4f5f9;
      margin: 20px 0 0 0;
      .li {
        // width: 100%;
        display: inline-block;
        padding: 0px 12px 20px;
        span {
          width: 230px;
          float: left;
          font-size: 14px;
          color: #333;
        }
        label {
          float: right;
          font-size: 13px;
          color: #999;
          cursor: pointer;
          i {
            font-size: 12px;
            color: #999;
            padding-left: 3px;
          }
        }
        &:last-child {
          padding: 0px 12px 0px;
        }
      }
    }
  }
  .list-work {
    .item {
      .btn-pop {
        top: 24px;
      }
    }
  }
  .doc {
    line-height: 26px;
  }
}
</style>