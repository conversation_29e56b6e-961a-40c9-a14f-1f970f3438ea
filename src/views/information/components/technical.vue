<template>
  <div class="technicalComponent borderLine2">
    <subheading
      @addItem="addItem"
      title="技术能力"
      :hasbtn="hasbtn"
    ></subheading>
    <div class="list-project list-unify">
      <ul class="">
        <li v-if="!readOnly">
          <technicalEdit
            @handleRtn="handleRtn"
            :resumeid="resumeid"
            :technologyid="technologyid"
          ></technicalEdit>
        </li>
        <li
          v-for="(p, index) in passData"
          :key="p.techId"
          @mouseover="showShadow = index"
          @mouseout="showShadow = 900"
          :class="showShadow == index ? 'item bar-shadow-unify' : 'item'"
        >
          <div class="li-readOnly" v-if="addshowindex !== index">
            <h1>{{ p.techName }}</h1>
            <div class="rank">
              <el-row>
                <el-col :span="8">
                  <span>使用时间</span>
                  <label>{{ p.monthUsed }}</label>
                </el-col>
                <el-col :span="8">
                  <span>掌握程度</span>
                  <label>{{ p.techLevel }}</label>
                </el-col>
              </el-row>
            </div>

            <!-- 按钮 -->
            <btnBox
              @deleteItem="deleteItem"
              @edit="edit"
              :passData="p"
              :ind="index"
            ></btnBox>
          </div>
          <technicalEdit
            @handleRtn="handleRtn"
            :resumeid="resumeid"
            :technologyid="technologyid"
            v-else
          ></technicalEdit>
        </li>
      </ul>
    </div>
    <!-- 为空时的状态 -->
    <emptyBox
      v-if="isempty"
      :resumeid="resumeid"
      mold="10"
      @handleBox="CopyDefaultesume"
    />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, computed } from "vue";
import btnBox from "./btnBox.vue";
import subheading from "@/views/information/components/subheading.vue";
import technicalEdit from "./technicalEdit.vue";
import emptyBox from "./emptyBox.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { deleteTechnology } from "../../../http/resumeApi";

export default {
  components: { subheading, technicalEdit, emptyBox, btnBox },
  emits: ["ReloadData"],
  props: {
    passData: {
      type: Array,
      default: () => [],
    },
    resumeid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const state = reactive({
      readOnly: true,
      resumeid: props.resumeid,
      showShadow: 900,
      technologyid: 0 as number,
      addshowindex: 999, //打开的窗口
      hasbtn: computed(() => {
        if (props.passData.length > 0) {
          return true;
        } else {
          return false;
        }
      }),
      isempty: computed(() => {
        if (props.passData.length > 0) {
          return false;
        } else if (props.passData.length < 0 || state.readOnly) {
          return true;
        } else {
          return false;
        }
      }),
    });
    const methods = {
      async deleteItem(itemId: number, resumeId: string|number) {
        let form = {
          resumeid: resumeId,
          technologyid: itemId,
        };
        let res: any = await deleteTechnology(form);
        if (res.code == 1) {
          // 删除成功 重新加载数据
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          emit("ReloadData");
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
    };
    //添加技能
    const addItem = () => {
      state.resumeid = props.resumeid;
      state.technologyid = 0;
      state.readOnly = false;
    };
    const edit = (p: any, ind: number) => {
      state.technologyid = p.techId;
      state.resumeid = p.resumeId;
      state.addshowindex = ind;
    };
    //删除--操作
    const deleteItem = (data:any) => {
      ElMessageBox.confirm("确定删除该技能吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          methods.deleteItem(data.techId, data.resumeId);
        })
        .catch(() => {
          return;
        });
    };
    // 从默认简历复制
    const CopyDefaultesume = (type: string) => {
      if (type == "1") {
        emit("ReloadData");
      } else {
        state.readOnly = false;
      }
    };
    // 接收子组件传过来的值
    const handleRtn = (type: number) => {
      if (type == 1) {
        emit("ReloadData");
      }
      state.addshowindex = 900;
      state.readOnly = true;
    };
    return {
      ...toRefs(state),
      addItem,
      edit,
      handleRtn,
      deleteItem,
      CopyDefaultesume,
    };
  },
};
</script>
<style lang="less" >
.technicalComponent {
  .list-project {
    .item {
      .btn-pop {
        top: 20px;
      }
      h1 {
        font-size: 16px;
        font-weight: normal;
      }
      .li-readOnly{
        width: 100%;
      }
    }
    .rank {
      padding-top: 10px;
      span {
        font-size: 14px;
        color: #999;
      }
      label {
        font-size: 14px;
        color: #333;
        padding-left: 16px;
      }
    }
  }
}
</style>