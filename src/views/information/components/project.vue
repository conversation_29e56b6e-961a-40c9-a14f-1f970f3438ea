<template>
  <div class="projectComponent borderLine2">
    <subheading
      @addItem="addItem"
      :hasbtn="hasbtn"
      title="项目经历"
    ></subheading>
    <div class="list-project list-unify">
      <ul class="">
        <li v-if="!readOnly">
          <projectEdit
            @handleRtn="handleRtn"
            :resumeid="resumeid"
            :projectid="projectid"
          ></projectEdit>
        </li>
        <li
          v-for="(p, index) in passData"
          :key="index"
          @mouseover="showShadow = index"
          @mouseout="showShadow = 900"
          :class="showShadow == index ? 'item bar-shadow-unify' : 'item'"
          :id='p.playRole'
        >
          <div class="li-readOnly" v-if="addshowindex !== index">
            <h1>
              {{ p.projectName }}
              <label>{{ p.beginTime }} - {{ p.endTime }}</label>
            </h1>
            <p class="doc" v-safe-html.relaxed="p.projectDescription"></p>
            <!-- 按钮 -->
            <btnBox
              @deleteItem="deleteItem"
              @edit="edit"
              :passData="p"
              :ind="index"
            ></btnBox>
          </div>
          <projectEdit
            @handleRtn="handleRtn"
            :resumeid="p.resumeId"
            :projectid="p.id"
            v-else
          ></projectEdit>
        </li>
      </ul>
    </div>
    <!-- 为空时的状态 -->
    <emptyBox
      v-if="isempty"
      :resumeid="resumeid"
      mold="2"
      @handleBox="CopyDefaultesume"
    />
  </div>
</template>

<script lang="ts">
import subheading from "@/views/information/components/subheading.vue";
import projectEdit from "./projectEdit.vue";
import { reactive, toRefs, defineComponent, computed } from "vue";
import emptyBox from "./emptyBox.vue";
import btnBox from "./btnBox.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { deleteProject } from "../../../http/resumeApi";


export default defineComponent({
  components: { subheading, projectEdit, emptyBox ,btnBox},
  emits: ["ReloadData"],
  props: {
    passData: {
      type: Array,
      default: () => [],
    },
    resumeid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const state = reactive({
      readOnly: true,
      projectid: 0,
      resumeid: props.resumeid,
      showShadow: 900,
      addshowindex: 999, //打开的窗口
      hasbtn: computed(() => {
        if (props.passData.length > 0) {
          return true;
        } else {
          return false;
        }
      }),
      isempty: computed(() => {
        if (props.passData.length > 0) {
          return false;
        } else if (props.passData.length < 0 || state.readOnly) {
          return true;
        } else {
          return false;
        }
      }),
    });
    const methods = {
      async deleteItem(p: any) {
        let form = {
          resumeid: p.resumeId,
          projectid: p.id,
        };
        let res: any = await deleteProject(form);

        if (res.code == 1) {
          // 删除成功 重新加载数据
          ElMessage.success(res.message);
          emit("ReloadData");
        } else {
          ElMessage.error(res.message);
        }
      },
    };
    //添加项目
    const addItem = () => {
      state.resumeid = props.resumeid;
      state.projectid = 0;
      state.readOnly = false;
    };
    //编辑
    const edit = (data: any, ind: number) => {
      state.addshowindex = ind;
    };

    //删除--操作
    const deleteItem = (p: any) => {
      ElMessageBox.confirm("确定删除该项目经历吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          methods.deleteItem(p);
        })
        .catch(() => {
          return;
        });
    };
    // 接收子组件传过来的值
    const handleRtn = (type: number) => {
      if (type == 1) {
        emit("ReloadData");
      }
      state.addshowindex = 900;
      state.readOnly = true;
    };
    // 从默认简历复制
    const CopyDefaultesume = (type: string) => {
      if (type == "1") {
        emit("ReloadData");
      } else {
        state.readOnly = false;
      }
    };
    return {
      ...toRefs(state),
      addItem,
      edit,
      handleRtn,
      deleteItem,
      CopyDefaultesume,
    };
  },
});
</script>
<style lang="less" >
.projectComponent {
  overflow: hidden;
  .doc {
    padding-top: 15px;
    line-height: 26px;
  }
  .list-project {
    .item {
      .btn-pop {
        top: 20px;
      }
    }
  }
}
</style>