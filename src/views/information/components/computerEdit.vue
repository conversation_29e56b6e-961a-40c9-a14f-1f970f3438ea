<template>
    <div class="edit-unify li-pop">
      <el-form
        :model="editForm"
        class="demo-form-inline"
        label-width="100px"
        :rules="rules"
        ref="ruleForm"
        :inline="true"
      >
        <el-form-item label="驾照" class="w280" prop="driverName">
          <el-select
            v-model="editForm.driverName"
            placeholder="请选择"
            
          >
            <el-option
              v-for="item in DrivingoptionsList"
              :key="item.keywordID"
              :label="item.keywordName"
              :value="item.keywordID"
              @click="editForm.driverId = item.keywordID"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="电脑水平" class="w280 fr" prop="computerName">
          <el-select v-model="editForm.computerName" placeholder="请选择">
            <el-option
              v-for="item in ComputeroptionsList"
              :key="item.keywordID"
              :label="item.keywordName"
              :value="item.keywordID"
              @click="editForm.computerId = item.keywordID"
            >
            </el-option>
          </el-select>
        </el-form-item>
         <el-form-item class="btn-end" style="width: 100%">
          <el-button type="primary" @click="onSubmit" class="sub"
            >保存</el-button
          >
          <el-button @click="cancel" class="cel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  onBeforeMount,
  Ref,
  ref,
} from "vue";
import {
  getDrivingoptions,
  getComputeroptions,
} from "../../../http/dictionary";
import { saveOtherability, getOtherability } from "../../../http/resumeApi";
import { ElMessage } from "element-plus";
export default defineComponent({
  emits: ["handleRtn"],
  components: {},
  props: {
    resumeid: {
     type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }: any) {
    const ruleForm: Ref<any> = ref(null);
    const state = reactive({
      readOnly: true,
      editForm: {
        resumeId: props.resumeid,
        driverId: "",
        computerId: "",
        driverName: "",
        computerName: "",
      },
      ComputeroptionsList: [], //电脑能力字典
      DrivingoptionsList: [], //驾照能力
    });
    onBeforeMount(() => {
      //获取数据
      methods.getData();
      //语言技能列表
      if (state.DrivingoptionsList.length < 1) {
        methods.getDrivingoptionsList();
      }
      // 英语技能
      if (state.ComputeroptionsList.length < 1) {
        methods.getComputeroptionsList();
      }
    });
    let rules = {
      driverName: [
        {
          required: false,
          message: "请选择驾照",
          trigger: "blur",
        },
      ],
      computerName: [
        {
          required: false,
          message: "请选择电脑水平",
          trigger: "blur",
        },
      ],
    };
    let prevent=1;
    const methods = {
      async getData() {
        let data = {
          resumeid: props.resumeid,
        };
        let res: any = await getOtherability(data);
        if (res.code == 1) {
          state.editForm = res.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //保存--工作
      async saveData() {
        if(prevent===2){
          return false
        }
        let form = state.editForm;
        prevent=2;
        let res: any = await saveOtherability(form);
        prevent=1;
        if (res.code == 1) {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          emit("handleRtn");
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //获取驾照
      async getDrivingoptionsList() {
        let res: any = await getDrivingoptions("");
        state.DrivingoptionsList = res.data;
      },
      //获取语言能力等级
      async getComputeroptionsList() {
        let res: any = await getComputeroptions("");
        state.ComputeroptionsList = res.data;
      },
    };
    const fun = {
      cancel() {
        emit("handleRtn");
      },
      // 保存数据
      onSubmit() {
        //  验证必填项是否为空
        ruleForm.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData();
          } else {
            ElMessage({
              message: "保存失败，请检查必填项",
              type: "error",
            });
            return false;
          }
        });
      },
    };
    return { ...toRefs(state), ...fun, rules, ruleForm };
  },
});
</script>
<style lang="less">
.languageComponent {
  .li-pop {
    .el-form-item__label {
      text-align: left;
    }
    .el-form {
      .fr {
        margin-right: 0;
      }
    }
    .btn-end {
      padding: 40px 0 0 0;
      margin-bottom: 0;
    }
  }
}
</style>