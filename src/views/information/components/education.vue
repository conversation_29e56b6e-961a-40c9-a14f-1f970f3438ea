<template>
  <div class="educationComponent">
    <subheading @addItem="addItem" :hasbtn="hasbtn" title="教育经历"></subheading>
    <div class="list-education list-unify">
      <ul class="">
        <li v-if="!readOnly">
          <educationEdit @handleRtn="handleRtn" :resumeid="resumeid" :educationid="educationid"></educationEdit>
        </li>

        <li v-for="(item, index) in passData" :key="index" @mouseover="showShadow = index" @mouseout="showShadow = 900"
          :class="showShadow == index ? 'item bar-shadow-unify' : 'item'">
          <div class="li-readOnly" v-if="addshowindex !== index">
            <h1>
              {{ item.school }} <label>{{ item.timeRang }}</label>
            </h1>
            <div class="info">
              <span>{{ item.specialityName || item.specialityInputName }}</span>
              <el-divider direction="vertical" v-if="item.specialityName"></el-divider>
              <!-- <span>{{ item.specialityInputName }}</span>
              <el-divider
                direction="vertical"
                v-if="item.specialityInputName"
              ></el-divider> -->
              <span>{{ item.education }}</span>
              <el-divider direction="vertical" v-if="item.education"></el-divider>
              <span>{{ item.fullTimeFlag ? "全日制" : "非全日制" }}</span>
            </div>
            <p class="doc">
              {{ item.specialityDescription }}
            </p>
            <!-- 实践内容 -->
            <div class="Practice-content" v-if="item.practiceList.length > 0">
              <ul>
                <li v-for="(p, index) in item.practiceList" :key="index">
                  <h1>
                    {{ p.practiceName }}
                    <label>{{ p.parcticeTimeSpan }}</label>
                  </h1>
                  <p class="doc" v-if="p.practiceDescription">
                    {{ p.practiceDescription }}
                  </p>
                </li>
              </ul>
            </div>
            <!-- 按钮 -->
            <btnBox @deleteItem="deleteItem" @edit="edit" :passData="item" :ind="index"></btnBox>
          </div>
          <educationEdit @handleRtn="handleRtn" :resumeid="item.resumeId" :educationid="item.id" v-else></educationEdit>
        </li>
      </ul>
    </div>
    <!-- 为空时的状态 -->
    <emptyBox v-if="isempty" :resumeid="resumeid" mold="3" @handleBox="CopyDefaultesume" />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, computed, defineComponent } from "vue";
import subheading from "@/views/information/components/subheading.vue";
import educationEdit from "./educationEdit.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import btnBox from "./btnBox.vue";
import emptyBox from "./emptyBox.vue";
import { deleteEducation } from "../../../http/resumeApi";
export default defineComponent({
  components: { subheading, educationEdit, btnBox, emptyBox },
  emits: ["ReloadData"],
  props: {
    passData: {
      type: Array,
      default: () => [],
    },
    resumeid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const state = reactive({
      readOnly: true,
      showShadow: 900,
      addshowindex: 999, //打开的窗口
      educationid: 0,
      resumeid: props.resumeid,
      hasbtn: computed(() => {
        if (props.passData.length > 0) {
          return true;
        } else {
          return false;
        }
      }),
      isempty: computed(() => {
        if (props.passData.length > 0) {
          return false;
        } else if (props.passData.length < 0 || state.readOnly) {
          return true;
        } else {
          return false;
        }
      }),
    });
    const methods = {
      async deleteItem(p: any) {
        try {
          let form = {
            resumeid: p.resumeId,
            educationid: p.id,
          };
          let res: any = await deleteEducation(form);
          if (res.code == 1) {
            // 删除成功 重新加载数据
            ElMessage.success(res.message);
            emit("ReloadData");
          } else {
            ElMessage.error(res.message);
          }
        } catch (error) {
          console.error('Error during delete operation:', error);
          ElMessage.error('An error occurred while trying to delete the education record.');
        }
      },
    };
    const fun = {
      CopyDefaultesume(type: string) {
        if (type == "1") {
          emit("ReloadData");
        } else {
          state.readOnly = false;
        }
      },
    };
    //删除--操作
    const deleteItem = (p: any) => {
      ElMessageBox.confirm("确定删除该项教育经历吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          methods.deleteItem(p);
        })
        .catch(() => {
          return;
        });
    };
    const addItem = () => {
      state.resumeid = props.resumeid;
      state.educationid = 0;
      state.readOnly = false;
    };
    const edit = (data: any, ind: number) => {
      state.addshowindex = ind;
    };
    // 接收子组件传过来的值
    const handleRtn = (type: number) => {
      if (type == 1) {
        emit("ReloadData");
      }
      state.addshowindex = 900;
      state.readOnly = true;
    };
    return { ...toRefs(state), addItem, edit, handleRtn, deleteItem, ...fun };
  },
});
</script>
<style lang="less">
.educationComponent {
  
  .Practice-content {
    padding: 24px;
    background: #fafafa;
    margin: 24px 0 0 0;

    .doc {
      padding-top: 15px;
    }
  }

  .list-education {
    .item {
      .btn-pop {
        top: 20px;
      }
    }
  }
}
</style>