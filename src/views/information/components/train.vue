<template>
  <div class="trainComponent borderLine2">
    <subheading
      @addItem="addItem"
      :hasbtn="hasbtn"
      title="培训经历"
    ></subheading>
    <div class="list-project list-unify">
      <ul class="">
        <li v-if="!readOnly">
          <trainEdit
            @handleRtn="handleRtn"
            v-if="!readOnly"
            :resumeid="resumeid"
            :trainId="trainId"
          ></trainEdit>
        </li>

        <li
          v-for="(p, index) in passData"
          :key="p.trainId"
          @mouseover="showShadow = index"
          @mouseout="showShadow = 900"
          :class="showShadow == index ? 'item bar-shadow-unify' : 'item'"
        >
          <div class="li-readOnly" v-if="addshowindex !== index">
            <h1>
              {{ p.trainCourse }}
              <label>{{ p.trainBeginTime }} 至 {{ p.trainEndTime }}</label>
            </h1>
            <p class="tit">{{ p.trainInstitution }}</p>
            <p class="doc" v-safe-html.relaxed="p.trainingDescription"></p>

            <!-- <span
            @click="deleteItem(p.trainId, p.resumeId)"
            class="btn-del-pop btn-pop"
          >
            <i class="iconfont icon-shanchu"></i>删除</span
          >
          <span
            @click="edit(p.trainId, p.resumeId)"
            class="btn-edit-pop btn-pop"
          >
            <i class="iconfont icon-bianji1"></i>编辑</span
          > -->
           <!-- 按钮 -->
            <btnBox @deleteItem="deleteItem" @edit="edit" :passData="p" :ind="index"></btnBox>
          </div>
          <trainEdit
            @handleRtn="handleRtn"
            v-else
            :resumeid="p.resumeId"
            :trainId="p.trainId"
          ></trainEdit>
        </li>
      </ul>
    </div>
    <!-- 为空时的状态 -->
    <emptyBox
      v-if="isempty"
      :resumeid="resumeid"
      mold="4"
      @handleBox="CopyDefaultesume"
    />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, computed } from "vue";
import subheading from "@/views/information/components/subheading.vue";
import trainEdit from "./trainEdit.vue";
import emptyBox from "./emptyBox.vue";
import btnBox from "./btnBox.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { deleteTrain } from "../../../http/resumeApi";

export default {
  components: { subheading, trainEdit, emptyBox,btnBox },
  props: {
    passData: {
      type: Array,
      default: () => [],
    },
    resumeid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const state = reactive({
      readOnly: true,
      resumeid: props.resumeid,
      showShadow: 900,
      addshowindex:999,//打开的窗口
      trainId: 0 as number,

      hasbtn: computed(() => {
        if (props.passData.length > 0) {
          return true;
        } else {
          return false;
        }
      }),
      isempty: computed(() => {
        if (props.passData.length > 0) {
          return false;
        } else if (props.passData.length < 0 || state.readOnly) {
          return true;
        } else {
          return false;
        }
      }),
    });
    const methods = {
      async deleteItem(itemId: string, resumeId: string) {
        let form = {
          resumeid: resumeId,
          trainId: itemId,
        };
        let res: any = await deleteTrain(form);
        if (res.code == 1) {
          // 删除成功 重新加载数据
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          emit("ReloadData");
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
    };
    //添加培训经历
    const addItem = () => {
      state.resumeid = props.resumeid;
      state.trainId = 0;
      state.readOnly = false;
    };
     //编辑
    const edit = (data:any,ind:number) => {
      state.addshowindex=ind;
    };
    //删除--操作
    const deleteItem = (p:any) => {
      ElMessageBox.confirm("确定删除该培训经历吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          methods.deleteItem(p.trainId, p.resumeId);
        })
        .catch(() => {
          return;
        });
    };
    // 从默认简历复制
    const CopyDefaultesume = (type: string) => {
      if (type == "1") {
        emit("ReloadData");
      } else {
        state.readOnly = false;
      }
    };
     // 接收子组件传过来的值
    const handleRtn = (type:number) => {
      if(type==1){ 
          emit("ReloadData");
      }
      state.addshowindex=900;
      state.readOnly = true;
    };
    return {
      ...toRefs(state),
      addItem,
      edit,
      handleRtn,
      deleteItem,
      CopyDefaultesume,
    };
  },
};
</script>
<style lang="less" >
.trainComponent {
  .doc {
    padding-top: 15px;
    line-height: 26px;
  }
  .list-project {
    .item {
      .btn-pop {
        top: 20px;
      }
    }
    .tit {
      font-size: 14px;
      color: #666;
      padding-top: 8px;
    }
  }
}
</style>