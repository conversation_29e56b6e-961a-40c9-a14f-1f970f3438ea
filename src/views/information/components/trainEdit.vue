<template>
    <div class="edit-unify li-pop">
      <el-form
        :model="editForm"
        class="demo-form-inline"
        label-width="100px"
        :rules="rules"
        ref="ruleForm"
      >
        <el-form-item label="培训课程" class="w400"  prop="trainCourse">
          <el-input
            v-model="editForm.trainCourse"
            placeholder="请输入培训课程名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="培训机构"
          class="w400"
          prop="trainInstitution"
        >
          <el-input
            v-model="editForm.trainInstitution"
            placeholder="请输入机构名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="培训时间" required>
          <el-col :span="6">
            <el-form-item prop="trainBeginTime">
              <el-date-picker
                v-model="editForm.trainBeginTime"
                type="month"
                placeholder="请选择"
                class="w180"
                format="YYYY-MM"
                :disabled-date="disabledDate"
                value-format="YYYY年MM月"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col class="line" :span="3">至</el-col>
          <el-col :span="7">
            <el-form-item prop="trainEndTime">
              <el-date-picker
                v-model="editForm.trainEndTime"
                type="month"
                placeholder="请选择"
                class="w180"
                format="YYYY-MM"
                value-format="YYYY年MM月"
                :disabled-date="disabledDate"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-form-item>

        <el-form-item label="培训内容" style="width: 100%">
          <el-input
            v-model="editForm.trainingDescription"
            type="textarea"
            maxlength="500"
            show-word-limit
            clearable
            size="medium"
            class="JobDescription"
            placeholder="请描述您在此培训中所学的知识、获得哪些进步等"
          ></el-input>
        </el-form-item>
        <el-form-item class="btn-end" style="width: 100%">
          <el-button type="primary" @click="onSubmit" class="sub"
            >保存</el-button
          >
          <el-button @click="cancel" class="cel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  watch,
  onBeforeMount,
  Ref,
  ref,
} from "vue";

import { saveTrain, getTrain } from "../../../http/resumeApi";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
export default defineComponent({
  emits: ["handleRtn"],
  components: {},
  props: {
    resumeid: {
      type: Number,
      default: 0,
    },
    trainId: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const ruleForm: Ref<any> = ref(null);
    const state = reactive({
      readOnly: true,
      showsenior: false,
      editForm: {
        trainBeginTime: "",
        trainCourse: "",
        trainEndTime: "",
        resumeId: props.resumeid,
        trainId: 0,
        trainInstitution: "",
        trainingDescription: "",
      },

      disabledDate(time: any) {
        return time.getTime() > Date.now();
      },
    });
    onBeforeMount(() => {
      if (props.trainId > 0) {
        methods.getData();
      }
    });
    const validateTrainEndTime = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请选择结束时间"));
      } else if (value<state.editForm.trainBeginTime) {
        callback(
          new Error("结束时间不得早于开始时间")
        );
      } else {
        callback();
      }
    };
    let rules = {
      trainCourse: [
        {
          required: true,
          message: "请输入培训课程",
          trigger: "blur",
        },
        {
          min: 1,
          max: 25,
          message: "长度1-25个字符",
          trigger: "blur",
        },
      ],
      trainInstitution: [
        {
          required: true,
          message: "请输入培训机构",
          trigger: "blur",
        },
        {
          min: 1,
          max: 25,
          message: "长度1-25个字符",
          trigger: "blur",
        },
      ],
      trainBeginTime: [
        {
          required: true,
          message: "请选择开始时间",
          trigger: "change",
        },
      ],
      // trainEndTime: [
      //   {
      //     required: true,
      //     message: "请选择结束时间",
      //     trigger: "change",
      //   },
      // ],
      trainEndTime: [{ validator: validateTrainEndTime, trigger: "blur" }],
    };
    let prevent=1;
    const methods = {
      async getData() {
        let data = {
          resumeid: props.resumeid,
          trainId: props.trainId,
        };
        let res: any = await getTrain(data);
        if (res.code == 1) {
          state.editForm = res.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //保存--工作
      async saveData() {
        if(prevent===2){
          return false
        }
        let form = state.editForm;
        prevent=2;
        let res: any = await saveTrain(form);
        if (res.code == 1) {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          store.commit("editorShow", 0);
          emit("handleRtn", 1);
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
        prevent=1;
      },
    };
    const fun = {
      cancel() {
        store.commit("editorShow", 0);
        emit("handleRtn", 2);
      },
      // 保存数据
      onSubmit() {
        //  验证必填项是否为空
        ruleForm.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData();
          } else {
            ElMessage({
              message: "保存失败，请检查必填项",
              type: "error",
            });
            return false;
          }
        });
      },
    };
    return { ...toRefs(state), ...fun, rules, ruleForm };
  },
});
</script>




<style lang="less">
.trainComponent {
  .li-pop {
    .el-form-item__label {
      width: 90px;
      text-align: left;
    }
    .el-form {
      .fr {
        margin-right: 0;
      }
    }
    .JobDescription {
      .el-textarea__inner {
        height: 130px;
      }
    }
    .btn {
      color: #457ccf;
      background: #fff;
      border: 1px solid #457ccf;
      font-size: 14px;
    }
    .extraMessage {
      padding: 20px 0 0 0;
      position: relative;
      .btn-del {
        position: absolute;
        top: 40px;
        right: 0px;
        font-size: 13px;
        color: #457ccf;
        cursor: pointer;
        i {
          padding-right: 5px;
        }
      }
    }
    .extraMessage::before {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      right: -24px;
      top: 0;
      left: -20px;
      border-bottom: 1px solid #f2f2f2;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
    }
    .line {
      font-size: 15px;
      color: #666;
      padding: 0 12px 0 8px;
    }
  }
}
</style>