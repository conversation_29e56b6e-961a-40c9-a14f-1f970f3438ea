<template>
  <div class="certificateComponent borderLine2">
    <subheading
      @addItem="addItem"
      :hasbtn="hasbtn"
      title="证书职称"
    ></subheading>
    <div class="list-project list-unify">
      <ul class="">
        <li v-if="!readOnly">
          <certificateEdit
            @handleRtn="handleRtn"
            :resumeid="resumeid"
            :certId="certId"
          ></certificateEdit>
        </li>
        <li
          v-for="(p, index) in passData"
          :key="p.certId"
          @mouseover="showShadow = index"
          @mouseout="showShadow = 900"
          :class="showShadow == index ? 'item bar-shadow-unify' : 'item'"
        >
          <div class="li-readOnly" v-if="addshowindex !== index">
            <h1>
              {{ p.certName }} <label>{{ p.getTime }}</label>
            </h1>
            <div class="rank">
              <span>{{ p.certTypeTitle }}</span>
              <el-divider direction="vertical"></el-divider>
              <span>{{ p.certTypeLevelName }}</span>
            </div>
            <btnBox
              @deleteItem="deleteItem"
              @edit="edit"
              :passData="p"
              :ind="index"
            ></btnBox>
          </div>
          <certificateEdit
            @handleRtn="handleRtn"
            :resumeid="resumeid"
            :certId="p.certId"
            v-else
          ></certificateEdit>
        </li>
      </ul>
    </div>
    <!-- 为空时的状态 -->
    <emptyBox
      v-if="isempty"
      :resumeid="resumeid"
      mold="5"
      @handleBox="CopyDefaultesume"
    />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, computed } from "vue";
import subheading from "@/views/information/components/subheading.vue";
import certificateEdit from "./certificateEdit.vue";
import emptyBox from "./emptyBox.vue";
import btnBox from "./btnBox.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { deleteCertificate } from "../../../http/resumeApi";
export default {
  emits: ["ReloadData"],
  components: { subheading, certificateEdit, emptyBox, btnBox },
  props: {
    passData: {
      type: Array,
      default: () => [],
    },
     resumeid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const state = reactive({
      readOnly: true,
      resumeid: props.resumeid,
      showShadow: 900,
      addshowindex: 999, //打开的窗口
      certId: 0 as number,
      hasbtn: computed(() => {
        if (props.passData.length > 0) {
          return true;
        } else {
          return false;
        }
      }),
      isempty: computed(() => {
        if (props.passData.length > 0) {
          return false;
        } else if (props.passData.length < 0 || state.readOnly) {
          return true;
        } else {
          return false;
        }
      }),
    });
    const methods = {
      async deleteItem(p:any) {
        let form = {
          resumeid: p.resumeId,
          certId: p.certId,
        };
        let res: any = await deleteCertificate(form);
        if (res.code == 1) {
          // 删除成功 重新加载数据
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          emit("ReloadData");
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
    };
    //添加证书职称
    const addItem = () => {
      state.resumeid = props.resumeid;
      state.certId = 0;
      state.readOnly = false;
    };
    //编辑
    const edit = (data: any, ind: number) => {
      state.addshowindex = ind;
    };
    //删除--操作
    const deleteItem = (p:any) => {
      ElMessageBox.confirm("确定删除该项证书职称吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          methods.deleteItem(p);
        })
        .catch(() => {
          return;
        });
    };
    // 从默认简历复制
    const CopyDefaultesume = (type: string) => {
      if (type == "1") {
        emit("ReloadData");
      } else {
        state.readOnly = false;
      }
    };
    // 接收子组件传过来的值
    const handleRtn = (type: number) => {
      if (type == 1) {
        emit("ReloadData");
      }
      state.addshowindex = 900;
      state.readOnly = true;
    };
    return {
      ...toRefs(state),
      addItem,
      edit,
      handleRtn,
      deleteItem,
      CopyDefaultesume,
    };
  },
};
</script>
<style lang="less" >
.certificateComponent {
  .doc {
    padding-top: 15px;
    line-height: 26px;
  }
  .list-project {
    .item {
      .btn-pop {
        top: 20px;
      }
    }
    .rank {
      font-size: 14px;
      color: #666;
      padding-top: 10px;
    }
    .li-readOnly{
      width: 100%;
    }
  }
}
</style>