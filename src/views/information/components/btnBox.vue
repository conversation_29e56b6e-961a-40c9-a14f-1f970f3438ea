<template>
  <span @click="deleteItem" class="btn-del-pop btn-pop" v-if="hasdel">
    <i class="iconfont icon-shanchu"></i>删除</span>
  <span @click="edit" class="btn-edit-pop btn-pop">
    <i class="iconfont icon-bianji1"></i>编辑</span>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, computed } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";

export default defineComponent({
  emits: ["deleteItem", "edit"],
  props: {
    passData: {
      type: Object,
      default: {},
    },
    hasdel: {
      type: Boolean,
      default: true,
    },
    ind: {
      type: Number,
      default: "",
    },
  },
  setup(props: any, { emit }) {
    const store = useStore();
    const state = reactive({
  data: props.passData,
});
    
    const ElMessagebShow = () => {
      ElMessage({
        showClose: true,
        message: "请先提交打开的编辑窗口",
        type: "warning",
        duration: 1000,
      });
    };

    const fun = {
      deleteItem() {
        let aa = store.state.editorid;
        if (aa == 0) {
          emit("deleteItem", state.data, props.ind);
        } else {
          ElMessagebShow();
        }
      },
      edit() {
        let aa = store.state.editorid;
        if (aa == 0) {
          emit("edit", state.data, props.ind);
          store.commit("editorShow", 1);
        } else {
          ElMessagebShow();
        }
      },
    };
    return { ...toRefs(state), ...fun };
  },
});
</script>