<template>
  <div class="li-pop">
    <div class="edit-unify">
      <el-form
        :inline="true"
        :model="FormData"
        class="demo-form-inline"
        :rules="rules"
        ref="ruleForm"
      >
        <el-form-item
          label="期望职位"
          class="w280 CareerClass"
          prop="expectCareerName"
        >
          <el-input
            v-model="FormData.expectCareerName"
            placeholder="请输入或选择职位"
            suffix-icon="el-icon-arrow-down"
            readonly
            @focus="getexpectCareer()"
          ></el-input>
          <div class="el-form-item__error" v-if="istestE">
            <i class="iconfont icon-warn2" style="font-size: 12px"></i
            >请您将期望职位分类选择至第三层级
          </div>
          <div class="isPerfect_o" v-if="isExpectCareer">待完善</div>
          <seleCareer
            @confirm="confirmCareer"
            v-if="dialogVisible"
            :hideValue="expectCareer"
          ></seleCareer>
        </el-form-item>
        <el-form-item label="期望行业" class="w280 fr" prop="IndustryNames">
          <el-input
            v-model="FormData.IndustryNames"
            placeholder="请选择"
            suffix-icon="el-icon-arrow-down"
            readonly
            @focus="getexpectIndustry()"
          ></el-input>
          <seleIndustry
            :hideValue="expectIndustry"
            @confirm="confirmIndustry"
            :maxCount="3"
            :positionId="expectCareer"
            v-if="dialogVisible2"
          ></seleIndustry>
        </el-form-item>

        <el-form-item class="btn-end" style="width: 100%">
          <el-button type="primary" @click="onSubmit" class="sub"
            >保存</el-button
          >
          <el-button @click="cancel" class="cel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  onBeforeMount,
  computed,
  Ref,
  ref,
} from "vue";
import seleCareer from "@/components/seleCareer.vue";
import seleIndustry from "@/components/seleIndustry.vue";
import {
  getCareer,
  saveCareer,
  savecareerandindustry,
} from "../../../http/resumeApi";
import { ElMessage } from "element-plus";
export default defineComponent({
  components: { seleCareer, seleIndustry },
  emits: ["handleRtn"],
  props: {
    resumeid: {
      type: Number,
      default: 0,
    },
    type: {
      type: Number,
      default: 1,
    },
  },
  setup(props: any, { emit }: any) {
    const ruleForm: Ref<any> = ref(null);
    const state = reactive({
      editForm: {} as any,
      dialogVisible: false,
      dialogVisible2: false,
      FormData: {
        expectCareerName: "", //期望职位--名称
        IndustryNames: "",
      },
      expectCareer: "",
      expectIndustry: [], //期望行业--id
      expectIndustryNames: [],
      readOnly: true,
      errorA: false,
      errorB: false,
      isExpectCareer: false, //是否有待完善的期望职位
      istestE: false,
    });
    let rules = {
      expectCareerName: [
        {
          required: true,
          message: "请选择期望职位",
          trigger: "change",
        },
      ],
      IndustryNames: [
        {
          required: true,
          message: "请选择期望行业",
          trigger: "change",
        },
      ],
    };
    onBeforeMount(() => {
      //获取数据
      methods.getData();
    });
    const methods = {
      //获取数据
      async getData() {
        let data = {
          resumeid: props.resumeid,
        };
        let res: any = await getCareer(data);
        if (res.code == 1) {
          state.editForm = res.data;
          if (props.type == 1) {
            state.expectCareer = state.editForm.expectCareer1;
            state.FormData.expectCareerName = state.editForm.expectCareer1Name;
            state.expectIndustry = state.editForm.expectIndustry1;
            state.expectIndustryNames = state.editForm.expectIndustry1Names;
            state.isExpectCareer = state.editForm.isExpectCareer1
              ? true
              : false;
          }
          if (props.type == 2) {
            state.expectCareer = state.editForm.expectCareer2;
            state.FormData.expectCareerName = state.editForm.expectCareer2Name;
            state.expectIndustry = state.editForm.expectIndustry2;
            state.expectIndustryNames = state.editForm.expectIndustry2Names;
            state.isExpectCareer = state.editForm.isExpectCareer2
              ? true
              : false;
          }
          if (props.type == 3) {
            state.expectCareer = state.editForm.expectCareer3;
            state.FormData.expectCareerName = state.editForm.expectCareer3Name;
            state.expectIndustry = state.editForm.expectIndustry3;
            state.expectIndustryNames = state.editForm.expectIndustry3Names;
            state.isExpectCareer = state.editForm.isExpectCareer3
              ? true
              : false;
          }
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
        fun.IndustryNamesA();
      },
      //保存
      async saveData(num: number) {
        let form = {
          resumeId: props.resumeid,
          career1: '',
          career2: '',
          career3: '',
          industryId1: state.editForm.expectIndustry1.toString(),
          industryId2: state.editForm.expectIndustry2.toString(),
          industryId3: state.editForm.expectIndustry3.toString(),
        };

        if (num == 1) {
          form.career1 = state.expectCareer;
          form.industryId1 = state.expectIndustry.toString();
        }
        if (num == 2) {
          form.career2 = state.expectCareer;
          form.industryId2 = state.expectIndustry.toString();
        }
        if (num == 3) {
          form.career3 = state.expectCareer;
          form.industryId3 = state.expectIndustry.toString();
        }
        let res: any = await savecareerandindustry(form);

        if (res.code == 1) {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          emit("handleRtn", 1);
        } else {
          if (res.message == "请您将期望职位分类选择至第三层级") {
            state.istestE = true;
          } else {
            ElMessage({
              showClose: true,
              message: res.message,
              type: "error",
            });
            state.istestE = false;
          }
        }
      },

      //旧版保存
      // async saveData(num: number) {
      //   let form = {
      //     expectWorkPlaceIds: state.editForm.expectWorkPlaceIds,
      //     expectIndustry1: state.editForm.expectIndustry1,
      //     expectIndustry2: state.editForm.expectIndustry2,
      //     expectIndustry3: state.editForm.expectIndustry3,
      //     expectCareer1: state.editForm.expectCareer1,
      //     expectCareer2: state.editForm.expectCareer2,
      //     expectCareer3: state.editForm.expectCareer3,
      //     expectSalaryVisible: state.editForm.expectSalaryVisible,
      //     workStatusId: state.editForm.workStatusId,
      //     salary: state.editForm.salary,
      //     resumeId: props.resumeid,
      //   };
      //   if (num == 1) {
      //     form.expectCareer1 = state.expectCareer;
      //     form.expectIndustry1 = state.expectIndustry;
      //   }
      //   if (num == 2) {
      //     form.expectCareer2 = state.expectCareer;
      //     form.expectIndustry2 = state.expectIndustry;
      //   }
      //   if (num == 3) {
      //     form.expectCareer3 = state.expectCareer;
      //     form.expectIndustry3 = state.expectIndustry;
      //   }
      //   let res: any = await saveCareer(form);
 
      //   if (res.code == 1) {
      //     ElMessage({
      //       showClose: true,
      //       message: res.message,
      //       type: "success",
      //     });
      //     emit("handleRtn",1);
      //   } else {
      //     ElMessage({
      //       showClose: true,
      //       message: res.message,
      //       type: "error",
      //     });
      //   }
      // },
    };

    const fun = {
      cancel() {
        emit("handleRtn", 2);
        state.istestE = false;
      },
      onSubmit() {
        ruleForm.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData(props.type);
          } else {
            return false;
          }
        });
      },
      //获取职位字典--弹窗
      getexpectCareer() {
        state.dialogVisible = true;
      },
      //获取期望行业--弹窗
      getexpectIndustry() {
        state.dialogVisible2 = true;
      },
      //接收从子集传过来的数据---职位
      confirmCareer(p: any) {
        state.dialogVisible = false;
        if (p) {
          state.FormData.expectCareerName = p.keywordName;
          state.expectCareer = p.keywordID;
        }
      },
      //接收从子集传过来的数据
      confirmIndustry(p: any) {
        state.dialogVisible2 = false;
        if (!p) {
          return false;
        }
        state.expectIndustry = p.keywordID;
        state.expectIndustryNames = p.map((i: any) => i.keywordName);
        // state.expectIndustry = p.map((i: any) => i.keywordID);
        state.expectIndustry = p ? p.map((i: any) => i.keywordID) : [];
        fun.IndustryNamesA();
      },
      //整理期望行业 为string
      IndustryNamesA() {
        let p = "";
        state.expectIndustryNames.forEach((i: any, index) => {
          if (index == 0) {
            p = i;
          }
          if (i && index != 0) {
            p = p + "," + i;
          }
        });
        state.FormData.IndustryNames = p;
      },
    };
    return { ...toRefs(state), ...fun, rules, ruleForm };
  },
});
</script>




<style lang="less">
.Resume-editor-main {
  .expectPositions {
    .el-form {
      .fr {
        margin-right: 0;
      }
    }
  }
  // .li-pop {
  //   padding: 24px 24px 0 24px;
  //   .edit-unify {
  //     padding: 24px 24px 0 24px;
  //   }
  //   .btn-end {
  //     padding: 24px 0 0 0;
  //   }

  // }
}
</style>