<template>
  <el-form
    :model="editForm"
    label-width="120px"
    label-position="left"
    :rules="rules"
    ref="ruleForm"
  >
    <el-form-item label="姓名" prop="name" class="w240">
      <el-input v-model="editForm.name"></el-input>
    </el-form-item>

    <el-form-item label="性别" prop="sex" class="radio">
      <el-radio-group v-model="sex">
        <el-radio-button
          label="男"
          name="1"
          @click="editForm.sex = false"
        ></el-radio-button>
        <el-radio-button
          label="女"
          name
          @click="editForm.sex = true"
        ></el-radio-button>
      </el-radio-group>
    </el-form-item>

    <el-form-item label="出生年月" prop="brithday">
      <el-date-picker
        placeholder="选择时间"
        v-model="editForm.brithday"
        class="w240"
        type="month"
        value-format="YYYY-MM"
        :clearable="false"
        :disabled-date="disabledDate2"
        :default-value="[2000]"
      ></el-date-picker>
    </el-form-item>

    <el-form-item label="工作经验" prop="isHaveWork" class="radio">
      <el-radio-group v-model="isHaveWork">
        <el-radio-button
          label="有"
          @click="editForm.isHaveWork = true"
        ></el-radio-button>
        <el-radio-button
          label="无"
          @click="editForm.isHaveWork = false"
        ></el-radio-button>
      </el-radio-group>
    </el-form-item>

    <!-- <el-form-item label="民族">
      <el-select
        v-model="editForm.nationName"
        placeholder="请选择民族"
        class="w400"
        @focus="getNation()"
      >
        <el-option
          v-for="(p, index) in nationoptions"
          :key="index"
          :label="p.keywordName"
          :value="p.keywordID"
          @click="editForm.nation = p.keywordID"
        ></el-option>
      </el-select>
    </el-form-item>-->
    <el-form-item
      label="参加工作时间"
      class="w400"
      v-if="editForm.isHaveWork"
      prop="attendWorkYear"
    >
      <el-date-picker
        placeholder="选择参加工作时间"
        v-model="editForm.attendWorkYear"
        value-format="YYYY-MM"
        style="width: 100%"
        :disabled-date="disabledDate"
        type="month"
      ></el-date-picker>
    </el-form-item>
    <el-form-item label="籍贯" class="w400" prop="domicileName">
      <el-input
        v-model="editForm.domicileName"
        placeholder="请选择籍贯"
        suffix-icon="el-icon-arrow-down"
        @focus="SelectDomicileNameCity()"
        clearable
      ></el-input>
      <seleICity
        @confirmCity="confirmDomicileNameCity"
        v-if="dialogVisibleA"
        title="籍贯"
        :maxCount="1"
        :hideValue="[editForm.domicile]"
      ></seleICity>
    </el-form-item>
    <el-form-item label="目前所在地" class="w400" prop="residencyName">
      <el-input
        v-model="editForm.residencyName"
        placeholder="请选择目前所在地"
        suffix-icon="el-icon-arrow-down"
        @focus="SelectCity()"
        :readonly="true"
      ></el-input>
      <seleICity
        @confirmCity="confirmCity"
        v-if="dialogVisibleB"
        title="目前所在地"
        :maxCount="1"
        :hideValue="[editForm.residency]"
      ></seleICity>
    </el-form-item>
    <el-form-item label="政治面貌" class="w400">
      <el-select
        v-model="editForm.politicalStatusName"
        placeholder="请选择政治面貌"
        @focus="Political"
        clearable
        @change="editForm.politicalStatus = $event"
      >
        <el-option
          v-for="(p, i) in PoliticalList"
          :key="i"
          :label="p.keywordName"
          :value="p.keywordID"
          @click="editForm.politicalStatus = p.keywordID"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item
      label="手机号码"
      class="w240 validate validate-phone"
      prop="firstContact"
    >
      <el-input v-model="editForm.firstContact" :readonly="true" type="text">
        <template #suffix>
          <span class="red" v-if="!editForm.phoneIsAuth">未验证</span>
          <span class="green" v-if="editForm.phoneIsAuth">已验证</span>
          <label class="revise" @click="testPhone(1)">修改</label>
          <label class="test" v-if="!editForm.phoneIsAuth" @click="testPhone(2)"
            >验证</label
          >
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label="联系邮箱" class="w400">
      <el-input v-model="editForm.email" type="text" clearable>
        <!-- <template #suffix>
          <span class="red" v-if="!editForm.emailIsAuth">未验证</span>
          <span class="green" v-if="editForm.emailIsAuth">未验证</span>
          <label class="revise" @click="reviseEmail">修改</label>
          <label class="txt">验证后可用邮箱登陆、找回登陆密码。</label>
          <label class="test" @click="testEmail">验证</label>
        </template>-->
      </el-input>
    </el-form-item>
    <el-form-item label="QQ号" class="w400">
      <el-input
        v-model="editForm.imContract"
        placeholder="请输入QQ号"
        clearable
        maxlength="16"
      ></el-input>
    </el-form-item>
    <el-form-item label="个人主页" class="w400">
      <el-input
        v-model="editForm.homePage"
        clearable
        placeholder="请输入个人主页网址"
      ></el-input>
    </el-form-item>
    <p class="Advantage-tags">
      优势标签
      <span>
        (
        <label class="blue">{{ tagsLength }}</label
        >/10 )
      </span>
    </p>
    <div class="tag-box autoBox">
      <el-tag
        v-for="(tag, index) in tags.auto"
        :key="index"
        type="info"
        @click="showTags(tag)"
        :class="{ showbg: tag.isVisible }"
        >{{ tag.tagName }}</el-tag
      >
    </div>
    <el-form-item label-width="0" class="add-inp">
      <el-input
        v-model="addtag"
        placeholder="请输入20个字以内的标签"
        maxlength="20"
        clearable
      ></el-input>
      <el-button @click.prevent="addDomain(addtag)" class="btn-blue"
        >添加</el-button
      >
    </el-form-item>
    <div class="tag-box">
      <el-tag
        v-for="(tag, index) in tags.custom"
        :key="tag.tagId"
        closable
        type="info"
        @click="showTags(tag)"
        :class="{ showbg: tag.isVisible }"
        @close="handleClose(tag.tagId, index)"
        >{{ tag.tagName }}</el-tag
      >
    </div>
    <el-form-item class="btn-end">
      <el-button type="primary" @click="onSubmit" class="sub">保存</el-button>
      <el-button @click="cancel" class="cel">取消</el-button>
    </el-form-item>
    <div class="Photo-mgt" @click="showphoto = true">
      <el-image
        :src="editForm.photo+ '?time=' + new Date().getTime()"
        fit="cover"
        v-if="editForm.photo"
      ></el-image>
      <p class="tit">照片管理</p>
    </div>
    <!-- 修改头像弹窗 -->
    <ModifyPhoto @confirmPhotograph="confirmPhotograph" v-if="showphoto" />
    <!-- 修改手机弹窗 -->
    <Changephone
      @revisePhone="revisePhone"
      :phone="editForm.firstContact"
      v-if="dialogVisiblePhone"
      :phoneIsAuth="ifrevise"
    />
  </el-form>
</template>
<script lang="ts">
import {
  reactive,
  toRefs,
  watch,
  ref,
  computed,
  defineComponent,
  onMounted,
  Ref,
} from "vue";
import {
  saveBaseinfo,
  saveResumetags,
  removeResumetags,
  baseinfo,
  getResumetags,
} from "../../../http/resumeApi";
import { useStore } from "vuex";
import { getNationoptions, getPoliticalstatus } from "../../../http/dictionary";
import seleICity from "@/components/seleCity.vue";
import ModifyPhoto from "./ModifyPhoto.vue";
import Changephone from "./changephone.vue";
import { ElMessage } from "element-plus";
export default defineComponent({
  emits: ["handleRtn"],
  components: { seleICity, ModifyPhoto, Changephone },
  props: {
    resumeid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }: any) {
    const ruleForm: Ref<any> = ref(null);
    const store = useStore();
    const state: any = reactive({
      editForm: {},
      sex: "",
      isHaveWork: "",
      nationoptions: [], //民族
      nationalitylist: "", //国籍
      domicileV: [], //籍贯
      PoliticalList: [], //政治面貌
      tags: [],
      addtag: "",
      tagsLength: 0,
      dialogVisibleA: false,
      dialogVisibleB: false,
      showphoto: false,
      dialogVisiblePhone: false,
      isChange: false,
      ifrevise:true,//是否修改手机号还是验证手机
      disabledDate(time: any) {
        return time.getTime() > Date.now();
      },
      disabledDate2(time: any) {
        return time.getTime() > Date.now()-3600 * 1000 * 24 * 365*16;
      },
    });
    const validateBrithday = (rule: any, value: any, callback: any) => {
      if (value === ""||value === "0") {
        callback(new Error("请选择出生年月"));
      } else if (Date.now()-value.getTime<504576000000){
        callback(new Error("请选择的出生年月不得小于16岁"));
      }else{
        callback();
      }
    };
    const validateWork = (rule: any, value: any, callback: any) => {
      if (value === ""||value === "0") {
        callback(new Error("请选择参加工作时间"));
      } else if (value<state.editForm.brithday){
        callback(new Error("参加工作时间不得早于出生时间"));
      }else{
        callback();
      }
    };
    let rules = {
      name: [
        {
          required: true,
          message: "请填写姓名",
          trigger: "blur",
        },
      ],
      sex: [
        {
          required: true,
          message: "请选择性别",
          trigger: "change",
        },
      ],
      brithday:[{ validator: validateBrithday, trigger: "blur" }],
      attendWorkYear:[{ validator: validateWork, trigger: "blur" }],
      domicileName: [
        {
          required: true,
          message: "请选择籍贯",
          trigger: "change",
        },
      ],
      residencyName: [
        {
          required: true,
          message: "请选择目前所在地",
          trigger: "change",
        },
      ],
      isHaveWork: [
        {
          required: true,
          message: "请选择工作经验",
          trigger: "change",
        },
      ],
      firstContact: [
        {
          required: true,
          message: "请填写手机号码",
          trigger: "blur",
        },
      ],
    };
    onMounted(() => {
      methods.getData(props.resumeid);
      methods.getDataResumetags(props.resumeid);
    });
    watch(
      () => state.editForm,
      (newValue, oldValue) => {
        state.sex = state.editForm.sex ? ref("女") : ref("男");
        state.isHaveWork = state.editForm.isHaveWork ? ref("有") : ref("无");
      }
    );
    watch(
      () => state.editForm.domicileName,
      (newValue, oldValue) => {
        if (!state.editForm.domicileName) {
          state.editForm.domicile = "0";
        }
      }
    );
    watch(
      () => state.tags,
      (newValue, oldValue) => {
        state.tagsLength = computed(() => {
          let num: number = 0;
          state.tags.auto.forEach((i: any) => {
            if (i.isVisible) {
              num++;
            }
          });
          state.tags.custom.forEach((i: any) => {
            if (i.isVisible) {
              num++;
            }
          });
          return num;
        });
      }
    );
    let prevent = 1;
    const methods = {
      //获取基本信息
      async getData(id: string) {
        let res: any = await baseinfo(id, "");
        if (res.code == 1) {
          state.editForm = res.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
        }
      },
      //获取优势标签列表
      async getDataResumetags(id: string) {
        let res: any = await getResumetags(id, "");
        if (res.code == 1) {
          state.tags = res.data;
        } else {
        }
      },
      //  保存
      async saveData() {
        if (prevent === 2) {
          return false;
        }
        let form = {
          name: state.editForm.name,
          sex: state.editForm.sex,
          nation: state.editForm.nation,
          attendWorkYear: state.editForm.attendWorkYear,
          brithday: state.editForm.brithday,
          politicalStatus: state.editForm.politicalStatus,
          domicile: state.editForm.domicile,
          resumeId: props.resumeid,
          imContract: state.editForm.imContract,
          homePage: state.editForm.homePage,
          isHaveWork: state.editForm.isHaveWork,
          residency: state.editForm.residency,
          tags: state.tags,
          email: state.editForm.email,
        };
        prevent = 2;
        let res: any = await saveBaseinfo(form);
        prevent = 1;
        if (res.code == 1) {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          emit("handleRtn", 1);
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //保存优势标签
      async saveStags(name: string) {
        let prevent = 1;
        let data = {
          resumeId: props.resumeid,
          tagName: name,
        };
        let res: any = await saveResumetags(data);
        if (res.code == 1) {
          state.addtag = "";
          let isVisible = state.tagsLength < 10 ? true : false;
          let arr = {
            isVisible: isVisible,
            tagId: res.data.tagId,
            tagName: name,
          };
          state.tags.custom.push(arr);
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //删除优势标签
      async delStags(id: string, index: string) {
        let data = {
          resumeId: props.resumeid,
          tagId: id,
        };
        let res: any = await removeResumetags(data);
        if (res.code == 1) {
          state.tags.custom.splice(index, 1);
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      // 获取民族列表
      async getNation() {
        let res: any = await getNationoptions("");
        state.nationoptions = res.data;
      },
      // 政治面貌
      async Political() {
        let res: any = await getPoliticalstatus("");
        state.PoliticalList = res.data;
      },
    };
    const fun = {
      // 取消
      cancel() {
        emit("handleRtn", 1);
        // if (state.isChange) {
        //   //数据有改变
        // } else {
        //   emit("handleRtn", 2);
        // }
      },
      onSubmit() {
        if (!state.editForm.isHaveWork) {
          state.editForm.attendWorkYear = "";
        }
        ruleForm.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData();
          } else {
            ElMessage.warning("请填完必选项");
            return false;
          }
        });
      },
      //民族
      getNation() {
        if (state.nationoptions.length <= 0) {
          methods.getNation();
        }
      },
      //政治面貌
      Political() {
        if (state.PoliticalList.length <= 0) {
          methods.Political();
        }
      },
      //选择籍贯
      SelectDomicileNameCity() {
        state.dialogVisibleA = true;
      },
      //接收子级传过来的籍贯 --像子级传值
      confirmDomicileNameCity(arr: any) {
        state.dialogVisibleA = false;
        if (arr.length > 0) {
          state.editForm.domicileName = arr[0].keywordName;
          state.editForm.domicile = arr[0].keywordID;
        }
      },
      //选择城市
      SelectCity() {
        state.dialogVisibleB = true;
      },
      //接收子级传过来的目前所在地 --像子级传值
      confirmCity(arr: any) {
        state.dialogVisibleB = false;
        if (!arr) return false;
        state.editForm.residencyName = arr[0].keywordName;
        state.editForm.residency = arr[0].keywordID;
      },
      //显示标签
      showTags(tag: object | any) {
        if (state.tagsLength >= 10 && !tag.isVisible) {
          ElMessage({
            showClose: true,
            message: "展示标签不能超过10个",
            type: "warning",
          });
          return false;
        }
        tag.isVisible = tag.isVisible ? false : true;
      },
      //删除标签
      handleClose(id: string, index: string) {
        methods.delStags(id, index);
      },
      //添加 优势标签
      addDomain(tags: string) {
        let a = 1;
        if (state.tags.custom.length >= 10 && a == 1) {
          a = 2;
          ElMessage({
            showClose: true,
            message: "自定义标签不能超过10个",
            type: "warning",
          });
          return false;
        }
        state.tags.custom.forEach((i: any) => {
          if (i.tagName == tags) {
            ElMessage({
              showClose: true,
              message: "该标签已存在",
              type: "warning",
            });
            a = 2;
            return false;
          }
        });
        if(a != 2){
          methods.saveStags(tags);
          a = 1;
        }
        
      },
      // 修改手机号
      revisePhone(phone: any) {
        state.dialogVisiblePhone = false;
        if (phone) {
          state.editForm.firstContact = phone;
          state.isChange = true;
          state.editForm.phoneIsAuth=true
        }
      },
      // 验证/修改手机号
      testPhone(type:number) {
        state.dialogVisiblePhone = true;
        if(type==1){
          state.ifrevise=true
        }else{
          state.ifrevise=false
        }
      },
      // 修改邮箱
      reviseEmail() {},
      // 验证邮箱
      testEmail() {},

      confirmPhotograph(type: number, phoneImg: string) {
        if (type == 1) {
          //头像有改变
          state.isChange = true;
          state.showphoto = false;
          state.editForm.photo = phoneImg+ "?t=" + Math.random();
        }else{
          state.showphoto = false;
        }
      },
    };
    return {
      ...toRefs(state),
      ...fun,
      rules,
      ruleForm,
    };
  },
});
</script>
<style lang="less">
.base-edit {
  position: relative;
  .Advantage-tags {
    font-size: 18px;
    padding: 40px 0 20px 0;
    color: #333;
    span {
      font-size: 14px;
      color: #999;
      label {
        color: #457ccf;
      }
    }
  }
  .add-inp {
    .el-input {
      width: 480px;
    }
    .el-input,
    .el-button {
      float: left;
    }
    .btn-blue {
      height: 38px;
      line-height: 38px;
      width: 76px;
      margin-left: 12px;
      padding: 0 0;
    }
  }
  .tag-box {
    padding-bottom: 20px;
    .el-tag {
      background: #fff;
      font-size: 14px;
      color: #666;
      border-radius: 20px;
      margin: 5px 12px 0 0;
      cursor: pointer;
    }
    .showbg {
      background: #f2f7ff;
      color: #457ccf;
      border: 1px solid #85b5fb;
    }
  }

  .Photo-mgt {
    width: 120px;
    height: 120px;
    position: absolute;
    top: 24px;
    right: 24px;
    z-index: 9;
    background: url("@/assets/img/moren.png") no-repeat center;
    background-size: 100%;
    p.tit {
      color: #fff;
      font-size: 16px;
      position: absolute;
      top: 50px;
      right: 26px;
      cursor: pointer;
    }
    .el-image {
      width: 120px;
      height: 120px;
      cursor: pointer;
      background: #000;
    }
  }
  .el-radio {
    width: 60px;
    height: 36px;
    text-align: center;
    border: 1px solid #f2f2f2;
    border-radius: 2px;
    background: #fff;
    margin-right: 12px;
    span.el-radio__input {
      opacity: 0;
    }
  }
  label.is-checked {
    background: #f2f7ff;
    border: 1px solid #5f9efc;
    span.el-radio__label {
    }
  }
  .validate {
    .el-input__suffix {
      right: unset;
      span {
        font-size: 14px;
      }
      label.revise {
        padding-left: 20px;
        text-decoration: underline;
      }
      label.test {
        padding-left: 10px;
        text-decoration: underline;
      }
      label {
        color: #457ccf;

        cursor: pointer;
      }
      span.red {
        color: #fc5c5b;
      }
      span.green {
        color: #07c160;
      }
    }
  }
  .validate-phone {
    .el-input__suffix {
      left: 185px;
    }
  }
  .validate-email {
    .el-input__suffix {
      left: 345px !important;
      label.txt {
        padding-left: 10px;
        color: #999;
      }
    }
  }
  .radio {
    .el-radio-button {
      margin-right: 12px;
      background: none;
      border: none;
    }
    .el-radio-button .el-radio-button__inner {
      background: #fff;
      border: 1px solid #f2f2f2;
      color: #999999;
      width: 62px;
      height: 38px;
      border-radius: 2px;
    }

    .el-radio-button__original-radio:checked + .el-radio-button__inner {
      background: #f2f7ff;
      border: 1px solid #5f9efc;
      color: #457ccf;
      font-size: 14px;
    }
    .el-radio-button:last-child {
      .el-radio-button__inner {
        border-left: none;
      }
    }
  }
}
</style>