<template>
  <div class="progress-box bg-white">
    <div class="circle">
      <el-progress
        type="circle"
        :percentage="wangzhengdu"
        status="exception"
        :width="150"
        :stroke-width="15"
        stroke-linecap="butt"
        color="#3486FF"
      >
        <template #default="{ percentage }">
          <p class="percentage-value">
            {{ percentage }}
            <i>%</i>
          </p>
          <span class="percentage-label">完整度</span>
        </template>
      </el-progress>
    </div>
    <el-button type="primary" @click="preview" class="btn-go">预览简历</el-button>
    <div class="Download" @click="showPop = true">
      <p class="tit">
        下载简历
        <i class="new">NEW</i>
      </p>
      <span>精美模板下载</span>
    </div>
    <DownloadTemplate v-if="showPop" :resumeid="resumeid" :resumeName="resumeName" @ClosePop="ClosePop"></DownloadTemplate>
  </div>
  <div class="link-list bg-white">
    <h1 class="tit">必填项</h1>
    <ul v-if="talent==2">
      <li v-for="(item, index) in menuList1" :key="index" class="clearfix">
        <a :href="'#' + item.id">{{ item.name }}</a>
        <i class="iconfont icon-tick2" v-if="item.finish"></i>
        <label class="improved orange" v-else>待完善</label>
      </li>
    </ul>
    <ul v-else>
      <li v-for="(item, index) in menuList1_1" :key="index" class="clearfix">
        <a :href="'#' + item.id">{{ item.name }}</a>
        <i class="iconfont icon-tick2" v-if="item.finish"></i>
        <label class="improved orange" v-else>待完善</label>
      </li>
    </ul>
  </div>

  <div class="link-list link-list2 bg-white">
    <ul v-if="talent==2">
      <li v-for="(item, index) in menuList2" :key="index" class="clearfix">
        <a :href="'#' + item.id">{{ item.name }}</a>
        <i class="iconfont icon-tick2" v-if="item.finish"></i>
        <label class="improved gray" v-else>待完善</label>
      </li>
    </ul>
    <ul v-else>
      <li v-for="(item, index) in menuList2_1" :key="index" class="clearfix">
        <a :href="'#' + item.id">{{ item.name }}</a>
        <i class="iconfont icon-tick2" v-if="item.finish"></i>
        <label class="improved gray" v-else>待完善</label>
      </li>
    </ul>
  </div>
  <MymallTemplate :drawer="teDrawer" />
</template>

<script lang="ts">
import { reactive, toRefs, computed, watch, defineComponent } from "vue";
import { useRoute, useRouter } from "vue-router";
import MymallTemplate from "@/components/MymallTemplate.vue";
import DownloadTemplate from "@/components/DownloadTemplate.vue";
export default defineComponent({
  components: { MymallTemplate, DownloadTemplate },
  props: {
    passData: {
      type: Object,
      default() {
        return {
          baseInfo: 1,
          career: 1,
          cert: 1,
          description: 1,
          education: 1,
          project: 1,
          technology: 1,
          totalScore: 0,
          train: 1,
          work: 1,
        };
      },
    },
    resumeName: {
      type: String,
      default: '简历名称'
    },
    talent:{
      type: Number,
      default: 0
    }
  },
  setup(props: any, { emit }: any) {
    const router = useRouter();
    const route = useRoute();
    const state = reactive({
      resumeid: parseInt(`${route.params.id}`),
      percentage: 60,
      menuList1: [
        {
          name: "个人信息",
          id: "baseInfo",
          finish: computed(() => {
            return props.passData.baseInfo;
          }),
        },
        {
          name: "求职意向",
          id: "careerObject",
          finish: computed(() => {
            return props.passData.career;
          }),
        },
        {
          name: "工作经历",
          id: "work",
          finish: computed(() => {
            return props.passData.work;
          }),
        },
        {
          name: "教育经历",
          id: "education",
          finish: computed(() => {
            return props.passData.education;
          }),
        },
      ],
      menuList1_1: [
        {
          name: "个人信息",
          id: "baseInfo",
          finish: computed(() => {
            return props.passData.baseInfo;
          }),
        },
        {
          name: "求职意向",
          id: "careerObject",
          finish: computed(() => {
            return props.passData.career;
          }),
        },
        {
          name: "教育经历",
          id: "education",
          finish: computed(() => {
            return props.passData.education;
          }),
        },
      ],
      menuList2: [
        {
          name: "项目经历",
          id: "project",
          finish: computed(() => {
            return props.passData.project;
          }),
        },
        {
          name: "培训经历",
          id: "train",
          finish: computed(() => {
            return props.passData.train;
          }),
        },
         {
          name: "技能能力",
          id: "technical",
          finish: computed(() => {
            return props.passData.technology;
          }),
        },
        {
          name: "证书职称",
          id: "certificate",
          finish: computed(() => {
            return props.passData.cert;
          }),
        },
       {
          name: "语言能力",
          id: "language",
          finish: computed(() => {
            return props.passData.language;
          }),
        },
        {
          name: "个人描述",
          id: "description",
          finish: computed(() => {
            return props.passData.description;
          }),
        },
        {
          name: "其他技能",
          id: "otherSkills ",
          finish: computed(() => {
            return 1; //不做处理
          }),
        },
        
      ],
      menuList2_1: [
        {
          name: "工作经历",
          id: "work",
          finish: computed(() => {
            return props.passData.work;
          }),
        },
        {
          name: "项目经历",
          id: "project",
          finish: computed(() => {
            return props.passData.project;
          }),
        },
        {
          name: "培训经历",
          id: "train",
          finish: computed(() => {
            return props.passData.train;
          }),
        },
         {
          name: "技能能力",
          id: "technical",
          finish: computed(() => {
            return props.passData.technology;
          }),
        },
        {
          name: "证书职称",
          id: "certificate",
          finish: computed(() => {
            return props.passData.cert;
          }),
        },
       {
          name: "语言能力",
          id: "language",
          finish: computed(() => {
            return props.passData.language;
          }),
        },
        {
          name: "个人描述",
          id: "description",
          finish: computed(() => {
            return props.passData.description;
          }),
        },
        {
          name: "其他技能",
          id: "otherSkills ",
          finish: computed(() => {
            return 1; //不做处理
          }),
        },
        
      ],
      wangzhengdu: computed(() => {
        return props.passData.totalScore;
      }),
      showPop: false,
      type: 0,
      teDrawer: { value: false },
    });
    const fun = {
      preview() {
        router.push({
          path: `/preview/${state.resumeid}`,
        });
      },
      ClosePop(num: Number) {
        if (num == 1) {
          state.showPop = false
        } else {
          state.teDrawer.value = true;
        }
      }
    };

    return { ...toRefs(state), ...fun };
  },
});
</script>
<style lang="less">
.Resume-editor-main {
  .progress-box {
    padding: 20px;
    .circle {
      text-align: center;
      padding: 24px;
    }
    .percentage-value {
      font-size: 32px;
      color: #333;
      font-weight: bold;
      i {
        font-size: 16px;
      }
    }
    .percentage-label {
      font-size: 16px;
      color: #666;
    }
    .btn-go {
      width: 100%;
      background: #5f9efc;
      font-size: 14px;
      border-radius: 2px;
      line-height: 31px;
      height: 32px;
      padding: 0 0 0 0;
    }
    .Download {
      background: #f2f7ff;
      height: 67px;
      padding: 0 14px;
      margin: 8px 0 0 0;
      border-radius: 2px;
      cursor: pointer;
      p.tit {
        font-size: 14px;
        color: #333;
        font-weight: bold;
        padding: 14px 0 0 0px;
      }
      i.new {
        font-size: 12px;
        color: #fc5c5b;

        font-family: monospace;
      }
      span {
        font-size: 12px;
        color: #666;
      }
      .el-dialog__headerbtn {
        top: 10px;
        right: 10px;
        i.el-dialog__close {
          font-size: 20px;
        }
      }
    }
  }
  .side-right {
    .link-list {
      margin: 12px 0 0 0;
      .tit {
        height: 48px;
        line-height: 48px;
        padding: 0 24px;
        font-size: 16px;
        color: #333;
        border-bottom: 1px solid #f2f2f2;
      }
      ul {
        padding: 10px 0;
      }
      li {
        padding: 12px 32px;

        a {
          font-size: 14px;
          color: #666;
          float: left;
        }

        i {
          color: #5f9efc;
          float: right;
        }
        label {
          font-size: 12px;
          float: right;
        }
      }
    }
    .link-list2 {
      margin-top: 12px;
      label {
        color: #bbbbbb;
      }
    }
    .Download {
      .el-dialog__title {
        font-size: 16px;
        color: #333;
      }
      .box {
        width: 160px;
        height: 100px;
        border: 1px solid #f3f2f2;
        text-align: center;
        border-radius: 4px;
        cursor: pointer;
        .el-image {
          padding: 16px 0 0 0;
        }
        p.geshi {
          font-size: 16px;
          color: #3333;
        }
      }
      .sel {
        border: 1px solid #457ccf;
        background: #f5faff;
      }
      .el-dialog__footer {
        height: 67px;
        background: #2b7cff url("@/assets/img/jlmb_bg.png") no-repeat top;
        background-size: 100%;
        padding: 0 0 0 0;
      }
      .dialog-footer {
        width: 160px;
        height: 35px;
        padding: 0 0;
        margin: 17px 0 0 210px;
        i {
          color: #fff;
        }
        span {
          color: #fff;
          font-size: 13px;
        }
      }
    }
  }
}
</style>