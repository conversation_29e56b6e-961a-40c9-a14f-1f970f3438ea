<template>
  <div class="languageComponent borderLine2">
    <subheading
      @addItem="addItem"
      :hasbtn="hasbtn"
      title="语言技能"
    ></subheading>
    <div class="list-project list-unify">
      <ul class="">
        <li v-if="!readOnly">
          <languageEdit
            @handleRtn="handleRtn"
            v-if="!readOnly"
            :resumeid="resumeid"
            :languageid="languageid"
          ></languageEdit>
        </li>
        <li
          v-for="(p, index) in passData"
          :key="p.langId"
          @mouseover="showShadow = index"
          @mouseout="showShadow = 900"
          :class="showShadow == index ? 'item bar-shadow-unify' : 'item'"
        >
          <div class="li-readOnly" v-if="addshowindex !== index">
            <h1>{{ p.langName }}</h1>
            <div class="rank">
              <el-row>
                <el-col :span="8">
                  <span>综合能力</span>
                  <label>{{ p.langLevel }}</label>
                </el-col>
                <el-col :span="8">
                  <span>听说能力</span>
                  <label>{{ p.lsLevel }}</label>
                </el-col>
                <el-col :span="8">
                  <span>读写能力</span>
                  <label>{{ p.rwLevel }}</label>
                </el-col>
              </el-row>
            </div>
            <!-- 按钮 -->
            <btnBox @deleteItem="deleteItem" @edit="edit" :passData="p" :ind="index"></btnBox>
          </div>
          <languageEdit
            @handleRtn="handleRtn"
            v-else
            :resumeid="resumeid"
            :languageid="p.listId"
          ></languageEdit>
        </li>
      </ul>
    </div>
    <!-- 为空时的状态 -->
    <emptyBox
      v-if="isempty"
      :resumeid="resumeid"
      mold="8"
      @handleBox="CopyDefaultesume"
    />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, computed } from "vue";
import btnBox from "./btnBox.vue";
import subheading from "@/views/information/components/subheading.vue";
import languageEdit from "./languageEdit.vue";
import emptyBox from "./emptyBox.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { deleteLanguage } from "../../../http/resumeApi";

export default {
  components: { subheading, languageEdit, emptyBox,btnBox },
  emits: ["ReloadData"],
  props: {
    passData: {
      type: Array,
      default: () => [],
    },
    resumeid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const state = reactive({
      readOnly: true,
      resumeid: props.resumeid,
      showShadow: 900,
      addshowindex:999,//打开的窗口
      languageid: 1 as number,
      hasbtn: computed(() => {
        if (props.passData.length > 0&&props.passData.length<3 ) {
          return true;
        } else {
          return false;
        }
      }),
      isempty: computed(() => {
        if (props.passData.length > 0) {
          return false;
        } else if (props.passData.length < 0 || state.readOnly) {
          return true;
        } else {
          return false;
        }
      }),
    });
    const methods = {
      async deleteItem(itemId: number) {
        let form = {
          resumeid: props.resumeid,
          languageid: itemId,
        };
        let res: any = await deleteLanguage(form);
        if (res.code == 1) {
          // 删除成功 重新加载数据
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          emit("ReloadData");
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
    };
    //添加证书职称
    const addItem = () => {
      languageAdd();
    };
    //编辑
    const edit = (data:any,ind:number) => {
      state.addshowindex=ind;
      state.languageid = data.listId;
      state.resumeid = props.resumeId;
    };
    //删除--操作
    const deleteItem = (data:any) => {
      ElMessageBox.confirm("确定删除该项语言技能吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          methods.deleteItem(data.listId);
        })
        .catch(() => {
          return;
        });
    };
    // 从默认简历复制
    const CopyDefaultesume = (type: string) => {
      if (type == "1") {
        emit("ReloadData");
      } else {
        state.readOnly = false;
      }
    };
     // 接收子组件传过来的值
    const handleRtn = (type:number) => {
      if(type==1){ 
          emit("ReloadData");
      }
      state.addshowindex=900;
      state.readOnly = true;
    };
    const languageAdd = () => {
      let index = props.passData.length || 0;
      let a = [1, 2, 3];
      if (index >= 3) {
        ElMessage({
          showClose: true,
          message: "语言技能已达最大数",
          type: "warning",
        });
      } else {
        let arr = props.passData.map((i: any) => i.listId);
        let difference = getArrDifference(a, arr);
        if (difference.length > 0) {
          state.resumeid = props.resumeid;
          state.languageid = difference[0];
          state.readOnly = false;
        }
      }
    };
    const getArrDifference = (arr1: any, arr2: any) => {
      return arr1.concat(arr2).filter(function (v: any, i: any, arr: any) {
        return arr.indexOf(v) === arr.lastIndexOf(v);
      });
    };

    return {
      ...toRefs(state),
      addItem,
      edit,
      handleRtn,
      deleteItem,
      CopyDefaultesume,
      languageAdd,
      getArrDifference,
    };
  },
};
</script>
<style lang="less" >
.languageComponent {
  .list-project {
    .item {
      .btn-pop {
        top: 20px;
      }
    }
    .rank {
      padding-top: 10px;
      width: 100%;
      span {
        font-size: 14px;
        color: #999;
      }
      label {
        font-size: 14px;
        color: #333;
        padding-left: 16px;
      }
    }
    .li-readOnly{
        width: 100%;
      }
  }
  .borderLine3 {
    position: relative;
    padding: 0 30px;
  }
  .borderLine3:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: 24px;
    top: 0;
    left: 24px;
    border-bottom: 1px solid #f2f2f2;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
}
</style>