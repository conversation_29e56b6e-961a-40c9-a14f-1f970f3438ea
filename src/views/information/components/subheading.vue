<template>
  <div class="subheading clearfix">
    <i class="shu" v-if="hasShu"></i>
    <h3 :class="hasShu?'tit':'tit tit2'">{{ title }}</h3>
    <el-button
      round
      icon="el-icon-plus"
      @click="addProject"
      class="btn"
      v-if="hasbtn"
    >
      添加{{ title }}
    </el-button>
  </div>
</template>

<script lang="ts">
import { reactive } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
export default {
  emits: ["addItem"],
  props: {
    title: {
      type: String,
      default: "小标题",
    },
    hasbtn: {
      type: Boolean,
      default: true,
    },

    hasShu: {
      type: Boolean,
      default: true,
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const state = reactive({
      editID:0
    });
    const addProject = () => {
       let aa= store.state.editorid;
       if(aa==0){
          emit("addItem");
          store.commit("editorShow", 1);
       }else{
        ElMessage({
            showClose: true,
            message: "请先提交打开的编辑窗口",
            type: "warning",
             duration:1000,
          });
       }
      
    };

    return { state, addProject };
  },
};
</script>
<style lang="less">
.subheading {
  padding: 40px 24px 10px 0;
  .tit {
    font-size: 20px;
    color: #333;
    float: left;
    padding: 0 0 0 20px;
    line-height: 20px;
    font-weight: normal;
  }
.tit2{
    padding: 0 0 0 24px;
}
  .shu {
    display: block;
    float: left;
    width: 4px;
    height: 16px;
    background: #5f9efc;
    margin: 2px 0 0 0;
  }
  .btn {
    float: right;
    width: auto;
    padding: 5px 10px;
  }
  .el-button {
    font-size: 14px;
    color: #457ccf;
    border: 1px solid #eeeeee;
  }
}
</style>