<template>
  <div class="ModifyPhoto">
    <el-dialog v-model="dialogVisible" title="上传头像" width="576px" :before-close="Confirm" custom-class="Photo-dialog">
      <div class="head-sculpture clearfix">
        <div class="big-img">
          <div class="imgBox controlUpload">
            <el-image v-if="phoneImg" :src="phoneImg" fit="cover" style="width: 180px; height: 180px"></el-image>
            <!-- 上传控件 -->
            <div class="control">
              <el-upload class="avatar-uploader" action="/api/photo/uploadavatar" :show-file-list="false"
                :on-success="headPortraitSuccess" :on-error="handleAvatarError" :before-upload="beforeAvatarUpload"
                method="post" name="image" :fileType="fileType">
                <div class="addImg">
                  <el-button type="primary" round v-if="phoneImg">重新选择</el-button>
                  <el-button type="primary" round v-else>选择文件</el-button>
                </div>
              </el-upload>
            </div>
          </div>
          <p class="tl">仅支持jpg、gif、png格式的照片 且文件小于2M。</p>
        </div>
        <div class="among-img">
          <div class="imgBox">
            <el-image v-if="phoneImg" :src="phoneImg" fit="cover" style="width: 150px; height: 150px"></el-image>
          </div>
          <p class="tc">150*150px</p>
        </div>
        <div class="small-img">
          <div class="imgBox">
            <el-image v-if="phoneImg" :src="phoneImg" fit="cover" style="width: 60px; height: 60px"></el-image>
          </div>
          <p class="tc">60*60px</p>
        </div>
        <div class="del" @click="delPhoto" v-if="showisDel"><i class="icon-shanchu iconfont"></i>删除</div>
      </div>
      <div class="figure-img">
        <h1>照片({{ num }}/4)</h1>
        <p class="doc">
          可以上传自己的生活照片、设计作品等，更加全面的展示自己，所上传的图片将会在简历中显示，最多4张
        </p>
        <div class="list">
          <ul class="clearfix">
            <li v-for="(item, index) in albumlist" :key="index" class="li">
              <el-image :src="item.imgUrl" fit="cover"></el-image>
              <div class="btn-box clearfix">
                <span :class="['fl', { gray: index === 0 }]" @click="index === 0 ? '' : methods.move(-1, item.id)">
                  <i class="iconfont icon-arrowLeft14"></i> 左移</span>
                <span :class="['fr', { gray: index === num - 1 }]"
                  @click="index === num - 1 ? '' : methods.move(1, item.id)">
                  右移<i class="iconfont icon-arrowRight14"></i>
                </span>
              </div>
              <div class="del" @click="methods.deletbigImg(item.id)">×</div>
            </li>
            <li v-if="num < 4">
              <el-upload class="avatar-uploader" action="/api/photo/uploadalbum" :show-file-list="false"
                :on-success="handleAvatarSuccess" :on-error="handleAvatarError" :before-upload="beforeAvatarUpload"
                method="post" name="image" :fileType="fileType">
                <div class="addImg">
                  <i class="iconfont icon-add2"></i>
                </div>
              </el-upload>
            </li>
          </ul>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="Confirm">完成</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, computed, onMounted } from "vue";
import subheading from "@/views/information/components/subheading.vue";
import { ElLoading, ElMessage } from "element-plus";
import { useStore } from 'vuex';
import {
  getphoto,
  getAlbumlPhoto,
  deletedAlbumlPhoto,
  AlbumSortphoto,
  deletHeadPhoto,
} from "@/http/resumeApi";

export default defineComponent({
  components: { subheading },
  emits: ["confirmPhotograph"],
  props: {
    passData: {
      type: Array,
      default: () => [],
    },
    resumeid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const state = reactive({
      dialogVisible: true,
      num: 3,
      imageUrl: "",
      url: "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
      img: "",
      phoneImg: "",
      albumlist: [],
      isChange: false,//数据被改变了
      showisDel: true,
    });
    const store = useStore();
    onMounted(() => {
      methods.getPhoto();
      methods.albumList();
    });
    const fileType = {
      type: "image",
      format: ".jpg,.gif,.png,.jpeg",
    };
    const methods = {
      //获取头像
      async getPhoto() {
        let data = await getphoto();
        if (data.code == 1 && data.data.avatar != "") {
          state.isChange = true;
          state.showisDel = true;//显示删除按钮
          state.phoneImg = data.data.avatar + "?t=" + Math.random();
        } else {

          state.showisDel = false;//显示删除按钮
        }
      },
      //获取个人形象
      async albumList() {
        let data = await getAlbumlPhoto();
        if (data.code == 1 && data.data.length > 0) {
          state.isChange = true;
          state.albumlist = data.data;
        } else {
          state.albumlist = [];
        }
        state.num = data.data.length;
      },
      // 删除个人形象
      async deletbigImg(id: number) {
        let form = {
          id: id,
        };
        let data = await deletedAlbumlPhoto(form);
        if (data.code == 1) {
          methods.albumList();
          state.isChange = true;
          ElMessage.success(data.message);

        } else {
          ElMessage.error(data.message);
        }
      },
      async move(sortType: any, id: number) {
        let form = {
          id: id,
          sortType: sortType,
        };
        let data = await AlbumSortphoto(form);
        if (data.code == 1) {
          this.albumList();
          state.isChange = true;
          ElMessage.success(data.message);
        } else {
          ElMessage.error(data.message);
        }
      },

      async deletHeadimg() {
        let data = await deletHeadPhoto();
        if (data.code == 1) {
          state.phoneImg = '';
          emit("confirmPhotograph", 1, state.phoneImg);
          state.showisDel = false;
          ElMessage.success(data.message);
        } else {
          ElMessage.error(data.message);
        }


      },
    };
    const fun = {
      Confirm() {
        console.log("点了确认")
        if (state.isChange) {
          emit("confirmPhotograph", 1, state.phoneImg);
        } else {
          emit("confirmPhotograph", 2);
        }

      },
      //上传成功
      handleAvatarSuccess(res: any, file: any) {
        // state.imageUrl = URL.createObjectURL(file.raw);
        ElMessage.success(res.message);
        methods.albumList();
      },
      //上传成功
      headPortraitSuccess(res: any, file: any) {
        // state.imageUrl = URL.createObjectURL(file.raw);
        ElMessage.success(res.message);
        state.showisDel = true;
        state.phoneImg = res.data.url + "?t=" + Math.random();
        emit("confirmPhotograph", 1, state.phoneImg);

        const headImgchange = computed(() => store.state.headImgchange);
 
        let currentHeadImgValue = Number(headImgchange.value); // 提取当前值
        currentHeadImgValue++; // 执行递增操作
        store.commit('setHeadImgchange', currentHeadImgValue); // 更新状态
      },
      //上传失败
      handleAvatarError(res: any, file: any) {
        ElMessage.error(res.message);
      },
      //上传之前的处理
      beforeAvatarUpload(file: any) {
        const isJPG =
          file.type == "image/gif" ||
          file.type == "image/jpeg" ||
          file.type == "image/png";
        const isLt2M = file.size / 1024 / 1024 < 2;

        if (!isJPG) {
          ElMessage("上传 图片的格式必须为gif jpeg png");
        }
        if (!isLt2M) {
          ElMessage("图片大小在 2MB以内!");
        }
        return isJPG && isLt2M;
      },
      delPhoto() {
        methods.deletHeadimg()
      },
    };
    return { ...toRefs(state), ...fun, methods, fileType };
  },
});
</script>
<style lang="less">
.Photo-dialog {
  .el-dialog__footer {
    text-align: center;

    .el-button {
      width: 200px;
      background: #5f9efc;
    }
  }

  .el-dialog__body {
    padding: 0 0 0 0;
  }

  .head-sculpture {
    padding: 16px 36px;
    border-bottom: 1px solid #f2f2f2;

    .big-img {
      width: 180px;
      height: 220px;
      float: left;

      .imgBox {
        width: 180px;
        height: 180px;
      }
    }

    .among-img {
      width: 150px;
      height: 220px;
      float: left;
      margin-left: 40px;

      .imgBox {
        width: 150px;
        height: 150px;
        margin-top: 30px;
      }
    }

    .small-img {
      width: 60px;
      height: 220px;
      margin-left: 20px;
      float: left;

      .imgBox {
        width: 60px;
        height: 60px;
        margin-top: 120px;
      }
    }

    .del {
      float: left;
      margin: 164px 0 0 10px;
      color: #bbb;
      font-size: 13px;
      cursor: pointer;
    }

    .imgBox {
      background: url("@/assets/img/moren.png") no-repeat center;
      background-size: 100%;
    }

    p {
      color: #bbbbbb;
      font-size: 12px;
      padding: 5px 0 0 0;
    }
  }

  .figure-img {
    padding: 30px 36px;

    h1 {
      font-size: 20px;
      color: #333;
    }

    .doc {
      font-size: 14px;
      color: #999999;
      padding: 10px 0 20px 0;
    }

    li {
      position: relative;
      width: 120px;
      float: left;
      margin-left: 8px;
    }

    ul li:first-child {
      margin-left: 0;
    }

    .el-image {
      width: 120px;
      height: 120px;
    }

    .del {
      position: absolute;
      width: 24px;
      height: 24px;
      top: 0;
      right: 0;
      z-index: 99;
      color: #fff;
      line-height: 24px;
      text-align: center;
      font-size: 20px;
      cursor: pointer;
      background: rgba(0, 0, 0, 0.383);
    }

    .btn-box {
      padding: 0 5px;

      span {
        font-size: 11px;
        color: #999;
        cursor: pointer;

        i {
          font-size: 11px;
        }
      }

      span.gray {
        color: #dddddd;
      }
    }

    .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
      border-color: #409eff;
    }

    .addImg .icon-add2 {
      font-size: 28px;
      color: #eeeeee;
      text-align: center;
    }

    .addImg {
      width: 120px;
      height: 120px;
      display: block;
      line-height: 120px;
    }
  }

  .controlUpload {
    position: relative;

    .control {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 9;
    }

    .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      background: #00000061;
    }

    .avatar-uploader .el-upload:hover {
      border-color: #409eff;
    }

    .addImg .icon-add2 {
      font-size: 28px;
      color: #eeeeee;
      text-align: center;
    }

    .addImg {
      width: 180px;
      height: 180px;
      display: block;
      line-height: 180px;
    }
  }
}
</style>