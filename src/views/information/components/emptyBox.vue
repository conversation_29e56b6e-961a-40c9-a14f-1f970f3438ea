<template>
  <div class="empty-box">
    <span class="cursor blue" @click="addItem()">
      <i class="iconfont">+</i>
      {{ typeBtn }}
    </span>
    <span class="huo" v-if="(mold == 1 || mold == 3) && !isdefault">或</span>
    <span
      class="blue cursor"
      @click="CopyDefaultesume"
      v-if="(mold == 1 || mold == 3) && !isdefault"
      >从默认简历中复制</span
    >
    <p class="gray" v-if="mold">{{ describe }}</p>
  </div>
</template>
<script lang="ts">
import { reactive, toRefs, defineComponent, computed } from "vue";
import { postClonefromresume } from "../../../http/resumeApi";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import { useRoute } from "vue-router";

export default defineComponent({
  components: {},
  emits: ["handleBox"],
  props: {
    mold: {
      //求职意向=0,工作经历=1,项目经验=2,教育经历=3,培训经历=4,证书职称=5,个人描述=7,个人技能语言技能=8,个人技能驾照=9,个人技能_其它技能=10
      type: String,
      default: "",
    },
    resumeid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const route = useRoute();
    const store = useStore();
    const state = reactive({
      typeBtn: computed(() => {
        if (props.mold == 0) {
          return "添加求职意向";
        }
        if (props.mold == 1) {
          return "添加工作经历";
        }
        if (props.mold == 2) {
          return "添加项目经验";
        }
        if (props.mold == 3) {
          return "添加教育经历";
        }
        if (props.mold == 4) {
          return "添加培训经历";
        }
        if (props.mold == 5) {
          return "添加证书职称";
        }
        if (props.mold == 7) {
          return "添加个人描述";
        }
        if (props.mold == 8) {
          return "添加语言技能";
        }
        if (props.mold == 10) {
          return "添加其他技能";
        }
      }),
      isdefault: computed(() => {
        if (props.resumeid == store.state.userInfo.defaultResumeId) {
          return true;
        } else {
          return false;
        }
      }), //是否是默认简历
      describe: computed(() => {
        if (props.mold == 0) {
          return "添加求职意向";
        }
        if (props.mold == 1) {
          return "工作经历是简历的关键部分";
        }
        if (props.mold == 2) {
          return "出色的项目经历为你在竞争中增加一个重要的砝码";
        }
        if (props.mold == 3) {
          return "教育经历是企业选人的关键";
        }
        if (props.mold == 4) {
          return "培训经历将增加您的竞争力";
        }
        if (props.mold == 5) {
          return "证书最能证明您的能力";
        }
        if (props.mold == 7) {
          return "自我评价具有重要的意义";
        }
        if (props.mold == 8) {
          return "出色的外语水平是重要的竞争力";
        }
        if (props.mold == 10) {
          return "技能是工作能力的重要体现";
        }
      }),
      resumeid: route.params.id,
    });
    let prevent = 1;
    const methods = {
      async Clonefromresume() {
        if (prevent === 2) {
          return false;
        }
        let resumeid = props.resumeid || state.resumeid,
          clonetype = props.mold;
        prevent = 2;
        let res: any = await postClonefromresume(resumeid, clonetype);

        if (res.message == "复制成功") {
          // 复制成功 重新加载数据
          emit("handleBox", "1");
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
        prevent = 1;
      },
    };
    const addItem = () => {
      let aa = store.state.editorid;
      if (aa == 0) {
        emit("handleBox");
        store.commit("editorShow", 1);
      } else {
        ElMessage({
          showClose: true,
          message: "请先提交打开的编辑窗口",
          type: "warning",
          duration: 1000,
        });
      }
    };
    const CopyDefaultesume = () => {
      methods.Clonefromresume();
    };

    return { ...toRefs(state), addItem, CopyDefaultesume };
  },
});
</script>
<style lang="less">
.Resume-editor-main {
  //空样式
  .empty-box {
    margin: 24px;
    height: 80px;
    background: #fafafa;
    text-align: center;
    span {
      font-size: 12px;
      padding: 20px 0 8px 0;
      display: inline-block;
    }
    span.huo {
      padding: 20px 5px 8px 5px;
      color: #bbb;
    }

    p {
      font-size: 12px;
      color: #bbb;
    }
    .cursor {
      cursor: pointer;
    }
    i.iconfont {
      font-size: 12px;
      font-style: normal;
      font-weight: bold;
    }
  }
}
</style>