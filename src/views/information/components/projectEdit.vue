<template>
    <div class="edit-unify li-pop">
      <el-form
        :model="editForm"
        class="demo-form-inline"
        label-width="100px"
        :rules="rules"
        ref="ruleForm"
      >
        <el-form-item label="项目名称" class="w400" prop="projectName">
          <el-input
            v-model="editForm.projectName"
            placeholder="请输入项目名称"
          ></el-input>
        </el-form-item>

        <el-form-item label="项目时间" required>
          <el-col :span="6">
            <el-form-item prop="beginTime">
              <el-date-picker
                v-model="editForm.beginTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w180"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col class="line" :span="3">至</el-col>
          <el-col :span="7">
            <el-form-item prop="endTime">
              <el-date-picker
                v-model="editForm.endTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w180"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="参与身份" class="w400">
          <el-select
            v-model="editForm.playRoleStr"
            placeholder="请选择参与身份"
          >
            <el-option
              v-for="(p, index) in identityList"
              :key="index"
              :label="p.text"
              :value="p.value"
              @click="editForm.playRole = p.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="项目描述" style="width: 100%">
          <el-input
            v-model="editForm.projectDescription"
            type="textarea"
            maxlength="500"
            show-word-limit
            clearable
            size="medium"
            class="JobDescription"
            placeholder="描述您在此项目中担任的角色、职责、成就等……"
          ></el-input>
          <div class="othersWrite">
            <el-popover
              v-model:visible="visible"
              placement="bottom"
              :width="451"
            >
              <template #reference>
                <span @click="visible = true" class="how">别人怎么写？</span>
              </template>
              <div class="describe-popover">
                <h2 class="tit">项目描述</h2>
                <p class="doc-p">
                  项目经历应提供与项目内容相关的细节，以便HR快速了解您之前的项目经历。
                </p>
                <p class="li">例：</p>
                <p>1.参与广西人才网个人后台优化项目</p>
                <p>2.在广西人才网个人后台优化项目中担任产品经理</p>
                <p>
                  3.在广西人才网个人后台优化项目中出色的完成了既定任务，完成了个人后台的升级改版
                </p>
                <p>4.为广西人才网个人后台添加数据监控功能</p>
                <p>5.顺利完成广西人才网个人后台升级</p>
              </div>
            </el-popover>
          </div>
        </el-form-item>
        <el-form-item class="btn-end" style="width: 100%">
          <el-button type="primary" @click="onSubmit" class="sub"
            >保存</el-button
          >
          <el-button @click="cancel" class="cel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  watch,
  onBeforeMount,
  Ref,
  ref,
} from "vue";
import { saveProject, getProject } from "../../../http/resumeApi";
import { ElMessage } from "element-plus";
import { getPlayroleitems } from "../../../http/dictionary";
import { useStore } from "vuex";

export default defineComponent({
  emits: ["handleRtn"],
  props: {
    resumeid: {
      type: Number,
      default: 0,
    },
    projectid: {
      type: Number,
      default: "",
    },
  },

  setup(props: any, { emit }: any) {
    const store = useStore();
    const ruleForm: Ref<any> = ref(null);
    const state = reactive({
      readOnly: true,
      editForm: {
        ResumeID: props.resumeid,
        beginTime: "",
        endTime: "",
        projectName: "",
        playRoleStr: "独立完成",
        playRole: 0,
        projectDescription: "",
        id: "0",
      },
      identityList: "", //参与身份
      visible: ref(false),
    });

    let rules = {
      projectName: [
        {
          required: true,
          message: "请输入项目名称",
          trigger: "blur",
        },
        {
          min: 1,
          max: 25,
          message: "长度1-25个字符",
          trigger: "blur",
        },
      ],
      beginTime: [
        {
          type: "date",
          required: true,
          message: "请选择开始时间",
          trigger: "change",
        },
      ],
      endTime: [
        {
          type: "date",
          required: true,
          message: "请选择结束时间",
          trigger: "change",
        },
      ],
    };

    onBeforeMount(() => {
      //获取数据
      if (props.projectid > 0) {
        methods.getData();
      }
      methods.getPositionList();
    });
    let prevent=1;
    const methods = {
      async getData() {
        let data = {
          resumeid: props.resumeid,
          projectid: props.projectid,
        };
        let res: any = await getProject(data);
        if (res.code == 1) {
          state.editForm = res.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //保存--工作
      async saveData() {
        if(prevent===2){
          return false
        }
        let form = state.editForm;
        prevent=2;
        let res: any = await saveProject(form);
        if (res.code == 1) {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          store.commit("editorShow", 0);
          emit("handleRtn",1);
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
        prevent=1;
      },
      //获取身份字典
      async getPositionList() {
        let form = {
          resumeid: props.resumeid,
        };
        let res: any = await getPlayroleitems(form);
        state.identityList = res.data;
      },
    };
    const fun = {
      cancel() {
        emit("handleRtn", 2);
        store.commit("editorShow", 0);
      },
      // 保存数据
      onSubmit() {
        //  验证必填项是否为空
        ruleForm.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData();
          } else {
            ElMessage({
              message: "保存失败，请检查必填项",
              type: "error",
            });
            return false;
          }
        });
      },
    };
    return { ...toRefs(state), ...fun, rules, ruleForm };
  },
});
</script>




<style lang="less">
.projectComponent {
  .li-pop {
    .el-form-item__label {
      width: 90px;
      text-align: left;
    }
    .el-form {
      .fr {
        margin-right: 0;
      }
    }
    .JobDescription {
      .el-textarea__inner {
        height: 130px;
      }
    }
    .btn {
      color: #457ccf;
      background: #fff;
      border: 1px solid #457ccf;
      font-size: 14px;
    }
    .extraMessage {
      padding: 20px 0 0 0;
      position: relative;
      .btn-del {
        position: absolute;
        top: 40px;
        right: 0px;
        font-size: 13px;
        color: #457ccf;
        cursor: pointer;
        i {
          padding-right: 5px;
        }
      }
    }
    .extraMessage::before {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      right: -24px;
      top: 0;
      left: -20px;
      border-bottom: 1px solid #f2f2f2;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
    }
    .line {
      font-size: 15px;
      color: #666;
      padding: 0 12px 0 8px;
    }
  }
}
</style>