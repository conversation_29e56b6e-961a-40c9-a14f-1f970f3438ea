<template>
  <div class="descriptionComponent borderLine2">
    <subheading
      @addItem="addItem"
      title="个人描述"
      btntext="描述"
      :hasbtn="hasbtn"
    ></subheading>
    <div class="list-project list-unify">
      <ul class="">
        <li v-if="!readOnly">
          <descriptionEdit
            @handleRtn="handleRtn"
            :resumeid="resumeid"
            :desId="desId"
          ></descriptionEdit>
        </li>
        <li
          v-for="(p, index) in passData"
          :key="p.desId"
          @mouseover="showShadow = index"
          @mouseout="showShadow = 900"
          :class="showShadow == index ? 'item bar-shadow-unify' : 'item'"
        >
          <div class="li-readOnly" v-if="addshowindex!==index">
            <h1>{{ p.desName }}</h1>
            <p class="doc" v-safe-html.relaxed="p.desContent"></p>
            <!-- 按钮 -->
            <btnBox @deleteItem="deleteItem" @edit="edit" :passData="p" :ind="index"></btnBox>
          </div>
          <descriptionEdit
            @handleRtn="handleRtn"
            :resumeid="resumeid"
            :desId="desId"
            v-else
          ></descriptionEdit>
        </li>
      </ul>
    </div>
    <!-- 为空时的状态 -->
    <emptyBox
      v-if="isempty"
      :resumeid="resumeid"
      mold="7"
      @handleBox="CopyDefaultesume"
    />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, computed } from "vue";
import subheading from "@/views/information/components/subheading.vue";
import btnBox from "./btnBox.vue";
import descriptionEdit from "./descriptionEdit.vue";
import emptyBox from "./emptyBox.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { deleteDescription } from "../../../http/resumeApi";

export default {
  components: { subheading, descriptionEdit, emptyBox,btnBox},
  emits: ["ReloadData"],
  props: {
    passData: {
      type: Array,
      default: () => [],
    },
    resumeid: {
     type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const state = reactive({
      readOnly: true,
      resumeid: props.resumeid,
      showShadow: 900,
      desId: 0 as number,
      addshowindex:999,//打开的窗口
      hasbtn: computed(() => {
        if (props.passData.length > 0) {
          return true;
        } else {
          return false;
        }
      }),
      isempty: computed(() => {
        if (props.passData.length > 0) {
          return false;
        } else if (props.passData.length < 0 || state.readOnly) {
          return true;
        } else {
          return false;
        }
      }),
    });
    const methods = {
      async deleteItem(itemId: number, resumeId: string) {
        let form = {
          resumeid: resumeId,
          desid: itemId,
        };
        let res: any = await deleteDescription(form);
        if (res.code == 1) {
          // 删除成功 重新加载数据
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          emit("ReloadData");
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
    };
    //添加个人描述--子集传来的
    const addItem = () => {
      state.resumeid = props.resumeid;
      state.desId = 0;
      state.readOnly = false;
    };
    //编辑
    const edit = (data:any,ind:number) => {
      state.desId = data.desId;
      state.resumeid = data.resumeId;
      state.addshowindex=ind;
      
    };
    //删除--操作
    const deleteItem = (data:any) => {
      ElMessageBox.confirm("确定删除个人描述吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          methods.deleteItem(data.desId, data.resumeId);
        })
        .catch(() => {
          return;
        });
    };
    // 从默认简历复制
    const CopyDefaultesume = (type: string) => {
      if (type == "1") {
        emit("ReloadData");
      } else {
        state.readOnly = false;
      }
    };
    // 接收子组件传过来的值
    const handleRtn = (type:number) => {
      if(type==1){ 
          emit("ReloadData");
      }
      state.addshowindex=900;
      state.readOnly = true;
    };
    return {
      ...toRefs(state),
      addItem,
      edit,
      handleRtn,
      deleteItem,
      CopyDefaultesume,
    };
  },
};
</script>
<style lang="less" >
.descriptionComponent {
  .list-project {
    .item {
      .btn-pop {
        top: 20px;
      }
      h1 {
        font-size: 16px;
        color: #333;
      }
    }
  }
}
</style>