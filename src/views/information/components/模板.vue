<template>
    <div class="careerComponent">
        <subheading @addItem="addItem" title="求职意向" hasbtn=false></subheading>
    </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, computed } from "vue";
import subheading from "@/views/information/components/subheading.vue";

export default defineComponent({
  components: { subheading },
   emits: ["ReloadData"],
  props: {
    passData: {
      type: Array,
      default: () => [],
    },
    resumeid: {
      type: Number,
      default: 0,
    },
  },
   setup(props: any, { emit }) {
    const state = reactive({
      readOnly: true,
      id:2,
    });
    const methods = {}
    const addItem=()=>{
    }
    const edit = () => {
      state.readOnly = false;
    };

    return { ...toRefs(state) ,addItem,edit};
  },
})
</script>
<style lang="less">
  .Resume-editor-main{
    
  }
</style>