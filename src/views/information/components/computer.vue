<template>
  <div class="languageComponent">
    <subheading @addItem="addItem" title="其他技能" :hasbtn="false"></subheading>
    <div class="list-project list-unify">
      <ul class="">
        <li @mouseover="handleMouseOver" @mouseout="handleMouseOut" :class="getClass()" v-if="readOnly">
          <div class="rank">
            <el-row>
              <el-col :span="8">
                <span>驾&#8195;&#8194; 照</span>
                <label>{{ passData.drivingLicense ? passData.drivingLicense : '无' }}</label>
              </el-col>
              <el-col :span="8">
                <span>电脑水平</span>
                <label>{{ passData.computerLevel?passData.computerLevel: '无' }}</label>
              </el-col>
            </el-row>
          </div>

          <!-- 实践内容 -->
          <span @click="edit()" class="btn-edit-pop btn-pop">
            <i class="iconfont icon-bianji1"></i>编辑</span>
        </li>
        <computerEdit @handleRtn="handleRtn" :resumeid="resumeid" v-if="!readOnly"></computerEdit>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent } from "vue";
import { useStore } from "vuex";
import subheading from "@/views/information/components/subheading.vue";
import computerEdit from "./computerEdit.vue";
import { ElMessage } from "element-plus";

export default defineComponent({
  components: { subheading, computerEdit },
  emits: ["ReloadData"],
  props: {
    passData: {
      type: Object,
      default() {
        return {};
      },
    },
    resumeid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const store = useStore();
    const state = reactive({
      readOnly: true,
      showShadow: 900,
    });
    const fun = {
      //编辑
      edit() {
        let aa = store.state.editorid;
        if (aa == 1) {
          ElMessage({
            showClose: true,
            message: "请先提交打开的编辑窗口",
            type: "warning",
            duration: 1000,
          });
          return false
        }
        store.commit("editorShow", 1);
        state.readOnly = false;
      },
      handleMouseOver() {
        state.showShadow = 1;
      },
      handleMouseOut() {
        state.showShadow = 2;
      },
      getClass() {
      return state.showShadow === 1 ? 'item bar-shadow-unify' : 'item';
    },
      handleRtn() {
        state.readOnly = true;
        emit("ReloadData");
        store.commit("editorShow", 0);
      },
      addItem() {

      }
    };

    return { ...toRefs(state), ...fun };
  },
});
</script>
<style lang="less">
.languageComponent {
  .list-project {
    .item {
      .btn-pop {
        top: 20px;
      }
    }

    .rank {
      padding-top: 10px;

      span {
        font-size: 14px;
        color: #999;
      }

      label {
        font-size: 14px;
        color: #333;
        padding-left: 16px;
      }
    }
  }

  .borderLine3 {
    position: relative;
    padding: 0 30px;
  }

  .borderLine3:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: 24px;
    top: 0;
    left: 24px;
    border-bottom: 1px solid #f2f2f2;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
}
</style>