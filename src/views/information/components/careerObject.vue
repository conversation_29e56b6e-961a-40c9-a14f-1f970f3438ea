<template>
  <div class="careerComponent">
    <subheading
      @addItem="addItem"
      title="求职意向"
      :hasbtn="hasbtn"
    ></subheading>
    <div class="list-read borderLine4" v-if="readOnly">
      <ul
        :class="{ 'bar-shadow-unify': showShadow, clearfix: true }"
        @mouseover="showShadow = true"
        @mouseout="showShadow = false"
      >
        <li>
          <span>期望工作地</span>
          <label>{{ expectWorkPlaceName }}</label>
        </li>
        <li>
          <span>目前薪资</span>
          <label v-if="!passData.currentSalaryVisible">已隐藏</label>
          <label v-else-if="passData.currentSalary > 0"
            >{{ passData.currentSalary }}元/月</label
          >
          <label v-else>未填写</label>
        </li>
        <li>
          <span>期望薪资</span>
          <label v-if="passData.expectSalaryVisible">面议</label>
          <label v-else>{{ passData.salary }}元/月</label>
        </li>
        <li @click="edit()" class="btn-edit-pop btn-pop">
          <i class="iconfont icon-bianji1"></i>编辑
        </li>
      </ul>
      <div
        class="queren_o"
        v-if="
          passData.isExpectCareer1 ||
          passData.isExpectCareer2 ||
          passData.isExpectCareer3
        "
      >
        <div class="tii">职位分类已全新升级~请您根据提示完善您的求职意向</div>
      </div>
    </div>
    <!-- 编辑弹窗 -->
    <div v-else class="careerEdit padding24">
      <careerObjectEdit
        @handleRtn="handleRtn"
        :editForm="passData"
        :resumeid="resumeid"
        :isSure="isSure"
      ></careerObjectEdit>
    </div>

    <div v-if="readOnly">
      <expectPositions
        name="期望职位"
        :passData="passData"
        :resumeid="resumeid"
        @ReloadData="handleRtn"
        v-if="passData.expectCareer1"
        :isAdd="isAdd"
        :num="num"
      ></expectPositions>
    </div>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, computed, onMounted } from "vue";
import subheading from "@/views/information/components/subheading.vue";
import careerObjectEdit from "./careerObjectEdit.vue";
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import expectPositions from "@/views/information/components/expectPositions.vue";

export default defineComponent({
  emits: ["ReloadData"],
  components: { subheading, careerObjectEdit, expectPositions },
  props: {
    passData: {
      type: Object,
      default: function () {
        return {
          workingState: "",
          expectWorkPlaceName: [],
          expectIndustry1Names: [],
          expectIndustry2Names: [],
          expectIndustry3Names: [],
        };
      },
    },
  },
  setup(props: any, { emit }) {
    const store = useStore();
    const route = useRoute();
    const state = reactive({
      readOnly: true,
      showShadow: false,
      isAdd: false,
      resumeid: parseInt(`${route.params.id}`),
      editForm: "",
      num: 0,
      isSure: 0,
      hasbtn: computed(() => {
        let len = 0;
        if (props.passData.expectCareer1) {
          len++;
        }
        if (props.passData.expectCareer2) {
          len++;
        }
        if (props.passData.expectCareer3) {
          len++;
        }
        return len < 3 && len > 0;
      }),
      expectWorkPlaceName: computed(() => {
        let arr = [];
        if (props.passData.expectWorkPlaceName[0])
          arr.push(props.passData.expectWorkPlaceName[0]);
        if (props.passData.expectWorkPlaceName[1])
          arr.push(props.passData.expectWorkPlaceName[1]);
        if (props.passData.expectWorkPlaceName[2])
          arr.push(props.passData.expectWorkPlaceName[2]);
        return arr.join("  ");
      }),
    });
    onMounted(() => {
      let url = window.location.href;
      if (url.indexOf("&isSure") != -1) {
        // state.isSure=1
      }
      if (url.indexOf("#iseditCarrer") != -1) {
        state.readOnly = false;
        window.location.hash = "#careerObject";
        store.commit("editorShow", 1);
      }
    });
    const methods = {};
    const fun = {
      //添加意向
      addItem() {
        let type = 1;
        if (!props.passData.expectCareer3) {
          type = 3;
        }
        if (!props.passData.expectCareer2) {
          type = 2;
        }
        if (!props.passData.expectCareer1) {
          type = 1;
        }
        state.num = type;
        state.isAdd = true;
        // state.readOnly = false;
      },
      edit() {
        let aa = store.state.editorid;
        if (aa == 0) {
          state.readOnly = false;
          store.commit("editorShow", 1);
        } else {
          ElMessage({
            showClose: true,
            message: "请先提交打开的编辑窗口",
            type: "warning",
            duration: 1000,
          });
        }
      },
      handleRtn(type: any) {
        state.readOnly = true;
        state.isAdd = false;
        store.commit("editorShow", 0);
        if (type == 1) {
          emit("ReloadData");
        }
      },
    };

    return { ...toRefs(state), ...fun };
  },
});
</script>
<style lang="less">
.careerComponent {
  padding: 0 0 16px 0;
  .list-read {
    line-height: 49px;
    cursor: pointer;
    margin-bottom: 10px;
    padding-bottom: 10px;
    ul {
      padding: 0px 24px;
      position: relative;
      :hover {
        box-shadow: none;
      }
      .btn-edit-pop {
        width: auto;
      }
    }
    li {
      width: 33%;
      float: left;
      i {
        font-style: normal;
      }
      span {
        font-size: 14px;
        color: #999;
        padding: 0 16px 0 0px;
      }
      label {
        font-size: 14px;
        color: #333;
      }
    }
  }
  .borderLine4 {
    position: relative;
  }
  .borderLine4:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: 24px;
    bottom: 0;
    left: 24px;
    border-bottom: 1px solid #f2f2f2;
    /* border-bottom: 1px solid #ee0404;  */

    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
  }
  .queren_o {
    padding: 3px 24px;
    .tii {
      background: #f2f7ff;
      display: flex;
      border: 1px dashed #457ccf;
      padding: 0px 16px;
      font-size: 12px;
      margin: 12px 0;
      color: #457ccf;
      flex: 1;
      height: 33px;
      line-height: 33px;
    }
  }
}
</style>
