<template>
  <div class="li-pop">
    <div class="edit-unify">
      <el-form
        :model="editForm"
        class="demo-form-inline"
        label-width="100px"
        :rules="rules"
        ref="ruleForm"
      >
        <el-form-item label="技能名称" class="w400" prop="techName">
          <el-input v-model="editForm.techName" placeholder="请输入技能名称" />
        </el-form-item>
        <el-form-item label="使用时间" class="w280 fl" prop="monthUsed">
          <el-input v-model="editForm.monthUsed" placeholder="请输入使用时间"  type="number">
            <template #suffix>
              <i class="yue">月</i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="掌握程度" class="w280 fr" prop="techLevel">
          <el-select v-model="editForm.techLevel" placeholder="请选择">
            <el-option
              v-for="item in technologylevelList"
              :key="item.keywordID"
              :label="item.keywordName"
              :value="item.keywordID"
              @click="editForm.techLevelId = item.keywordID"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item class="btn-end" style="width: 100%">
          <el-button type="primary" @click="onSubmit" class="sub"
            >保存</el-button
          >
          <el-button @click="cancel" class="cel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  watch,
  onBeforeMount,
  Ref,
  ref,
} from "vue";
import { saveTechnology, getTechnology } from "../../../http/resumeApi";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import { getTechnologylevel } from "../../../http/dictionary";
export default defineComponent({
  emits: ["handleRtn"],
  props: {
    resumeid: {
      type: Number,
      default: 0,
    },
    technologyid: {
      type: Number,
      default: "",
    },
  },

  setup(props: any, { emit }: any) {
    const store = useStore();
    const ruleForm: Ref<any> = ref(null);
    const state = reactive({
      readOnly: true,
      editForm: {
        techId: 0,
        resumeId: props.resumeid,
        techLevel: "",
        monthUsed: null,
        techLevelId: null,
        techName: "",
      },
      technologylevelList: "", //参与身份
      visible: ref(false),
    });

    let rules = {
      techName: [
        {
          required: true,
          message: "请输入技能名称",
          trigger: "blur",
        },
        {
          min: 1,
          max: 25,
          message: "长度1-25个字符",
          trigger: "blur",
        },
      ],
      monthUsed: [
        {
          required: true,
          message: "请输入使用时间",
          trigger: "blur",
        },
      ],
      techLevel: [
        {
          required: true,
          message: "请选择掌握程度",
          trigger: "change",
        },
      ],
    };

    onBeforeMount(() => {
      if (props.technologyid > 0) {
        methods.getData();
      }
      if (state.technologylevelList.length <= 0) {
        methods.getTechnologylevelList();
      }
    });
    let prevent="1";
    const methods = {
      async getData() {
        let data = {
          resumeid: props.resumeid,
          technologyid: props.technologyid,
        };
        let res: any = await getTechnology(data);
        if (res.code == 1) {
          state.editForm = res.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //保存--工作
      async saveData() {
        if(prevent==="2"){
          return false
        }
        let form = state.editForm;
        prevent="2";
        let res: any = await saveTechnology(form);
        if (res.code == 1) {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          store.commit("editorShow", 0);
          emit("handleRtn",1);
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
        prevent="1";
      },
      //获取技术能力字典
      async getTechnologylevelList() {
        let form = {
          resumeid: props.resumeid,
        };
        let res: any = await getTechnologylevel(form);
        state.technologylevelList = res.data;
      },
      
    };
    const fun = {
      cancel() {
          store.commit("editorShow", 0);
        emit("handleRtn",2);
      },
      // 保存数据
      onSubmit() {
        //  验证必填项是否为空
        ruleForm.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData();
          } else {
            // ElMessage({
            //   message: "保存失败，请填写完整信息",
            //   type: "error",
            // });
            return false;
          }
        });
      }
    };
    return { ...toRefs(state), ...fun, rules, ruleForm };
  },
});
</script>


<style lang="less">
.technicalComponent {
  overflow: hidden;
  .li-pop {
    .el-form-item__label {
      text-align: left;
    }
    .el-form {
      .fr {
        margin-right: 0;
      }
    }
    .btn-end {
      padding: 40px 0 0 0;
      margin-bottom: 0;
    }
    .yue {
      font-style: normal;
    }
  }
}
.Resume-editor-main
.technicalComponent
  .edit-unify
  .el-form-item.is-required:not(.is-no-asterisk)
  > .el-form-item__label::after {
  content: " " !important;
}
</style>