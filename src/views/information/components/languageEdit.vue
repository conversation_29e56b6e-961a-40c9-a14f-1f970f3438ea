<template>
  <div class="li-pop">
    <div class="edit-unify">
      <el-form
        :model="editForm"
        class="demo-form-inline"
        label-width="100px"
        :inline="true"
      >
        <el-form-item label="外语语种" class="w280">
          <el-select
            v-model="editForm.languageName"
            placeholder="请选择"
            prop="NameA"
            hide-required-asterisk="true"
          >
            <el-option
              v-for="item in languageList"
              :key="item.keywordID"
              :label="item.keywordName"
              :value="item.keywordID"
              @click="selectlanguageName(item.keywordID)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="综合能力"
          class="w280 fr"
          hide-required-asterisk="true"
        >
          <el-select v-model="editForm.levelName" placeholder="请选择">
            <el-option
              v-for="item in mixedAbility"
              :key="item.keywordID"
              :label="item.keywordName"
              :value="item.keywordID"
              @click="editForm.level = item.keywordID"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="听说能力"
          class="w280"
          hide-required-asterisk="true"
        >
          <el-select v-model="editForm.readLevelName" placeholder="请选择">
            <el-option
              v-for="item in columns"
              :key="item.keywordID"
              :label="item.keywordName"
              :value="item.keywordID"
              @click="editForm.readLevel = item.keywordID"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="读写能力"
          class="w280 fr"
          hide-required-asterisk="true"
        >
          <el-select v-model="editForm.writeLevelName" placeholder="请选择">
            <el-option
              v-for="item in columns"
              :key="item.keywordID"
              :label="item.keywordName"
              :value="item.keywordID"
              @click="editForm.writeLevel = item.keywordID"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="btn-end" style="width: 100%">
          <el-button type="primary" @click="onSubmit" class="sub"
            >保存</el-button
          >
          <el-button @click="cancel" class="cel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  onBeforeMount,
  Ref,
  ref,
  watch,
} from "vue";
import {
  getLanguageoptions,
  getEnglishoptions,
  getOtherlanguageleveloptions,
} from "../../../http/dictionary";
import { saveLanguage, getLanguage } from "../../../http/resumeApi";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
export default defineComponent({
  emits: ["handleRtn"],
  components: {},
  props: {
    resumeid: {
      type: Number,
      default: 0,
    },
    languageid: {
      type: Number,
      default: "",
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const state = reactive({
      readOnly: true,
      showsenior: false,
      editForm: {
        languageId: 0,
        languageName: "",
        level: 0,
        levelName: "",
        readLevel: 0,
        readLevelName: "",
        resumeId: props.resumeid,
        writeLevel: 0,
        writeLevelName: "",
        id: "1",
      },
      languageList: [], //语言字典
      columns: [], //语言能力等级
      englishColumns: [], //英语能力等级
      mixedAbility: [], //综合能力切换
      disabledDate(time: any) {
        return time.getTime() > Date.now();
      },
    });
    onBeforeMount(() => {
      //获取数据
      methods.getData();
      //语言技能列表
      if (state.languageList.length < 1) {
        methods.getLanguageList();
      }
      // 非英语技能
      if (state.columns.length < 1) {
        methods.getColumns();
      }
      // 英语技能
      if (state.englishColumns.length < 1) {
        methods.getEnglishColumns();
      }
    });
    watch(
      () => state.editForm.languageId,
      (newVal, oldVal) => {
        if (state.editForm.languageId == 1013) {
          state.mixedAbility = state.englishColumns;
        } else {
          state.mixedAbility = state.columns;
        }
      }
    );
    let prevent=1;
    const methods = {
      async getData() {
        let data = {
          resumeid: props.resumeid,
          id: props.languageid,
        };
        let res: any = await getLanguage(data);
        if (res.code == 1) {
          if (res.data.languageId == 0) {
            //新建的情况下 赋值默认英语
            state.editForm = {
              languageId: 1013,
              languageName: "英语",
              level: 652,
              levelName: "一般",
              readLevel: 751,
              readLevelName: "一般",
              resumeId: props.resumeid,
              writeLevel: 751,
              writeLevelName: "一般",
              id: props.languageid,
            };
            
          } else {
            state.editForm = res.data;
          }
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //保存--工作
      async saveData() {
        if(prevent===2){
          return false
        }
        let form = state.editForm;
        prevent=2;
        let res: any = await saveLanguage(form);
        prevent=1;
        if (res.code == 1) {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          store.commit("editorShow", 0);
          emit("handleRtn", 1);
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //获取语言字典
      async getLanguageList() {
        let res: any = await getLanguageoptions("");
        state.languageList = res.data;
      },
      //获取语言能力等级
      async getColumns() {
        let res: any = await getOtherlanguageleveloptions("");
        state.columns = res.data;
      },
      //获取英语能力等级
      async getEnglishColumns() {
        let res: any = await getEnglishoptions("");
        state.englishColumns = res.data;
        if (state.editForm.languageId == 1013) {
          state.mixedAbility = state.englishColumns;
        } else {
          state.mixedAbility = state.columns;
        }
      },
    };
    const fun = {
      cancel() {
        store.commit("editorShow", 0);
        emit("handleRtn", 2);
      },
      // 保存数据
      onSubmit() {
        //  验证必填项是否为空
        methods.saveData();
      },
      selectlanguageName(keywordID: number) {
        state.editForm.languageId = keywordID;
        if (keywordID !== 1013) {
          state.editForm.level = 751;
          state.editForm.levelName = "一般";
        }
      },
    };
    return { ...toRefs(state), ...fun };
  },
});
</script>
<style lang="less">
.languageComponent {
  .li-pop {
    .el-form-item__label {
      text-align: left;
    }
    .el-form {
      .fr {
        margin-right: 0;
      }
    }
    .btn-end {
      padding: 40px 0 0 0;
      margin-bottom: 0;
    }
  }
}
</style>