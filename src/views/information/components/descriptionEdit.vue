<template>
  <div class="li-pop">
    <div class="edit-unify">
      <el-form
        :model="editForm"
        class="demo-form-inline"
        label-width="100px"
        :rules="rules"
        ref="ruleForm"
      >
        <el-form-item label="主题" prop="desName" >
          <el-input
            v-model="name"
            placeholder="请填写主题名称"
            class="desName-input"
            style="width: 400px;"
            :disabled="disabled"
            maxlength="30"
                  show-word-limit
          >
            <template #prepend>
              <el-select
                v-model="editForm.desName"
                placeholder="请填写主题名称"
                style="width: 120px; cursor: pointer;"
              >
                <el-option
                  v-for="(p, i) in columns"
                  :key="i"
                  :label="p.text"
                  :value="p.id"
                  @click="editForm.descType = p.id;editForm.desName = p.text"
                ></el-option>
              </el-select>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="描述" prop="desContent">
          <el-input
            v-model="editForm.desContent"
            maxlength="500"
            placeholder="出色的自我评价能使你的简历在众多求职者中脱颖而出，简明扼要的介绍你的综合素质与核心竞争力，让用人单位快速了解你…"
            show-word-limit
            type="textarea"
            class="textarea"
          />
        </el-form-item>

        <el-form-item class="btn-end" style="width: 100%">
          <el-button type="primary" @click="onSubmit" class="sub"
            >保存</el-button
          >
          <el-button @click="cancel" class="cel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  watch,
  onBeforeMount,
  Ref,
  ref,
} from "vue";
import { useStore } from "vuex";
import { saveDescription, getDescription } from "../../../http/resumeApi";
import { ElMessage } from "element-plus";
export default defineComponent({
  emits: ["handleRtn"],
  props: {
    resumeid: {
     type: Number,
      default: 0,
    },
    desId: {
      type: Number,
      default: "",
    },
  },

  setup(props: any, { emit }: any) {
    const store = useStore();
    const ruleForm: Ref<any> = ref(null);
    const state:any = reactive({
      editForm: {
        resumeId: props.resumeid,
        desId: 0,
        desName: '',
        descType: 0,
        desContent: "",
      },
      name:'',
      disabled:false,
      technologylevelList: "", //参与身份
      visible: ref(false),
      columns: [
        { text: "自定义主题", id: 0 },
        { text: "特长", id: 1 },
        { text: "兴趣爱好", id: 2 },
        { text: "自我评价", id: 3 },
        { text: "工作体会", id: 4 },
        { text: "个人荣誉", id: 5 },
      ],
    });
    watch(() => state.editForm.descType, (newVal, oldVal) => {
        if(state.editForm.descType==0){
            state.disabled=false
        }else{
          state.name='';
          state.disabled=true
        }
    })

    let rules = {
      desName: [
        {
          required: true,
          message: "请选择主题",
          trigger: "blur",
        },
      ],
      desContent: [
        {
          required: true,
          message: "请输入描述",
          trigger: "blur",
        },
      ],
    };

    onBeforeMount(() => {
      //获取数据
      if (props.desId > 0) {
        methods.getData();
      }
    });
    let prevent=1;
    const methods = {
      async getData() {
        let data = {
          resumeid: props.resumeid,
          desId: props.desId,
        };
        let res: any = await getDescription(data);
        if (res.code == 1) {
          state.editForm = res.data;
          if(res.data.descType==0){
            state.name=state.editForm.desName;
            state.editForm.desName="自主定义主题"
          }
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //保存--工作
      async saveData() {
        if(prevent===2){
          return false
        }
        let form = state.editForm;
        if(form.descType==0){
            form.desName=state.name;
        }
        prevent=2;
        let res: any = await saveDescription(form);
        prevent=1;
        if (res.code == 1) {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          store.commit("editorShow", 0);
          emit("handleRtn",1);
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
    };
    const fun = {
      cancel() {
         store.commit("editorShow", 0);
         emit("handleRtn",2);
      },
      // 保存数据
      onSubmit() {
        //  验证必填项是否为空
        ruleForm.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData();
          } else {
            // ElMessage({
            //   message: "保存失败，请填写完整信息",
            //   type: "error",
            // });
            return false;
          }
        });
      },
    };
    return { ...toRefs(state), ...fun, rules, ruleForm };
  },
});
</script>
<style lang="less">
.descriptionComponent {
  overflow: hidden;
  .li-pop {
    .el-form-item__label {
      text-align: left;
    }
    .el-form {
      .fr {
        margin-right: 0;
      }
    }
    .btn-end {
      padding: 40px 0 0 0;
      margin-bottom: 0;
    }
    .textarea {
      .el-textarea__inner {
        height: 130px;
      }
    }
  }
  .desName-input{
    width: 400px;
    .el-input-group__append, .el-input-group__prepend{
      border: 1px solid #f2f2f2;
      background: #fff;
      border-right: none;
    }
    .el-input__inner{
      border-left: none !important;
    }
    .el-select .el-input__inner{
      cursor: text;
    }
  }
}
</style>