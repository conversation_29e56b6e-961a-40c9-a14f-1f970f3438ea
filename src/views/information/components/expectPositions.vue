<template>
  <div class="expectPositions borderLine2">
    <div class="list-expect">
      <ul class="clearfix">
        <li v-if="isAdd">
          <expectPositionsEdit
            @handleRtn="handleRtn"
            :type="num"
            :resumeid="resumeid"
          ></expectPositionsEdit>
        </li>
        <li
          :class="
            showShadow == 0 ? 'bar-shadow-unify clearfix li' : 'clearfix li'
          "
          @mouseover="showShadow = 0"
          @mouseout="showShadow = 9999"
          v-if="passData.expectCareer1||passData.expectIndustry1[0]>1"
        >
          <div class="li-readOnly" v-if="addshowindex !== 1">
            <h2  :style="passData.expectCareer1Name?'':'color:#FDAA08'">{{ passData.expectCareer1Name?passData.expectCareer1Name:'请您重新选择职位分类'}}<span class="isPerfect"  v-if="passData.isExpectCareer1">待完善</span></h2>
            <div class="item">
              <span>{{ passData.expectIndustry1Names[0] }}</span>
              <el-divider
                direction="vertical"
                v-if="passData.expectIndustry1Names[1]"
              ></el-divider>
              <span>{{ passData.expectIndustry1Names[1] }}</span>
              <el-divider
                direction="vertical"
                v-if="passData.expectIndustry1Names[2]"
              ></el-divider>
              <span>{{ passData.expectIndustry1Names[2] }}</span>
            </div>
            <btnBox
              @deleteItem="deleteItem"
              @edit="edit"
              :ind="1"
              :hasdel='false'
            ></btnBox>
          </div>
          <expectPositionsEdit
            @handleRtn="handleRtn"
            :type="1"
            :resumeid="resumeid"
            v-else
          ></expectPositionsEdit>
        </li>
        <li
          :class="
            showShadow == 1 ? 'bar-shadow-unify clearfix li' : 'clearfix li'
          "
          @mouseover="showShadow = 1"
          @mouseout="showShadow = 9999"
          v-if="passData.expectCareer2||passData.expectIndustry2[0]>1"
        >
          <div class="li-readOnly" v-if="addshowindex !== 2">
            <h2 :style="passData.expectCareer2Name?'':'color:#FDAA08'">{{ passData.expectCareer2Name?passData.expectCareer2Name:'请您重新选择职位分类' }}<span class="isPerfect"  v-if="passData.isExpectCareer2">待完善</span></h2>
            <div class="item">
              <span>{{ passData.expectIndustry2Names[0] }}</span>
              <el-divider
                direction="vertical"
                v-if="passData.expectIndustry2Names[1]"
              ></el-divider>
              <span>{{ passData.expectIndustry2Names[1] }}</span>
              <el-divider
                direction="vertical"
                v-if="passData.expectIndustry2Names[2]"
              ></el-divider>
              <span>{{ passData.expectIndustry2Names[2] }}</span>
            </div>
            <btnBox
              @deleteItem="deleteItem"
              @edit="edit"
              :ind="2"
            ></btnBox>
          </div>
          <expectPositionsEdit
            @handleRtn="handleRtn"
            :type="2"
            :resumeid="resumeid"
            v-else
          ></expectPositionsEdit>
        </li>
        <li
          :class="
            showShadow == 2 ? 'bar-shadow-unify clearfix li' : 'clearfix li'
          "
          @mouseover="showShadow = 2"
          @mouseout="showShadow = 9999"
          v-if="passData.expectCareer3||passData.expectIndustry3[0]>1"
        >
          <div class="li-readOnly" v-if="addshowindex !== 3">
            <h2 :style="passData.expectCareer3Name?'':'color:#FDAA08'">{{ passData.expectCareer3Name?passData.expectCareer3Name:'请您重新选择职位分类'}}<span class="isPerfect" v-if="passData.isExpectCareer3">待完善</span></h2>
            <div class="item">
              <span>{{ passData.expectIndustry3Names[0] }}</span>
              <el-divider
                direction="vertical"
                v-if="passData.expectIndustry3Names[1]"
              ></el-divider>
              <span>{{ passData.expectIndustry3Names[1] }}</span>
              <el-divider
                direction="vertical"
                v-if="passData.expectIndustry3Names[2]"
              ></el-divider>
              <span>{{ passData.expectIndustry3Names[2] }}</span>
            </div>
            <btnBox
              @deleteItem="deleteItem"
              @edit="edit"
              :ind="3"
            ></btnBox>
          </div>
          <expectPositionsEdit
            @handleRtn="handleRtn"
            :type="3"
            :resumeid="resumeid"
            v-else
          ></expectPositionsEdit>
        </li>
      </ul>
    </div>
    <!-- 为空时的状态 -->
    <!-- <div class="empty-box" v-if="passData.expectCareer1 == 0">
      <span class="btn-add intention-add blue" @click="addItem()">
        <i class="iconfont">+</i>添加期望职位</span
      >
    </div> -->
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, computed ,defineComponent} from "vue";
import subheading from "@/views/information/components/subheading.vue";
import expectPositionsEdit from "./expectPositionsEdit.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { delcareer,saveCareer } from "../../../http/resumeApi";
import { useStore } from "vuex";
import btnBox from "./btnBox.vue";

export default defineComponent({
  components: { subheading, expectPositionsEdit,btnBox },
  emits: ["ReloadData"],
  props: {
    passData: {
      type: Object,
      default() {
        return {
          expectWorkPlaceName: [],
          expectIndustry1Names: [],
          expectIndustry2Names: [],
          expectIndustry3Names: [],
        };
      },
    },
    resumeid: {
      type: Number,
      default: 0,
    },
    num: {
      type: Number,
      default: 0,
    },
    isAdd:{
      type: Boolean,
      default: false,
    }
  },
  setup(props: any, { emit }) {
    const store = useStore();
    const state = reactive({
      readOnly: true,
      showShadow: 9999, // a 是随意的一个字母就行 不能是false
      addshowindex: 999, //打开的编辑窗口
      editForm: {} as Object,
    });
    const methods = {
      // 删除 期望职位----新的接口
      async delCareer(num: number) {
        let form = {
          resumeId: props.resumeid,
          isDelExpectCareer1: false,
          isDelExpectCareer2: false,
          isDelExpectCareer3: false
        }

        if (num == 1) {
          form.isDelExpectCareer1 = true;
        }  
        if (num == 2) {
          form.isDelExpectCareer2 = true;
        }
        if (num == 3) {
          form.isDelExpectCareer3 = true;
        }
        let res: any = await delcareer(form);
        if (res.code == 1) {
          ElMessage.success("删除成功")
          // 删除成功 重新加载数据
          emit("ReloadData",1);
        }else{
          ElMessage.error(res.message)
        }
      },

      //旧版
      // async delCareer(num: number) {
      //   let form = {
      //     expectWorkPlaceIds: props.passData.expectWorkPlaceIds,
      //     expectIndustry1: props.passData.expectIndustry1,
      //     expectIndustry2: props.passData.expectIndustry2,
      //     expectIndustry3: props.passData.expectIndustry3,
      //     expectCareer1: props.passData.expectCareer1,
      //     expectCareer2: props.passData.expectCareer2,
      //     expectCareer3: props.passData.expectCareer3,
      //     expectSalaryVisible: props.passData.expectSalaryVisible,
      //     workStatusId: props.passData.workStatusId,
      //     salary: props.passData.salary,
      //     resumeId: props.resumeid,
      //   };
      //   if (num == 2) {
      //     form.expectIndustry2 = [0, 0, 0];
      //     form.expectCareer2 = 0;
      //   }
      //   if (num == 3) {
      //     form.expectIndustry3 = [0, 0, 0];
      //     form.expectCareer3 = 0;
      //   }
      //   let res: any = await saveCareer(form);
      //   if (res.code == 1) {
      //     ElMessage.success("删除成功")
      //     // 删除成功 重新加载数据
      //     emit("ReloadData",1);
      //   }else{
      //     ElMessage.error(res.message)
      //   }
      // },    




    };
    const fun ={
    //删除--操作
     deleteItem (p:any,num: number)  {
      ElMessageBox.confirm("确定删除求职意向吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          methods.delCareer(num)
        })
        .catch(() => {
          return;
        });
    },
    // 编辑
     edit (P:any,num: number)  {
      state.num = num;
      state.addshowindex = num;
      store.commit("editorShow", 1);
    },
    // 取消---承接子组件的函数-取消 1  取消。2保存
     handleRtn  (type:any)  {
      // state.readOnly = true;
      emit("ReloadData",type);
      state.addshowindex = 999;
      store.commit("editorShow", 0);
    },
    }
    return { ...toRefs(state), ...fun };
  },
});
</script>
<style lang="less">
.expectPositions {
  .list-expect {
    .li {
      width: 100%;
      height: auto;
      .li-readOnly {
        line-height: 49px;
        width: 100%;
        height: 49px;
      }
      h2 {
        font-size: 16px;
        color: #333;
        font-weight: bold;
        float: left;
        padding: 0 24px;
        width: 300px;
      }
      .item {
        span {
          font-size: 14px;
          color: #666;
          font-weight: normal;
        }
      }
      .el-divider {
        margin: 0 15px;
      }
    }
    .borderLine2::after {
      bottom: -16px;
    }
    .isPerfect{
      font-size: 12px;
      color: #ED9020;
      background: #FFF5E8;
      padding: 2px 3px;
      margin: 0 0 0 5px;
    }
  }
  .empty-box {
    line-height: 80px;
    span.intention-add {
      cursor: pointer;
      padding: 0px 0 8px 0;
    }
  }
}
</style>