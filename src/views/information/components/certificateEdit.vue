<template>
    <div class="edit-unify li-pop">
      <el-form
        :model="editForm"
        class="demo-form-inline"
        label-width="100px"
        :rules="rules"
        ref="ruleForm"
        :inline="true"
      >
        <el-form-item label="证书名称" class="w280"  prop="certName">
          <el-input
            v-model="editForm.certName"
            placeholder="请输入证书名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="证书类别" class="w280 fr" prop="certTypeTitle">
          <el-input
            v-model="editForm.certTypeTitle"
            placeholder="请选择证书类别"
            suffix-icon="el-icon-caret-bottom"
              @click="dialogVisible=true"
          ></el-input>
           <seleCertificate
            @confirm="confirmCertificate"
            v-if="dialogVisible"
            :hideValue="editForm.certificateType"
          ></seleCertificate>
        </el-form-item>
        <el-form-item label="获得时间" class="w280" prop="getTime">
          <el-date-picker
            v-model="editForm.getTime"
            type="month"
            placeholder="请选择"
            class="w280"
            format="YYYY-MM"
            :disabled-date="disabledDate"
            value-format="YYYY年MM月"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="证书等级" class="w280 fr" prop="certTypeLevelName" v-if="editForm.levelVisible">
          <el-select
            v-model="editForm.certTypeLevelName"
            placeholder="请选择证书等级"
          >
            <el-option
              v-for="(p, index) in certificatetypelevelsList"
              :key="index"
              :label="p.keywordName"
              :value="p.keywordID"
              @click="editForm.certTypeLevel = p.keywordID"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="btn-end" style="width: 100%">
          <el-button type="primary" @click="onSubmit" class="sub"
            >保存</el-button
          >
          <el-button @click="cancel" class="cel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  watch,
  onBeforeMount,
  Ref,
  ref,
} from "vue";
import {
  getCertificatetypelevels,
} from "../../../http/dictionary";
import { useStore } from "vuex";
import seleCertificate from "@/components/seleCertificate.vue";
import { saveCertificate, getCertificate } from "../../../http/resumeApi";
import { ElMessage } from "element-plus";
export default defineComponent({
  emits: ["handleRtn"],
  components: {seleCertificate},
  props: {
    resumeid: {
      type: Number,
      default: 0,
    },
    certId: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const ruleForm: Ref<any> = ref(null);
    const state = reactive({
      showsenior: false,
      editForm: {
        certId: 0,
        resumeId: props.resumeid,
        certName: "",
        getTime: "",
        CertificateScore: "",
        certTypeTitle: "",
        certificateType: 0,
        certTypeLevel: 0,
        certTypeLevelName: "",
        levelVisible: false,
      },
      certificatetypelevelsList: "", //证书等级
      certificatelist: "", //证书类别字典
      dialogVisible:false,
      disabledDate(time: any) {
        return time.getTime() > Date.now();
      },
    });

    onBeforeMount(() => {
      //获取数据
      if (props.certId > 0) {
        methods.getData();
      }
        methods.getlevelsList();
    });
    let rules = {
      certName: [
        {
          required: true,
          message: "请输入证书名称",
          trigger: "blur",
        },
      ],
      certTypeTitle: [
        {
          required: true,
          message: "请选择证书类别",
          trigger: "change",
        },
      ],
      getTime: [
        {
        
          required: true,
          message: "请选择时间",
          trigger: "[change,blur]",
        },
      ],
      certTypeLevelName: [
        {
          required: true,
          message: "请选择证书类别",
          trigger: "blur",
        },
      ],
    };
    let prevent="1"; 
    const methods = {
      async getData() {
        let data = {
          resumeid: props.resumeid,
          certId: props.certId,
        };
        let res: any = await getCertificate(data);
        if (res.code == 1) {
          state.editForm = res.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //保存--
      async saveData() {
        if(prevent==="2"){
          return false
        }
        let form = state.editForm;
        prevent="2";
        let res: any = await saveCertificate(form);
        prevent="1";
        if (res.code == 1) {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "success",
          });
          store.commit("editorShow", 0);
          emit("handleRtn", 1);
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //获取证书等级
      async getlevelsList() {
          let result = store.state.levelsList;
          if(result.length <= 0){
              let res: any = await getCertificatetypelevels("");
              state.certificatetypelevelsList = res.data;
              store.commit("setLevelsList", res.data);
          }else{
            state.certificatetypelevelsList=result;
          }
      },
    };
    const fun = {
      cancel() {
        emit("handleRtn", 2);
        store.commit("editorShow", 0);
      },
      // 保存数据
      onSubmit() {
        //  验证必填项是否为空
        ruleForm.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData();
          } else {
            return false;
          }
        });
      },
      //接收子集传过来的数据
      confirmCertificate(data:any){
        state.dialogVisible=false
        if(!data){
            return false
        }
          state.editForm.certTypeTitle=data.title;
          state.editForm.certificateType=data.id;
          state.editForm.levelVisible=data.hasLevel;
      }
    };
    return { ...toRefs(state), ...fun, rules, ruleForm };
  },
});
</script>
<style lang="less">
.certificateComponent {
  .li-pop {
    .el-form-item__label {
      text-align: left;
    }
    .el-form {
      .fr {
        margin-right: 0;
      }
    }
    .btn-end {
      padding: 40px 0 0 0;
      margin-bottom: 0;
    }
  }
}
.el-date-editor{
  width: auto;
}
.el-date-editor.el-input, .el-date-editor.el-input__inner{
  width: auto;
}
</style>