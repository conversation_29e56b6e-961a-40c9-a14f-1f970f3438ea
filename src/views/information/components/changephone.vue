<template>
  <div class="revise-phone-pop">
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="380px"
      :before-close="handleClose"
    >
      <div class="con">
        <p class="tit">
          当前手机号码：<label>{{ phone }}</label>
        </p>
        <el-form
          :model="FormPhone"
          label-width="100px"
          class=""
          ref="FormUserB"
          :rules="rulesphone"
        >
          <el-form-item prop="phone" v-if="phoneIsAuth" label="新手机号:">
            <el-input
              v-model.number="FormPhone.phone"
              placeholder="手机号"
              :clearable="true"
              class="inpt"
              maxlength="11"
            ></el-input>
          </el-form-item>
          <el-form-item prop="smsCode" label="短信验证码:">
            <el-input
              v-model="FormPhone.smsCode"
              placeholder="短信验证码"
              :clearable="true"
              class="code"
            >
              <template #append>
                <span class="gray" v-if="send">{{ countTime }}s重新获取</span>
                <span class="blue" v-else @click="sendSmsCode">发送验证码</span>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <p class="tips">
          手机可用于登录、找回密码、接收HR来电。<br />
          验证成功后，请使用该手机号登录。
        </p>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="Confirm" :loading="loading"
            >提交</el-button
          >
          <el-button @click="handleClose">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { changephonebind } from "@/http/resumeApi";
import { watch } from "vue";
import { geetestSendCode, isregister } from "@/http/api";
import { ElMessage } from "element-plus";
import { initGeet } from "@/mixins/geetestMixin";
import {
  reactive,
  defineComponent,
  computed,
  ref,
  toRefs,
  onBeforeMount,
  Ref,
} from "vue";

export default defineComponent({
  components: {},
  emits: ["revisePhone"],
  props: {
    resumeid: {
      type: Number,
      default: 0,
    },
    phone: {
      type: String,
      default: "",
    },
    phoneIsAuth: {
      type: Boolean,
      default: true,
    },
  },
  setup(props: any, { emit }) {
    const FormUserB: Ref<any> = ref(null);
    const state = reactive({
      dialogVisible: true,
      FormPhone: {
        phone: "",
        smsCode: "",
      },
      loading: false,
      captcha: {} as any, //验证码实例
      send: false,
      clock: 0,
      countTime: 60,
      isBinding: false,
      title:props.phoneIsAuth?'修改手机':'验证手机'
    });
    const validatephone = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输入手机号码"));
      } else {
        if (!/^1[3456789]\d{9}$/.test(value)) {
          callback(new Error("请输入正确的11位手机号码"));
        } else if (!state.isBinding) {
          callback(new Error("该手机号已绑定其他账户"));
        } else {
        }
        callback();
      }
    };
    const validatecode = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输短信验证码"));
      } else {
        if (/^[0-9]\d{3,5}$/.test(value)) {
        } else {
          callback(new Error("请输入正确的短信验证码"));
        }
        callback();
      }
    };
    let rulesphone = {
      phone: [{ validator: validatephone, trigger: "blur" }],
      smsCode: [{ validator: validatecode, trigger: "blur" }],
    };
    onBeforeMount(async () => {
      state.captcha = await initGeet();
    });
    watch(
      () => state.FormPhone.phone,
      (newValue, oldValue) => {
        methods.testBinding(newValue);
      }
    );
    const methods = {
      async initGeet() {
        state.captcha = await initGeet();
      },
      //极验
      async geetestValidate() {
        state.captcha.verify();
        await state.captcha.onSuccess(async () => {
          const result = state.captcha.getValidate();
          if (typeof result === 'object' && result !== null) {
            state.loading = true;
            let data = {
              geetest_challenge: result.geetest_challenge,
              geetest_validate: result.geetest_validate,
              geetest_seccode: result.geetest_seccode,
              phone: state.FormPhone.phone,
              site: 1,
            };

            let res: any = await geetestSendCode(data);
            state.loading = false;
            if (res.code == 1) {
              ElMessage.success(res.message);
              fun.countDown();
            } else if (res.code !== 1) {
              ElMessage.error(res.message);
              return;
            } else {
              ElMessage.error("验证失败，请稍后再试");

              return;
            }
          } else {
            ElMessage.error("验证失败，请稍后再试");
            return;
          }
        });
      },
      //手机号修改
      async onSubmit() {
        state.loading = true;
        let form = state.FormPhone;
        if(!state.FormPhone.phone&&!props.phoneIsAuth){
          ElMessage.error("请先获取验证码");
          return false
        }



        let data: any = await changephonebind(form);
        state.loading = false;
        // if (data.code == 1) {
        //   ElMessage.success(data.message);
        //   emit("revisePhone", state.FormPhone.phone);
        // } else {
        //   ElMessage.error(data.message);
        // }
        if (typeof data === 'object' && data !== null) {
        if (data.code == 1) {
          ElMessage.success(data.message);
          emit("revisePhone", state.FormPhone.phone);
        } else {
          ElMessage.error(data.message);
        }
      } else {
        ElMessage.error("服务器返回数据异常");
      }



      },
      //验证手机号是否绑定
      async testBinding(phones: string) {
        if (/^1[3456789]\d{9}$/.test(phones)) {
          let data: any = await isregister({ phone: phones });
          if (data.code == 1) {
            state.isBinding = true;
          } else {
            state.isBinding = false;
          }
        }else{state.isBinding = false;}
      },
    };
    const fun = {
      Confirm() {
        FormUserB.value.validate((valid: boolean) => {
          if (valid) {
            methods.onSubmit();
          }
        });
      },
      handleClose() {
        emit("revisePhone", "");
      },
      sendSmsCode() {
        if (!props.phoneIsAuth) {
          state.FormPhone.phone = props.phone;
          methods.geetestValidate();
          return false;
        }
        FormUserB.value.validateField("phone", (valid: boolean) => {
          if (!valid) {
            methods.geetestValidate();
          }
        });
      },
      countDown() {
        state.send = true;
        state.clock = window.setInterval(() => {
          state.countTime--;
          if (state.countTime < 0) {
            window.clearInterval(state.clock);
            state.countTime = 0;
            state.send = false;
            //重新初始化极验
            methods.initGeet();
          }
        }, 1000);
        state.countTime = 60;
      },
    };

    return { ...toRefs(state), ...fun, rulesphone, FormUserB };
  },
});
</script>
<style lang="less">
.revise-phone-pop {
  border-radius: 5px;
  .el-input__inner {
    border: 1px solid #f2f2f2;
  }
  .con {
    .tit {
      padding: 10px 0;
      color: #999;
      label {
        color: #333;
      }
    }
  }
  .el-dialog__body {
    padding: 0 20px;
  }
  .inpt {
    .el-input__inner {
      width: 240px;
    }
  }
  .code {
    .el-input__inner {
      border-right: none;
    }
    .el-input-group__append {
      background: #fff;
      border: 1px solid #f2f2f2;
      border-left: none;
      cursor: pointer;
    }
  }
  .dialog-footer {
    .el-button {
      height: 35px;
      line-height: 35px;
      padding: 0 0 0 0;
      width: 115px;
    }
    .el-button--primary {
      background: #457ccf;
    }
  }
  .tips {
    font-size: 12px;
    color: #f59d1d;
  }
}
</style>