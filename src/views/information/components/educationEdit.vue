<template>
    <div class="edit-unify li-pop">
      <el-form
        :model="editForm"
        class="demo-form-inline"
        label-width="100px"
        ref="ruleForm"
        :rules="rules"
      >
        <el-form-item label="就读院校" class="w400 universities clearfix" prop="school" style="width: 750px;">
          <el-select
            v-model="editForm.school"
            filterable
            placeholder="请输入院校名称"
            allow-create
            :remote-method="collegeNameText"
            :loading="loadingSEl"
            remote
          >
            <el-option
              v-for="(item, index) in collegeList"
              :key="index"
              :label="item"
              :value="item"
              class
            ></el-option>
          </el-select>

          <el-radio-group v-model="fullTimeFlag">
            <el-radio-button label="全日制" name="1" @click="editForm.fullTimeFlag = true"></el-radio-button>
            <el-radio-button label="非全日制" name="2" @click="editForm.fullTimeFlag = false"></el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="在校时间" required>
          <el-col :span="6">
            <el-form-item prop="experienceStartTime">
              <el-date-picker
                v-model="editForm.experienceStartTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w180"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col class="line" :span="3">至</el-col>
          <el-col :span="7">
            <el-form-item prop="experienceFinishTime">
              <el-date-picker
                v-model="editForm.experienceFinishTime"
                type="month"
                value-format="YYYY-MM"
                placeholder="请选择"
                class="w180"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-form-item>

        <el-form-item label="学历" class="w400" prop="education">
          <el-select v-model="editForm.education" placeholder="请选择">
            <el-option
              v-for="(p, index) in educationalList"
              :key="index"
              :label="p.keywordName"
              :value="p.keywordID"
              @click="editForm.degreeId = p.keywordID"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="专业类别" prop="specialityName" class v-if="iscollege">
          <el-input
            v-model="editForm.specialityName"
            placeholder="请选择"
            class="w400"
            @focus="showspeciality"
            readonly
          ></el-input>
          <seleSpecialty
            @confirmSpeciality="confirmSpeciality"
            v-if="dialogVisible"
            :hideValue="editForm.specialityId"
          ></seleSpecialty>
        </el-form-item>
        <el-form-item label="专业名称" class="w400" v-if="iscollege">
          <el-input v-model="editForm.specialityInputName" placeholder="请输入专业名称"></el-input>
        </el-form-item>
        <el-form-item label="专业描述" style="width: 100%" v-if="iscollege">
          <el-input
            v-model="editForm.specialityDescription"
            type="textarea"
            maxlength="500"
            show-word-limit
            clearable
            size="medium"
            class="JobDescription"
            placeholder="请输入您对此专业的描述……"
          ></el-input>
        </el-form-item>

        <!-- 校园实践盒子 -->
        <div class="practice-box">
          <div
            class="extraMessage clearfix"
            v-for="(item, index) in editForm.practiceList"
            :key="index"
          >
            <div class="li" v-if="!item.edit">
              <el-row>
                <el-col :span="8">
                  <div class="tit">{{ item.practiceName }}</div>
                </el-col>
                <el-col :span="8">
                  <div class="time">{{ item.parcticeTimeSpan }}</div>
                </el-col>
                <el-col :span="8" class="tr">
                  <span @click="edit(index)" class="btn-edit-small">
                    <i class="iconfont icon-bianji1"></i>编辑
                  </span>
                  <span @click="deleteItem(index,item)" class="btn-edit-small">
                    <i class="iconfont icon-shanchu"></i>删除
                  </span>
                </el-col>
              </el-row>
            </div>
            <div class="li-edit" v-else>
              <el-form-item label="实践名称" class="w400-suffix" :prop="(item.practiceName)" >
                <el-input v-model="item.practiceName" placeholder="请输入实践名称" maxlength="30" show-word-limit></el-input>
              </el-form-item>
              <el-form-item label="实践时间">
                <el-col :span="6">
                   <el-date-picker
                    v-model="item.beginTime"
                    type="month"
                    value-format="YYYY-MM"
                    placeholder="请选择"
                    class="w180"
                    @blur="endTimeC(item.beginTime,item.endTime,index)"
                    @change="(item.beginTime,item.endTime,index)"
                  ></el-date-picker>
                </el-col>
                <el-col class="line" :span="2">至</el-col>
                <el-col :span="11">
                  <el-date-picker
                    v-model="item.endTime"
                    type="month"
                    value-format="YYYY-MM"
                    placeholder="请选择"
                    class="w180"
                    @blur="endTimeC(item.beginTime,item.endTime,index)"
                    @change="(item.beginTime,item.endTime,index)"
                  ></el-date-picker>
                </el-col>
              </el-form-item>
              <el-form-item label="实践描述">
                <el-input
                  v-model="item.practiceDescription"
                  type="textarea"
                  maxlength="500"
                  show-word-limit
                  clearable
                  size="medium"
                  class="JobDescription"
                  placeholder="请描述这个实践的经过、收获、感想等……"
                ></el-input>
                <div class="othersWrite" v-if="index == 0">
                  <el-popover v-model:visible="visible" placement="bottom" :width="451">
                    <template #reference>
                      <span @click="visible = true" class="how">别人怎么写？</span>
                    </template>
                    <div class="describe-popover">
                      <h2 class="tit">实践描述</h2>
                      <p class="doc-p">实践经历应提供与实践内容相关的细节，以便HR快速了解您之前的实践经历。</p>
                      <p class="li">例：</p>
                      <p>1.在校创办了计算机协会，在其中担任计算机协会副会长</p>
                      <p>2.在校期间参与了摄影协会，在其中负责协会招新工作</p>
                      <p>3.暑假期间在某专卖店担任销售专员，完成了店长制定的销售工作</p>
                      <p>4.参与2014年元旦晚会筹备组工作，负责现场秩序指挥</p>
                      <p>5.撰写论文期间参与了某项目的开发维护，负责数据库维护工作</p>
                    </div>
                  </el-popover>
                </div>
              </el-form-item>
              <span @click="deleteItem(index,item)" class="btn-del">
                <i class="iconfont icon-shanchu"></i>删除
              </span>
            </div>
          </div>
        </div>
        <div class="tl btn-add">
          <el-button
            plain
            type="primary"
            icon="el-icon-circle-plus-outline"
            @click="addPractice"
            class="btn"
          >添加实践经历</el-button>
        </div>
        <el-form-item class="btn-end" style="width: 100%">
          <el-button type="primary" @click="onSubmit" class="sub">保存</el-button>
          <el-button @click="cancel" class="cel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  ref,
  Ref,
  defineComponent,
  watch,
  computed,
  onMounted,
  onBeforeMount,
} from "vue";
import { getEducation, saveEducation ,edupractice} from "../../../http/resumeApi";
import { getDegreeoptions } from "../../../http/dictionary";
import { searchcollege } from "../../../http/searchAPI";
import { ElMessage,ElMessageBox } from "element-plus";
import seleSpecialty from "@/components/seleSpecialty.vue";
import { useStore } from "vuex";

export default defineComponent({
  emits: ["handleRtn"],
  props: {
    resumeid: {
      type: Number,
      default: 0,
    },
    educationid: {
      type: Number,
      default: "",
    },
  },

  components: { seleSpecialty },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const ruleForm: Ref<any> = ref(null);
    const state = reactive({
      readOnly: true,
      editForm: {
        experienceStartTime: "",
        school: "",
        education: "",
        degreeId: 0,
        experienceFinishTime: "",
        fullTimeFlag: true,
        id: 0,
        specialityDescription: "",
        specialityInputName: "",
        specialityName: "",
        specialityId: 0,
        resumeId: props.resumeid,
        practiceList: [],
      } as any,
      fullTimeFlag: "" as any,
      showsenior: false,
      educationalList: "", //学历字典列表
      collegeList: "", //x搜索到的学校 列表
      visible: ref(false),
      showInd: 0,
      dialogVisible: false, //专业类别的弹窗
      iscollege: true,
      loadingSEl: false,
    });
    let rules = {
      school: [
        {
          required: true,
          message: "请填写就读院校",
          trigger: "blur",
        },
      ],
      specialityName: [
        {
          required: true,
          message: "请选择专业类别",
          trigger: "change",
        },
      ],
      education: [
        {
          required: true,
          message: "请选择学历",
          trigger: "change",
        },
      ],
      experienceFinishTime: [
        {
          type: "date",
          required: true,
          message: "请选择结束时间",
          trigger: "change",
        },
      ],
      experienceStartTime: [
        {
          type: "date",
          required: true,
          message: "请选择开始时间",
          trigger: "change",
        },
      ],
      practiceName0:[
      {
          required: true,
          message: "请填写实践名称",
          trigger: "change",
        },
      ]

    };
    watch(
      () => state.editForm.fullTimeFlag,
      (newValue, oldValue) => {
        state.fullTimeFlag = state.editForm.fullTimeFlag
          ? ref("全日制")
          : ref("非全日制");
      }
    );
    watch(
      () => state.editForm.degreeId,
      (newValue, oldValue) => {
        state.iscollege = newValue > 353 ? true : false;

      }
    );
    let prevent=1
    const methods = {
      //获取工作数据
      async getData() {
        let data = {
          resumeid: props.resumeid,
          educationid: props.educationid,
        };
        let res: any = await getEducation(data);
        if (res.code == 1) {
          state.editForm = res.data;
          state.showsenior = res.data.higherUp;
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      //获学历字典
      async GETeducationalList() {
        let result = store.state.educationalList;
        if (result.length <= 0) {
          let res: any = await getDegreeoptions("");
          state.educationalList = res.data;
          store.commit("setEducationalList", res.data);
        } else {
          state.educationalList = result;
        }
      },

      // 就读学校--搜索
      async getCollegeNameText(text: any) {
        let data: any = await searchcollege(text);
        if (data.code == 1 && data.data.count > 0) {
          state.collegeList = data.data.result;
        }
      },

      //保存--教育经历
      async saveData() {
        if(prevent==2){
          return false
        }
        let arr: any = [];
        if (state.editForm.practiceList.length > 0) {
          let att = state.editForm.practiceList.map((item: any) => {
            (item.practiceId = item.practiceID),
              (item.experienceId = props.educationid);
            return item;
          });
          arr = att;
        }
        let form = {
          resumeId: props.resumeid,
          education: {
            id: state.editForm.id,
            school: state.editForm.school,
            experienceStartTime: state.editForm.experienceStartTime,
            experienceFinishTime: state.editForm.experienceFinishTime,
            degreeId: state.editForm.degreeId,
            fullTimeFlag: state.editForm.fullTimeFlag,
            specialityId: state.editForm.specialityId,
            specialityInputName: state.editForm.specialityInputName,
            specialityDescription: state.iscollege?state.editForm.specialityDescription:"",
          },
          practices: arr,
        };
        prevent=2
        let res: any = await saveEducation(form);
        prevent=1
        if (res.code == 1) {
          ElMessage.success(res.message);
          store.commit("editorShow", 0);
          emit("handleRtn", 1);
          
        } else {
          ElMessage.error(res.message);
        }
        
      },
    };
    onBeforeMount(() => {
      //获取数据
      if (props.educationid > 0) {
        methods.getData();
      }
      // 获取学历字典
      methods.GETeducationalList();
    });
    const fun = {
      cancel() {
        emit("handleRtn", 2);
        store.commit("editorShow", 0);
      },
      onSubmit() {
        if (state.editForm.experienceFinishTime < state.editForm.experienceStartTime) {
          ElMessage.warning("在校时间的结束时间不能早于开始时间");
          return false;
        }
        ruleForm.value.validate((valid: boolean) => {
          if (!valid) {
            return false;
          }
          let a=1
          state.editForm.practiceList.forEach((i:any,index:number) => {
            if(!i.practiceName){
              ElMessage.warning(`请填写完整第`+(index+1)+`个实践的实践名称`);
              a=2
              return false;
            }
            if(!i.beginTime){
              ElMessage.warning(`请选择第`+(index+1)+`个实践开始时间`);
              a=2
              return false;
            }
            if(!i.endTime){
              ElMessage.warning(`请选择第`+(index+1)+`个实践结束时间`);
              a=2
              return false;
            }
            if(i.beginTime>i.endTime){
              ElMessage.warning(`第`+(index+1)+`个实践的结束时间不得早于开始时间`);
              a=2
              return false;
            }
            
          });
          if( a!=2){
            methods.saveData();
          }
        });
      },
      //删除在校实践经历
      async  deleteItem(index: number,p:any) {
        ElMessageBox.confirm("确定删除该项教育实践吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let form = {
          resumeid: props.resumeid,
          practiceid: p.practiceID,
          eduid:0
        }
        let res: any = await edupractice(form)
          state.editForm.practiceList.splice(index, 1);
        })
        .catch(() => {
          return;
        });
        
      },
       collegeNameText(query: string) {
        if (query) {
          state.loadingSEl = true
          setTimeout(() => {
            methods.getCollegeNameText(query);
            state.loadingSEl = false
          }, 200)
        } else {
        }
      },
      //新增教育实践
      addPractice() {
        let arr = {
          beginTime: "",
          endTime: "",
          practiceDescription: "",
          practiceID: 0,
          practiceName: "",
          edit: true,
        };
        state.editForm.practiceList.push(arr);
      },
      edit(index: number) {
        state.editForm.practiceList[index].edit = true;
      },
      showspeciality() {
        state.dialogVisible = true;
      },
      //接收子集传来的数据---专业类别选择
      confirmSpeciality(data: any) {
        state.dialogVisible = false;
        if (data) {
          state.editForm.specialityName = data.name;
          state.editForm.specialityId = data.id;
          if (!state.editForm.specialityInputName) {
            state.editForm.specialityInputName = data.name;
          }
        }
      },
      endTimeC(timeS:string,timeE:string,index:number){
        
        if(timeS&&timeE&&(timeS>timeE)){
          ElMessage.warning("实践结束时间不能早于实践开始时间");
          state.editForm.practiceList[index].endTime = '';

        }
      }
    };
    return { ...toRefs(state), ...fun, rules, ruleForm };
  },
});
</script>

<style lang="less">
.educationComponent {
  padding-bottom: 24px;
  .li-pop {
    .el-form-item__label {
      width: 90px;
      text-align: left;
    }
    .el-form {
      .fr {
        margin-right: 0;
      }
    }
    .JobDescription {
      .el-textarea__inner {
        height: 130px;
      }
    }
    .btn-add {
      margin: 15px 0 0 100px;
    }
    .btn {
      color: #457ccf;
      background: #fff;
      border: 1px solid #457ccf;
      font-size: 14px;
    }
    .extraMessage {
      padding: 0px 0 12px 0;
      position: relative;
      .btn-del {
        position: absolute;
        top: 12px;
        right: 0px;
        font-size: 13px;
        color: #457ccf;
        cursor: pointer;
        i {
          padding-right: 5px;
        }
      }
      .li {
        width: 100%;

        height: 83px;
        line-height: 83px;
        .tit {
          font-size: 18px;
          color: #333;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .time {
          font-size: 14px;
          color: #bbb;
        }
        .btn-edit-small {
          cursor: pointer;
          font-size: 13px;
          color: #457ccf;
          margin-left: 20px;
        }
      }
      .li-edit {
        width: 100%;
        position: relative;
      }
    }
    .practice-box {
      position: relative;
    }
    .practice-box::before {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      right: -24px;
      top: -8px;
      left: -24px;
      border-bottom: 1px solid #f2f2f2;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
    }
    .extraMessage::after {
      position: absolute;
      box-sizing: border-box;
      content: " ";
      pointer-events: none;
      right: -24px;
      bottom: 8px;
      left: -24px;
      border-bottom: 1px solid #f2f2f2;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
    }

    .line {
      font-size: 15px;
      color: #666;
      padding: 0 12px 0 8px;
    }
    .universities {
      .el-select {
        float: left;
      }
      .el-radio-group {
        float: left;
        margin-left: 16px;
        .el-radio-button {
          .el-radio-button__inner {
            height: 34px;
            line-height: 34px;
            border: 1px solid #f2f2f2;
            background: #ffffff;
            color: #999999;
            padding: 0px 20px;
            margin-right: 12px;
            border-radius: 2px 2px 2px 2px;
          }
        }
        .is-active {
          .el-radio-button__inner {
            height: 34px;
            line-height: 34px;
            border: 1px solid #5f9efc;
            background: #f2f7ff;
            color: #457ccf;
            padding: 0px 20px;
            margin-right: 12px;
            border-radius: 2px 2px 2px 2px;
          }
        }
      }
    }
  }
  .el-input__inner{
    cursor: revert;
  }
}
</style>