<template>
  <div class="password-page">
    <TopMenu :index="1" :isInformation="true" />
    <div class="bg-white">
      <div class="tit">
        <h2>密码管理</h2>
        <p>
          定期更换密码可以让您的账户更加安全
          <el-button type="text" @click="setPasswordDialogVisible = true">如何设置安全密码？</el-button>
        </p>
      </div>
      <!-- 已设置密码1 -->
      <div class="con" v-if="identity == 1">
        <el-form :model="FormA" :rules="rulesA" ref="ruleFormA" status-icon label-width="90px" label-position="left"
          class="demo-ruleForm">
          <el-form-item label="当前密码" prop="oldPass">
            <el-input v-model="FormA.oldPass" type="password" show-password autocomplete="off"
              placeholder="请输入当前密码"></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPass" class="passwordStrength">
            <el-input v-model="FormA.newPass" type="password" show-password placeholder="请输入新密码"></el-input>
            <div class="strengthBox">
              <div class="level" :style="{
      'background-color':
        score == 15
          ? '#FC5F76'
          : score >= 25 && score < 65
            ? '#FF9900'
            : score == 75
              ? '#33CC00'
              : '#eeeeee',
    }"></div>
              <div class="level" :style="{
      'background-color':
        score >= 25 && score < 65
          ? '#FF9900'
          : score == 75
            ? '#33CC00'
            : '#eeeeee',
    }"></div>
              <div class="level" :style="{
      'background-color': score == 75 ? '#33CC00' : '#eeeeee',
    }"></div>
            </div>
          </el-form-item>
          <el-form-item label="确认新密码" prop="checkPass">
            <el-input v-model="FormA.checkPass" type="password" show-password autocomplete="off"
              placeholder="请再次输入新密码"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm" class="confirmBtn">确认修改</el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- //未设置密码 2-->
      <div class="con" v-else>
        <el-form ref="ruleFormB" :model="FormB" :status-icon="false" :rules="rulesB" label-width="90px"
          label-position="left" class="demo-ruleForm">
          <el-form-item label="手机号">{{ phone }}</el-form-item>
          <el-form-item prop="code" label="验证码">
            <el-input v-model="FormB.code" placeholder="短信验证码" clearable class="code">
              <template #append>
                <span class="gray" v-if="send">{{ countTime }}s重新获取</span>
                <span class="blue" v-else @click="sendSmsCode">发送验证码</span>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="新密码" prop="newPass" class="passwordStrength">
            <el-input v-model="FormB.newPass" type="password" show-password placeholder="请输入新密码" clearable></el-input>
            <div class="strengthBox">
              <div class="level" :style="{
      'background-color':
        score == 15
          ? '#FC5F76'
          : score >= 25 && score < 65
            ? '#FF9900'
            : score == 75
              ? '#33CC00'
              : '#eeeeee',
    }"></div>
              <div class="level" :style="{
      'background-color':
        score >= 25 && score < 65
          ? '#FF9900'
          : score == 75
            ? '#33CC00'
            : '#eeeeee',
    }"></div>
              <div class="level" :style="{
      'background-color': score == 75 ? '#33CC00' : '#eeeeee',
    }"></div>
            </div>
          </el-form-item>
          <el-form-item label="确认新密码" prop="checkPass">
            <el-input v-model="FormB.checkPass" type="password" show-password autocomplete="off" placeholder="请再次输入新密码"
              clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitFormB" class="confirmBtn">确认修改</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-dialog v-model="tipsDialogVisible" title="提示" width="30%">
      <div class="message-tips">{{ message }}</div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="tipsDialogVisible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="setPasswordDialogVisible" title="如何设置安全密码" width="700px" center>
      <div class="set-pass-box">
        <p>为了您的账户安全，在设置密码时，请参考以下建议：</p>
        <p>一、密码长度为6到16个字符；</p>
        <p>
          二、设置时使用英文字母、数字和符号的组合，如cqmdt_042，或者7756jzm#$等，尽量不要有规律；
        </p>
        <p>
          三、如果设置以下安全性过低的密码，系统都会提醒您修改密码，直至符合安全性要求：
        </p>
        <p class="ti">1、密码与会员名或电子邮件地址相同；</p>
        <p class="ti">2、全部由英文字母组成；</p>
        <p class="ti">3、全部由数字组成；</p>
        <p class="ti">4、包含6个及以上相同连续字符组成；</p>
        <p>四、定期更改密码，并做好书面记录，以免忘记。</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="setPasswordDialogVisible = false">知道了</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  onMounted,
  watch,
  ref,
  getCurrentInstance,
  toRefs,
  Ref,
  onBeforeMount,
} from "vue";
import {
  changePassword,
  changePassWordIdentity,
  getPhone,
  sendcodebyphone,
  newChangePassword,
} from "@/http/api";
import TopMenu from "@/components/TopMenu.vue";
import { ElMessage } from "element-plus";
import { initGeet } from "@/mixins/geetestMixin";

export default defineComponent({
  components: { TopMenu },
  setup() {
    const ruleFormA: Ref<any> = ref(null);
    const ruleFormB: Ref<any> = ref(null);
    const { proxy, ctx } = getCurrentInstance() as ComponentInternalInstance;
    const _this = ctx;
    const state = reactive({
      FormA: {
        newPass: "",
        checkPass: "",
        oldPass: "",
      },
      FormB: {
        code: "",
        checkPass: "",
        newPass: "",
      },

      setPasswordDialogVisible: ref(false),
      tipsDialogVisible: ref(false),
      message: "",
      identity: 1, //1为已设置密码 2为未设置密码
      phone: "网络错误，手机号无法显示",
      loading: false,
      captcha: {} as any, //验证码实例
      send: false,
      clock: 0,
      countTime: 60,
      score: 0,
    });
    let textPass =
      /^(?:(?=.*[0-9].*)(?=.*[A-Za-z].*)(?=.*[,\.#@%'!&\+\$\*\=\|\\\?\(\)/<>~\{\}\[\]"\-:;^_`].*))[,\.#@%'!&\+\$\*\=\|\\\?\(\)/<>~\{\}\[\]\-:;^_`0-9A-Za-z]{6,20}$/;
    //密码长度为6到16个字符 英文字母、数字和符号
    let textNoPass = /[^\x00-\xff]/;
    const validateOldPass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入当前密码"));
      } else {
        callback();
      }
    };
    const validateNewPass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入新密码"));
      } else if (!textPass.test(value) || textNoPass.test(value)) {
        callback(new Error("请输入密码长度为6到16个字符由英文字母、数字和英文状态下的符号组合"));
      } else {
        callback();
      }
    };
    const validateCheckPass = (rule, value, callback) => {
      //密码不能为相同数字或者连续数字！
      if (value === "") {
        callback(new Error("请再次输入新密码"));
      } else if (value !== state.FormA.newPass) {
        callback(new Error("两次输入的密码不匹配"));
      } else {
        callback();
      }
    };
    // 有手机号码的验证
    const validateCode = (rule, value, callback) => {
      let reg = /^\d{4,6}$/;
      if (value === "") {
        callback(new Error("请输入验证码"));
      } else if (!reg.test(value)) {
        callback(new Error("请输入正确的验证码"));
      } else {
        callback();
      }
    };
    const validateNewPassB = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入新密码"));
      } else if (!textPass.test(value) || textNoPass.test(value)) {
        callback(new Error("请输入密码长度为6到16个字符由英文字母、数字和英文状态下的符号组合"));
      } else {
        callback();
      }
    };
    const validateCheckPassB = (rule, value, callback) => {
      //密码不能为相同数字或者连续数字！
      if (value === "") {
        callback(new Error("请再次输入新密码"));
      } else if (value !== state.FormB.newPass) {
        callback(new Error("两次输入的密码不匹配"));
      } else {
        callback();
      }
    };

    let rulesA = {
      newPass: [{ validator: validateNewPass, trigger: "blur" }],
      checkPass: [{ validator: validateCheckPass, trigger: "blur" }],
      oldPass: [{ validator: validateOldPass, trigger: "blur" }],
    };
    let rulesB = {
      code: [{ validator: validateCode, trigger: "blur" }],
      checkPass: [{ validator: validateCheckPassB, trigger: "blur" }],
      newPass: [{ validator: validateNewPassB, trigger: "blur" }],
    };

    onBeforeMount(async () => {
      state.captcha = await initGeet();
    });
    watch(
      () => state.FormB.newPass,
      (newValue, oldValue) => {
        if (newValue.length < 6 && newValue.length > 1) {
          state.score = 15;
        } else if (newValue.length > 5) {
          let reg = RegExp(/[0-9]/gi);
          let reg2 = RegExp(/[A-Za-z]/gi);
          let reg3 = RegExp(
            /[,\.#@%'!&\+\*\=\|\\\?\(\)/<>~\{\}\[\]"\-:;^_`]/gi
          );
          let more = 0;
          if (reg.exec(newValue)) {
            more++;
          }
          if (reg2.exec(newValue)) {
            more++;
          }
          if (reg3.exec(newValue)) {
            more++;
          }
          state.score = Number(25 * more);
        } else {
          state.score = 0;
        }
      }
    );
    watch(
      () => state.FormA.newPass,
      (newValue, oldValue) => {
        if (newValue.length < 6 && newValue.length > 1) {
          state.score = 15;
        } else if (newValue.length > 5) {
          let reg = RegExp(/[0-9]/gi);
          let reg2 = RegExp(/[A-Za-z]/gi);
          let reg3 = RegExp(
            /[,\.#@%'!&\+\*\=\|\\\?\(\)/<>~\{\}\[\]"\-:;^_`]/gi
          );
          let more = 0;
          if (reg.exec(newValue)) {
            more++;
          }
          if (reg2.exec(newValue)) {
            more++;
          }
          if (reg3.exec(newValue)) {
            more++;
          }
          state.score = Number(25 * more);
        } else {
          state.score = 0;
        }
      }
    );

    onMounted(() => {
      methods.getData();
    });

    const methods = {
      async initGeet() {
        state.captcha = await initGeet();
      },
      //极验
      async geetestValidate() {
        state.captcha.verify();
        await state.captcha.onSuccess(async () => {
          const result = state.captcha.getValidate();
          if (result != undefined) {
            state.loading = true;
            let data = {
              geetest_challenge: result.geetest_challenge,
              geetest_validate: result.geetest_validate,
              geetest_seccode: result.geetest_seccode,
            };

            let res: any = await sendcodebyphone(data);
            state.loading = false;
            if (res.code == 1) {
              ElMessage.success(res.message);
              fun.countDown();
            } else if (res.code !== 1) {
              ElMessage.error(res.message);
              return;
            } else {
              ElMessage.error("验证失败，请稍后再试");

              return;
            }
          } else {
            ElMessage.error("验证失败，请稍后再试");
            return;
          }
        });
      },
      //1-设置过密码

      async postChangePassword() {
        const res = await changePassword({
          oldPassword: state.FormA.oldPass,
          password: state.FormA.newPass,
        });
        state.tipsDialogVisible = true;
        state.message = res.message;
        methods.getData();
      },
      //2--从未设置密码
      async postSetNewPassword() {
        const res = await newChangePassword({
          code: state.FormB.code,
          password: state.FormB.newPass,
        });
        state.tipsDialogVisible = true;
        state.message = res.message;
        methods.getData();
      },
      async getData() {
        const res = await changePassWordIdentity();
        state.identity = res.data.identity || 1;
        if (res.data.identity != 1) {
          methods.getphone();
        }
      },
      async getphone() {
        const res = await getPhone();
        if (res.code == 1) {
          state.phone = res.data.phone;
        }
      },
    };
    const fun = {
      submitForm() {
        ruleFormA.value.validate((valid: boolean) => {
          if (valid) {
            methods.postChangePassword();
          } else {
            return false;
          }
        });
      },
      submitFormB() {
        ruleFormB.value.validate((valid: boolean) => {
          if (valid) {
            methods.postSetNewPassword();
          } else {
            return false;
          }
        });
      },
      sendSmsCode() {
        methods.geetestValidate();
      },
      countDown() {
        state.send = true;
        state.clock = window.setInterval(() => {
          state.countTime--;
          if (state.countTime < 0) {
            window.clearInterval(state.clock);
            state.countTime = 0;
            state.send = false;
            //重新初始化极验
            methods.initGeet();
          }
        }, 1000);
        state.countTime = 60;
      },
    };

    return {
      ...toRefs(state),
      ...fun,
      rulesA,
      rulesB,
      ruleFormA,
      ruleFormB,
    };
  },
});
</script>
<style lang="less">
.password-page {
  .bg-white {
    padding: 20px 20px 50px;
    border-radius: 2px;
  }

  .tit {
    padding-bottom: 40px;

    h2 {
      height: 30px;
      line-height: 30px;
      padding-bottom: 10px;
      font-size: 18px;
    }

    p {
      color: #999999;
    }
  }

  .con {
    width: 380px;

    .el-form-item {
      margin-bottom: 30px;
    }

    .passwordStrength {
      position: relative;

      .strengthBox {
        position: absolute;
        top: 20px;
        right: -170px;
      }

      .level {
        width: 50px;
        height: 5px;
        float: left;
        margin-right: 2px;
      }
    }
  }

  .message-tips {
    font-size: 14px;
  }

  .set-pass-box {
    p {
      line-height: 28px;
    }
  }

  .confirmBtn {
    background: #457ccf;
  }

  .el-input-group__append {
    cursor: pointer;
  }
}
</style>