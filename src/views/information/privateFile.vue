<template>
  <div class="private-file-page">
    <TopMenu :index="0" :isInformation="true" />
    <div class="bg-white">
      <div class="tit">
        <el-switch
          v-model="state.value"
          @change="methods.saveData"
        />我是建档立卡贫困家庭高校毕业生
      </div>

      <div class="con">
        <div class="qa">
          <h3>什么是建档立卡贫困家庭？</h3>
          <p>
            各省（自治区、直辖市）在已有工作基础上，坚持扶贫开发和农村最低生活保障制度有效衔接，按照县为单位、规模控制、分级负责、精准识别、动态管理的原则，对每个贫困户建档立卡，建设全国扶贫信息网络系统。专项扶贫措施要与贫困识别结果相衔接，深入分析致贫原因，逐村逐户制定帮扶措施，集中力量予以扶持，切实做到扶真贫、真扶贫，确保在规定时间内达到稳定脱贫目标。
          </p>
        </div>

        <div class="qa">
          <h3>填写此项有什么用？</h3>
          <p>
            我们将为您推送招聘会信息、就业见习岗位，提供免费的就业指导服务、提供免费的职业测评、推送就业创业培训资讯等。
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, onMounted } from "vue";
import { createFileStatus, createFileStatusSave } from "@/http/api";
import { ElMessage } from "element-plus";
import TopMenu from "@/components/TopMenu.vue";

export default defineComponent({
  components: { TopMenu },
  setup() {
    const state = reactive({
      value: true,
      prevent: 1,
    });
    onMounted(() => {
      methods.getData();
    });
    const methods = {
      async getData() {
        const res = await createFileStatus();
        state.value = res.data.state;
      },

      async saveData() {
        if(state.prevent===2){
          return false
        }
        state.prevent=2
        const res = await createFileStatusSave(state.value);
        state.prevent=1
        if (res && res.code === 1) {
          ElMessage.success(res.message);
        } else if (res) {
          ElMessage.error(res.message || 'An error occurred');
        } else {
          ElMessage.error('No response received');
        }
      },
    };

    return {
      state,
      methods,
    };
  },
});
</script>
<style lang="less" >
.private-file-page {
  .bg-white {
    padding: 20px;
    .tit {
      height: 50px;
      line-height: 50px;
      border-bottom: 1px solid #f2f2f2;
      padding-bottom: 20px;
      font-size: 18px;
      font-weight: bold;
      .el-switch__core {
        margin-right: 10px;
      }
    }
    .con {
      .qa {
        padding: 20px 0;
        h3 {
          font-size: 16px;
          padding-bottom: 20px;
        }
        p {
          line-height: 26px;
          font-size: 13px;
          color: #666666;
        }
        border-bottom: 1px solid #f2f2f2;
      }
      .qa:last-child {
        border-bottom: 0;
      }
    }
  }
}
</style>