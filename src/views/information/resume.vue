<template>
  <!-- //简历编辑页 -->
  <div class="Resume-editor-main clearfix">
    <div class="side-left">
      <headr
        :resumeListinfo="resumeListinfo"
        @changeResume="ReloadData"
        :resumeID="resumeid"
        @ReloadresumeList="ReloadresumeList"
      ></headr>
      <div class="mn">
        <el-config-provider :locale="localeZH">
          <div class="white-radius">
            <baseinfo
              name="个人信息"
              id="baseinfo"
              :passData="baseInfo.baseInfo"
              :album="baseInfo.album"
              :workingState="workingState"
              :tags="baseInfo.tags"
              @ReloadData="ReloadData"
            ></baseinfo>
            <careerObject
              name="求职意向"
              id="careerObject"
              :passData="baseInfo.career"
              @ReloadData="ReloadData"
            ></careerObject>
            <work
              name="工作经历"
              id="work"
              :passData="baseInfo.work||[]"
              :projectData="baseInfo.project"
              :resumeid="resumeid"
              @ReloadData="ReloadData"
            ></work>
            <education
              name="教育经历"
              id="education"
              :passData="baseInfo.education"
              :resumeid="resumeid"
              @ReloadData="ReloadData"
            ></education>
            
          </div>
          <div class="white-radius">
            <project
              name="项目经历"
              id="project"
              :passData="baseInfo.project"
              :resumeid="resumeid"
              @ReloadData="ReloadData"
            ></project>
            <train
              name="培训经历"
              id="train"
              :passData="baseInfo.train"
              :resumeid="resumeid"
              @ReloadData="ReloadData"
            ></train>
            <technical
              name="技术能力"
              id="technical"
              :passData="baseInfo.technology"
              :resumeid="resumeid"
              @ReloadData="ReloadData"
            ></technical>
            <certificate
              name="证书职称"
              id="certificate"
              :passData="baseInfo.certificate"
              :resumeid="resumeid"
              @ReloadData="ReloadData"
            ></certificate>
            <language
              name="语言技能"
              id="language"
              :passData="baseInfo.language"
              :resumeid="resumeid"
              @ReloadData="ReloadData"
            ></language>
            <description
              name="个人描述"
              id="description"
              :passData="baseInfo.description"
              :resumeid="resumeid"
              @ReloadData="ReloadData"
            ></description>
             <computer
              name="其他技能"
              id="otherSkills"
              :passData="baseInfo.otherAbility"
              :resumeid="resumeid"
              @ReloadData="ReloadData"
            ></computer>
          </div>
        </el-config-provider>
      </div>
    </div>
    <!-- //右边菜单 -->
    <div class="side-right">
      <RightMenu :passData="completestate" :resumeName="resumeName" :talent="talentDegree"/>
    </div>
  </div>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  onBeforeMount,
  onActivated,
  onUpdated,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import { useStore } from "vuex";
import { resumelist, resumeinfo, getCompletestate } from "../../http/resumeApi";
import RightMenu from "@/views/information/components/RightMenu.vue";
import headr from "@/views/information/components/headr.vue";
import baseinfo from "@/views/information/components/baseinfo.vue";
import careerObject from "@/views/information/components/careerObject.vue";
import expectPositions from "@/views/information/components/expectPositions.vue";
import work from "@/views/information/components/work.vue";
import project from "@/views/information/components/project.vue";
import education from "@/views/information/components/education.vue";
import train from "@/views/information/components/train.vue";
import certificate from "@/views/information/components/certificate.vue";
import language from "@/views/information/components/language.vue";
import computer from "@/views/information/components/computer.vue";
import description from "@/views/information/components/description.vue";
import technical from "@/views/information/components/technical.vue";
import { ElMessage } from "element-plus";
import { ElConfigProvider } from "element-plus";
import zhCn from "element-plus/lib/locale/lang/zh-cn";
export default defineComponent({
  components: {
    headr,
    baseinfo,
    careerObject,
    RightMenu,
    expectPositions,
    work,
    education,
    project,
    train,
    certificate,
    language,
    computer,
    description,
    technical,
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    const state = reactive({
      resumeListinfo: [],
      resumeid: parseInt(`${route.params.id}`),
      baseInfo: {
        work: [],
      },
      workingState: "",
      completestate: {}, //右边菜单 简历完成度
      resumeName: "", //简历名称
      localeZH: zhCn,
      talentDegree:0,
    });
    onBeforeMount(() => {
      store.commit("editorShow", 0);
    });
    onMounted(() => {
      methods.resumeList();
      methods.getData(state.resumeid);
    });
    onActivated(() => { });
    const methods = {
      async resumeList() {
        let res: any = await resumelist("");
        if (res.code == 1) {
          state.resumeListinfo = res.data.resumeList;
        }
      },
      async getData(id: number | any) {
        let data = {
          resumeid: id,
        };
        let res: any = await resumeinfo(data);
        if (res.code == 1) {
          state.baseInfo = res.data;
          state.workingState = res.data.career.workingState;
          state.resumeName = res.data.resumeName;
          state.talentDegree=res.data.baseInfo.talentDegree;
          methods.getCompletestate(id);

          if(state.baseInfo.work){
            state.baseInfo.work.forEach((j: any) => {
            let a = 0;
            
            res.data.project.forEach((p: any) => {
              if (p.playRole == j.workId) {
                a++;
                j.hasProject = a;
              }
              else {
                j.hasProject = 0;
              }
            });
          });
          }

        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
      },
      async getCompletestate(id: number | any) {
        let res: any = await getCompletestate(id, null);
        if (res.code == 1) {
          // if(res.data.career==0){
          //      router.replace({ name: "registerStep" });
          // }
          state.completestate = res.data;
        }
      },
    };
    // 重新加载数据
    const ReloadData = () => {
      let resumeid: any =  state.resumeid;
      methods.getData(resumeid);
      // methods.resumeList();
    };
    const ReloadresumeList=()=>{
      methods.resumeList();
    }
    return { ...toRefs(state), ReloadData ,ReloadresumeList};
  },
});
</script>
<style lang="less" >
.Resume-editor-main {
  .side-right {
    width: 240px;
    float: right;
  }
  //每一项编辑弹窗的统一样式---star
  .edit-unify {
    .el-form-item__label {
      font-size: 15px;
      color: #666;
    }
    .el-input__inner {
      border: 1px solid #f2f2f2;
      height: 36px;
      font-size: 14px;
      color: #333;
    }

    .w240 {
      
      .el-input__inner {
        width: 240px;
      }
    }
    .w180 {
      .el-input__inner {
        width: 180px;
      }
    }
    .w400 {
       width: 520px;
      .el-input__inner {
        width: 400px;
      }
    }
    .w400-suffix {
       width: 520px;
      .el-input__inner {
        width: 400px;
      }
      .el-input__suffix {
        right: auto;
        left: 353px;
      }
    }

    .w280 {
      .el-input__inner {
        width: 280px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: revert;
      }
    }
    .btn-end {
      text-align: right;
      .sub {
        font-size: 16px;
        width: 128px;
        height: 45px;
        background: #457ccf;
      }
      .cel {
        font-size: 16px;
        border: 1px solid #f2f2f2;
        width: 126px;
      }
    }
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label-wrap
      > .el-form-item__label:before,
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label::after {
      content: "*";
      color: var(--el-color-danger);
      margin-right: 4px;
    }
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label-wrap
      > .el-form-item__label:before,
    .el-form-item.is-required:not(.is-no-asterisk)
      > .el-form-item__label:before {
      content: "" !important;
      color: var(--el-color-danger);
      margin-right: 0px;
    }
  }
  //编辑--删除按钮统一样式
  .btn-pop {
    font-size: 13px;
    color: #457ccf;
    position: absolute;
    top: 0px;
    right: 24px;
    display: none;
    i {
      padding-right: 5px;
    }
  }
  .btn-del-pop {
    right: 98px;
  }
  .btn-edit-pop {
    right: 24px;
  }
  //鼠标经过统一的阴影
  .bar-shadow-unify {
    position: relative;
    box-shadow: 0 0 20px #5a687e20;
    .btn-pop {
      display: block;
    }
  }
  .edit-unify {
    background: #fafafa;
    padding: 24px;
  }
  .padding24 {
    padding: 24px;
  }
  // 各模块列表统一样式
  .list-unify {
    li.item {
      // margin-bottom: 30px;
      padding: 20px 24px;
      .edit-unify{
        width: 100%;
      }
    }
    h1 {
      font-size: 18px;
      color: #333;
      font-weight: bold;
      label {
        font-size: 14px;
        color: #bbb;
        padding-left: 16px;
        font-weight: normal;
      }
    }
    h2 {
      font-size: 16px;
      color: #333;
      font-weight: normal;
    }
    .info {
      padding: 5px 0 15px 0;
      span {
        color: #666;
        font-size: 14px;
      }
      .el-divider {
        background-color: #f2f2f2;
        margin: 0 10px;
      }
    }
    .doc {
      color: #666;
      font-size: 14px;
      line-height: 26px;
      white-space: break-spaces;
      word-break: break-all;
    }
  }
  .el-divider {
    background-color: #f2f2f2;
  }
  .el-form-item__error {
    left: 15px;
  }
  .el-divider--vertical {
    height: 10px;
  }
  // ````````统一样式-end
  .borderLine2{
    padding-bottom: 25px;
  }
}
//项目描述模板样式
.othersWrite {
  top: 120px;
  left: 0px;
  .how {
    font-size: 12px;
    color: #457ccf;
    cursor: pointer;
  }
}
.describe-popover {
  width: 450;
  h2.tit {
    font-size: 14px;
    color: #333333;
    padding: 10px 0 10px 0;
  }
  p {
    font-size: 12px;
    color: #666666;
    line-height: 24px;
  }
  p.li {
    font-size: 13px;
    color: #457ccf;
    padding: 5px 0;
  }
  p.doc-p {
    color: #999;
  }
}
</style>