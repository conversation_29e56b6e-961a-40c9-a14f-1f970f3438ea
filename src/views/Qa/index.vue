<template>
  <div class="page-feed">
    <el-header class="feed-common">
      <div class="w1200 clearfix">
        <div class="logo">
          <Logo :bid="bid" />
        </div>

        <div class="sdr">
          <ul class="clearfix">
            <li class="blue">企业服务热线：400-0771-056</li>
            <li>
              <a class href="/Login" target="_blank">人才会员</a>
            </li>
            <li>
              <a class href="//vip.gxrc.com/Login" target="_blank">企业登陆</a>
            </li>
            <li>
              <a class href="//www.gxrc.com/About/Bank" target="_blank"
                >联系我们</a
              >
            </li>
            <li>
              <a class href="//www.gxrc.com/About/Index" target="_blank"
                >业务办理指南</a
              >
            </li>
          </ul>
        </div>
      </div>
    </el-header>
    <el-main class="feed-el-main" >
      <div class="bg">
        <div class="w1200">
          <h3>用户意见反馈</h3>
          <p>
            您好，我是gxrc.com的网站管理员，欢迎您给我们提供在使用产品过程中遇到的问题和建议！
          </p>
        </div>
      </div>
      <div class="w1200">
        <div class="table-box bg-white">
          <el-form
            :model="editForm"
            label-width="120px"
            label-position="right"
            :rules="rules"
            ref="ruleForm"
            v-if="opinetitle=='网站问题'"
          >
            <el-form-item label="主题" class="radio">
              <div class="change-box">
                <div class="cell pitchOn" @click="goReport('网站问题')">
                  网站问题
                </div>
                <div class="cell" @click="goReport('失信举报')">失信举报</div>
              </div>
            </el-form-item>
            <div>
              <el-form-item label="用户名/邮箱地址" class="w300">
                <el-input
                  v-model="editForm.email"
                  type="text"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="联系人" class="w300">
                <el-input v-model="editForm.userName" disabled></el-input>
              </el-form-item>
              <el-form-item label="手机号码" class="w300" prop="phone">
                <el-input
                  v-model="editForm.phone"
                  type="text"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="验证码" class="w550" prop="captchaCode">
                <el-input
                  v-model="editForm.captchaCode"
                  placeholder="请输入右侧图片验证码"
                  :clearable="true"
                  class="code"
                >
                  <template #append>
                    <img
                      :src="'data:image/jpeg;base64,' + security"
                      @click="getcaptcha()"
                    />
                  </template>
                </el-input>
              </el-form-item>
            </div>

            <el-form-item label="内容" class="w780" prop="content">
              <el-input
                v-model="editForm.content"
                type="textarea"
                maxlength="300"
                show-word-limit
                clearable
                size="medium"
                class="JobDescription"
                placeholder="输入您的反馈意见，最多能输入300个字…"
              ></el-input>
            </el-form-item>
            <el-form-item label="上传文件">
              <div class="upload-wrap">
                <el-upload
                  class="upload-demo"
                  :on-remove="handleRemove"
                  :before-remove="beforeRemove"
                  multiple
                  action="https://my.gxrc.com/Qa/UploadImages"
                  :limit="2"
                  accept="image/*"
                  :on-exceed="handleExceed"
                  :file-list="fileList"
                  :http-request="upload"
                  :before-upload="beforeUpload"
                  :on-change="handleChange"
                >
                  <el-button type="primary" class="selectbtn"
                    >选择文件</el-button
                  >
                </el-upload>
              </div>
              <p class="tips">
                请选择大于20KB，小于300KB的jpg、png、gif图片，最多同时上传2张
              </p>
            </el-form-item>
            <el-form-item class="btn-end">
              <el-button type="primary" @click="onSubmit" class="sub"
                >保存</el-button
              >
            </el-form-item>
          </el-form>
          <!-- 分割线666666666666666666666666666失信举报666666666666666666666666666666分割线 -->
          <el-form
            :model="editForm2"
            label-width="120px"
            label-position="right"
            :rules="rulesB"
            ref="ruleFormB"
            v-else
          >
            <el-form-item label="主题" class="radio">
              <div class="change-box">
                <div class="cell" @click="goReport('网站问题')">
                  网站问题
                </div>
                <div class="cell pitchOn" @click="goReport('失信举报')">失信举报</div>
              </div>
            </el-form-item>
            <div>
              <el-form-item label="举报职位" class="w300" prop="positionName">
                <el-input
                  v-model="editForm2.positionName"
                  :disabled="disabledpositionName"
                ></el-input>
              </el-form-item>
              <el-form-item label="举报公司" class="w300" prop="enterpriseName">
                <el-input
                  v-model="editForm2.enterpriseName"
                  :disabled="disabledenterpriseName"
                ></el-input>
              </el-form-item>
              <el-form-item label="举报类型" class="w300">
                <el-input v-model="editForm2.opineTitle" disabled></el-input>
              </el-form-item>
            </div>

            <el-form-item label="内容" class="w780" prop="opineContent">
              <el-input
                v-model="editForm2.opineContent"
                type="textarea"
                maxlength="300"
                show-word-limit
                clearable
                size="medium"
                class="JobDescription"
                placeholder="输入您的反馈意见，最多能输入300个字…"
              ></el-input>
            </el-form-item>
            <el-form-item label="上传文件">
              <div class="upload-wrap">
                <el-upload
                  class="upload-demo"
                  :on-remove="handleRemove2"
                  :before-remove="beforeRemove"
                  multiple
                  action="https://my.gxrc.com/Qa/UploadImages"
                  :limit="2"
                  accept="image/*"
                  :on-exceed="handleExceed"
                  :file-list="fileList2"
                  :http-request="upload"
                  :before-upload="beforeUpload"
                  :on-change="handleChange2"
                >
                  <el-button type="primary" class="selectbtn"
                    >选择文件</el-button
                  >
                </el-upload>
              </div>
              <p class="tips">
                请选择大于20KB，小于300KB的jpg、png、gif图片，最多同时上传2张
              </p>
            </el-form-item>
            <el-form-item class="btn-end">
              <el-button type="primary" @click="onSubmit2" class="sub"
                >保存</el-button
              >
            </el-form-item>
          </el-form>
          <div class="contact">
           <div class="contact-item">
            <div style="display: flex;">
              <img src="https://image.gxrc.com/gxrcsite/My/2024/lianxi.png" style="padding-right: 5px;" alt="">
              联系我们
            </div>
            <div class="qqchat" @click="qq">
              <i class="iconfont icon-qq2 qqicon"></i>
              QQ交谈
            </div>
           </div>
           <div class="line"></div>
           <div class="contact-item">
            <div style="display: flex;align-items: flex-start;">
              <img src="https://image.gxrc.com/gxrcsite/My/2024/wechat.png" style="padding-right: 5px;" alt="">
              <div>
                微信客服
                <div class="tip">微信扫描二维码</div>
              </div>
            </div>
            <div>
              <img src="https://image.gxrc.com/gxrcsite/zt/MobileApp/images/qzz.png" style="width: 71px;" alt="" />
            </div>
           </div>
          </div>
        </div>
        <div class="fankui">
          <ul v-if="opinetitle=='网站问题'">
            <li
              class="one bg-white"
              v-for="(item, index) in fankuiList"
              :key="index"
            >
            <div v-if="item.opineTitle==opinetitle" class="box">
              <h3>
                {{ editForm.userName }}
                <span class="date">{{ item.opineTime }}</span>
              </h3>
              <div class="wenti">
                <pre class="neirong">{{ item.opineContent }}</pre>
                <ul class="tupian" v-if="item.images != ''">
                  <li v-for="(item2, index2) in item.images" :key="index2">
                    <el-image
                      style="width: 100px; height: 100px"
                      :src="item2"
                      :preview-src-list="item.images"
                      :initial-index="index2"
                      fit="cover"
                    ></el-image>
                  </li>
                </ul>
              </div>
              <div class="huifu" v-if="item.replyTime">
                <h3 class="name">
                  {{ item.replyer }}回复
                  <span class="date">{{ item.replyTime }}</span>
                </h3>
                <pre class="neirong" v-safe-html.relaxed="item.replyContent"></pre>
                <ul class="tupian" v-if="item.replyImages != ''">
                  <li v-for="(item2, index2) in item.replyImages" :key="index2">
                    <el-image
                      style="width: 100px; height: 100px"
                      :src="item2"
                      :preview-src-list="item.replyImages"
                      :initial-index="index2"
                      fit="cover"
                    ></el-image>
                  </li>
                </ul>
              </div>
            </div>
            </li>
              
          </ul>
          <ul v-if="opinetitle=='失信举报'">
            <li
              class="one bg-white"
              v-for="(item, index) in fankuiList"
              :key="index"
            >
              <div v-if="item.opineTitle==opinetitle" class="box">
              <h3>
                {{ editForm.userName }}
                <span class="date">{{ item.opineTime }}</span>
              </h3>
              <div class="wenti">
                <div class="cel"><span class="tit">举报职位</span><span class="doc">{{item.positionName}}</span></div>
                <div class="cel"><span class="tit">举报公司</span><span class="doc">{{item.enterpriseName}}</span></div>
                <div class="cel"><span class="tit">举报类型</span><span class="doc">{{item.opineTitle}}</span></div>
                <pre class="neirong">{{ item.opineContent }}</pre>
                <ul class="tupian" v-if="item.images != ''">
                  <li v-for="(item2, index2) in item.images" :key="index2">
                    <el-image
                      style="width: 100px; height: 100px"
                      :src="item2"
                      :preview-src-list="item.images"
                      :initial-index="index2"
                      fit="cover"
                    ></el-image>
                  </li>
                </ul>
              </div>
              <div class="huifu" v-if="item.replyTime">
                <h3 class="name">
                  {{ item.replyer }}回复
                  <span class="date">{{ item.replyTime }}</span>
                </h3>
                <pre class="neirong" v-safe-html.relaxed="item.replyContent"></pre>
                <ul class="tupian" v-if="item.replyImages != ''">
                  <li v-for="(item2, index2) in item.replyImages" :key="index2">
                    <el-image
                      style="width: 100px; height: 100px"
                      :src="item2"
                      :preview-src-list="item.replyImages"
                      :initial-index="index2"
                      fit="cover"
                    ></el-image>
                  </li>
                </ul>
              </div>
            </div>
            </li>
              


          </ul>
        </div>
        
      </div>
    </el-main>
  </div>
</template>

<script lang="ts">
import {
  reactive,
  toRefs,
  defineComponent,
  Ref,
  ref,
  onBeforeMount,
  computed,
} from "vue";
import { feedbacklist, feedback, feedbackID } from "@/http/resumeApi";
import type { UploadFile } from "element-plus/es/components/upload/src/upload.type";
import Logo from "@/components/logo.vue";
import { getCookies } from "@/utils/common";
import { useRouter, useRoute } from "vue-router";
import { getSecurityImageAsync, myInfo, report } from "@/http/api";
import { baseinfo } from "@/http/resumeApi";
import { ElMessage, ElMessageBox } from "element-plus";
export default defineComponent({
  components: { Logo },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const ruleForm: Ref<any> = ref(null);
    const ruleFormB: Ref<any> = ref(null);
    interface RawFile {
      name: string;
      url: string;
    }
    const state = reactive({
      editForm: {
        email: "",
        userName: "",
        phone: "",
        captchaCode: "",
        tokenId: "",
        content: "",
        images: [],
        opinetitle: "网站问题",
        positionGuid: 0, //职位id
        positionId: 0,
        enterpriseID: 0, //企业id
        positionName: "",
        enterpriseName: "",
        remark: "PC端",
        opineType: 10,
      },
      editForm2: {
        opineTitle: "失信举报", //类型
        opineContent: "", //内容
        positionId: 0,
        enterpriseId: 0,
        positionName: "",
        enterpriseName: "",
        images: [],
        remark: "PC端", //备注
        opineType: 10,
      },
      opinetitle: ref("网站问题"),
      fankuiList: [],
      security: "",
      fileList: ref<RawFile[]>([]),
      fileList2: ref<RawFile[]>([]),
      bid: computed(() => {
        return parseInt(getCookies("bid"));
      }),
      disabledpositionName:false,
      disabledenterpriseName:false,
      qqqzkf: 3219321043
    });
    const validatephone = (rule: any, value: any, callback: any) => {
      if (value === "") {
        callback(new Error("请输入手机号码"));
      } else {
        if (/^1[3456789]\d{9}$/.test(value)) {
        } else {
          callback(new Error("请输入正确的11位手机号码"));
        }
        callback();
      }
    };
    let rules = {
      content: [
        {
          required: true,
          message: "内容不能为空",
          trigger: "change",
        },
      ],
      captchaCode: [
        {
          required: true,
          message: "验证码不能为空",
          trigger: "change",
        },
      ],
      phone: [{ validator: validatephone, trigger: "blur", required: true }],
    };
    let rulesB = {
      opineContent: [
        {
          required: true,
          message: "内容不能为空",
          trigger: "change",
        },
      ],
      positionName: [
        {
          required: true,
          message: "举报的职位不能为空",
          trigger: "blur",
        },
      ],
      enterpriseName: [
        {
          required: true,
          message: "举报的企业不能为空",
          trigger: "blur",
        },
      ],
    };
    onBeforeMount(async () => {
      if (route.query.t && route.query.t == 1) {
        state.opinetitle = "失信举报";
        state.editForm.opinetitle = "失信举报";
        if (route.query.enterpriseID && route.query.positionGuid) {
          methods.getPositionGuid(
            route.query.positionGuid,
            route.query.enterpriseID
          );
        }
      }
      methods.getfeedbacklist();
      methods.getcaptcha();
      methods.getUserInfo();
    });
    let prevent = 1;
    const methods = {
      async getUserInfo() {
        const res = await myInfo();
        if (res.code == 1) {
          methods.getDta(res.data.defaultResumeId);
        }
      },
      async getDta(id: String) {
        let res: any = await baseinfo(id, "");
        if (res.code == 1) {
          state.editForm.phone = res.data.firstContact;
          state.editForm.userName = res.data.name;
          state.editForm.email = res.data.email;
        }
      },
      async getfeedbacklist() {
        const res = await feedbacklist();
        if (res.code == 1) {
          state.fankuiList = res.data.items;
        }
      },
      //获取图形验证码
      async getcaptcha() {
        let data: any = await getSecurityImageAsync("");
        state.security = data.data.image;
        state.editForm.tokenId = data.data.token;
      },
      //获取公司名称
      async getPositionGuid(Gid: any, eid: any) {
        let res: any = await feedbackID({
          positionguid: Gid,
          enterpriseid: eid,
        });
        if (res.code == 1) {
          state.editForm2.enterpriseId = res.data.enterpriseId;
          state.editForm2.enterpriseName = res.data.enterpriseName;
          state.editForm2.positionId = res.data.positionId;
          state.editForm2.positionName = res.data.positionName;
          state.disabledpositionName=res.data.positionName?true:false
          state.disabledenterpriseName=res.data.enterpriseName?true:false
        } else {
          ElMessage.error("找不到该公司，请您手动填写");
        }
      },
      //  保存
      async saveData() {
        if (prevent === 2) {
          return false;
        }
        let form = {
          opinetitle: state.editForm.opinetitle,
          email: state.editForm.email,
          userName: state.editForm.userName,
          phone: state.editForm.phone,
          content: state.editForm.content,
          captchaCode: state.editForm.captchaCode,
          tokenId: state.editForm.tokenId,
          images: [],
          remark: "PC端",
        };
        if (state.fileList[0] != undefined) {
          form.images.push("data:image/jpeg;base64," + state.fileList[0].url);
        }
        if (state.fileList[1] != undefined) {
          form.images.push("data:image/jpeg;base64," + state.fileList[1].url);
        }
        prevent = 2;
        let res: any = await feedback(form);
        if (res.code == 1) {
          ElMessageBox.confirm(
            "提交成功，感谢您的反馈！您可以在本页查看是否有回复。",
            "温馨提示",
            {
              confirmButtonText: "确定",
              showCancelButton: false,
              type: "success",
            }
          )
            .then(() => {})
            .catch(() => {});
            state.editForm.content=''
            state.editForm.images=[];
            state.fileList=[];
          methods.getfeedbacklist();
          methods.getcaptcha();
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
        prevent = 1;
      },
      //  保存
      async saveData2() {
        if (prevent === 2) {
          return false;
        }
        let form = state.editForm2;
        if (state.fileList2[0] != undefined) {
          form.images.push("data:image/jpeg;base64," + state.fileList2[0].url);
        }
        if (state.fileList2[1] != undefined) {
          form.images.push("data:image/jpeg;base64," + state.fileList2[1].url);
        }
        prevent = 2;
        let res: any = await report(form);
        if (res.code == 1) {
          ElMessageBox.confirm(
            "提交成功，感谢您的反馈！您可以在本页查看是否有回复。",
            "温馨提示",
            {
              confirmButtonText: "确定",
              showCancelButton: false,
              type: "success",
            }
          )
            .then(() => {})
            .catch(() => {});
            state.editForm2.opineContent='';
            state.editForm2.images=[];
            state.fileList2=[];
          
          methods.getfeedbacklist();
        } else {
          ElMessage({
            showClose: true,
            message: res.message,
            type: "error",
          });
        }
        prevent = 1;
      },
    };
    const fun = {
      onSubmit() {
        ruleForm.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData();
          }
        });
      },
      onSubmit2() {
        ruleFormB.value.validate((valid: boolean) => {
          if (valid) {
            methods.saveData2();
          }
        });
      },
      getcaptcha() {
        methods.getcaptcha();
      },
      upload(file: UploadFile) {},
      beforeUpload(file) {
        if (file.size > 500000) {
          ElMessage.warning(`图片大小不能超过500kb`);
          return false;
        }
      },
      handleChange(file, fileList) {
        const reader = new FileReader();
        reader.onload = function (e) {
          let ary = reader.result.match(/data\:image\/\w+\;base64\,/);
          let len = ary[0].length;
          let url = reader.result.substr(len);
          state.fileList.push({ name: file.name, url: url });
        };
        reader.readAsDataURL(file.raw);
      },
      handleChange2(file, fileList) {
        const reader = new FileReader();
        reader.onload = function (e) {
          let ary = reader.result.match(/data\:image\/\w+\;base64\,/);
          let len = ary[0].length;
          let url = reader.result.substr(len);
          state.fileList2.push({ name: file.name, url: url });
        };
        reader.readAsDataURL(file.raw);
      },
      handleRemove(file: UploadFile, fileList: UploadFile[]) {
        state.fileList = fileList;
      },
      handleRemove2(file: UploadFile, fileList: UploadFile[]) {
        state.fileList2 = fileList;
      },
      handleExceed(files: FileList, fileList: UploadFile[]) {
        ElMessage.warning(`最多同时上传2张图片`);
      },
      beforeRemove(file: UploadFile, fileList: UploadFile[]) {
        if (file.size > 500000) {
          return ElMessageBox.confirm(
            `图片大小不能超过500kb，请删除 ${file.name}`
          );
        } else {
          return ElMessageBox.confirm(`确定删除 ${file.name} ?`);
        }
      },
      goReport(name: string) {
        state.opinetitle = name;
      },
      qq(){
        window.open(`http://wpa.qq.com/msgrd?v=3&uin=${state.qqqzkf}&site=qq&menu=yes`);
      }
    };

    return {
      ...toRefs(state),
      ...fun,
      rules,
      rulesB,
      ruleForm,
      ruleFormB,
    };
  },
});
</script>
<style lang="less">
.feed-common {
  background: #fff;
  width: 100%;
  height: 60px;
  text-align: left;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
  position: fixed;
  z-index: 100;
  line-height: 60px;

  .logo {
    float: left;
    img {
      width: 150px;
      margin: 8px 15px 0 0;
    }
  }
  .sdr {
    float: right;
    li {
      float: left;
      margin-left: 40px;
      a {
        font-size: 14px;
        color: #666;
      }
    }
    li.blue {
      color: #5f9efc;
    }
  }
}
.feed-el-main {
  background: #f4f5f9;
  padding: 60px 0 0 0;
  .bg {
    height: 128px;
    background: url(@/assets/img/QAbg.png) no-repeat center center;
    h3 {
      padding: 40px 0 10px 0;
      font-size: 20px;
      color: #333;
    }
    p {
      font-size: 14px;
      color: #666;
    }
  }
  .table-box {
    padding: 32px;
    position: relative;
    .w300 {
      .el-input__inner {
        width: 300px;
        border: 1px solid #f2f2f2;
      }
    }
    .w550 {
      width: 540px;
      .el-input__inner {
        width: 300px;
        border: 1px solid #f2f2f2;
      }
      .el-input-group__append {
        padding: 0 0;
        border: 1px solid #f2f2f2;
      }
    }
    .w780 {
      .el-textarea {
        width: 780px;
        height: 140px;
      }
      .el-textarea__inner {
        height: 140px;
        border: 1px solid #f2f2f2;
      }
    }
    .el-form-item__label {
      font-size: 14px;
      color: #666;
    }
    .btn-end {
      text-align: center;
    }
    .sub {
      width: 140px;
      height: 50px;
      color: #fff;
      background: #5f9efc;
    }
    .selectbtn {
      width: 104px;
      height: 38px;
      color: #fff;
      background: #5f9efc;
    }
    .tips {
      color: #bbbbbb;
      font-size: 12px;
    }
    .change-box {
      width: 300px;
      display: flex;
      .cell {
        background: #fff;
        border: 1px solid #f2f2f2;
        color: #999999;
        width: 140px;
        height: 38px;
        border-radius: 2px;
        text-align: center;
        margin-right: 16px;
        cursor: pointer;
      }

      .pitchOn {
        background: #f2f7ff;
        border: 1px solid #5f9efc;
        color: #457ccf;
        font-size: 14px;
      }
    }
  }
  .fankui {
    margin: 20px 0 0 0;
    li.one {
      .box{
        border-bottom: 10px solid #f4f6f7;
      padding: 30px;
      }
      
      h3 {
        font-size: 16px;
        color: #457ccf;
        font-weight: normal;
      }
      .date {
        font-size: 12px;
        color: #bbbbbb;
      }
      .neirong {
        font-size: 14px;
        color: #333333;
        padding: 14px 0;
        white-space: pre-wrap;
        white-space: -moz-pre-wrap;
        white-space: -pre-wrap;
        white-space: -o-pre-wrap;
        word-wrap: break-word;
      }
      .huifu {
        background: #fafafa;
        padding: 24px 24px 0 24px;
      }
      .wenti{
        padding-top: 10px;
        .cel{
          font-size: 14px;
          padding-bottom: 5px;
          .tit{
            color: #BBBBBB;
          }
          .doc{
            color: #333;
            padding-left: 10px;
          }

        }
      }
    }
    .tupian {
      display: flex;
      padding-bottom: 20px;
      li {
        padding-right: 20px;
      }
      .el-image {
        width: 100px;
        height: 100px;
        cursor: pointer;
      }
    }
  }
  .contact{
    position: absolute;
    top: 32px;
    right: 24px;
    width: 292px;
    background: linear-gradient(134deg, #FFFFFF 0%, #EDF7FD 100%);
    border-radius: 2px;
    border: 1px solid #D8E8FF;
    padding: 16px 0;
    &-item{
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      font-size: 14px;
      padding: 0 16px;
      .qqchat{
        background: #5E9EFC;
        border-radius: 2px;
        color: #fff;
        padding: 5px 8px;
        display: flex;
        align-items: center;
        font-size: 10px;
        cursor: pointer;
        .qqicon{
          font-size: 9px;
          padding-right: 4px;
        }
      }
      .tip{
        font-size: 10px;
        color: #BBBBBB;
      }
    }
    .line{
      width: 292px;
      height: 1px;
      background: #DDEBFF;
      margin: 15px 0;
    }
  }
}
</style>