<template>
  <div class="shieldent-main">
    <subheading :step="3"></subheading>
    <div class="cont">
      <div class="title clearfix">
        <h4>企业名称屏蔽</h4>
        <span class="tips">(<i class="iconfont icon-warn"></i>选择要屏蔽的公司，被勾选的公司将无法看到您的简历。)</span>
        <el-button plain round @click="dialogVisible=true"><i class="iconfont icon-add2"></i>添加</el-button>
      </div>
      <div class="list" v-if="total>0" v-loading="loading">
        <ul>
          <li v-for="(item, ind) in list" :key="ind" class="list-one clearfix">
            <p>{{ item.enterpriseName }}</p>
            <i
              class="iconfont"
              @click="onDelete(item.enterpriseId, ind, item.enterpriseName)"
              >×</i
            >
          </li>
        </ul>
      </div>
      <el-empty description="暂无数据" v-else></el-empty>
    </div>
    <!-- <Pagination
      :pageSize="pagesize"
      :totalCount="total"
      @handlePageChange="handlePageChange"
    /> -->
    <Addshield  v-if="dialogVisible" @addConfirm="AddConfirm"/>
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, onBeforeMount, h } from "vue";
import { getHideEnterprise, deleteEnterprise } from "@/http/resumeApi";
import { useRouter, useRoute } from "vue-router";
import {  ElMessage, ElMessageBox } from "element-plus";
import subheading from "@/views/resumeManagement/subheading.vue";
import Addshield from "@/views/resumeManagement/addshield.vue";
import Pagination from "@/components/Pagination.vue";

export default defineComponent({
  components: { subheading, Pagination ,Addshield},
  setup() {
    const router = useRouter();
    const route = useRoute();
    const state = reactive({
      list: [],
      page: 1,
      pageSize: 30,
      total: 0,
      loading: true,
      dialogVisible:false
    });
    onBeforeMount(() => {
      methods.getlist(1);
    });
    const methods = {
      async getlist(page: number) {
        state.loading=true;
        let from = {
          page: page,
          pageSize: state.pageSize,
        };
        let data = await getHideEnterprise(from);
         state.loading=false;
        if (data.code === 1) {
          state.list = data.data.items;
          state.page = data.data.pageIndex;
          state.pageSize = data.data.pageSize;
          state.total = data.data.totalCount;
        } else {
          ElMessage.error(data.message);
        }
      },
      async deleteItem(id: number, index: number) {
        var ids = [];
        ids.push(id);
        let form = {
          ids: ids,
        };
        let data = await deleteEnterprise(form);
        if (data.code == 1) {
          state.list.splice(index, 1);
          ElMessage.success(data.message);
        } else {
          ElMessage.error(data.message);
        }
      },
    };
    const fun = {
      onDelete(id: number, ind: number, Cname: string) {
        ElMessageBox.confirm(
          `确定取消 ${Cname} 的屏蔽？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          methods.deleteItem(id, ind);
        }).catch(() => {
          // 用户取消删除操作
        });
      },
      handlePageChange(val: number) {
        state.page = val;
        methods.getlist(val);
      },
      //添加屏蔽之后
      AddConfirm(p:any){
         state.dialogVisible = false;
         if(p=="1"){
           methods.getlist(1);
         }
          
      }
    };
    return { ...toRefs(state), ...fun };
  },
});
</script>
<style lang="less">
.shieldent-main {
  .cont {
    background: #fff;
    padding: 30px 30px 0 30px;
    margin: 12px 0;
    .title {
      h4 {
        float: left;
      }
      .el-button {
        float: right;
        height: 32px;
        padding: 0 20px;
        line-height: 24px;
        color: #457ccf;
        font-size: 14px;
      }
      .iconfont{
          font-size: 12px;
      }
    }
    .tips{
      font-size: 12px;
      color: #f4a168;
      padding-left: 5px;
    }
    .list {
      padding: 30px 0 0 0;
      li {
        line-height: 50px;
        line-height: 51px;
        border-bottom: 1px solid #f2f2f2;
      }
      p {
        color: #333333;
        font-size: 14px;
        float: left;
      }
      .iconfont {
        float: right;
        cursor: pointer;
        font-size: 20px;
        color: #d4d4d4;
      }
    }
  }
}
</style>