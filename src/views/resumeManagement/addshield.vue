<template>
  <div class="pop-Addshield">
    <el-dialog
      v-model="dialogVisible"
      title="Tips"
      width="800px"
      :before-close="handleClose"
      close-on-click-modal="false"
    >
      <template #title>
        <label class="tit">添加屏蔽</label>
      </template>
      <div class="box clearfix">
        <div class="inp">
          <el-input
            v-model="keywords"
            placeholder="输入企业全称/企业简称"
            @input="onSearch(keywords)"
          />
        </div>
        <p class="character">企业名称屏蔽</p>
        <div class="list-search">
          <ul>
            <li
              v-for="item in list"
              :key="item.enterpriseID"
              :class="{ blue: item.checked }"
            >
              <label v-html="item.enterpriseName" @click="Additem(item)">
              </label>
              <i v-if="item.checked">✔</i>
            </li>
          </ul>
        </div>
      </div>
      <template #footer>
        <div class="Selected">
          已选：
          <label
            class="li"
            v-for="(item, index) in addList"
            :key="item"
            @click="delItem(item, index)"
            > <span v-html="item.name"></span><i>×</i></label
          >
        </div>
        <span class="dialog-footer">
          <el-button type="primary" @click="submit"
            >确定({{ total }})</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import { computed, defineComponent, reactive, toRefs, ref, watch } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { findEnterprise } from "@/http/searchAPI";
import { hideEnterprise } from "@/http/resumeApi";

export default defineComponent({
  emits: ["addConfirm"],
  props: {},
  setup(props: any, { emit }: any) {
    const store = useStore();
    const loading = ref(false);
    const state: any = reactive({
      list: [],
      keywords: "",
      dialogVisible: true,
      addList: [],
      total: 0,
    });
    watch(() => [state.addList], (now, old) => {
      if(now!=old){
      }
    })
    const methods = {
      // 搜索
      async onSearch(text: any) {
        let form = {
          keyword: text,
        };
        let data: any = await findEnterprise(form);
        if (data.code == 1 && data.data.items.length > 0) {
          state.list = data.data.items.map((i: any) => {
            i.checked = false;
            return i;
          });
        }
      },
      async onSubmit() {
      var ids=state.addList.map((i:any)=> i.id)
      let form={
        "ids":ids
      }
      let data  = await hideEnterprise(form);
      if (data.code==1) {
        ElMessage.success(data.message)
        emit("addConfirm", "1");
      }else{
           ElMessage.error(data.message)
      } 
    },

    };
    const fun = {
      handleClose() {
        emit("addConfirm", "");
      },
      //搜索
      onSearch(val: any) {
        methods.onSearch(val);
      },
      submit(){
        if(state.total==0){
            ElMessage("请选择企业")
            return false
        }
        methods.onSubmit()
      },
      Additem(item: any) {
        // item.checked=item.checked?false:true
        if (!item.checked) {
          let arr = {
            name: item.enterpriseName,
            id: item.enterpriseID,
          };
          state.addList.push(arr);
          item.checked = true;
        } else {
          //删除
          item.checked = false;
          state.addList = state.addList.filter(
            (i: any) => i.id != item.enterpriseID
          );
        }
        fun.count();
      },
      delItem(p: any, index: number) {
        state.addList.splice(index, 1);
      state.list.forEach((i: any) =>
          i.enterpriseID == p.id ? (i.checked = false) : ""
        );
        fun.count();
      },
      count() {
        state.total = computed(() => {
          return state.addList.length;
        });
      },
    };
    return {
      loading,
      ...toRefs(state),
      ...fun,
    };
  },
});
</script>
<style lang="less">
.pop-Addshield {
  .el-dialog__header {
    text-align: center;
    font-size: 20px;
    color: #333;
  }
  .character {
    font-size: 13px;
    color: #bbb;
    padding: 16px 0;
  }
  .list-search {
    height: 280px;
    overflow-y: scroll;
    li {
      line-height: 40px;
      color: #444;
      font-size: 15px;

      i {
        color: #457ccf;
      }
      label {
        cursor: pointer;
      }
    }
    li.blue {
      color: #457ccf;
    }
  }
  .el-dialog__body {
    padding: 32px;
  }
  .Selected {
    text-align: left;
    .li {
      font-size: 12px;
      color: #457ccf;
      padding: 2px 5px;
      background: #f2f7ff;
      margin: 10px 10px 0 0;
      display: inline-block;
      i {
        font-size: 12px;
        font-style: normal;
        cursor: pointer;
        padding-left: 5px;
      }
    }
  }
}
</style>
