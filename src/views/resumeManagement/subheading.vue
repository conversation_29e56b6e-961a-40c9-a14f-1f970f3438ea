<template>
  <div class="head-bar">
    <el-row :gutter="1">
      <el-col :span="8"
        >
        <router-link to="/enterpriseView/0">
        <div :class="['li', { select: step == 1 }]">谁看过我</div>
        </router-link>
        </el-col
      >
      <el-col :span="8">
       <router-link to="/resumeList">
        <div :class="['li', { select: step == 2 }]">简历列表</div>
        </router-link></el-col
      >
      
      <el-col :span="8">
         <router-link to="/companyShield">
        <div :class="['li', { select: step == 3 }]">公司屏蔽</div>
        </router-link></el-col
      >

    </el-row>
  </div>
</template>

<script lang="ts">
import { reactive } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
export default {
  props: {
    step: {
      type: Number,
      default: 1,
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const state = reactive({
      editID: 0,
    });
    const addProject = () => {
      let aa = store.state.editorid;
      if (aa == 0) {
        emit("addItem");
        store.commit("editorShow", 1);
      } else {
        ElMessage({
          showClose: true,
          message: "请先提交打开的编辑窗口",
          type: "warning",
          duration: 1000,
        });
      }
    };

    return { state, addProject };
  },
};
</script>
<style lang="less">
.head-bar {
  width: 100%;

  background: #fff;
  .li {
    cursor: pointer;
    height: 55px;
    line-height: 55px;
    font-size: 14px;
    color: #666;
    text-align: center;
  }
  .el-row .el-col:not(:last-child){
      border-right: 1px solid #f2f2f2;
  }
// .li:last-child{
//     border-left: 1px solid #000;
// }
  .select{
      color: #457CCF;  
  }
}
</style>