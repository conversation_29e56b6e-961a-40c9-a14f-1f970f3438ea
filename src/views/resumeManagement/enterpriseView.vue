<template>
  <div class="enterpriseView-main">
    <subheading :step="1"></subheading>
    <div class="cont">
      <el-tabs v-model="state" @tab-click="handleClick">
        <el-tab-pane
          v-for="(item, index) in titleArr"
          :label="item.label"
          :key="index"
          :name="item.state"
        >
          <div class="list" v-if="list.length > 0">
            <a
              :href="escapeURL(`//www.gxrc.com/Company/${encodeURIComponent(item.enterpriseGuid)}`)"
              v-for="(item, index) in list"
              :key="index"
              class="item bg-white clearfix"
              target="_blank"
            >
              <div class="sdl clearfix">
                <el-image style="width: 56px; height: 56px" :src="item.logoUrl" fit="cover"></el-image>
                <div class="mn">
                  <h3 class="ellipsis">{{ item.enterpriseName }}</h3>
                  <p class="info">
                    <span>{{ item.enterprisePropertyName }}</span>
                    <el-divider direction="vertical"></el-divider>
                    <span>{{ item.enterpriseEmployeeNumberName }}</span>
                    <el-divider direction="vertical"></el-divider>
                    <span class="ellipsis">{{ item.enterpriseIndustryName }}</span>
                    <el-divider direction="vertical"></el-divider>
                    <span>{{ item.enterpriseLocationName }}</span>
                  </p>
                </div>
              </div>

              <div class="sdr">
                <div class="bg-fafa box">
                  <p class="look ellipsis">
                    <span>查看简历</span>
                    {{ item.resumeName }}
                  </p>
                  <p class="look time">
                    <span>查看时间</span>
                    {{ item.lastViewTime }} ({{ item.viewCount }}次)
                  </p>
                </div>
              </div>
            </a>
          </div>
          <el-empty description="暂无数据" v-else></el-empty>
        </el-tab-pane>
      </el-tabs>
    </div>
    <Pagination :pageSize="pageSize" :totalCount="total" @handlePageChange="handlePageChange" />
  </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, onBeforeMount } from "vue";
import { getViewMe } from "@/http/resumeApi";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import subheading from "@/views/resumeManagement/subheading.vue";
import Pagination from '@/components/Pagination.vue';

export default defineComponent({
  components: { subheading, Pagination },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const state = reactive({
      titleArr: [
        {
          label: "全部",
          state: "0",
        },
        {
          label: "企业搜索",
          state: "1",
        },
        {
          label: "我的投递",
          state: "2",
        },
      ],
      list: [],
      page: 1,
      pageSize: 20,
      state: route.params.id.toString(),
      total: 0,
    });
    onBeforeMount(() => {
      methods.getlist();
    });
    const methods = {
      async getlist() {
        // const loading = ElLoading.service({
        //   lock: true,
        //   text: "Loading",
        //   background: "rgba(0, 0, 0, 0.7)",
        // });
        let from = {
          page: state.page,
          pageSize: state.pageSize,
          state: state.state,
        };
        let data = await getViewMe(from);
        // loading.close();
        if (data.code === 1) {
          state.list = data.data.items;
          state.page = data.data.pageIndex;
          state.pageSize = data.data.pageSize;
          state.total = data.data.totalCount;
        } else {
          ElMessage.error(data.message);
        }
      },
    };
    const fun = {
      handleClick(tab: any, event: any) {
        state.state = tab.index;
        router.push({ path: `/enterpriseView/${tab.index}` });
        methods.getlist();
      },
      handlePageChange(val: number) {
        state.page = val
        methods.getlist();
      },
      escapeURL(url:string) {
    return url.replace(/&/g, '&amp;').replace(/"/g, '&quot;').replace(/'/g, '&#39;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
  },
    };
    return { ...toRefs(state), ...fun };
  },
});
</script>
<style lang="less">
.enterpriseView-main {
  .list {
    .item {
      display: block;
      padding: 20px 0;
      border-bottom: 1px solid #f2f2f2;
    }

    .sdl,
    .sdr {
      width: 50%;
      float: left;
    }
    .sdl {
      .el-image {
        float: left;
        padding: 10px 16px 0 16px;
      }
      .mn {
        float: left;
      }
      h3 {
        font-size: 16px;
        color: #333;
        padding: 10px 0;
        width: 390px;
      }
      p {
        font-size: 12px;
        color: #999999;
      }
      span {
      }
      p.info {
        display: flex;
        span.ellipsis {
          max-width: 130px;
        }
      }
    }
    .sdr {
      .box {
        width: 376px;
        height: 82px;
      }
      p {
        padding: 16px 16px 0 16px;
        font-size: 14px;
        color: #333;
      }
      p.time {
        padding: 12px 16px 0 16px;
      }
      span {
        color: #999999;
        padding-right: 10px;
      }
    }
  }
  .cont {
    margin: 12px 0 0 0;
    .el-tabs__header {
      .el-tabs__nav-scroll {
        background: #fff;
        height: 50px;
        line-height: 50px;
        padding: 0 40px;
      }
    }
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #f2f2f2;
    }
    .el-tabs__item {
      color: #666;
    }
  }
}
</style>