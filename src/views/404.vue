<template>
    <div class="error_404">
    <div class="error_main">
      <img src="../assets/img/notfound.png" alt="error" />

      <div class="error_tips">
        <!-- <h4>Hmmmm...</h4> -->
        <p>您访问的页面不存在~</p>
      </div>
    </div>

    <div class="return_button" @click="router.push({ name: 'index' })">
      <p>返回首页</p>
    </div>
    
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { useRouter } from "vue-router"
export default defineComponent({
    setup() {
        const router = useRouter()

        return {
            router
        }
    }
})
</script>

<style lang="less" scoped>
.error_404 {
  width: 100%;
  height: 100vh;
  background: #f4f6fb;
  overflow: hidden;
  .return_button {
    cursor: pointer;
    width: 260px;
    height: 44px;
    background: #374dc3;
    opacity: 1;
    border-radius: 8px;
    margin: 0 auto;
    text-align: center;
    margin-top: 10px;
    p {
      font-size: 16px;
      //font-family: Microsoft YaHei;
      font-weight: 400;
      line-height: 44px;
      color: #ffffff;
      opacity: 1;
    }
  }
  .error_main {
    height: 452px;
    margin-top: calc(50vh - 226px);
    text-align: center;
    img {
      height: 452px;
      margin-left: 100px;
    }
    .error_tips {
      margin-top: -70px;
    }

    h4 {
      height: 31px;
      font-size: 24px;
      //font-family: Microsoft YaHei;
      font-weight: 400;
      line-height: 31px;
      color: #302c48;
      opacity: 1;
    }
    p {
      height: 20px;
      font-size: 15px;
      //font-family: Microsoft YaHei;
      font-weight: 400;
      line-height: 25px;
      color: #302c48;
      opacity: 0.7;
    }
  }
}
</style>