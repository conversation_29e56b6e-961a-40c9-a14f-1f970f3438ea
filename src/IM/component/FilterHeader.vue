<template>
  <el-header height="32px" style="padding: 0">
    <div class="im-page-header">
        <!-- <selector @choosePosition="choosePosition" /> -->
      <div class="im-page-header__button">
        <el-button
          class="header-button"
          :class="{ 'header-button--focus': select == 0 }"
          size="small"
          plain
          @click="filterClick(0)"
        >全部</el-button>
        <el-button
          class="header-button no-margin new-message"
          :class="{ 'header-button--focus': commit == 1 }"
          size="small"
          plain
          @click="commitType(1)"
        >新招呼
        <!-- <div class="new-message-red"></div> -->
      </el-button>
         <el-button
          class="header-button no-margin"
          :class="{ 'header-button--focus': select == 1 }"
          size="small"
          plain
          @click="filterClick(1)"
        >邀面试</el-button>
        <!-- <el-button
          class="header-button no-margin"
          :class="{ 'header-button--focus': commit == 2 }"
          size="small"
          plain
          @click="commitType(2)"
        >沟通中</el-button> -->
       
        <el-button
          class="header-button no-margin"
          :class="{ 'header-button--focus': block }"
          size="small"
          plain
          @click="filterBlock(true)"
        >不合适</el-button>
        <div class="im-page-header__filter">
          <div class="im-page-header__filter--conditions">
            <div class="im-header-filter">
              <el-button
                class="no-margin button--plain"
                size="small"
                v-if="select != 0 || actionType != 2 || block || commit != 0"
                @click="cancel"
              >
                <i class="icon size iconfont icon-funnel2"></i>取消标签筛选
              </el-button>
              <el-popover
                placement="bottom-start"
                trigger="click"
                ref="filterPop"
                popper-class="filterPops"
                v-model:visible="visible"
               :width="250"
              >
                <template #reference>
                  <el-button
                    v-if="select == 0 && actionType == 2 && !block && commit == 0"
                    class="im-page-header__filter--light filter-button no-margin"
                    :class="{ 'filter-button--focus': focusClass }"
                    size="small"
                  >
                    <i class="icon size iconfont icon-funnel2"></i>更多筛选
                  </el-button>
                  <el-button
                    v-else
                    class="button--plain icon-button"
                    size="small"
                    ref="popButton"
                  >
                    <i class="el-icon-arrow-down"></i>
                  </el-button>
                </template>
                <template #default>
                  <div class="filter-popover__inner">
                    <div class="im-header-filter-main">
                      <div class="im-header-filter__group">
                        <div class="im-filter-item">
                          <div class="im-filter-item__title">发起方</div>
                          <div class="im-filter-item__container">
                            <div class="im-filter-item__container--button">
                              <el-button
                                size="mini"
                                ref="actionMe"
                                plain
                                round
                                :class="{ 'fl-button--primary': middleActionType == 0 }"
                                class="fl-button--outlined"
                                @click="changeActionType(0)"
                              >我发起</el-button>
                            </div>
                            <div class="im-filter-item__container--button">
                              <el-button
                                size="mini"
                                plain
                                round
                                :class="{ 'fl-button--primary': middleActionType == 1 }"
                                class="fl-button--outlined"
                                @click="changeActionType(1)"
                              >对方发起</el-button>
                            </div>
                          </div>
                        </div>
                        <div class="im-filter-item">
                          <div class="im-filter-item__title">沟通状态</div>
                          <div class="im-filter-item__container">
                            <!-- <div class="im-filter-item__container--button">
                              <el-button
                                size="mini"
                                plain
                                round
                                :class="{ 'fl-button--primary': commitSelect == 1 }"
                                class="fl-button--outlined"
                                @click="commitTypeChange(1)"
                              >新招呼{{ 2 }}</el-button>
                            </div>
                            <div class="im-filter-item__container--button">
                              <el-button
                                size="mini"
                                plain
                                round
                                :class="{ 'fl-button--primary': commitSelect == 2 }"
                                class="fl-button--outlined"
                                @click="commitTypeChange(2)"
                              >沟通中</el-button>
                            </div> -->

                            <div class="im-filter-item__container--button">
                              <el-button
                                size="mini"
                                plain
                                round
                                :class="{ 'fl-button--primary': middleSelect == 1 }"
                                class="fl-button--outlined"
                                @click="changeState(1)"
                              >已约面</el-button>
                            </div>
                            <div class="im-filter-item__container--button">
                              <el-button
                                size="mini"
                                plain
                                round
                                :class="{ 'fl-button--primary': middleBlock }"
                                class="fl-button--outlined"
                                @click="changeBlock(true)"
                              >不合适</el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="im-header-filter__footer">
                      <el-button size="small" @click="cancelChoose">取消</el-button>
                      <el-button size="small" type="primary" @click="comfirmChoose">确定</el-button>
                    </div>
                  </div>
                </template>
              </el-popover>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-header>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from "vue";
import { useStore } from "../../store";
// import selector from "./selector";
export default defineComponent({
  name: "positionFilter",
  setup() {
    const state = reactive({
      visible: false,
      select: 0,
      focusClass: false,
      middleSelect: 0,
      actionType: 2,
      middleActionType: 2,
      block: false,
      middleBlock: false,
      commit: 0,
      commitSelect: 0,
    })
    const store = useStore()
    const methods = {
      clearStatus() {
        state.block = state.middleBlock = false
        state.select = state.middleSelect = -1
        state.commit = state.commitSelect = 0
      },
      choosePosition(item) {
        store.commit("updateActivePosition", item);
      },
      commitType(value) {
        methods.clearStatus()
        state.commit = value
        state.commitSelect = state.commit
        let obj = { communicatintType: state.commitSelect }
        store.commit("updateFilterObj", obj);
      },
      clearChange() {
        state.commitSelect = 0
        state.middleSelect = -1
        state.middleBlock = false
      },
      commitTypeChange(value) {
        methods.clearChange()
        state.commitSelect = value
      },
      filterClick(value) {
        //全部，
        methods.clearStatus()
        state.select = value;
        state.middleSelect = state.select;
        let obj = { select: state.select };
        if (state.actionType != 2) {
          obj.actionType = state.actionType;
        }
        store.commit("updateFilterObj", obj);
      },
      filterBlock(value) {
        //不合适
        methods.clearStatus()

        state.block = value;
        state.middleBlock = state.block;
        let obj = { blocking: state.block };
        store.commit("updateFilterObj", obj);
      },
      cancel() {
        //取消标签筛选
        state.actionType = state.middleActionType = 2;
        state.block = state.middleBlock = false
        methods.clearStatus()
        // this.visible = false;
        methods.filterClick(0);
      },
      changeState(value) {
        //更多筛选的已约面

        methods.clearChange()
        if (state.middleSelect === value) {
          state.middleSelect = 0;
        } else {
          state.middleSelect = value;
        }
      },
      changeActionType(value) {
        //更多筛选的我发起，对方发起
        let current = state.middleActionType;
        if (current == value) {
          state.middleActionType = 2;
        } else {
          state.middleActionType = value;
        }
      },
      changeBlock(value) {
        //选择更多筛选的不合适

        methods.clearChange()
        if (state.middleBlock === value) {
          state.middleBlock = false;
        } else {
          state.middleBlock = value;
        }
      },
      comfirmChoose() {
        //更多筛选的确定
        state.visible = false;
        state.commit = state.commitSelect
        state.select = state.middleSelect;
        state.actionType = state.middleActionType;
        state.block = state.middleBlock
        let obj = { select: state.select, actionType: state.actionType, blocking: state.block, communicatintType: state.commitSelect };
        store.commit("updateFilterObj", obj);
      },
      cancelChoose() {
        //更多筛选的取消
        state.visible = false;
        //state.select = state.middleSelect;
        if (state.select != state.middleSelect) {
          state.middleSelect = 0
        }
        if (state.actionType != state.middleActionType) {
          state.middleActionType = 2
        }
        if (state.block != state.middleBlock) {
          state.middleBlock = false;
        }
      },
    }
    return {
      ...toRefs(state),
      ...methods
    }
  }
})
</script>
<style lang="less">
::v-deep .filterPops{
  border: 0;
  padding: 0;
  width: 400px;
}
</style>
<style lang="less" scoped>
.im-page-header {
  display: flex;
  align-items: center;
  .im-pos-filter {
    min-width: 0;
    margin: 0 10px;
    border: 1px solid #cccccc;

    border-radius: 3px;

    .pos-select {
      width: 240px;
    }
  }
  .im-page-header__button {
    display: flex;
    padding-top: 2px;
    .header-button {
      width: 75px;
      margin-right: 8px;
      border-color: transparent;
      border: 1px solid #cccccc;
      &--focus {
        background: #fff;
        color: #409eff;
        border: 1px solid #409eff;
      }
      
    }
    .new-message{
      position: relative;
      .new-message-red{
        width: 10px;
        height: 10px;
        background-color: red;
        border-radius: 50%;
        margin-left: 5px;
        position: absolute;
        top: -2px;
        right: -2px;
      }
    }
    .no-margin {
      margin-right: 0px;
    }
    .el-button-group > .el-button:not(:last-child):after {
      content: "|";
      color: #e9ecef;
      background-color: #fff;
      position: absolute;
      display: flex;
      align-items: center;
      top: 10px;
      left: 72px;
    }
    .im-page-header__filter {
      width: 180px;
      .button--plain {
        border-color: transparent;
        color: #1e9bf5;
      }
      .icon-button {
        margin-left: 2px;
        padding: 9px 8px;
      }
      .icon-button:focus {
        background-color: #fff;
      }
      &--conditions {
        margin-left: 8px;
        .filter-button {
          width: 90px;
          border-color: transparent;
          background: #fff;
          &--focus {
            background-color: #fff;
          }
        }
        .filter-button:hover {
          background-color: #909399;
          color: #fff;
          opacity: 0.3;
        }
        .size {
          font-size: 12px;
          margin-right: 2px;
        }
      }
      &--light {
        background-color: transparent;
        color: #787c8a;
      }
    }
  }
}

.filter-popover__inner {
  padding: 12px 16px;
  overflow: hidden;
  color: #252a35;
  font-size: 14px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #dbdfe6;
  height: 100%;
  .im-header-filter {
    &__group {
      display: flex;
      flex-direction: column;
      width: 220px;
      .im-filter-item {
        display: flex;
        flex-direction: column;
        margin: 8px 0;
        font-size: 12px;
        &__title {
          margin-bottom: 12px;
          font-weight: 700;
        }
        &__container {
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;
          margin: -4px;
          &--button {
            margin: 0 4px 8px;
            .fl-button--outlined {
              border-color: #d8dce6;
              color: #787c8a;
            }
            .fl-button--primary {
              background: #1e9bf5 !important;
              border-color: #1e9bf5;
              color: #fff;
            }
          }
        }
      }
    }
    &__footer {
      display: flex;
      justify-content: center;
      margin-top: 20px;
      margin-bottom: 8px;
    }
  }
}
</style>