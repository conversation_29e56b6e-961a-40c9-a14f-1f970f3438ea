<template>
  <el-scrollbar
    class="sessions"
    ref="sessionScroll"
    view-class="r-scrollbar__view"
    view-style="position:relative;height:auto"
    wrap-style="calc(100% + 17px)"
    v-loading="loading"
    @scroll="handleScrollbar"
  >
    <ul class="m-panel" v-if="sessionlists?.length > 0">
      <transition-group name="flip-list" tag="ul">
        <li
          class="panel_item"
          v-for="session in sessionlists"
          :key="session.id"
          :class="{
            isActive: session.id === activeSessionId,
            'session-action-open': session.id === dropdownSelect,
            'panel_item-top': session.topSetting == 1,
          }"
          @click="chat(session.id, type)"
        >
          <div class="panel_avatar">
            <el-avatar :src="session.avatar"></el-avatar>
            <b class="panel_count" v-if="session.unread > 0">
              {{ session.unread }}
            </b>
          </div>
          <div class="panel_text">
            <p class="panel_multi-row">
              <span class="panel_nick">{{ session.name }}</span>
              <span style="flex-grow: 1"></span>
              <span class="panel_time">{{ session.time }}</span>
              <span @click.stop>
                <el-dropdown
                  class="im-session-item__action"
                  trigger="click"
                  placement="bottom"
                  append-to-body
                  @visible-change="dropdownChange(session.id, $event)"
                >
                  <el-button
                    class="transparent"
                    size="mini"
                    circle
                    icon="el-icon-more"
                  ></el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        @click.native="
                          setTop(session.accID, session.topSetting == 1 ? 0 : 1)
                        "
                      >
                        {{ session.topSetting == 1 ? "取消置顶" : "置顶" }}
                      </el-dropdown-item>
                      <el-dropdown-item
                        @click.native="deleteSession(session.accID, session.id)"
                        >删除聊天</el-dropdown-item
                      >
                      <el-dropdown-item
                        @click.native="
                          reportDetail(
                            session.localID,
                            session.name,
                            session.id
                          )
                        "
                        >举报</el-dropdown-item
                      >
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </span>
            </p>
            <p class="panel_multi-row">
              <span
                class="panel_lastMsg2"
                v-html="session.sessionTagString"
              ></span>
              <span class="panel_lastMsg" :title="session.lastContent">
                {{ session.lastContent }}
              </span>
              <i
                v-if="session.isBlack"
                class="el-icon-close-notification"
                style="float: right; margin-top: 4px; color: #8899a7"
              ></i>
            </p>
          </div>
        </li>
      </transition-group>
    </ul>
    <div class="session-empty is-mt-40 is-pl-20 is-pr-20" v-else>
      <span class="isNone">{{ noMathText }}</span>
    </div>
    <report v-model:Model="visible" :sessionId="reportId" :name="reportName" />
  </el-scrollbar>
</template>

<script lang="ts">
import sessionMixins from "../mixins/sessionMixins";
import { mapState } from "vuex";
import {
  getSessions,
  info,
  login,
  sessionSetting,
  sendgreeting,
  imSendmsgupdateposition,
} from "../api/index";
import config from "../configs";
import report from "../component/report.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  computed,
  defineComponent,
  reactive,
  toRef,
  toRefs,
  ref,
  onMounted,
  provide
} from "vue";
import { useStore } from "../../store";
import { nextTick } from "process";
import { watch } from "vue";
export default defineComponent({
  props: {
    type: {
      type: Number,
      default: 1,
    },
    sessionId: {
      //以前用路由传，现在用props
      type: Number,
      default: 0,
    },
    seach: {
      type: String,
      default: "",
    },
    check: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    report,
  },
  setup(props, { emit }) {
    const store = useStore();
    store.dispatch("connect");

    const {
      sessionlists,
      loading,
      activeSessionId,
      dropdownSelect,
      chat,
      initList,
      sessionScroll,
      handleScrollbar,
      list,
      requestPost,
      model,
      updataCheck,
      updataSeach,
      unreadCount,
      errorSessionMsg,
    } = sessionMixins.setup();
    const state = reactive({
      reportId: "",
      reportName: "",
      visible: false,
      positionId: 0,
      entId: 0,
    });
    onMounted(() => {
      window.addEventListener("message", methods.handleMessage);
    });

    watch(
      () => props.check,
      (val) => {
        updataCheck(val);
      }
    );
    watch(
      () => props.seach,
      (val) => {
        updataSeach(val);
      }
    );
    watch(
      () => unreadCount.value,
      (val) => {
        store.commit("updateUnreadCount", val);
      }
    );
    const noMathText = computed(() => {
      return errorSessionMsg.value != ""
        ? errorSessionMsg.value
        : "暂无聊天信息";
    });

    const inviteFlag = computed(() => store.state.imModules.inviteFlag);
    const filterObj = computed(() => store.state.imModules.filterObj);
    watch(
      () => filterObj.value,
      (val) => {
        const obj = {
          index: 1,
          size: model.value.size,
          inviteFlag: val.select,
          actionType: val.actionType,
          blocking: val.blocking,
          communicatintType: val.communicatintType,
        };
        methods.getSessionsList(obj);
      }
    );
    watch(
      () => inviteFlag.value,
      (val) => {
        const obj = {
          index: 1,
          size: model.value.size,
          inviteFlag: val,
        };
        methods.getSessionsList(obj);
      }
    );
    const methods = {
      async handleMessage(event: Record<string, any>) {
        //为了实现新设计启用widget/session 移植过来的
        let { data, origin } = event;

        let t = config.isTest ? "t" : "";
        var pat = new RegExp(`.${t}gxrc.com`);

        if (pat.test(origin)) {
          let { entId, type, positionId } = data;

          let seekerId = entId;
          state.entId = seekerId;
          if (type == 2) {
            await login();
            return;
          }
          if (positionId) store.commit("updatePositionId", Number(positionId));

          if (sessionlists.value && sessionlists.value.length > 0 && seekerId) {
            if (
              sessionlists.value.find(
                (item) => item.localID == parseInt(seekerId)
              )
            ) {
              // store.commit(
              //   "updateWidgetActiveSessionId",
              //   `p2p-ent${seekerId}`
              // );
              store.commit("updateFixedWindowOpen", true);
              nextTick(() => {
                chat(`p2p-ent${seekerId}`, 2);
              });
            } else {
              methods.addSeeker(seekerId);
            }
          } else {
            if (seekerId) {
              methods.addSeeker(seekerId);
            }
          }
          if (seekerId) {
            let obj = {
              platform: 0,
              entId: Number(seekerId),
              positionId: positionId || 0,
            };
            sendgreeting(obj);
          }
        }
      },
      async addSeeker(seekerId: number) {
        let data: any = await info(`ent${seekerId}`);
        if (data.code === 1) {
          let item = {
            accID: `ent${seekerId}`,
            avatar: data.data.avatar,
            localID: seekerId,
            name: data.data.name,
          };
          list.value.splice(0, 0, item);
          store.commit("updateFixedWindowOpen", true);
          nextTick(() => {
            chat(`p2p-ent${seekerId}`, 2);
          });
        } else {
          console.log("返回有误");
        }
      },
      dropdownChange(sessionId: string, val: any) {
        if (val) {
          dropdownSelect.value = sessionId;
        } else {
          nextTick(() => {
            setTimeout(() => {
              dropdownSelect.value = "";
            }, 50);
          });
        }
      },
      async setTop(localId: number, value: number) {
        let data = await sessionSetting({
          enterId: localId,
          topSetting: value,
        });
        if (data.code === 1) {
          await initList();
        } else {
          ElMessage({
            type: "error",
            message: data.message,
          });
        }
      },

      getSessionsList(obj: Record<string, unknown>) {
        list.value = [];
        loading.value = true;
        requestPost.value = obj;
        getSessions(obj).then((result) => {
          let data = result;
          loading.value = false;
          if (data.code == 1) {
            list.value = data.data;
          }
        });
      },
      deleteSession(localId: number, id: string) {
        if (props.type == 2) {
          //如果是侧边条的话需要弹出聊天框才够显示
          chat(id, props.type);
        }
        ElMessageBox.confirm("确定要删除此会话吗？", "提示", {
          confirmButtonText: "删除",
          cancelButtonText: "取消",
          customClass: "del-session",
        })
          .then(async () => {
            let nim = store.state.imModules.nim;
            if (nim) {
              nim.resetSessionUnread("p2p-" + localId);
            }
            let data = await sessionSetting({
              enterId: localId,
              delSetting: 1,
            });
            let sessionId = props.sessionId;
            if (data.code === 1) {
              if (sessionId && sessionId == localId) {
                // this.$router.push({ name: "index" });
              }
              await initList();
            } else {
              ElMessage({
                type: "error",
                message: data.message,
              });
            }
          })
          .catch(() => {});
      },
      reportDetail(localId: number, name: string, id: string) {
        if (props.type == 2) {
          //如果是侧边条的话需要弹出聊天框才够显示举报
          chat(id, props.type);
        }
        state.reportId = localId;
        state.reportName = name;
        state.visible = true;
      }
    };
    
    return {
      sessionlists,
      loading,
      noMathText,
      activeSessionId,
      dropdownSelect,
      chat,
      ...methods,
      sessionScroll,
      handleScrollbar,
      ...toRefs(state),
    };
  },
});
</script>
<style lang="less" scoped>
.r-scrollbar__view {
  .is-mt-40 {
    margin-top: 40px !important;
  }
  .is-pr-20 {
    padding-right: 20px !important;
  }
  .is-pl-20 {
    padding-left: 20px !important;
  }
  .session-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    color: #707e97;
    .isNone {
      font-size: 14px;
    }
  }
}

.sessions {
  height: 100%;
  //height: 40%;
  position: relative;
  background-color: #fff;
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}
.m-panel {
  margin: 0;
  padding: 0;
}
.m-panel .panel_text .panel_lastMsg,
.m-panel .panel_text .panel_lastMsg2 {
  color: #8899a7;
}
.m-panel .panel_item {
  //border-bottom: 1px solid #eaeeef;
  position: relative;
  overflow: hidden;
  height: 67px;
  padding: 12px 20px;
  box-sizing: border-box;
  cursor: pointer;
}
.panel_item-top:before {
  content: "";
  width: 0;
  height: 0;
  border: none;
  border-top: 10px solid #1c90f0;
  border-right: 10px solid transparent;
  position: absolute;
  left: 0;
  top: 0;
}
.m-panel .panel_text .panel_nick,
.m-panel .panel_text .panel_lastMsg {
  display: inline-block;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.m-panel .panel_text .panel_lastMsg2 {
  display: inline-block;
  line-height: 20px;
  white-space: nowrap;
}
.m-panel .panel_text .panel_nick {
  width: 60%;
  font-size: 14px;
}
.m-panel .panel_text .panel_lastMsg,
.m-panel .panel_text .panel_lastMsg2 {
  font-size: 12px;
}
.m-panel li {
  list-style: none;
}
.panel_avatar {
  float: left;
  position: relative;
  .panel_image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    vertical-align: top;
  }
  .panel_count {
    position: absolute;
    background-color: #f24c3d;
    display: inline-block;
    font-size: 12px;
    color: #fff;
    border-radius: 10px;
    right: -10px;
    top: -7px;
    text-align: center;
    padding: 0 2px;
    min-width: 15px;
    max-width: 50px;
    line-height: 18px;
  }
}
.panel_text {
  margin: 0 0 0 60px;
  box-sizing: border-box;
  position: relative;
  height: 100%;
  overflow: hidden;
  .panel_multi-row {
    height: 20px;
    line-height: 20px;
    margin-bottom: 4px;
    display: flex;
  }
  .panel_nick {
    color: #3a4a59;
  }
  .panel_time {
    font-size: 12px;
    color: #777c89;
    white-space: nowrap;
    flex-shrink: 0;
  }
  .transparent {
    border: transparent;
    background-color: transparent;
  }
  .el-button:focus,
  .el-button:hover {
    color: #409eff !important;
    border-color: #c6e2ff !important;
    background-color: rgba(30, 155, 245, 0.25) !important;
  }
  ::v-deep(.im-session-item__action) {
    display: none;
    position: relative;
    line-height: 1;
    align-items: center;
  }
}
.panel_item:hover .panel_time,
.session-action-open .panel_time {
  display: none;
}
.panel_item:hover ::v-deep(.im-session-item__action),
.session-action-open ::v-deep(.im-session-item__action) {
  display: flex;
}
.m-panel p,
.m-panel span,
.m-panel ul,
.m-panel li {
  padding: 0;
  margin: 0;
}
.isActive {
  background-color: #e4ecf3;
}
.flip-list-move {
  transition: transform 1s;
}
// .readmore {
//   text-align: center;
//   font-size: 12px;
//   color: #6b8299;
//   display: block;
// }
.loading {
  margin-top: 330px;
}
.el-dropdown-menu__item {
  margin: 0 5px;
  color: #707e97;
}
.del-session {
  padding: 10px;
}
</style>
