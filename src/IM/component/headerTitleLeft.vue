<template>
  <div class="im-side-panel">
    <div class="session-panel">
      <div class="collect" v-if="type == 2">
        <i class="el-icon-arrow-right" @click="collect"></i>
      </div>
      <div class="title">最近联系</div>
      <div class="side-panel-header__checkbox">
        <el-button
          type="text"
          icon="el-icon-circle-close"
          v-if="type == 1 && false"
          @click="AllRead"
          >全部已读</el-button
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useStore } from '../../store';

const store = useStore();
const props = withDefaults(
  defineProps<{
    type: number;
  }>(),
  { type: 1 }
);

const emit = defineEmits<{
  (e: 'AllRead'): void;
}>();
const AllRead = (): void => {
  emit('AllRead');
};
const collect = () => {
  store.commit('updateFixedWindowOpen', false);
  store.commit('updateActiveSessionId', '');
  store.commit('updateWidgetActiveSessionId', '');

  try {
    throw "test"
    document.domain = import.meta.env.VITE_APP_DO_MAIN;
    if (window.self != window.top) {
      let el = parent.window.document.getElementById('im_widget');
      if (el) {
        el.style.width = '55px';
        el.style.height = '100%';
      }

      emit('resizeHeight', '100%');
    }
  } catch {
    if (window.self != window.top) {

      window.parent.postMessage(
        {
          methods: 'change_style',
          data: {
            el:'#im_widget',
            value: [
              { value: '100%', style: 'height' },
              { value: '55px', style: 'width' },
            ],
          },
        },
        '*'
      );
      emit('resizeHeight', '100%');
    }
  }
};
</script>

<style lang="less" scoped>
.im-side-panel {
  border-right: 1px solid #e8ebef;
  width: 300px;
  // border: 1px solid #f2f2f2;
  align-items: center;

  background: rgba(252, 252, 252, 0.39);
  height: 100%;

  .session-panel {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    box-sizing: border-box;
    padding: 0 16px;
    justify-content: space-between;
    background: #fff;
    .collect {
      cursor: pointer;
    }
    .title {
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      opacity: 1;
    }
    .side-panel-header__checkbox {
      .el-button {
        font-size: 12px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #999999;
        opacity: 1;
      }
    }
  }
}
</style>
