<template>
  <div class="map-wrap" :style="{ 'height': height }">
    <p v-if="showType == 1">您可以拖动图标在地图上选择地点</p>
    <div :id="`map${id}`" :style="{ height: height }"></div>
  </div>
</template>
<script lang="ts">
import {
  defineComponent,
  onMounted,
  watch,
} from 'vue'
export default defineComponent({
  props: {
    addressValue: {
      type: Array,
      default: () => {
        return []
      }
    },
    showType: {
      type: Number,
      default: 1, //1是选择，2是展示
    },
    selectValue: {
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: '554px'
    },
    id:{
      type: String,
    }
  },
  setup(props: any, {
    emit
  }: any) {
    watch(props, () => {
      mapCenter()
    })
    let mapCenter = () => {
      // 百度地图API功能
      // let map = new BMap.Map("map");
      let gc = new BMap.Geocoder(); //地址解析类
      let myCity = new BMap.LocalCity();
      myCity.get((result: any) => {
        let address: number[] = []
        if (props.selectValue) {
          gc.getPoint(props.selectValue, function (pointValue: any) {
            if (pointValue) {
              emit('inputMap', pointValue)
              address.push(pointValue.lng)
              address.push(pointValue.lat)
              intMap(address)
              return
            } else {
              alert('您选择的地址没有解析到结果！');
            }
          }, props.selectValue)
          return
        } else if (props.addressValue.length > 0) {
          address.push(...props.addressValue)
        } else if (result.center.lng) {
          address.push(result.center.lng)
          address.push(result.center.lat)
        } else {
          address = [108.371509, 22.81461]
        }
        intMap(address)
      })
    }

    function intMap(address: number[]) {
      let opts: any = {
        type: BMAP_NAVIGATION_CONTROL_LARGE
      } //控件
      let gc = new BMap.Geocoder(); //地址解析类
      let map = new BMap.Map(`map${props.id}`);
      let point = new BMap.Point(address[0], address[1])
      map.centerAndZoom(point, 16);

      var myIcon = new BMap.Icon("https://image.gxrc.com/gxrcsite/vip/icon_position2.png", new BMap.Size(40, 80));
      myIcon.setImageSize(new BMap.Size(20, 20)); //设置图标大小
      let marker = new BMap.Marker(point, {
        icon: myIcon
      }); // 创建标注
      props.showType == 1 && marker.enableDragging(); //可以移动坐标
      map.addOverlay(marker); //添加标注
      // map.addControl(new BMap.NavigationControl(opts)); //添加控件

      marker.addEventListener("dragend", function (e: any) {
        //获取地址信息  
        gc.getLocation(e.point, function (rs: any) {
          showLocationInfo(e.point, rs);
        });
      });

      //显示地址信息窗口  
      function showLocationInfo(pt: any, rs: any) {
        // var opts = {
        //     width: 250, //信息窗口宽度  
        //     height: 100, //信息窗口高度  
        //     title: "" //信息窗口标题  
        // }
        var addComp = rs.addressComponents;
        // var addr = "当前位置：" + addComp.province + ", " + addComp.city + ", " + addComp
        //     .district + ", " + addComp.street + ", " + addComp.streetNumber + "<br/>";
        // addr += "纬度: " + pt.lat + ", " + "经度：" + pt.lng;
        let addressDetailed = {
          province: addComp.province,
          city: addComp.city,
          area: addComp.district,
          street: addComp.street,
          houseNumber: addComp.streetNumber,
          lng: pt.lng,
          lat: pt.lat,
          msg: ''
        }
        let msg = ''
        for (let i in addressDetailed) {
          if (i != 'lat' && i != 'lng') {
            msg += addressDetailed[i]
          }
        }
        addressDetailed.msg = msg
        emit('select', addressDetailed)
      }
    }
    onMounted(() => {
      mapCenter()
    })
  },
})
</script>
<style scoped lang='less'>
.map-wrap {
  width: 100%;
  height: 554px;
  text-align: center;
}

.map-wrap > p {
  line-height: 32px;
  margin-bottom: 16px;
  font-size: 18px;
  color: rgb(34, 33, 33);
}

#map {
  width: 100%;
  height: 100%;
  position: relative;
}

.center-point {
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  bottom: 0;
  margin: 0 auto;
  margin-top: -50px;

  i {
    color: red;
    font-size: 58px;
  }
}

.point {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 10px;
  height: 10px;
  background: red;
  border-radius: 50%;
}
</style>