<template>
  <el-dialog
    v-model="showResumeDialog"
    width="448px"
    custom-class="resume_dialog"
    @close="handleClose"
  >
    <template #title>
      <div class="resume_dialog_title">
        <h1>{{ positionData?.positionName }}</h1>
        <h2>{{ positionData?.paypackage }}</h2>
        <p>{{ positionData?.enterpriseName }}</p>
      </div>
    </template>
    <div class="resume_dialog_main">
      <h1>请选择{{type == 1 ? '发送' : '投递'}}简历：</h1>
      <el-scrollbar height="188px">
        <div class="scroll_view">
          <div
            class="scroll_view_row"
            :class="{
              active_class: active == item.resumeId,
              disable_class: item.resumeState != 2 && item.resumeState != 1,
            }"
            @click="
              selectRow(
                item.resumeState != 2 && item.resumeState != 1
                  ? active
                  : item.resumeId
              )
            "
            v-for="item in resumeList"
          >
            <div id="triangle-topright">
              <i class="el-icon-check"></i>
            </div>
            <div class="left">
              <h2>{{ item.resumeName }}</h2>
              <p>
                {{
                  item.resumeState == 0
                    ? '未完成'
                    : item.resumeState == 1
                    ? '未审核'
                    : item.resumeState == 3
                    ? '未通过审核'
                    : item.stars * 20 + '% 完整度'
                }}
              </p>
            </div>
            <div class="right">
              <i class="el-icon-edit" @click="editResume(item.resumeId, 1)"></i>
              <i class="el-icon-view" @click="editResume(item.resumeId, 2)"></i>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, onBeforeMount, ref, toRefs } from 'vue';
import { getResumeList } from '../../http/api';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { useStore } from "vuex";
export default defineComponent({
  props: {
    showResumeDialog: {
      type: Boolean,
      default: false,
    },
    positionData: {
      type: Object,
      default: () => {},
    },
    type: {
      type: Number,
      default: 1,
    },
  },
  emits: ['closeResumeDialog', 'submit'],

  setup(props, { emit }) {
    const store = useStore();
    const router = useRouter();
    onBeforeMount(async () => {
      let data: any = await getResumeList({ time: new Date() });
      if (data.code == 1) {
        resumeList.value = data.data.resumeList;
        store.commit("setResumeList", data.data.resumeList);
        let defaultItem = resumeList.value.find((item) => {
          return item.isDefault == true;
        });
        if (defaultItem) {
          active.value = defaultItem.resumeId;
        }
      }
    });
    const resumeList = ref({});
    const handleClose = () => {
      emit('closeResumeDialog');
    };
    const active = ref(0);
    const selectRow = (id: number) => {
      active.value = id;
    };
    const submit = () => {
      if (active.value) {
        if (props.type == 1) {
          emit('submit', active.value);
        } else {
          emit(
            'submit',
            resumeList.value.find((item) => {
              return item.resumeId == active.value;
            })
          );
        }
      } else {
        ElMessage.error('请先选择简历');
        return;
      }
    };
    const editResume = (id: number, type: number) => {
      let routeData = router.resolve({
        path: type == 1 ? `/resume/${id}` : `/preview/${id}`,
      });
      window.open(routeData.href, '_blank');
      handleClose();
    };
    return {
      handleClose,
      ...toRefs(props),
      resumeList,
      selectRow,
      active,
      submit,
      editResume,
    };
  },
});
</script>
<style lang="less">
.resume_dialog {
  .el-dialog__header {
    background-color: #fafafa;
  }
  .el-dialog__body {
    padding: 20px;
  }
}
</style>
<style lang="less" scoped>
.el-button {
  width: 100%;
}
.resume_dialog_main {
  height: 200px;
  width: 100%;

  .scroll_view {
    overflow-y: auto;

    &_row {
      width: 100%;
      height: 78px;
      background: #fafafa;
      opacity: 1;
      border-radius: 2px;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 16px;
      border: 1px solid #fafafa;
      box-sizing: border-box;
      position: relative;
      #triangle-topright {
        width: 0;
        height: 0;
        border-top: 20px solid #5f9efc;
        border-left: 20px solid transparent;
        position: absolute;
        top: 0;
        right: 0;
        display: none;
        i {
          position: absolute;
          left: -11px;
          top: -18px;
          font-size: 12px;
          color: #fff;
        }
      }
      .left {
        flex: 1;
        justify-content: space-between;
        flex-direction: column;
        display: flex;
        cursor: pointer;
        h2 {
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          line-height: 19px;
          color: #333333;
          opacity: 1;
        }
        p {
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #457ccf;
          opacity: 1;
        }
      }
      .right {
        width: 50px;
        justify-content: space-between;
        display: flex;
        align-items: center;
        i {
          font-size: 16px;
          cursor: pointer;
        }
      }
    }
    .active_class {
      background: rgba(242, 247, 255, 0.39);
      border: 1px solid #5f9efc;
      #triangle-topright {
        display: block;
      }
    }
    .disable_class {
      #triangle-topright {
        display: none;
      }

      .left {
        h2 {
          color: #bbbbbb;
        }
        p {
          color: #fc5c5b;
        }
      }
      .right {
        i {
          color: #bbbbbb;
        }
      }
    }
  }
  h1 {
    height: 19px;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    line-height: 19px;
    color: #666666;
    opacity: 1;
    margin-bottom: 16px;
  }
}
.resume_dialog_title {
  h1 {
    height: 21px;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    line-height: 21px;
    color: #333333;
    opacity: 1;
  }
  h2 {
    height: 21px;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    line-height: 21px;
    color: #fc5c5b;
    opacity: 1;
    margin: 8px 0;
  }
  p {
    height: 16px;
    font-size: 12px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    line-height: 16px;
    color: #999999;
    opacity: 1;
  }
}
</style>
