<template>
  <div class="im-widget-side-panel_seach_box">
    <el-input prefix-icon="el-icon-search" v-model="seach" @input="input" placeholder="输入公司名称搜索聊天信息"></el-input>
    <el-checkbox v-model="check" @change="change">未读</el-checkbox>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref } from "vue"
const emit = defineEmits<{
  (e: 'update:Seach', value: string): void
  (e: 'update:Check', value: Boolean): void
}>()
type Props = {
  Seach: string,
  Check: boolean
}
const props = withDefaults(defineProps<Props>(), {
  Seach: '',
  Check: false
})
let seach = ref(props.Seach)
let check = ref(props.Check)
// onMounted(() => {
//   seach.value = props.Seach
//   check.value = props.Check
// })
const input = (value: string | number) => {
  value = value.toString()
  emit('update:Seach', value)
}
const change = (value: boolean) => {
  emit('update:Check', value)
}


</script>
<style scoped lang="less">
.im-widget-side-panel_seach_box {
  display: flex;
  flex-shrink: 0;
  height: 100%;
  background-color: #fff;
  align-items: center;
  padding: 0 10px;

  .el-input__icon {
    line-height: 32px;
  }
  .el-input {
    margin-right: 5px;
    input {
      height: 32px;
    }
  }
  .el-checkbox__label {
    font-size: 10px;
    color: #7a7f99;
  }
}
</style>