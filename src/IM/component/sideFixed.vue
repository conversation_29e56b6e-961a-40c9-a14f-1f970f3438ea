<template>
  <div class="imView_small">
    <div class="imView_small_top">
      <div class="chat_box box" @click="openImView(isLogin)">
        <div class="i">
          <i class="iconfont icon-talk"></i>
          <div class="count" v-if="unreadCount > 0">
            {{ unreadCount > 99 ? '99+' : unreadCount }}
          </div>
        </div>

        <p>聊天
        <div class="count" v-if="unreadCount > 0">
          {{ unreadCount > 99 ? '99+' : unreadCount }}
        </div>
        </p>
        <p v-if="!isLogin" style="color: rgb(245, 79, 79)">未登录</p>
      </div>
      <div class="box" @click="lately(isLogin)">
        <i class="iconfont icon-toudi"></i>
        <p>最近</p>
        <p>投递</p>
        <!-- <p v-if="!isLogin" style="color: rgb(245, 79, 79)">未登录</p> -->
      </div>
      <div class="box" @click="my(isLogin)">
        <i class="iconfont icon-wode"></i>
        <p>我的</p>
        <!-- <p v-if="!isLogin" style="color: rgb(245, 79, 79)">未登录</p> -->
      </div>
      <div class="box" @click="openLink('//my.gxrc.com/Qa')">
        <i class="iconfont icon-yijian"></i>
        <p>意见</p>
        <p>反馈</p>
      </div>
    </div>
    <div class="imView_small_bottom">
      <div class="chat_box box" @click="goTop()">
        <i class="iconfont icon-top"></i>
        <p>回到</p>
        <p>顶部</p>
      </div>

      <el-popover placement="left" @show="AppShow" @hide="closePopover" :width="140" trigger="hover">
        <template #reference>
          <div class="box">
            <i class="iconfont icon-app"></i>
            <p>APP</p>
            <p>下载</p>
          </div>
        </template>
        <div class="wxgzh_3_11">
          <img src="https://image.gxrc.com/gxrcsite/global/app_code.png" alt="" />
          <p>扫码二维码下载APP查看</p>
        </div>
      </el-popover>

      <el-popover placement="left" @show="wxShow" @hide="closePopover" :width="100" trigger="hover">
        <template #reference>
          <div class="box">
            <i class="iconfont icon-wechat"></i>
            <p>微信</p>
            <p>公众号</p>
          </div>
        </template>
        <div class="wxgzh_3_11">
          <img :src="config.gzh" alt="" />
          <p>微信公众号</p>
        </div>
        <div class="wxgzh_3_11" style="margin-top: 15px;" v-if="config.sph">
          <img :src="config.sph" alt="" />
          <p>视频号</p>
        </div>
      </el-popover>
      <el-popover placement="left" @show="wxShow" @hide="closePopover" :width="120" trigger="hover">
        <template #reference>
          <div class="box">
            <i class="iconfont icon-douyin"></i>
            <p>抖音</p>
          </div>
        </template>
        <div class="wxgzh_3_11">
          <img :src="config.dyh" alt="" />
          <p style="margin-top: 5px;">{{ config.douyinName }}抖音号：</p>
          <p style="color:#999999;margin-top: 2px;">{{ config.dyAccount }}</p>
        </div>
      </el-popover>

      <el-popover placement="left" v-if="showWxNotice" @show="wxShow" @hide="closePopover" :width="100" trigger="hover">
        <template #reference>
          <div class="box">
            <i class="iconfont icon-inform"></i>
            <p>微信</p>
            <p>通知</p>
          </div>
        </template>
        <div class="wxgzh_3_11">
          <img src="https://image.gxrc.com/gxrcsite/global/<EMAIL>?v=2" alt="" />
          <p>扫码开启微信通知</p>
        </div>
      </el-popover>

      <el-popover placement="left" :width="220" @show="kfShow" @hide="closePopover" trigger="hover">
        <template #reference>
          <div class="box">
            <i class="iconfont icon-kefu1"></i>
            <p>在线</p>
            <p>客服</p>
          </div>
        </template>
        <div class="kf_3_11">
          <div class="line_1">
            <p>AI客服</p>
            <span class="line_1_span" @click="open('https://smartqa.gxrc.com/s/5n8c8t4oq')">立即咨询</span>
          </div>
          <div class="line_1" style="padding-top: 10px;">
            <p>求职客服</p>
            <span @click="qq(config.qzkfqq)"><i class="iconfont icon-QQ"></i> QQ交谈</span>
          </div>
          <div class="line_3" v-if="config.showEwm">
            <div>
              <p>微信客服</p>
              <p>(微信扫一扫)</p>
            </div>
            <img src="https://image.gxrc.com/gxrcsite/zt/MobileApp/images/qzz.png" alt="" />
          </div>
          <div class="line_2">
            <p>企业客服</p>
            <span @click="qq(config.qykfqq)"><i class="iconfont icon-QQ"></i> QQ交谈</span>
          </div>
          <div class="line_3" v-if="config.showEwm">
            <div>
              <p>微信客服</p>
              <p>(微信扫一扫)</p>
            </div>
            <img src="https://image.gxrc.com/gxrcsite/zt/MobileApp/images/qy.png" alt="" />
          </div>
          <div class="line_4" v-if="config.showEwm">
            <div>
              <p>毕业生免费</p>
              <p>就业指导预约</p>
              <p>（QQ扫一扫）</p>
            </div>
            <!-- <span @click="qq(config.qykfqq)"><i class="iconfont icon-QQ"></i> QQ交谈</span> -->
            <img src="https://image.gxrc.com/gxrcsite/zt/MobileApp/images/bys-qr.png" alt="" />
          </div>
          <div class="line_5">
            <div>
              <p>{{ config.phhone }}</p>
              <span>{{ config.text }}</span>
            </div>
          </div>
          <div class="line_6">
            <p>服务时间：周一至周五{{ config.serviceTime }}</p>
          </div>
        </div>
      </el-popover>

      <div class="box" @click="openLink('//my.gxrc.com/Qa?t=1')">
        <i class="iconfont icon-jubao"></i>
        <p>失信</p>
        <p>举报</p>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
//这个侧边条主站也用
import { computed, onBeforeMount, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from '../../store';
import gldyh from '@/assets/sideFixedImg/gldyh.jpg';
import glwxh from '@/assets/sideFixedImg/glwxh.jpg';
import glsph from '@/assets/sideFixedImg/glsph.jpg';

import fcgdyh from '@/assets/sideFixedImg/fcgdyh.jpg';
import fcgwxh from '@/assets/sideFixedImg/fcgwxh.jpg';
import fcgsph from '@/assets/sideFixedImg/fcgsph.jpg';


import lbdyh from '@/assets/sideFixedImg/lbdyh.jpg';
import lbwxh from '@/assets/sideFixedImg/lbwxh.jpg';
import lbsph from '@/assets/sideFixedImg/lbsph.jpg';

import wzdyh from '@/assets/sideFixedImg/wzdyh.jpg';
import wzwxh from '@/assets/sideFixedImg/wzwxh.jpg';
import wzsph from '@/assets/sideFixedImg/wzsph.jpg';

import bsdyh from '@/assets/sideFixedImg/bsdyh.jpg';
import bswxh from '@/assets/sideFixedImg/bswxh.jpg';
import bssph from '@/assets/sideFixedImg/bssph.jpg';


import yldyh from '@/assets/sideFixedImg/yldyh.jpg';
import ylwxh from '@/assets/sideFixedImg/ylwxh.jpg';
import { recordVisitInfoLog } from "@/http/api";

const props = withDefaults(
  defineProps<{
    unreadCount: number;

    myself: boolean
  }>(),
  { unreadCount: 0, myself: false },

);
const emit = defineEmits<{
  (e: 'openImView'): void;
}>();
const store = useStore();
const isLogin = computed(() => store.state.imModules.isLogin);

const Dictionary = {
  gl: {
    qzkfqq: **********,
    qykfqq: **********,
    phhone: '0773-7799333',
    text: '业务服务热线电话',
    douyinName: '广西人才网·桂林站',
    serviceTime: '9:00-12:00，14:00-18：00',
    showEwm: false,
    dyAccount: 'gxglrcwxw',
    dyh: gldyh,
    gzh: glwxh,
    sph: glsph
  },
  fcg: {
    qzkfqq: **********,
    qykfqq: **********,
    phhone: '0770-3260018',
    text: '业务服务热线电话',
    douyinName: '防城港人才网',
    serviceTime: '8:00-12:00，15:00-18：00',
    showEwm: false,
    dyAccount: 'fcgrcw2831608',
    dyh: fcgdyh,
    gzh: fcgwxh,
    sph: fcgsph
  },
  lb: {
    qzkfqq: **********,
    qykfqq: **********,
    phhone: '0772-4274885、4274887',
    text: '业务服务热线电话',
    douyinName: '来宾人才网',
    serviceTime: '8:00-12:00，15:00-18：00',
    showEwm: false,
    dyAccount: 'lb.gxrc',
    dyh: lbdyh,
    gzh: lbwxh,
    sph: lbsph
  },
  wz: {
    qzkfqq: **********,
    qykfqq: **********,
    phhone: '0774-3848639',
    text: '业务服务热线电话',
    douyinName: '梧州人才网',
    serviceTime: '9:00-12:00，14:00-18：00',
    showEwm: false,
    dyAccount: 'wz.gxrc',
    dyh: wzdyh,
    gzh: wzwxh,
    sph: wzsph
  },
  bs: {
    qzkfqq: **********,
    qykfqq: **********,
    phhone: '0776-2991716、2991718',
    text: '业务服务热线电话',
    douyinName: '百色人才网',
    serviceTime: '8:00-12:00，15:00-18：00',
    showEwm: false,
    dyAccount: 'bsrc2991718',
    dyh: bsdyh,
    gzh: bswxh,
    sph: bssph
  },
  yl: {
    qzkfqq: **********,
    qykfqq: **********,
    phhone: '0775-2226661',
    text: '业务服务热线电话',
    douyinName: '玉林人才网',
    serviceTime: '9:00-12:00，14:00-18：00',
    showEwm: false,
    dyAccount: 'Yl.gxrc',
    dyh: yldyh,
    gzh: ylwxh,
    sph: ''
  }
}

const config = reactive({
  qzkfqq: **********,
  qykfqq: **********,
  phhone: '400-0771-056',
  text: '企业服务转1 求职者服务转2',
  douyinName: '广西人才网',
  serviceTime: '9：00-18：00',
  showEwm: true,
  dyAccount: '**********',
  dyh: 'https://image.gxrc.com/gxrcsite/global/<EMAIL>?v=2',
  gzh: 'https://image.gxrc.com/gxrcsite/global/qrcode_gxrcw2003_s.jpg?v=2',
  sph: 'https://image.gxrc.com/gxrcsite/global/<EMAIL>?v=2'
})

const showWxNotice = ref(true);
onMounted(() => {
  recordVisitSource()

  try {
    if (window.self != window.top) {
      throw "test"
      document.domain = import.meta.env.VITE_APP_DO_MAIN;
      let ds = parent?.window.document.location.hostname.split(".")[0]
      if (ds) {
        if (ds != 'www' && Dictionary[ds]) {
          showWxNotice.value = false
          for (let i in config) {
            config[i] = Dictionary[ds][i]
          }
        }
      }
    }
  } catch {
    if (window.self != window.top) {
      window.parent.postMessage(
        {
          methods: 'get_location',
          data: { value: "" },
        },
        '*'
      );

      window.addEventListener('message', (e) => {
        const methods = e.data.methods;
        if (methods == 'get_location') {
          let ds = e.data.data.value
          if (ds) {
            if (ds != 'www' && Dictionary[ds]) {
              showWxNotice.value = false
              for (let i in config) {
                config[i] = Dictionary[ds][i]
              }
            }
          }
        }

      });
    }
  }
})

const recordVisitSource = async () => {//记录访问源
  function getQueryParam(name, url) {
    name = name.replace(/[\[\]]/g, '\\$&');
    var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
      results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, ' '));
  }

  let parentUrl = null;
  if (parent !== window) {
    try {
      parentUrl = parent.location.href;
    } catch (e) {
      let myUrl = window.location.href;
      parentUrl = getQueryParam("url", myUrl);
    }
  } else {
    parentUrl = window.location.href;
  }

  const res = await recordVisitInfoLog(parentUrl);
}

const qq = (qqNumber: number) => {
  if (props.myself) {
    window.open(`http://wpa.qq.com/msgrd?v=3&uin=${qqNumber}&site=qq&menu=yes`);
    return
  }

  if (window.self != window.top) {
    try {
      throw "test"
      document.domain = import.meta.env.VITE_APP_DO_MAIN;
      parent.window.open(`http://wpa.qq.com/msgrd?v=3&uin=${qqNumber}&site=qq&menu=yes`)
    } catch {
      window.parent.postMessage(
        {
          methods: 'open_link',
          data: {
            value: `http://wpa.qq.com/msgrd?v=3&uin=${qqNumber}&site=qq&menu=yes`,
          },
        },
        '*'
      );
    }
  }
};

const open = (url:string) =>{
  if (props.myself) {
    window.open(url);
    return
  }

  if (window.self != window.top) {
    window.parent.postMessage(
        {
          methods: 'open_link',
          data: {
            value: url,
          },
        },
        '*'
      );
  }
}

const openImView = (type: boolean) => {
  if (type) {
    emit('openImView');
  } else {

    if (window.self != window.top) {
      try {
        throw "test"

        document.domain = import.meta.env.VITE_APP_DO_MAIN;
        let url = parent.window.document.location.href.split("/");
        let str = ''
        if (url[2]) {
          str = url[2].split(".")[0];
        }
        parent.window.document.location.href =
          `//my.${import.meta.env.VITE_APP_DO_MAIN}/${str && str != 'www' ? str + '/' : ''}login`;
      } catch {
        window.parent.postMessage(
          {
            methods: 'open_login',
            data: {
              value: import.meta.env.VITE_APP_DO_MAIN,
            },
          },
          '*'
        );
      }
    }
  }
};
const openLink = (url: string) => {
  window.open(url);
};
const goTop = () => {
  if (window.self != window.top) {
    try {
      throw "test"
      document.domain = import.meta.env.VITE_APP_DO_MAIN;
      let el = parent.window.document.body;
      if (el) {
        el.scrollTop = parent.window.document.documentElement.scrollTop = 0;
      }
    } catch {
      window.parent.postMessage(
        {
          methods: 'go_top',
          data: {
            value: 0,
          },
        },
        '*'
      );
    }
  } else {
    if (props.myself) {

      document.documentElement.scrollTop = 0;
      return
    }
  }
};
const router = useRouter()
const lately = (type: boolean) => {

  // if(!type) return

  if (window.self != window.top) {
    try {
      throw "test"
      document.domain = import.meta.env.VITE_APP_DO_MAIN;
      parent.window.document.location.href =
        `//my.${import.meta.env.VITE_APP_DO_MAIN}/Apply/0`;
    } catch {

      window.parent.postMessage(
        {
          methods: 'open_href',
          data: {
            value: `//my.${import.meta.env.VITE_APP_DO_MAIN}/Apply/0`,
          },
        },
        '*'
      );
    }
  } else {
    if (props.myself) {
      router.push('/apply/0')
      return
    }
  }
};
const my = (type: boolean) => {

  // if(!type) return

  if (window.self != window.top) {
    try {
      throw "test"
      document.domain = import.meta.env.VITE_APP_DO_MAIN;
      parent.window.document.location.href = `//my.${import.meta.env.VITE_APP_DO_MAIN}/`;
    } catch {
      window.parent.postMessage(
        {
          methods: 'open_href',
          data: {
            value: `//my.${import.meta.env.VITE_APP_DO_MAIN}/`,
          },
        },
        '*'
      );
    }
  } else {
    if (props.myself) {
      router.push('/')
      return
    }
  }
};
const kfShow = () => {
  setTimeout(() => {
    parentWindow('309px');
  }, 200);
};
const wxShow = () => {
  setTimeout(() => {
    parentWindow('309px');
  }, 200);
};
const AppShow = () => {
  setTimeout(() => {
    parentWindow('309px');
  }, 200);
};
const parentWindow = (width: string) => {

  if (window.self != window.top) {
    try {
      throw "test"
      document.domain = import.meta.env.VITE_APP_DO_MAIN;
      let el = parent.window.document.getElementById('im_widget');

      if (el) {
        if (width == '55px' && el.style.width != '309px') {
          return;
        }
        el.style.width = width;
      }
    } catch {
      window.parent.postMessage(
        {
          methods: 'change_width',
          data: {
            value: width,
          },
        },
        '*'
      );
    }
  }
};
const closePopover = () => {
  parentWindow('55px');
};
</script>
<style lang="less">
.kf_3_11 {
  .line_6 {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    p {
      color: #ccc;
      font-size: 12px;
    }
  }

  .line_5 {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(204, 204, 204, 0.493);

    >div {
      text-align: left;

      p {
        color: rgb(32, 32, 32);
        font-size: 14px;
      }

      span {
        color: #ccc;
        font-size: 12px;
      }
    }
  }

  .line_4 {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(204, 204, 204, 0.493);

    p {
      font-size: 14px;
      color: rgb(32, 32, 32);
    }

    span {
      padding: 2px 3px;
      border: 1px solid #5798fc;
      border-radius: 4px;
      font-size: 10px;
      color: #5798fc;
      cursor: pointer;
    }

    img {
      width: 85px;
      height: 85px;
      margin-right: -12px;
    }
  }

  .line_3 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(204, 204, 204, 0.493);

    >div {
      width: 70px;
      text-align: center;

      p {
        color: #ccc;
        font-size: 14px;
      }
    }

    img {
      width: 75px;
      height: 75px;
      margin-right: -5px;
    }
  }

  .line_2 {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;

    p {
      font-size: 14px;
      color: rgb(32, 32, 32);
    }

    span {
      padding: 2px 3px;
      border: 1px solid #5798fc;
      border-radius: 4px;
      font-size: 10px;
      color: #5798fc;
      cursor: pointer;
    }
  }

  .line_1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(204, 204, 204, 0.493);

    p {
      font-size: 14px;
      color: rgb(32, 32, 32);
    }

    span {
      padding: 2px 3px;
      border: 1px solid #5798fc;
      border-radius: 4px;
      font-size: 10px;
      color: #5798fc;
      cursor: pointer;
    }
    .line_1_span{
      width: 55px;
      height:22px;
      line-height: 22px;
      text-align: center;
      display: inline-block;
    }
  }
}

.wxgzh_3_11 {
  margin: 0;
  text-align: center;

  img {
    width: 100px;
    height: 100px;
  }

  p {
    line-height: 12px;
    font-size: 12px;
  }
}

.el-popover.el-popper {
  min-width: auto !important;
}
</style>
<style scoped lang="less">
@keyframes myfirst {
  from {
    width: 30px;
  }

  to {
    width: 48px;
  }
}

@-webkit-keyframes myfirst

/* Safari and Chrome */
  {
  from {
    width: 30px;
  }

  to {
    width: 48px;
  }
}

.imView_small:hover {
  // animation: myfirst .5s ;
  // -webkit-animation: myfirst .5s; /* Safari and Chrome */
  // animation-fill-mode:forwards
  width: 48px;
  transition: all 0.3s linear;
}

.imView_small {
  width: 30px;
  height: 100%;
  background: #fff;
  box-shadow: -4px 0px 10px rgba(237, 239, 245, 0.5);
  opacity: 1;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s linear;

  .box {
    width: 100%;

    height: 55px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    cursor: pointer;

    .i {
      position: relative;
    }

    .count {
      background: #fe5c5b;
      opacity: 1;
      border-radius: 10px;
      padding: 0 4px;
      height: 15px;
      font-size: 10px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      line-height: 14px;
      color: #ffffff;
      opacity: 1;
      position: absolute;
      right: -7px;
      top: -3px;
    }

    p {
      display: none;
      font-size: 12px;
      color: #fff;
      position: relative;
    }

    i {
      font-size: 16px;
      color: rgb(119, 119, 119);
    }
  }

  .box:hover {
    background: #5798fc;

    .i,
    i {
      display: none;
    }

    p {
      display: block;
    }
  }

  .no_hover:hover {
    i {
      display: block;
    }
  }

  .imView_small_top {
    width: 100%;
  }

  .imView_small_bottom {
    width: 100%;
  }
}
</style>
