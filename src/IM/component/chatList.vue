<template>
  <div class="chat-content">

    <el-scrollbar style="height: 100%" ref="myScrollbar" id="chat-list" view-style="height:auto">
      <div style="position: relative; height: 100%">
        <div style="display: flex; flex-direction: column">
          <chat-item v-for="(msg, index) in msglist" :type="type" :rawMsg="msg" :isRobot="isRobot"
            :userInfos="userInfos" :myInfo="myInfo" :avatar="avatar" :key="index" :isHistory="isHistory"
            :deliverList="deliverList" @refreshStatus="refreshStatus" :showMap="showMap"
                :isBlack="isBlack" :inviteChats="inviteChatsStatus" @Delivery="DeliveryChat"
            @openResumeDialog="handleOpenResumeDialog" @ResumeSubmit="ResumeSubmit"></chat-item>
        </div>
      </div>
      <div class="alert-list" v-if="isBlack">
        <el-alert type="warning" :closable="false" style="background-color: #eef2f5; color: #333333">
          <div slot="title">
            <span>您已设置消息免打扰，对方发送的消息将不再显示</span>
            <span class="alert-list-opera" @click="cancelBlack">取消免打扰</span>
          </div>
        </el-alert>
      </div>
    </el-scrollbar>
    <!-- <div class="im-alert-list">
      <el-alert type="warning">
        <div class="report__content" slot="title">
          <span class="report__text">防骗提示：对方要求添加QQ、微信、转账、汇款等行为， 均可能涉嫌诈骗。</span>
          <div class="report__button-box">
            <a href="//vip.tgxrc.com/Qa" class="report__button" target="_blank" rel="noopener noreferrer">举报</a>
          </div>
        </div>
      </el-alert>
    </div>-->
    <ResumeDialog :positionData="positionData" :showResumeDialog="showResumeDialog"
      @closeResumeDialog="closeResumeDialog" @submit="ResumeSubmit" :type="submitType"></ResumeDialog>
  </div>
</template>

<script lang="ts">
import ResumeDialog from './resumeDialog.vue';
import ChatItem from './chatItem.vue';
import { mapState, useStore } from 'vuex';
import { baiduMap } from '@/utils/syncSDK';
import { DeliverResume } from '@/IM/api/miniapp';
import {sendResume} from "../../IM/api";
import { getCookies } from '@/utils/common';
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus';
import Vue, {
  onMounted,
  onUpdated,
  reactive,
  nextTick,
  defineComponent,
  ref,
  watch,
  onUnmounted,
  computed,
  Ref,
} from 'vue';
export default defineComponent({
  props: {
    type: String, // 类型，chatroom, session
    canLoadMore: [String, Boolean],
    showLoadMore: {
      type: Boolean,
      default() {
        return false;
      },
    },
    isRobot: {
      type: Boolean,
      default() {
        return false;
      },
    },
    msglist: {
      type: Array,
      default() {
        return [];
      },
    },
    userInfos: {
      type: Object,
      default() {
        return {};
      },
    },
    myInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    isHistory: {
      type: Boolean,
      default() {
        return false;
      },
    },
    isBlack: {
      type: Boolean,
      default: false,
    },
    scrollHeight: {
      type: Boolean,
      default: false,
    },
    chatlistHeight: {
      type: String,
    },
    account: String,
    avatar: String,
    deliverList: Array,
    inviteChats: Array,
  },
  components: {
    ChatItem,
    ResumeDialog,
  },
  setup(props: any, { emit }) {
    const store = useStore();
    const state = reactive({
      currPagePos: 0,
      scrollTimer: '' as any,
    });
    const myScrollbar: any = ref(null);
    const pageUpDown = computed(() => {
      return store.state.imModules.pageUpDown;
    });
    const inviteChatsStatus = computed(() => {
      return props.inviteChats;
    });

    const DeliveryChat = (obj: {
      name: string;
      guid: string;
      entName: string;
      salary: string;
      entId: number;
    }) => {
      positionData.positionGuid = obj.guid;
      positionData.positionName = obj.name;
      positionData.enterpriseName = obj.entName;
      positionData.paypackage = obj.salary;
      positionData.enterpriseId = obj.entId;
      showResumeDialog.value = true;
    };
    const positionData = reactive({
      positionName: '',
      positionGuid: '',
      enterpriseName: '',
      paypackage: '',
      enterpriseId: 0,
    });
    const showResumeDialog = ref(false);
    const submitType = ref(2);
    const handleOpenResumeDialog = (data:any,type:any) => {
      positionData.enterId = data.enterId;
      positionData.positionId= data.positionId,
      positionData.positionGuid = data.positionGuid;
      positionData.positionName = data.positionName;
      positionData.enterpriseName = data.enterpriseName;
      positionData.paypackage = data.paypackage;
      positionData.enterpriseId = data.enterpriseId;
      showResumeDialog.value = true;
      submitType.value = type;
    };
    const closeResumeDialog = () => {
      showResumeDialog.value = false;
    };

    const ActiveSessionId = computed(
      () => store.state.imModules.ActiveSessionId
    );
    const chat = (sessionIds: string, type = 1) => {
      let id = sessionIds.replace(/^p2p-ent/, '');
      let sessionId = ActiveSessionId.value;
      if (sessionId === id) return;
      store.commit('updateWidgetActiveSessionId', sessionIds);

      store.commit('updateActiveSessionId', id);
    };
    const  sendResumeSubmit= async(ResumeId: number) => {
        let districtId = getCookies("bid");
        let data = await sendResume({
          resumeId: ResumeId,
          enterId: positionData.enterId,
          positionId: positionData.positionId,
          districtId: districtId,
          from: 0,
        });
        if (data.code == 0) {
          ElMessageBox.alert(
            data.message ? data.message : "对不起，发送失败，请稍后重试",
            {
              confirmButtonText: "确定",
              callback: (action) => { },
            }
          );
        }
        emit("refreshStatus");
        closeResumeDialog();
      }
    const ResumeSubmit = async (Resume: any,chatItemPositionData:any) => {
      if(submitType.value == 1){
        sendResumeSubmit(Resume);
      }else{
        const loading = ElLoading.service({
        lock: true,
        text: '投递中',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      let districtId = getCookies('bid');
      let positionGuid = chatItemPositionData?.positionGuid||positionData.positionGuid
      let enterpriseId = chatItemPositionData?.enterpriseId||positionData.enterpriseId

      let post={
        PositionGuid: positionGuid,
        ResumeGuid: Resume.resumeGuid || '',
        IsTop: false,
        districtId: districtId,
        from: 0,
        resumeDeliveryType:5
      }
      let data = await DeliverResume(post);
      if (data.succeeded) {
        ElMessage({
          message: data.errors,
          type: 'success',
        });
        localDeliver.value.push(positionGuid);
        chat(`p2p-ent${enterpriseId}`, 1);
      } else {
        ElMessage({
          message: data.errors,
          type: 'error',
        });
      }
      loading.close();
      closeResumeDialog();
      emit("refreshStatus");
      }
    };
    const deliverList = computed(() =>
      props.deliverList.concat(localDeliver.value)
    );
    const localDeliver: Ref<any[]> = ref([]);

    const msglist = computed(() => props.msglist);
    const methods = {
      scrollDown() {

        let height: any = myScrollbar.value.wrap.scrollHeight;
        if (props.scrollHeight) {
          //翻聊天记录

          myScrollbar.value.wrap.scrollTop = height - state.currPagePos;
          store.commit('scrollHeiht', false);
        } else if (pageUpDown.value) {
          //聊天入底
          myScrollbar.value.wrap.scrollTop = height;
        }
        state.currPagePos = height;
      },
      listenScrollbar() {
        let scrollbarEl = myScrollbar.value.wrap;
        scrollbarEl.onscroll = () => {
          let top = scrollbarEl.scrollTop;
          if (top === 0) {
            if (state.scrollTimer) clearTimeout(state.scrollTimer);
            state.scrollTimer = setTimeout(() => {
              store.commit('updatePageUpDown', false);
              emit('getHistory');
            }, 50);
          }
        };
      },
      cancelBlack() {
        emit('cancelBlack');
      },
      refreshStatus(value: any, flag: any) {
        // let item = this.msglist.find(i => i.idClient == value)
        // item.wxStatus = flag
        // let index = this.msglist.findIndex(i => i.idClient == value)
        // this.$set(this.msglist,index,item)
        emit('refreshStatus');
      },
    };
    onUpdated(() => {
      methods.scrollDown();
    });

    const intNum = ref(0);
    watch(
      () => props.msglist,
      () => { }
    );

    const showMap = ref(false);

    onMounted(() => {
      baiduMap().then(() => {
        showMap.value = true;
      });
      methods.listenScrollbar();
      nextTick(() => {
        const child = myScrollbar.value.wrap;
        child.style.height = 'calc(100% )';
        //this.height = 'height: calc(100% - 60px)'
        //let length = this.msglist.length;
        let list = props.msglist.filter((item) => item.type != 'timeTag');

        if (list && list.length < 10) {
          store.dispatch('getHistoryMsgs', {
            scene: 'p2p',
            to: props.account,
            msgLimit: 10 - length,
          });
        }

        methods.scrollDown();
      });
    });

    return {
      myScrollbar,
      ...methods,
      msglist,
      showMap,
      positionData,
      showResumeDialog,
      closeResumeDialog,
      ResumeSubmit,
      DeliveryChat,
      handleOpenResumeDialog,
      submitType,
      inviteChatsStatus,
    };
  },
});
</script>

<style lang="less">
.chat-content {
  // width: 100%;
  // overflow-y: auto;
  // background-color: #fff;
  // height: 100%;
  // position: relative;
  // display: flex;
  // flex-direction: column;
  // overflow: hidden;
  bottom: 0;
  left: 0;
  position: absolute !important;
  right: 0;
  top: 0;

  .im-alert-list {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
    background-color: hsla(0, 0%, 100%, 0.5);

    .el-alert__content {
      width: 100%;
    }

    .report {
      &__content {
        display: flex;
        flex-grow: 1;
      }

      &__text {
        display: inline-block;
        flex: 1;
        color: #aa6c00;
      }

      &__button {
        display: inline-block;
        width: 34px;
        height: 18px;
        color: #aa6c00 !important;
        line-height: 18px;
        margin: 0 auto;
        border: 1px solid rgba(170, 108, 0, 0.19);
        border-radius: 2px;

        &-box {
          display: inline-block;
          width: 70px;
          text-align: center;
        }
      }
    }
  }
}

.no-msg span {
  padding: 3px 15px;
  background-color: #e5f4ff;
  color: #6b8299;
  font-size: 12px;
}

.chat-content .el-scrollbar__wrap {
  overflow-x: hidden;
}

.el-scrollbar__thumb {
  background-color: rgba(144, 147, 153, 0.3);
}

.el-scrollbar__view {
  height: 100%;
}

.alert-list {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;

  .el-alert__description {
    color: #333333 !important;
  }
}

.alert-list-opera {
  margin-left: 15px;
  font-weight: 900;
  text-decoration: underline;
  cursor: pointer;
}
</style>
