<template>
  <div
    class="m-emoji-wrapper"
    style="width: 410px; height: auto; display: block"
  >
    <el-scrollbar style="height: 350px">
      <div class="m-emoji-picCol" style="width: 410px; height: 100%">
        <ul class="m-emoji-picCol-ul" style="height: auto">
          <span
            v-for="(item, index) in currEmoji.list"
            :key="index"
            :class="{
              'm-emoji-img': item.type === 'emoji',
              'm-emoji-pinup': item.type === 'pinup',
            }"
            @click="selectEmoji(item)"
          >
            <img :src="item.img" />
          </span>
        </ul>
      </div>
    </el-scrollbar>
    <div class="m-emoji-chnCol" style="width: 410px; height: auto">
      <div class="m-emoji-chnCol-ul" style="width: auto; height: 30px">
        <span
          v-for="(item, index) in emoji"
          :key="index"
          :class="{ 'f-sel': item.name == currAlbum }"
        >
          <img :src="item.album" />
        </span>
        <!-- <span  v-for="(item,index) in pinup" :key="index" :class="{'f-sel':item.name==currAlbum}" @click.stop="selectAlbum(item)">
          <img :src="item.album" alt />
        </span>-->
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, toRefs } from 'vue';
import emojiObj from '../configs/emoji';
// import { sendExpression } from "@/api";
function genEmojiList(type, emojiList) {
  let result = {};
  for (let name in emojiList) {
    let emojiMap = emojiList[name];
    let list = [];
    for (let key in emojiMap) {
      list.push({
        type,
        name,
        key,
        img: emojiMap[key].img,
      });
    }
    if (list.length > 0) {
      result[name] = {
        type,
        name,
        list,
        album: list[0].img,
      };
    }
  }
  return result;
}
export default defineComponent({
  props: {
    type: String,
    scene: String,
    to: String,
  },
  setup(props,{emit}) {
    const state = reactive({
      currType: 'emoji',
      currAlbum: 'emoji',
    });
    const emoji = computed(() => {
      return genEmojiList('emoji', emojiObj.emojiList);
    });
    const pinup = computed(() => {
      return genEmojiList('pinup', emojiObj.pinupList);
    });
    const currEmoji = computed(() => {
      if (state.currType === 'emoji') {
        return emoji.value[state.currAlbum];
      } else if (state.currType === 'pinup') {
        return pinup.value[state.currAlbum];
      }
      return [];
    });
    const methods = {
      selectAlbum(album: Record<string, string>) {
        state.currType = album.type;
        state.currAlbum = album.name;
      },
      async selectEmoji(emoji) {
      if (state.currType === "emoji") {
        // 由触发父组件事件，增加表情文案
        emit("add-emoji", emoji.key);
      } else if (state.currType === "pinup") {
        // if (this.type === "session") {
        //   let to = this.to.replace("ent", "").replace("job", "");
        //   let Catalog = this.currAlbum;
        //   let Chartlet = emoji.key;
        //   let { data } = await sendExpression(to, Catalog, Chartlet);
        //   if (data.code === 400) {
        //     this.$alert(data.message);
        //   }
        // } else if (this.type === "chatroom") {
        //   this.$store.dispatch("sendChatroomMsg", {
        //     type: "custom",
        //     pushContent: "[贴图表情]",
        //     content: {
        //       type: 3,
        //       data: {
        //         catalog: this.currAlbum,
        //         chartlet: emoji.key
        //       }
        //     }
        //   });
        // }
        // this.$emit("hide-emoji");
      }
    }
    };
    return {
      ...toRefs(state),
      currEmoji,
      ...methods,
      emoji
    };
  },
});
</script>

<style lang="less">
ul {
  margin: 0;
  padding: 0;
}
.m-emoji-wrapper {
  //position: absolute;
  //border: 1px solid #ccc;
  // width: 400px;
  // height: 300px;
  background-color: #fff;
  // position: absolute;
  // top: 180px;
  // left: 39px;
  z-index: 100;
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
}

.el-scrollbar__thumb {
  background-color: rgba(144, 147, 153, 0.3);
}
.m-emoji-picCol {
  position: relative;
  // background: #fff;
  // overflow-y: scroll;
}
.m-emoji-picCol-ul {
  zoom: 1;
}
.m-emoji-picCol-ul {
  position: relative;
  list-style-type: none;
}
.m-emoji-picCol-ul span {
  float: left;
  display: inline-block;
  margin: 0;
  background: #fff;
  // border-right: #eee 1px solid;
  // border-bottom: #eee 1px solid;
  cursor: pointer;
  padding: 10px;
}
.m-emoji-chnCol {
  border-top: 1px solid #e8ebef;
  padding: 4px;
}
.m-emoji-chnCol-ul {
  position: relative;
  list-style-type: none;
}
.m-emoji-chnCol-ul span.f-sel {
  background: #fff;
}
.m-emoji-chnCol-ul span {
  float: left;
  display: inline-block;
  height: 100%;
  width: 50px;
  background: #f0f0f0;
  cursor: pointer;
}
.m-emoji-chnCol-ul span img {
  display: block;
  height: 80%;
  width: auto;
  margin: 10%;
}
.m-emoji-img {
  width: 19px;
  height: auto;
}
.m-emoji-pinup {
  width: 75px;
  height: auto;
}
</style>
