<template>
  <div class="push_info_box" @click="openLink">
    <div class="push_info_content">
      <img :src="contentimg" :mode="'scaleToFill'" />
    </div>
    <div class="push_info_title">
      <text>{{ title }}</text>
    </div>
  </div>
</template>

<script setup lang="ts">

import {Recordpushinfolog} from "@/http/ad"
import { ref } from "vue";
// import { ad } from "@/services/my/ad";
const props = withDefaults(
  defineProps<{
    contentimg: string;
    title: string;
    infoUrl: string|undefined;
    infoKey: string|number;
    activityType: string|number;
  }>(),
  {}
);

const openLink = () => {
  Recordpushinfolog({
    ActivityType: props.activityType,
    InfoKey: props.infoKey,
    Type: 0
  }).then((data)=>{
    console.log(data);
    
  })
  // ad.Recordpushinfolog({
  //   ActivityType: props.activityType,
  //   InfoKey: props.infoKey,
  //   Type: 0,
  //   // #ifdef MP-WEIXIN
  //   from: 1,
  //   // #endif
  //   // #ifdef MP-ALIPAY
  //   from: 7,
  //   // #endif
  //   // #ifdef MP-TOUTIAO
  //   from: 6,
  //   // #endif
  // }).then((data) => {
  //   console.log(data);
  // });
  window.open(props.infoUrl);
};
</script>
<style lang="less" scoped>
.push_info_box {
  background: #F2F2F2;
  width: 100%;
  cursor: pointer;

  .push_info_content {
    width: 100%;
    img {
      width: 100%;
      height: 120px;
    }
  }
  .push_info_title {
    box-sizing: border-box;
    padding: 12px 10px;
    padding-top: 6px;
    text {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 13px;
      color: #333333;
      line-height: 21px;
    }
  }
}
</style>
