<template>
  <el-dialog
    title="举报"
    v-model="report"
    width="550px"
    top="30vh"
    :append-to-body="true"
    :close-on-click-modal="false"
    @close="cancel"
  >
    <el-form class="report-form">
      <el-form-item label="公司名称">
        <span class="pd-left">{{ name }}</span>
      </el-form-item>
      <el-form-item label="举报原因">
        <el-radio-group v-model="type" class="group pd-left">
          <el-radio
            class="block"
            v-for="item in list"
            :key="item.item"
            :label="item.item"
            >{{ item.value }}</el-radio
          >
          <el-input
            v-if="type == 15"
            type="textarea"
            v-model="content"
            placeholder="请描述举报内容"
            maxlength="200"
            resize="none"
            rows="4"
            show-word-limit
            clearable
          ></el-input>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <div class="im-report-resume-footer">
        <el-button size="small" @click="cancel">取消</el-button>
        <el-button size="small" type="primary" @click="reportData"
          >举报</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch } from 'vue';
import { report } from '../../http/api';
import {getPosition} from "../api/index"
import { ElMessage, ElMessageBox } from 'element-plus';
export default defineComponent({
  props: {
    Model: {
      type: Boolean,
      default: false,
    },
    sessionId: {
      type: String,
    },
    name: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const state = reactive({
      report: false,
      type: 0,
      content: '',
      list: [
        { item: 0, value: '广告合作' },
        { item: 1, value: '色情骚扰' },
        { item: 2, value: '政治敏感内容' },
        { item: 3, value: '人生攻击' },
        { item: 4, value: '职位不招了' },
        { item: 5, value: '薪资不实' },
        { item: 6, value: '公司地址不实' },
        { item: 7, value: '公司虚假' },
        { item: 8, value: '拖欠工资' },
        { item: 9, value: '网络博彩' },
        { item: 10, value: '招生' },
        { item: 11, value: '空号' },
        { item: 12, value: '劳务中介' },
        { item: 13, value: '虚假职位' },
        { item: 14, value: '交费' },
        { item: 15, value: '其他原因' },
      ],
    });
    watch(
      () => props.Model,
      (val) => {
        state.report = val;
      }
    );
    const methods = {
      cancel() {
        emit('update:Model', false);
      },
      async reportData() {
        if (!props.sessionId) {
          ElMessage.error('错了哦，请选择举报对象');
          return;
        }
        if (state.type == 15 && !state.content) {
          ElMessage.error('请填写举报原因哦');
          return;
        }
        let position = null
        let positionData: Record<string, any> = await getPosition(`ent${props.sessionId}`);
      
          if (positionData.code == 1) {
            position = positionData.data;
          }
        let content =
          state.type == 15
            ? state.content
            : state.list.find((item) => item.item == state.type).value;
        let model = {
          enterpriseId: props.sessionId,
          opineContent: content,
          opineType: 10,
          opineTitle: content,
          enterpriseName:props.name,
          positionId:position ? position.positionID : 0
        };
        let data = await report(model);
        ElMessageBox.alert(data.message, '提示', {
          confirmButtonText: '确定',
          callback: (action) => {
            methods.cancel();
          },
        });
      },
    };
    return {
      ...methods,
      ...toRefs(state),
      ...toRefs(props),
    };
  },
});
</script>

<style lang="less">
.report-form {
  padding: 10px 30px;
  .group {
    display: flex;
    // flex-direction: column;
    flex-wrap: wrap;
  }
  .block {
    // padding: 10px 0;
  }
  .el-radio {
    height: 30px;
  }
  .pd-left {
    padding-left: 10px;
    padding-top: 4px;
  }
  .el-form-item {
    margin-bottom: 5px;
  }
  .el-form-item__label {
    color: #777c89;
  }
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .im-report-resume-footer {
    display: flex;
    width: 100%;
    justify-content: center;
  }
}
</style>
