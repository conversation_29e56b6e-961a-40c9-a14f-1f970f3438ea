<template>
  <div class="chat-message">
    <p class="u-msgTime" v-if="msgs.type === 'timeTag' || msgs.type === 'commonNotice-tip'">
      {{ msgs.showText }}
    </p>
    <div v-else-if="
      msgs.type === 'tip' ||
      msgs.type === 'wechat-tip' ||
      msgs.type === 'resume-tip' ||
      msgs.type === 'invite-interview-tip' ||
      msgs.type === 'phone-tip' ||
      msgs.type === 'commonNotice-toast'
    " class="u-notice tc item">
      <span>{{ msgs.showText }}</span>
    </div>
    <div v-else-if="msgs.type === 'notification' && msgs.scene === 'team'" class="notification">
      {{ msgs.showText }}
    </div>
    <div v-else-if="msgs.type === 'wechat-yes'" class="wechat-yes">
      <div class="wechat-content">
        <div class="wechat-info">
          <div class="info-left">
            <img class="img" :src="avatar" />
          </div>
          <div class="wechat-text">
            <p class="user-name">对方的微信号:</p>
            <p class="user-wx">{{ msgs.wechat }}</p>
          </div>
        </div>
        <div class="wechat-button">
          <el-button type="primary" style="padding: 10px 40px" @click="copy(msgs.wechat)">复制微信号</el-button>
        </div>
      </div>
    </div>
    <div v-else-if="
      msgs.type === 'position-info' ||
      msgs.type === 'custom-SendInviteChatPosition'
    " class="position-info">
      <div class="position-info__content">
        <div class="position-info__title">
          <a style="display: flex" :href="`//www.${T}gxrc.com/jobDetail/${msgs.showCustom.PositionGuid}`"
            target="_blank">
            <div class="position-info__title--name">
              {{ msgs.showCustom.positionName }}
            </div>
            <div class="position-info__title--salary">
              {{ msgs.showCustom.salary }}
            </div>
          </a>
        </div>
        <div class="position-info__tags">
          <div class="position-info__tag" v-if="msgs.showCustom.workYear">
            {{ msgs.showCustom.workYear }}
          </div>
          <div class="position-info__tag" v-if="msgs.showCustom.educationDegree">
            {{ msgs.showCustom.educationDegree }}
          </div>
          <div class="position-info__tag" v-if="msgs.showCustom.workPlace">
            {{ msgs.showCustom.workPlace }}
          </div>
        </div>
        <div class="position-info__footer">
          <div class="position-info__footer--name">
            {{ msgs.showCustom.enterpriseName }}
          </div>
          <div class="position-info__footer--link">
            <a class="link" :href="`//www.${T}gxrc.com/jobDetail/${msgs.showCustom.PositionGuid}`" target="_blank">
              查看详情
              <i class="el-icon-arrow-right position-info__footer--link--img"></i>
              <!-- <img :src="slice" class="position-info__footer--link--img" /> -->
            </a>
          </div>
        </div>
        <div v-if="msgs.showCustom.remark" class="position-info__time">
          {{ msgs.showCustom.remark }}
        </div>
      </div>
      <div class="position-info--tip" v-if="msgs.posiSwitchTip">
        <div class="position-info--tip__inner">{{ msgs.posiSwitchTip }}</div>
      </div>
    </div>

    <div v-else-if="msgs.flow === 'in' || msgs.flow === 'out'">
      <!-- <div style="text-align: center" v-if="msgs.blackContent">
        <span class="blacktips">{{ msgs.blackContent }}</span>
      </div> -->
      <div class="item" :class="{
        'item-me': msgs.flow === 'out',
        'item-you': msgs.flow === 'in',
        read: msgs.flow === 'out' && msgs.isRead,
      }">
        <img v-if="msgs.avatar" :src="msgs.avatar.replace('http:', 'https:')" class="img j-img" />
        <el-tooltip :disabled="(msgs.flow == 'in' && msgs.type != 'audio') || (show && !showTranslateText)"
          effect="dark" placement="top-start">
          <template #content>
            <div style="cursor: pointer">

              <span v-if="msgs.flow === 'out' && !show" @click="revocateMsg(msgs)"
                :style="`${msgs.type === 'audio' ? 'padding-right: 10px;' : ''}cursor: pointer;`">撤回</span>
              <span v-if="msgs.type === 'audio'" @click="translateToText" style="cursor: pointer;">转文字</span>
            </div>
          </template>

          <div class="msg msg-text" v-if="msgs.type === 'text' || msgs.type === 'custom-text'" :id="msgs.idServer"
            @mouseover="revocate">
            <div class="box">
              <div class="cnt" v-html="msgs.showText"></div>
            </div>
          </div>
          <div class="msg msg-text" v-else-if="msgs.type === 'custom-type3'" :id="msgs.idServer">
            <div class="box">
              <div class="cnt">
                <div ref="mediaMsg"></div>
              </div>
            </div>
          </div>
          <div class="msg msg-text msg-image" v-else-if="msgs.type === 'image'" :id="msgs.idServer"
            @mouseover="revocate">
            <div class="cnt">
              <el-image :src="msgs.file.url" :preview-src-list="[msgs.file.url]"></el-image>
            </div>
          </div>
          <div class="msg msg-text msg-audio" v-else-if="msgs.type === 'audio'" :id="msgs.idServer"
            @mouseover="revocate" @click="play" v-loading="translateLoaing">
            <div class="box">
              <div class="cnt audio" :style="{ width: msgs.width }">
                <div>{{ duration }}s</div>
                <img :src="`https://image.gxrc.com/gxrcsite/wxMiniApp/2024/${audioIconType}.png`" alt=""
                  style="width: 30px; height: 30px" />
                <audio ref="audioref" @pause="onPause" @ended="onEnded">
                  <source :src="msgs.audioSrc" type="audio/wav" />
                </audio>
              </div>
            </div>
          </div>
          <div class="msg msg-text" v-else-if="msgs.type === 'notification'">
            <div class="box">
              <div class="message__notification">
                {{ msgs.showText }}
                <div class="message__notification-icon">
                  <i class="el-icon-video-camera notification-icon"></i>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="msgs.type == 'custom-position'" :class="{
            'custom-me': msgs.flow === 'out',
            'custom-you': msgs.flow === 'in',
          }" :id="msgs.idServer">
            <div class="jobBox-card">
              <div class="box-title">{{ msgs.short }}</div>
              <div class="box-body">
                <div class="body-info">
                  <span class="positionName">{{
                    msgs.showCustom.positionName
                  }}</span>
                  <span class="salary">{{ msgs.showCustom.salary }}</span>
                </div>
                <div class="ent">{{ msgs.showCustom.enterpriseName }}</div>
              </div>
            </div>
          </div>
          <div v-else-if="msgs.type === 'custom-wechat'" class="msg msg-text">
            <div class="box">
              <div class="cnt">
                <div>{{ msgs.showCustom }}</div>
                <div class="ctrl-button" v-if="status === 0">
                  <el-button size="medium" @click="WeChatRefuse()">拒绝</el-button>
                  <el-button type="primary" size="medium" @click="wechatAgree()">同意</el-button>
                </div>
              </div>
            </div>
          </div>

          <div class="app-message" v-else-if="msgs.type === 'invite-interview-1'">
            <h1>
              <i class="el-icon-warning"></i>
              {{ msgs.showCustom.interviewTypeName }}
            </h1>
            <div class="table-row">
              <div class="row">面试职位</div>
              <p>{{ msgs.showCustom.positionName }}</p>
            </div>
            <div class="table-row">
              <div class="row">面试时间</div>
              <p>{{ msgs.showCustom.interviewTimeRange }}</p>
            </div>
            <el-popover placement="bottom" :width="110" trigger="click">
              <template #default>
                <div class="ewm">
                  <el-image src="https://image.gxrc.com/gxrcsite/global/app_code.png"></el-image>
                </div>
              </template>
              <template #reference>
                <div class="footer">
                  <el-button>去APP查看</el-button>
                </div>
              </template>
            </el-popover>
          </div>
          <div v-else-if="msgs.type === 'custom-InterviewReminder'" class="app-message">
            <h1><i class="el-icon-warning"></i>签到提醒</h1>
            <p>若您已到面试现场，请点击签到 签到后将为您通知面试官</p>

            <el-popover placement="bottom" :width="110" trigger="click">
              <template #default>
                <div class="ewm">
                  <el-image src="https://image.gxrc.com/gxrcsite/global/app_code.png"></el-image>
                </div>
              </template>
              <template #reference>
                <div class="footer">
                  <el-button>去APP查看</el-button>
                </div>
              </template>
            </el-popover>
          </div>

          <div v-else-if="msgs.type === 'custom-SendLocation'" class="location"
            @click="clickMap(msgs.showText.address)">
            <h1>{{ msgs.showText.name }}</h1>
            <h2>{{ msgs.showText.address }}</h2>
            <div class="map" v-if="showMapRef">
              <Map :showType="2" :height="'104px'" :id="msgs.idServer"
                :addressValue="[msgs.showText.lon, msgs.showText.lat]"></Map>
            </div>
          </div>

          <div v-else-if="msgs.type === 'custom-SendResumeAppendix'" class="ResumeAppendix"
            @click="clickAppendix(msgs.showText)">
            <div class="ResumeAppendix_left">
              <img v-if="msgs.showText.fileIcon" :src="msgs.showText.fileIcon" />
            </div>
            <div class="ResumeAppendix_right">
              <h1>{{ msgs.showText.fileName }}</h1>
              <h2>{{ msgs.showText.fileSize }}</h2>
            </div>
          </div>
          <div v-else-if="msgs.type === 'custom-SendInviteChatText'" class="SendInviteChatText">
            <h1 class="title" v-html="msgs.showText.htmlContent"></h1>

            <div class="line"></div>
            <div v-if="
              deliverList.includes(msgs.showText.positionId) ||
              deliverList.includes(msgs.showText.positionGuid)
            " class="button" @click="continueChat(msgs.showText.enterpriseId)">
              <text>继续沟通</text>
            </div>
            <div v-else class="button" @click="
              DeliveryChat(
                msgs.showText.positionName,
                msgs.showText.positionGuid,
                msgs.showText.enterpriseName,
                msgs.showText.salary,
                msgs.showText.enterpriseId
              )
              ">
              <p>投递简历并沟通</p>
            </div>
          </div>
          <view v-else-if="msgs.type === 'push-info'" class="push_info">
            <pushInfo :contentimg="msgs.showText.infoImage" :title="msgs.showText.infoTitle"
              :infoUrl="msgs.showText.infoPcUrl" :infoKey="msgs.showText.infoKey"
              :activityType="msgs.showText.activityType"></pushInfo>
          </view>
          <div v-else-if="msgs.type === 'invitation-chat-resume'" class="invitation-chat-resume">
            <div class="invitation-chat-resume__content">
              <div class="title">{{ msgs.showCustom.content }}</div>
              <div class="con">
                <div class="resume">
                  <div class="iconfont icon-resume"></div>当前投递的简历是{{ resumeName }}
                </div>
                <div v-if="inviteChatsCannotClick" class="edit-resume edit-resume-unable">修改<div
                    class="iconfont icon-arrowRight9"></div>
                </div>
                <div v-else class="edit-resume" @click="changeResume()">修改<div class="iconfont icon-arrowRight9"></div>
                </div>
              </div>
              
              <div v-if="inviteChatsCannotClick" class="ctrl-button ctrl-button-unable">
                <el-button size="medium">不感兴趣</el-button>
                <el-button type="primary" size="medium" class="ctrl-button-unable">同意</el-button>
              </div>
              <div v-else class="ctrl-button">
                <el-button size="medium" @click="refuseSentResume()">不感兴趣</el-button>
                <el-button type="primary" size="medium" @click="agreeDeliverResume()">同意</el-button>
              </div>
            </div>
          </div>
          <div v-else></div>
        </el-tooltip>
        <div class="readMsg" v-if="msgs.flow === 'out' && msgs.isRead && msgs.type != 'audio'">
          已读
        </div>
        <div class="readMsg"
          v-else-if="msgs.flow === 'out' && !msgs.isRead && !msgs.isInBlackList && msgs.type != 'audio'">
          送达
        </div>
        <!-- <span
          class="warningTip"
          v-if="msgs.flow === 'out' && msgs.isInBlackList"
        >
          
          <i class="el-icon-warning"></i>
        </span> -->
        <div class="translate_text" v-if="msgs.type === 'audio' && translateText">{{ translateText }}</div>
        <span class="is-pad im-message__bubble-gap"></span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import util from "../utils/index";
import config from "../configs/index";
import emojiObj from "../configs/emoji";
import {
  agreeWechat,
  refuseWeChat,
  recall,
  acceptInvitation,
  refuseinterview,
  inputselectitems,
  sendResume,
  blackbyinvitationchat,
} from "../api/index";
import { DeliverResume } from '@/IM/api/miniapp';
import { attachmentPreview } from "@/http/api";
import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  ref,
  toRefs,
  watch,
  h,
} from "vue";
import { nextTick } from "process";
import { useStore } from "../../store";
import { ElMessage, ElMessageBox, ElCheckTag,ElLoading } from "element-plus";
import useClipboard from "vue-clipboard3";
import Map from "./Map.vue";
import pushInfo from "./msgItem/pushInfo.vue";
import { useRouter, useRoute } from "vue-router";
import { getCookies } from "@/utils/common";

export default defineComponent({
  props: {
    type: String, // 类型，chatroom, session
    rawMsg: {
      type: Object,
      default() {
        return {};
      },
    },
    userInfos: {
      type: Object,
      default() {
        return {};
      },
    },
    myInfo: {
      type: Object,
      default() {
        return {};
      },
    },
    isRobot: {
      type: Boolean,
      default() {
        return false;
      },
    },
    isHistory: {
      type: Boolean,
      default() {
        return false;
      },
    },
    showMap: {
      type: Boolean,
      default() {
        return false;
      },
    },
    avatar: String,
    deliverList: {
      type: Array,
      default: () => [],
    },
    inviteChats: {
      type: Array,
      default: () => [],
    },
    isBlack: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    Map,
    pushInfo,
  },
  setup(props: any, { emit }) {
    const router = useRouter();
    const { toClipboard } = useClipboard();
    const store = useStore();
    const state = reactive({
      msg: props.rawMsg,
      show: false,
    });
    const showMapRef = computed(() => props.showMap);
    const mediaMsg: any = ref(null);
    const resumeList = computed(() => store.state.resumeList);
    const resumeName = computed(() => resumeList.value[0]?.resumeName || '');
    const resumeId = computed(() => resumeList.value[0]?.resumeId || '');
    const resumeGuid = computed(() => resumeList.value[0]?.resumeGuid || '');
    const animationFlag = ref(false);
    const audioIconType = ref("yuyin3");
    const translateText = ref("");
    const translateLoaing = ref(false)
    const audioref = ref<any>(null);
    const inviteChatsCannotClick = ref(false)
    const DeliveryChat = (
      name: string,
      guid: string,
      entName: string,

      salary: string,
      entId: number
    ) => {
      emit("Delivery", { name, guid, entName, salary, entId });
    };

    onMounted(() => {
      let item = props.msgs;
      nextTick(() => {
        if (item !== undefined) {
          let media: any = null;
          if (item.type === "image") {
            // 图片消息缩略图
            media = new Image();
            media.src = item.file.url + "?imageView&thumbnail=180x0&quality=85";
          } else if (item.type === "custom-type1") {
            // 猜拳消息
            media = new Image();
            media.className = "emoji-middle";
            media.src = item.imgUrl;
          } else if (item.type === "custom-type3") {
            // 贴图表情
            media = new Image();
            media.className = "chartlet";
            media.src = item.imgUrl;
          } else if (item.type === "video") {
            if (/(mov|mp4|ogg|webm)/i.test(item.file.ext)) {
              media = document.createElement("video");
              media.src = item.file.url;
              media.width = 640;
              media.height = 480;
              media.autoStart = false;
              media.preload = "metadata";
              media.controls = "controls";
            } else {
              let aLink = document.createElement("a");
              aLink.href = item.file.url;
              aLink.target = "_blank";
              aLink.innerHTML = `<i class="u-icon icon-file"></i>${video.name}`;
              mediaMsg.value.appendChild(aLink);
            }
          }
          if (media) {
            if (mediaMsg) {
              mediaMsg.value.appendChild(media);
            }
          }
          emit("msg-loaded");
        }
      });
    });
    const msgs = computed(() => {
      if (!props.rawMsg || Object.keys(props.rawMsg).length === 0) {
        return {};
      }
      let item = Object.assign({}, props.rawMsg);
      if (props.type === "session") {
        if (item.flow == "in") {
          if (item.type === "robot" && item.content && item.content.msgOut) {
            // 机器人下行消息
            let robotAccid = item.content.robotAccid;
            item.avatar = props.robotInfos[robotAccid].avatar;
            item.isRobot = true;
          } else if (item.from !== store.state.imModules.userUID) {
            item.avatar =
              (props.userInfos[item.from] &&
                props.userInfos[item.from].avatar) ||
              props.avatar;
            // (this.userInfos[item.from] && this.userInfos[item.from].avatar) ||
            // config.defaultUserIcon;

            //todo  如果是未加好友的人发了消息，是否能看到名片
          } else {
            item.avatar = props.myInfo.avatar;
          }
        } else if (item.flow === "out") {
          item.avatar = props.myInfo.avatar;
        }
      } else {
        // 标记时间，聊天室中
        item.showTime = util.formatDate(item.time);
      }
      if (item.type === "timeTag") {
        // 标记发送的时间
        item.showText = item.text;
      } else if (item.type === "text") {
        // 文本消息
        item.showText = util.escape(item.text);
        if (/\[[^\]]+\]/.test(item.showText)) {
          let emojiItems = item.showText.match(/\[[^\]]+\]/g);
          emojiItems.forEach((text: string) => {
            let emojiCnt = emojiObj.emojiList.emoji;
            if (emojiCnt[text]) {
              item.showText = item.showText.replace(
                text,
                `<img class="emoji" src="${emojiCnt[text].img}">`
              );
            }
          });
        }
      } else if (item.type === "custom") {
        if (item.content === "") {
          item.type = "custom-text";
          item.showText = "[未知消息类型]";
          return item;
        }
        let content = JSON.parse(item.content);
        // type 1 为猜拳消息
        if (content.type === 1) {
          let data = content.data;
          if (data) {
            let resourceUrl = config.resourceUrl;
            // item.showText = `<img class="emoji-middle" src="${resourceUrl}/im/play-${data.value}.png">`
            item.type = "custom-type1";
            item.imgUrl = `${resourceUrl}/im/play-${data.value}.png`;
          }

          // type 3 为贴图表情
        } else if (content.type === 3) {
          let data = content.data;
          let emojiCnt: any = "";
          if (emojiObj.pinupList[data.catalog]) {
            if (data.chartlet) {
              emojiCnt = emojiObj.pinupList[data.catalog][data.chartlet];
            }
            item.type = "custom-type3";
            item.imgUrl = `${emojiCnt.img}` || "";
          }
        } else {
          if (content.rcevent) {
            let event = content.rcevent;
            if (event === "positionvideo") {
              let message = {};
              try {
                message = JSON.parse(content.message);
              } catch (e) { }
              item.type = "custom-position";
              item.showCustom = message;
              item.short = content.rcshort;
              //item = { ...item, showCustom: message, short: content.rcshort };
            } else if (event === "sendresume") {
              let message = {};
              message = content.message.pcTips || "";
              item.type = "resume-tip";
              item.showText = "已向对方发送简历";
            }
            else if (event === "SendInviteChat") {
              item.type = "text";
              item.showText = content.message.content;
            } else if (event === "SendInviteChatResume") {

              item.type = "invitation-chat-resume";
              item.showCustom = content.message;
            }
            else if (event === "ApplyWeChatExch_Step1") {
              const types = content.actionType;
              let message = content.jobmessage.seekerTip;
              if (types === 1) {
                item.type = "wechat-tip";
                item.showText = message;
              } else {
                item.type = "custom-wechat";
                item.showCustom = message;
                item.askToken = content.AskToken;
                //item = { ...item, showCustom: message, short: content.rcshort };
              }
            } else if (event === "ApplyWeChatExch_Step2_YES") {
              let message = content.jobmessage.seekerAcceptedWeChat;
              item.type = "wechat-yes";
              let entemessage = content.entemessage;
              item.wechat = entemessage.enteSelfWeChatNo; //
              item.showCustom = message;
            } else if (event === "InviteInterview_Step1") {
              item.type = "invite-interview-1";
              item.showCustom = content.inviteInfo;
              item.askToken = content.AskToken;
            } else if (
              event === "InviteInterview_Step2_YES" ||
              event === "InviteInterview_Step2_NO" ||
              event === "recallMessage"
            ) {
              item.type = "invite-interview-tip";
              item.showText = content.jobmessage.seekerTip;
            } else if (
              event === "SendJobSeekerPosiInfo" ||
              event === "SendJobSeekerPosiInfoSwitch" ||
              event === "SendPosiJobSeekerInfo" ||
              event === "SendPosiJobSeekerInfoSwitch"
            ) {
              item.type = "position-info";
              item.showCustom = content.entemessage;
              if (
                event === "SendJobSeekerPosiInfoSwitch" ||
                event === "SendPosiJobSeekerInfoSwitch"
              )
                item.posiSwitchTip = content.posiSwitchTip;
            } else if (event === "sendPhone") {
              item.type = "phone-tip";
              // item.showText = `${content.message.name}的手机号:${content.message.phone}`;
              item.showText = `已成功发送电话号码:${content.message.phone}`;
            } else if (event === "SendLocation") {
              item.type = "custom-SendLocation";
              item.showText = content.message;
            } else if (event === "SendResumeAppendix") {
              item.type = "custom-SendResumeAppendix";
              item.showText = content.message;
            } else if (event === "SendInviteChatPosition") {
              //邀约聊职位
              item.type = "custom-SendInviteChatPosition";
              item.showCustom = content.position;
            } else if (event === "SendInviteChatText") {
              //邀约聊文本
              item.type = "custom-SendInviteChatText";
              item.showText = content.data;
            } else if (event === "InterviewReminder") {
              //面试签到邀请
              item.type = "custom-InterviewReminder";
              item.showText = content;
              // let r = JSON.parse();
            } else if (event === "commonNotice") {
              //通用通知消息

              if (content.noticeType == "tips") {
                item.type = "commonNotice-tip";
              } else {
                item.type = "commonNotice-toast";
              }
              if (content.jobseekText) {
                item.showText = content.jobseekText;
              } else {
                item.display = false;
              }
            } else if (event === "PushInfo") {
              item.type = "push-info";
              item.showText = content.message;
            } else {
              item.type = "custom-text";
              item.showText = content.message;
            }
          } else {
            item.type = "custom-text";
            item.showText = util.parseCustomMsg(item);
            // if (item.showText !== "[自定义消息]") {
            //   item.showText += ",请到手机或电脑客户端查看";
            // }
          }
        }
      } else if (item.type === "image") {
        // 原始图片全屏显示
        item.originLink = item.file.url;
      } else if (item.type === "video") {
        // ...
      } else if (item.type === "audio") {
        const duration = formatDuration(item.file.dur);
        const maxWidth = 180;
        item.width =
          (50 + 8 * (duration - 1) > maxWidth
            ? maxWidth
            : 50 + 8 * (duration - 1)) + "px";
        item.audioSrc = item.file.mp3Url;
        item.showText =
          "<i>" + Math.round(item.file.dur / 1000) + '"</i> 点击播放';
        // if (!props.isHistory && nim.useDb) {
        //   item.unreadAudio = !item.localCustom;
        // }
      } else if (item.type === "file") {
        item.fileLink = item.file.url;
        item.showText = item.file.name;
      } else if (item.type === "notification") {
        // if (item.scene === "team") {
        //   item.showText = util.generateTeamSysmMsg(item);
        // } else {
        //   //对于系统通知，更新下用户信息的状态
        //   item.showText = util.generateChatroomSysMsg(item);
        // }
        item.showText = item.text;
      } else if (item.type === "tip") {
        //对于系统通知，更新下用户信息的状态
        item.showText = item.tip;
      } else if (item.type === "robot") {
        let content = item.content || {};
        let message = content.message || [];
        if (!content.msgOut) {
          // 机器人上行消息
          item.robotFlow = "out";
          item.showText = item.text;
        } else if (content.flag === "bot") {
          item.subType = "bot";
          message = message.map((item: any) => {
            if (item.type === "template") {
              // 在vuex(store/actions/msgs.js)中已调用sdk方法做了转换
              return item.content.json;
            } else if (item.type === "text" || item.type === "answer") {
              // 保持跟template结构一致
              return [
                {
                  type: "text",
                  text: item.content,
                },
              ];
            } else if (item.type === "image") {
              // 保持跟template结构一致
              return [
                {
                  type: "image",
                  url: item.content,
                },
              ];
            }
          });
          item.message = message;
        } else if (item.content.flag === "faq") {
          item.subType = "faq";
          item.query = message.query;
          let match = message.match.sort((a: any, b: any) => {
            // 返回最匹配的答案
            return b.score - a.score;
          });
          item.message = match[0];
        }
      } else {
        // item.showText = `[${util.mapMsgType(item)}]`;
        // item.type = "1";
      }
      methods.initInviteChatsStatus(item)
      return item;
    });
    const status = computed(() => {
      return state.msg.wxStatus || 0;
    });
    const T = computed(() => {
      return config.isTest ? "t" : "";
    });
    const showRevocate = computed(() => {
      if (msgs.value.flow === "in" || msgs.value.type === "notification")
        return true;
      let tempDate = new Date().getTime();

      if (
        tempDate - msgs.value.time <= 1000 * 60 * 2 &&
        !msgs.value.isInBlackList
      ) {
        return false;
      } else {
        return true;
      }
    });

    // 音频时长
    const duration = computed(() => {
      return formatDuration(msgs.value.file.dur);
    });

    const showTranslateText = computed(() => {
      return msgs.value.type == 'audio'
    })
    const methods = {
      initInviteChatsStatus(item: any) {
        if (props.isBlack && props.isBlack == true) {
          inviteChatsCannotClick.value = true;
          return;
        } else {
          inviteChatsCannotClick.value = false;
        }
        if (item.showCustom && item.showCustom?.positionId && props.inviteChats.length > 0) {
          props.inviteChats.forEach((element: any) => {
            if (element.positionID == item.showCustom.positionId) {
              if (element.inviteChat == 1) {
                inviteChatsCannotClick.value = true;
                return;
              } else {
                inviteChatsCannotClick.value = false;
              }
            }
          });
        }
      },
      changeResume() {
        let positionData = {
          enterId: msgs.value.from,
          positionId: msgs.value.showCustom.positionId,
          positionGuid: msgs.value.showCustom.positionGuid,
          positionName: msgs.value.showCustom.positionName,
          enterpriseName: msgs.value.fromNick,
          paypackage: msgs.value.showCustom.payRemark,
          enterpriseId: msgs.value.showCustom.enterpriseId
        }
        emit('openResumeDialog', positionData, 2);
      },
      refuseSentResume() {
        ElMessageBox.confirm(
          "设置后，对方将无法向您发送信息，您可以随时在聊天窗口中取消该设置",
          "不感兴趣",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            customClass: "wechat-box",
            closeOnClickModal: false,
          }
        )
          .then(async ({ value }) => {
            // const wechatForm = {
            //   enterid: msgs.value.to,
            //   WechatNo: value,
            // };
            let data: any = await blackbyinvitationchat({
              "platform": 0,
              "enterId": msgs.value.from,
              "blackType": 1,
              "blackValue": 1,
            });
            if (data.code != 1) {
              ElMessage({
                message: data.message,
                type: "error",
              });
            } else {
              ElMessage({
                message: data.message,
                type: "success",
              });
            }
            emit("refreshStatus");
            // store.commit('updateReplyState', true);
          })
          .catch(() => { });
      },
      async agreeDeliverResume() {
        const Resume = {
          resumeGuid: resumeGuid.value
        };
        const positionData = {
          positionGuid: msgs.value.showCustom.positionGuid,
          enterpriseId: msgs.value.showCustom.enterpriseId
        };
        emit('ResumeSubmit', Resume, positionData);
        emit("refreshStatus");
      },
      async refuseInvitation(enterId, askToken) {
        const value = ref(0);
        const click = (id: number, index: number) => {
          let list: NodeListOf<Element> | undefined = document
            .querySelector(".ElMessageBox_3_1")
            ?.querySelectorAll(".el-check-tag");
          if (list) {
            list.forEach((item) => {
              item.classList.remove("is-checked");
            });
            list[index].classList.add("is-checked");
          }

          value.value = id;
        };
        let list = [];
        let data: any = await inputselectitems({ selectitemtype: 200 });
        if (data.code == 1) {
          list = data.data;
        }

        ElMessageBox({
          title: "选择拒绝/取消面试原因",
          message: h(
            "div",
            null,
            list.map((item, index) =>
              h(
                ElCheckTag,
                {
                  checked: false,
                  onClick: () => click(item.keyID, index),
                  style: "margin:4px",
                },
                item.selectItemContent
              )
            )
          ),
          showCancelButton: true,
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          customClass: "ElMessageBox_3_1",
        })
          .then(async () => {
            let data = await refuseinterview({
              enterId,
              askToken,
              refuseReason: value.value,
            });
            if (data.error || data.code == 0) {
              ElMessageBox.alert(
                data.message ? data.message : "对不起，发送失败，请稍后重试",
                {
                  confirmButtonText: "确定",
                  callback: (action) => { },
                }
              );
            }
            emit("refreshStatus");
          })
          .catch(() => { });
      },
      acceptInvitation(enterId, askToken) {
        ElMessageBox.confirm("确认接受面试邀请?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            // ElMessage({
            //   type: 'success',
            //   message: '删除成功!',
            // });
            let data = await acceptInvitation({ enterId, askToken });
            if (data.error || data.code == 0) {
              ElMessageBox.alert(
                data.message ? data.message : "对不起，发送失败，请稍后重试",
                {
                  confirmButtonText: "确定",
                  callback: (action) => { },
                }
              );
            }
            emit("refreshStatus");
          })
          .catch(() => { });
      },
      revocate() {
        if (msgs.value.flow === "in") return;
        let tempDate = new Date().getTime();
        if (tempDate - msgs.value.time <= 1000 * 60 * 2) {
          state.show = false;
        } else {
          state.show = true;
        }
      },
      async revocateMsg(msg: Record<string, string>) {
        if (store.state.imModules.currSessionId) {
          if (msg.type === "robot") return;
          if (msg.type == "image" || msg.type == "audio") {
            store.dispatch("revocateMsg", props.rawMsg);
            return;
          }
          if (msg.flow === "out") {
            let model = {
              enterId: msg.to,
              msgId: msg.idServer,
            };
            // let { idClient } = msg;
            let data: any = await recall(model);
            if (data.error || data.code == 0) {
              ElMessageBox.alert(data.message, {
                confirmButtonText: "确定",
                callback: (action) => { },
              });
            }
          } else {
            alert("对不起，只能撤回自己发送的消息");
          }
        }
      },
      async copy(msg: string) {
        try {
          await toClipboard(msg);
          methods.onCopy();
        } catch (e) {
          console.error(e);
        }
      },

      onCopy() {
        ElMessage({
          message: "已复制到剪贴板",
          offset: 200,
          type: "success",
        });
      },
      wechatAgree() {
        let token = msgs.value.askToken;
        let yxId = msgs.value.from;

        ElMessageBox.prompt("", "确认与对方交换微信吗？", {
          confirmButtonText: "发送",
          cancelButtonText: "取消",
          inputPlaceholder: "请输入您的微信号或对应手机号",
          customClass: "wechat-box",
          inputPattern: /^([-_a-zA-Z0-9]{6,20})$/,
          inputErrorMessage: "请使用有效微信号",
        })
          .then(async ({ value }) => {
            const wechatForm = {
              enterId: yxId,
              weChat: value,
              askToken: token,
            };
            let data: any = await agreeWechat(wechatForm);
            if (data.error || data.code != 1) {
              ElMessageBox.alert(data.message, {
                confirmButtonText: "确定",
                callback: (action) => { },
              });
            } else {
              state.msg.wxStatus = 1;
              // this.$set(this.msg, "wxStatus", 1);
              emit("refreshStatus", msgs.value.idClient, 1);
            }
          })
          .catch(() => { });
      },
      async WeChatRefuse() {
        let token = msgs.value.askToken;
        let yxId = msgs.value.from;
        const wechatForm = {
          enterId: yxId,
          askToken: token,
        };
        let data: any = await refuseWeChat(wechatForm);
        if (data.error || data.code != 1) {
          ElMessageBox.alert(data.message, {
            confirmButtonText: "确定",
            callback: (action) => { },
          });
        } else {
          state.msg.wxStatus = 2;
          emit("refreshStatus", msgs.value.idClient, 2);
        }
      },
      play() {
        nextTick(() => {
          const audio = audioref.value;

          if (audio?.paused) {
            audio.play();
            this.playAudioAnimation();
          } else {
            audio.pause();
            animationFlag.value = false;
          }
        });
      },
      onPause() {
        animationFlag.value = false;
      },
      onEnded() {
        animationFlag.value = false;
      },
      playAudioAnimation() {
        try {
          animationFlag.value = true;
          let audioIcons = ["yuyin1", "yuyin2", "yuyin3"];
          const handler = () => {
            const icon = audioIcons.shift();
            if (icon) {
              audioIconType.value = icon;
              if (!audioIcons.length && animationFlag.value) {
                audioIcons = ["yuyin1", "yuyin2", "yuyin3"];
              }
              if (audioIcons.length) {
                setTimeout(handler, 300);
              }
            }
          };
          handler();
        } catch (error) { }
      },
      translateToText() {
        if (translateLoaing.value) return
        const nim = store.state.imModules.nim
        translateLoaing.value = true
        nim?.audioToText({
          url: msgs.value.file.url,
          done: function (err: any, res: any) {
            console.log(res);
            translateLoaing.value = false
            translateText.value = res.text
            if (!res.text || res.text == '') {
              ElMessage.error('检测不到有效语音')
            }
          }
        })
      }
    };
    watch(
      () => props.rawMsg,
      (newVal, oldVal) => {
        if (newVal && Object.keys(newVal).length > 0) {
          state.msg = newVal;
        }
      },
      { immediate: true, deep: true }
    );

    const clickMap = (address) => {
      window.open(
        "http://api.map.baidu.com/geocoder?address=" +
        address +
        "&output=html&src=webapp.baidu.openAPIdemo"
      );
    };

    const clickAppendix = (obj) => {
      attachmentPreview({ id: obj.fileId }).then((data) => {
        if (data.code == 1) {
          window.open(data.data);
        } else {
          ElMessageBox.alert(data.message, {
            confirmButtonText: "确定",
            callback: (action) => { },
          });
        }
      });
    };
    const ActiveSessionId = computed(
      () => store.state.imModules.ActiveSessionId
    );
    const deliverList = computed(() => props.deliveredList);
    const continueChat = (id: number) => {
      chat(`p2p-ent${id}`, 1);
    };
    const chat = (sessionIds: string, type = 1) => {
      let id = sessionIds.replace(/^p2p-ent/, "");
      let sessionId = ActiveSessionId.value;
      if (sessionId === id) return;
      store.commit("updateWidgetActiveSessionId", sessionIds);

      store.commit("updateActiveSessionId", id);
    };

    // 格式化音频时长
    const formatDuration = (duration: number) => {
      return Math.round(duration / 1000) || 1;
    };
    return {
      ...toRefs(state),
      mediaMsg,
      msgs,
      status,
      showRevocate,
      audioIconType,
      audioref,
      ...methods,
      T,
      showMapRef,
      duration,
      showTranslateText,
      translateLoaing,
      translateText,
      clickMap,
      clickAppendix,
      DeliveryChat,
      continueChat,
      resumeList,
      resumeName,
      resumeId,
      inviteChatsCannotClick,
    };
  },
});
</script>
<style lang="less">
.ElMessageBox_3_1 {
  .el-check-tag {
    background: #fafafa;
    border: 1px solid #f2f2f2;
    opacity: 1;

    font-size: 13px;
    font-weight: 400;
    color: #999999;
    border-radius: 2px;
  }

  .is-checked {
    background: rgba(242, 247, 255, 0.39);
    border: 1px solid #93beff;
    opacity: 1;
    border-radius: 2px;

    color: #457ccf;
  }
}

.chat-message {
  margin-bottom: 20px;
  display: block;
  padding: 0 20px;
  border-bottom: none;
  min-width: 0;

  .push_info {
    width: 355px;
    border-radius: 4px;
    border: 1px solid #f2f2f2;
  }

  .SendInviteChatText {
    padding: 10px 14px;
    padding-bottom: 0;
    width: 367px;
    min-height: 99px;
    background: #eef2f5;
    border-radius: 8px 8px 8px 8px;
    box-sizing: border-box;
    opacity: 1;

    .title {
      font-size: 14px;
      font-family: PingFang SC-Light, PingFang SC;
      font-weight: 300;
      color: #333333;

      line-height: 24px;
      display: inline-block;
    }

    .line {
      width: 100%;
      height: 0px;
      opacity: 1;
      border: 1px solid #e5e5e5;
      margin-top: 10px;
    }

    .button {
      font-size: 14px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #4b7ef3;
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      height: 40px;
    }
  }

  .ResumeAppendix {
    width: 360px;
    height: 72px;
    background: #ffffff;
    box-shadow: 0px 0px 6px 1px rgba(0, 0, 0, 0.1);
    opacity: 1;
    border: 1px solid #efefef;
    padding: 16px 9px;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;

    .ResumeAppendix_left {
      height: 40px;
      width: 40px;
      margin-right: 8px;

      img {
        height: 40px;
        width: 40px;
      }
    }

    .ResumeAppendix_right {
      width: 0;
      flex: 1;

      h1 {
        margin: 0;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #333333;

        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //
        word-break: break-all;
      }

      h2 {
        margin: 0;
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #bbbbbb;
      }
    }
  }

  .app-message {
    width: 242px;
    min-height: 136px;
    background: linear-gradient(180deg, #e6f0ff 0%, #ffffff 100%);
    border-radius: 5px;
    border: 1px solid #deebff;
    padding: 16px 18px;
    box-sizing: border-box;

    h1 {
      margin-bottom: 16px;
      font-size: 16px;
      font-family: SourceHanSansCN-Medium, SourceHanSansCN;
      font-weight: 500;
      color: #333333;
      line-height: 24px;

      i {
        color: #457ccf;
        margin-right: 5px;
      }
    }

    p {
      font-size: 13px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #333333;
      line-height: 18px;
    }

    .table-row {
      display: flex;
      margin-bottom: 12px;
      align-items: center;

      .row {
        min-width: 56px;
        font-size: 14px;
        font-family: SourceHanSansCN-Regular, SourceHanSansCN;
        font-weight: 400;
        color: #999999;
        margin-right: 10px;
      }
    }

    .table-row:last-child {
      margin-bottom: 0px;
    }

    .ewm {
      width: 95px;
      height: 85px;
      background: #ffffff;
      border: 1px solid #457ccf;
    }

    .footer {
      margin-top: 10px;
      width: 100%;
      text-align: center;
    }
  }

  .location {
    width: 285px;
    height: 180px;
    padding: 14px;
    padding-bottom: 4px;
    box-sizing: border-box;
    cursor: pointer;
    background: #fcfcfd;
    box-shadow: 0px 0px 6px 1px rgba(31, 47, 70, 0.12);
    opacity: 1;
    border: 1px solid #e8e8e8;

    h1 {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      margin: 0;
      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; //溢出不换行
    }

    h2 {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #999999;
      margin: 0;

      overflow: hidden; //超出的文本隐藏
      text-overflow: ellipsis; //溢出用省略号显示
      white-space: nowrap; //溢出不换行
    }

    .map {
      height: 104px;
    }
  }

  .wechat-yes {
    // display: flex;
    // justify-content: center;
    width: 328px;
    margin: auto;
  }

  .wechat-content {
    //background: #fbfbfb;
    border: 1px solid rgba(235, 235, 235, 1);
    padding: 15px 30px;
    border-radius: 10px;

    .wechat-info {
      display: flex;

      .info-left {
        position: relative;

        .img {
          height: 56px;
          width: 56px;
          border-radius: 290486px;
          box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.1);
        }
      }

      .wechat-text {
        margin-left: 17px;
        padding-top: 3px;

        p {
          line-height: 1.7;
          margin: 0;
          padding: 0;
        }

        .user-name {
          color: #999;
        }

        .user-wx {
          font-weight: 500;
          font-size: 16px;
        }
      }
    }

    .wechat-button {
      margin-top: 10px;
      display: flex;
      justify-content: center;
    }
  }

  .invite-interview {
    background: #fbfbfb;
    border: 1px solid rgba(235, 235, 235, 1);
    box-shadow: 0px 0px 6px 0px rgba(173, 173, 173, 0.35);
    padding: 15px 16px;
    border-radius: 10px;

    .button_box {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      padding-top: 10px;
      border-top: 1px solid #ebebeb;
      margin-top: 10px;
    }

    .invite-content {
      width: 370px;

      .span_title {
        width: 28px;
        height: 14px;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        line-height: 21px;
        color: #999999;
        opacity: 1;
      }

      &__type {
        font-size: 18px;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid rgba(235, 235, 235, 1);
      }

      &__time {
        color: #ff6666;
        font-size: 16px;
        font-weight: 500;
      }

      &__color {
        color: #666666;
        white-space: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }
    }
  }

  .position-info {
    display: flex;
    justify-content: center;
    flex-direction: column;

    &__time {
      font-size: 14px;
      font-family: PingFang SC-Light, PingFang SC;
      font-weight: 300;
      color: #333333;
      line-height: 24px;
      margin-top: 10px;
    }

    &__content {
      // background: #fbfbfb;
      border: 1px solid rgba(235, 235, 235, 1);
      padding: 20px;
      border-radius: 10px;
      margin: 0 auto;
      max-width: 450px;
    }

    &__title {
      display: flex;
      flex-direction: row;
      margin-bottom: 8px;

      &--name {
        font-size: 16px;
        font-weight: 700;
        margin-right: 50px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex-grow: 1;
        color: #333;
      }

      &--salary {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        color: #457ccf;
        font-size: 14px;
      }
    }

    &__tags {
      display: flex;
      margin-bottom: 18px;
    }

    &__tag {
      border: 1px solid rgba(230, 230, 230, 1);
      border-radius: 3px;
      padding: 3px;
      margin-right: 8px;
      color: #a3a3a3;
      font-size: 12px;
    }

    &__footer {
      display: flex;
      flex-direction: row;
      border-top: 1px solid #e6e6e6;
      align-items: center;
      justify-content: space-between;

      &--name {
        margin-top: 19px;
        font-size: 16px;
        color: #696d6f;
      }

      &--link {
        display: flex;
        align-items: center;
        margin-top: 19px;
        color: #696d6f;
        cursor: pointer;
        margin-left: 10px;
        font-size: 12px;

        &--img {
          // width: 17px;
          // margin-left: 5px;
          display: flex;
          font-size: 15px;
          align-items: center;
        }

        .link {
          display: flex;
          color: #696d6f;
        }
      }
    }
  }
}

.wechat-box {
  .el-message-box__header {

    // background: #F1F1F1;
    .el-message-box__title {
      font-size: 16px;
    }
  }

  .el-message-box__btns {
    text-align: center;
  }
}

.u-msgTime {
  text-align: center;
  font-size: 12px;
  color: #aaaaaa;
  line-height: 20px;
}

.blacktips {
  font-size: 12px;
  background-color: #f4f4f5;
  color: #909399;
}

.item {
  // overflow: hidden;
  // padding: 4px 0px 10px 0px;
  display: flex;
}

.item-you .img,
.item-me .img {
  width: 40px;
  height: 40px;
}

.img {
  // cursor: pointer;
  border-radius: 50%;
  flex-shrink: 0;
  overflow: hidden;
}

.item-you {
  flex-direction: row;
}

.item-you img {
  // float: left;
  margin-right: 5px;
}

img {
  border: 0;
}

.item-you .msg-text {
  background: #eef2f5;
  color: #333333;
  border-color: #eef2f5;
  border-radius: 10px;
}

.item-you .msg-image {
  background: none;
  border: none;

  .el-image {
    width: 200px;
    margin: 0;
  }

  .box {
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
  }

  .cnt {
    line-height: normal;
    width: 200px;
  }
}

.msg .box {
  // position: relative;
  // padding: 7px 12px;
  // zoom: 1;
  // max-width: 500px;
  margin: 0 8px;
  max-width: 500px;
  padding: 8px 12px;
  min-width: 0;
  position: relative;
  border-radius: 0 4px 4px 4px;
  transform-origin: 0 2%;
  will-change: opacity, transform;
}

.msg .cnt {
  // position: relative;
  // min-height: 20px;
  // line-height: 20px;
  // overflow: hidden;
  // white-space: pre-wrap;
  // // word-wrap: break-word;
  // word-break: break-word;
  line-height: 1.7;
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 14px;
}

.msg .audio {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-you .msg .audio {
  flex-direction: row-reverse;

  img {
    transform: rotate(180deg);
  }
}

.msg .ctrl-button {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.message__notification {
  display: flex;
  align-items: center;

  .message__notification-icon {
    justify-content: flex-end;

    .notification-icon {
      transform: rotate(180deg);
      margin-left: 6px;
      margin-top: 5px;
      font-size: 17px;
    }
  }
}

.item-me {
  flex-direction: row-reverse;
}

.item-me .msg-text {
  // float: right;
  color: #ffffff;
  //background: #2378ff;
  background: #538ade;
  border-radius: 10px;
}

.item-me .msg-image {
  background: none;
  border: none;

  .box {
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
    box-sizing: border-box;
  }

  .cnt {
    line-height: normal;
    width: 200px;
  }
}

// .item.read .readMsg {
//   display: block;
// }
.item .readMsg {
  color: #6b8299;
  padding-right: 5px;
  text-align: right;
  font-size: 0.75rem;
  transition: color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  display: flex;
  align-items: flex-end;
}

.tc {
  text-align: center;
  justify-content: center;
}

.msg {
  position: relative;
  // margin-top: 5px;
  border-radius: 3px;
  // border: 1px solid;
}

.invitation-chat-resume {
  border: 1px solid #ebebeb;
  padding: 20px;
  border-radius: 10px;
  max-width: 450px;

  .title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    font-weight: bold;
  }

  .con {
    display: flex;
    font-size: 14px;
    color: #666666;
    padding: 10px 0 20px;

    .resume {
      flex: 4;
      font-size: 14px;

      .iconfont {
        font-size: 14px;
        margin: 3px 3px 0 0;
        float: left;
      }
    }

    .edit-resume {
      flex: 1;
      font-size: 14px;
      color: #457ccf;
      cursor: pointer;
      text-align: right;

      .iconfont {
        font-size: 7px;
        margin: 7px 0 0 3px;
        float: right;
      }
    }

    .edit-resume-unable {
      color: #ccc;
      cursor: not-allowed;
    }
  }
  .ctrl-button-unable{
    .el-button{
      background: #eee;
    color: #ccc;
    cursor: not-allowed;
    border: 1px solid #eee;
    }
  }
}

.item-me img {
  margin-left: 5px;
}

.custom-me {
  float: right;
  width: 400px;
  font-size: 12px;
}

.custom-you {
  float: left;
  width: 400px;
  font-size: 12px;
}

.jobBox-card {
  position: relative;
  overflow: hidden;
  border: 1px solid #eaebec;
  font-size: 14px;
  color: #333b52;
  background: #fff;
  text-align: left;
  border-radius: 5px;

  .box-title {
    white-space: nowrap;
    text-overflow: ellipsis;
    padding: 6px 20px;
    border-bottom: 1px solid #e8ebef;
    font-weight: 300;
    color: #777c89;
    font-size: 14px;
  }

  .box-body {
    padding: 15px 20px;
    color: #666;

    .body-info {
      line-height: 26px;
      font-size: 16px;

      .positionName {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 70%;
        display: inline-block;
      }

      .salary {
        color: #f66;
        font-size: 16px;
        float: right;
      }
    }
  }
}

.im-message__bubble-gap {
  min-width: 36px;
}

.is-pad {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.msg .cnt .chartlet {
  width: 120px;
  margin: 0;
}

.msg .cnt .emoji {
  width: 20px;
  margin: 0;
  vertical-align: middle;
  float: none;
}

a {
  text-decoration: none;
}

.u-notice span {
  display: inline;
  padding: 5px 15px;
  // background-color: #e5f4ff;
  background: rgba(238, 238, 238, 1);
  color: #6b8299;
  font-size: 12px;
  // line-height: 30px;
  word-wrap: break-word;
  word-break: break-all;
  border-radius: 10px;
}

.warningTip {
  color: #ff5050 !important;
  font-size: 15px;
  padding: 5px 5px;
  text-align: right;
  line-height: 15px;
  float: right;
  margin-top: 5px;
  // cursor: pointer;
}

.fontSize(@val) {
  font-size: @val;
}

.translate_text {
  padding: 0 20px;
  word-break: break-all;
  font-size: 14px;
  line-height: 40px;
}

// .custom-resume {
//   display: flex;
//   justify-content: center;
//   width: 100%;
//   .resume-card {
//     border: 1px solid #e8ebef;
//     background-color: transparent !important;
//     .fontSize(12px);
//     color: #333b52;
//     background: #fff;
//     padding: 20px 20px;
//     border-radius: 2px;
//     .resume-card-head {
//       display: flex;
//       &__info {
//         display: flex;
//         flex-direction: column;
//         justify-content: space-between;
//       }
//     }
//     &__title {
//       display: flex;
//       flex-direction: row;
//       align-items: center;
//       &--name {
//         font-weight: 600;
//         .fontSize(14px);
//         flex-shrink: 0;
//       }
//       &--state {
//         color: #707e97;
//         margin-left: 10px;
//         display: flex;
//         align-items: center;
//         // line-height: 21px;
//       }
//     }
//     &__tags {
//       display: flex;
//       flex-direction: row;
//       margin-top: 10px;
//       &--tag {
//         border: 1px solid rgba(230, 230, 230, 1);
//         border-radius: 5px;
//         padding: 4px 10px;
//         color: rgba(163, 163, 163, 1);
//         margin-right: 5px;
//         flex-shrink: 1;
//       }
//     }
//     &__avatar {
//       margin-left: 10px;
//       width: 80px;
//       height: 80px;
//     }
//     &__position {
//       margin-top: 10px;
//       color: #707e97;
//     }
//   }
// }</style>
