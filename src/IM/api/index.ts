import {get,post } from '../utils/http'
const publicParams = {
  platform:0
}
//登录
export const login = async () =>{
    let rs = await get('/api/im/account')
    
    return rs
}
export const permission = async ()=>{
    let rs = await post('/api/i-m/permission?_=' + Math.floor(Math.random()*****************))
}
//获取职位列表
// export const positionList = async (id:number) => {
//     let rs = await post('/api/i-m/positionList/'+id,{})
//     return rs
// }

//获取求职者和企业的会话职位
export const getPosition = async (id:number) => {

  
  let rs = await get('/api/im/dialogueposition/'+id,{})
  return rs
}

//最近会话
export const getSessions = async(obj:Record<string,unknown>) =>{
  // let arr = []
  // for(let i=0;i<99;i++){
  //   arr.push(
  //     {id:i,topSetting:i,type:'text',lastMsg:'text',avatar: "//image.gxrc.com/gxrcsite/vip/headpic_boy.jpg",name: "蔡世的分"+i,time:'21/02/01',localID:i+''+i}
  //   )
  // }
  // return {data:{code:200,data:[...arr]}}
    let rs = await get('/api/im/latestsessions?_=' +Math.floor(Math.random()*****************),obj,{show:true,target:'.left_menu'})
    return rs
    
}
//常用语
export const quickReply = async() =>{
    let rs = await post('/api/im/commonexpression?_=' + Math.floor(Math.random()*****************))
    return rs
}

//发送消息
export const sendMsg = async (To,Content) =>{
    let rs = await post('/api/im/sendtext?_=' + Math.floor(Math.random()*****************),{enterId:To,text:Content,...publicParams})
    return rs
}

//发送简历
export const sendResume = async (obj) =>{
  let rs = await post('/api/im/sendresume?_=' + Math.floor(Math.random()*****************),Object.assign(obj,publicParams))
  return rs
}

//发送简历附件
export const sendAppendix = async (obj) =>{
  let rs = await post('/api/im/sendappendix?_=' + Math.floor(Math.random()*****************),Object.assign(obj,publicParams))
  return rs
}






export const applyPhone = async (phone)=>{
  let re = await post('/api/im/sendphone/'+phone,{...publicParams})
  return re;
}
//申请交换微信
export const applyWechat = async (model) => {
    let rs = await post('/api/im/applywechat?_=' + Math.floor(Math.random()*****************),Object.assign(model,publicParams))
    return rs
}
//同意微信交换
export const agreeWechat = async (model) =>{
    let rs = await post('/api/im/agreewechat?_=' + Math.floor(Math.random()*****************), Object.assign(model,publicParams))
    return rs
}

//拒绝微信交换
export const refuseWeChat = async (model) => {
    let rs = await post('/api/im/refusewechat?_=' + Math.floor(Math.random()*****************),Object.assign(model,publicParams))
    return rs
}
// //重新获取微信
// export const againSendWechat = async (seekerId) =>{
//     let rs = await post('/api/im/againSendWechat?_=' + Math.floor(Math.random()*****************),{seekerId})
//     return rs
// }
//获取所有询问状态 //enterId企业云信id
export const allMsgReply = async(enterId:string) =>{
    let rs = await get('/api/im/msgreplystatus?_=' + Math.floor(Math.random()*****************),{enterId})
    return rs
}
// //邀请预约面试
// export const enteInviteInterview = async(model) =>{
//     let rs = await post('/api/i-m/enteInviteInterview?_=' + Math.floor(Math.random()*****************),model)
//     return rs
// }
// //获取邀约面试的配置项
// export const selectItemList = async(selectItemTypes)=>{
//     let rs = await post('/api/i-m/selectItemList?_=' + Math.floor(Math.random()*****************),{selectItemTypes})
//     return rs
// }

//发起面试是否需要扣点
export const isNeedPay = async(seekerYXID) =>{
    let rs = await post('/api/i-m/isNeedPayVideo?_=' + Math.floor(Math.random()*****************),{seekerYXID})
    return rs
}

//发起面试扣点
export const payForVideo = async(seekerYXID) =>{
    let rs = await post('/api/i-m/payForVideo?_=' + Math.floor(Math.random()*****************),{seekerYXID})
    return rs
}

//黑名单操作
export const blackOpera = async(model) =>{
    let rs = await post('/api/im/black?_=' + Math.floor(Math.random()*****************),Object.assign(model,publicParams))
    return rs
}

//黑名单操作（邀约聊点不感兴趣）
export const blackbyinvitationchat = async(model)=>{
  return await post('/api/im/blackbyinvitationchat?_='+ Math.floor(Math.random()*****************), model)
}


//获取信息
export const info = async(enterId:string) =>{
    let rs = await get(`/api/im/nameandavatar/${enterId}`,{})
    return rs
}
// //举报求企业
// export const report = async(model) =>{
//     let rs = await post('/api/my/feedback?_=' + Math.floor(Math.random()*****************),model)
//     return rs
// }
//会话设置
export const sessionSetting = async (model) =>{
    let rs = await post('/api/im/sessionitemsetting?_=' + Math.floor(Math.random()*****************), model)
    return rs
}

export const logvideo = async (seekerYXID) => {
    let rs = await post('/api/i-m/logvideo?_=' + Math.floor(Math.random()*****************),{seekerYXID})
    return rs
}
export const resumeDetail = async(resumeGuid) =>{
    let rs = await get('/api/resume/',{resumeGuid})
    return rs
}

//设置已读
export const setRead = async (model) =>{
  let rs = await post('/api/im/setread?_=' + Math.floor(Math.random()*****************), model)
  return rs
}
//撤回信息
export const recall = async (model) =>{
  return await post('/api/im/recall?_='+ Math.floor(Math.random()*****************), model)
}
//同意面试邀请
export const acceptInvitation = async (model) =>{
  return await post('/api/im/agreeinterview?_='+ Math.floor(Math.random()*****************),Object.assign(model,publicParams))
}
//拒绝面试邀请
export const refuseinterview = async (model)=>{
  return await post('/api/im/refuseinterview?_='+ Math.floor(Math.random()*****************), Object.assign(model,publicParams))
}
//拒绝面试理由字典
export const inputselectitems = async (model)=>{
  return await get('/api/im/inputselectitems?_='+ Math.floor(Math.random()*****************), model)
}

// export const getResumeList = async()=>{
//   return await get('/appmy/api/resume/resumelist',{})
// }
export const imDeliverpositionids = async ()=>{
  return await get('/api/im/deliverpositionids?_='+ Math.floor(Math.random()*****************), {})
}

//获取聊天设置
export const chatsettings = async()=>{
    return await get('/api/chatsettings/chatsettings?_='+ Math.floor(Math.random()*****************), {})
}

//设置 是否接收聊天
export const changeisacceptchatting = async(model)=>{
  return await post('/api/chatsettings/changeisacceptchatting?_='+ Math.floor(Math.random()*****************), model)
}

//获取全部招呼语
export const allgreetings = async(isloadcustom:boolean)=>{
  if(isloadcustom==null){
    return await get('/api/greeting/allgreetings?_='+ Math.floor(Math.random()*****************), {})
  }else{
    return await get('/api/greeting/allgreetings?isloadcustom='+isloadcustom+'&_='+ Math.floor(Math.random()*****************), {})
  }
}

//获取用户当前招呼语
export const jobseekergreeting = async()=>{
    return await get('/api/greeting/jobseekergreeting?_='+ Math.floor(Math.random()*****************), {})
}

//设置默认的招呼语
export const setdefaultgreeting = async(model)=>{
  return await post('/api/greeting/setdefaultgreeting?_='+ Math.floor(Math.random()*****************), model)
}

//调整自动发送招呼语状态
export const changegreetingsendstate = async(model)=>{
  return await post('/api/greeting/changegreetingsendstate?_='+ Math.floor(Math.random()*****************), model)
}

//获取常用语模板列表
export const chattemplates = async()=>{
  return await get('/api/im/chattemplates?_='+ Math.floor(Math.random()*****************), {})
}

//获取常用语模板详细
export const chattemplatebyid = async(templateid:number)=>{
  return await get('/api/im/chattemplatebyid?templateid='+templateid+'&_='+ Math.floor(Math.random()*****************), {})
}

//常用语排序
export const sortchatgreeting = async(model)=>{
  return await post('/api/im/sortchatgreeting?_='+ Math.floor(Math.random()*****************), model)
}

//获取常用语维护的关键字
export const commonexpressionkeywords = async()=>{
  return await get('/api/im/commonexpressionkeywords?_='+ Math.floor(Math.random()*****************))
}

//常用语
export const commonexpression = async()=>{
  return await post('/api/im/commonexpression?_='+ Math.floor(Math.random()*****************), {})
}

//常用语操作
export const operatecommonexpression = async(model)=>{
  return await post('/api/im/operatecommonexpression?_='+ Math.floor(Math.random()*****************), model)
}


export const sendgreeting = async(data)=>{
  return await post('/api/im/sendgreeting', data )
}

export const imSendmsgupdateposition = async(data)=>{
  return await post('/api/im/sendmsgupdateposition', data )
}
