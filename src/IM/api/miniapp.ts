import axios from '@/http/axiosMapi';

//投递简历
export const DeliverResume = (data: any) => {
  let url = `/api/User/Deliver?PositionGuid=${data.PositionGuid}&ResumeGuid=${data.ResumeGuid}&districtId=${data.districtId}&from=${data.from}`
  if (data.resumeDeliveryType) {
    url = `/api/User/Deliver?PositionGuid=${data.PositionGuid}&ResumeGuid=${data.ResumeGuid}&resumeDeliveryType=${data.resumeDeliveryType}&districtId=${data.districtId}&from=${data.from}`
  }
  return axios({
    url: url,
    method: "post",
    data,
    config: {
      headers: {},
      timeout: 10000,
    }
  })
}
