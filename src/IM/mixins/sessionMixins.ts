import {
  computed,
  defineComponent,
  onMounted,
  reactive,
  ref,
  toRefs,
  watch,
} from 'vue';
import { getSessions } from '../api';
import { useStore } from '../../store';
import util from '../../IM/utils';

interface props {
  jobid: number;
}
interface state { }
export default {
  setup(state?: state, props?: props) {
    const updataCheck = (val) => {
      mixinState.check = val;
    };
    const updataSeach = (val) => {
      mixinState.seach = val;
    };

    onMounted(async () => {
      await initList();
      handleScrollbar();
    });
    const initList = async function () {
      if (window.location.href.indexOf('/findpwd') > -1 || window.location.href.indexOf('/login') > -1|| window.location.href.indexOf('/oAuth/Login') > -1|| window.location.href.indexOf('/oAuth/Callback') > -1|| window.location.href.indexOf('/oAuth/Login') > -1|| window.location.href.indexOf('/ServiceCenterGrantAuth') > -1) {
      } else {
        let jobid = props?.jobid;
        if (jobid) mixinState.model.positionId = jobid;
        let rs: Record<string, any> = await getSessions(mixinState.model);
        let { data } = rs;
        if (rs.code == 1) {
          mixinState.list = data;
        } else {
          mixinState.errorSessionMsg = rs.message;
        }
      }
    };
    const sessionScroll = ref();

    const handleScrollbar = () => {
      const el = sessionScroll.value?.wrap;
      if(!el) return
      el.onscroll = () => {
        let top = el.scrollTop;
        let height = el.scrollHeight;
        let clientHeight = el.clientHeight;
        if (top + clientHeight >= height) {
          if (mixinState.getMore && !mixinState.isload) {
            mixinState.isload = true;
            scrollMore();
          }
        }
      };
    };

    const scrollMore = async () => {
      const page = mixinState.model.page + 1;
      const obj = {
        Index: page,
        Size: mixinState.model.size,
      };
      let o = Object.assign(mixinState.requestPost, obj);
      let data  = await getSessions(o);
  
    
      
      if (data.code == 1) {
        const result = data.data;
        if (result.length > 0) {
          result.map((item) => {
            mixinState.list.push(item);
          });
          mixinState.model.page = page;
        } else {
          mixinState.getMore = false;
        }
        mixinState.isload = false;
      }
    };

    const store = useStore();

    const mixinState = reactive({
      list: [] as sessionsItemMy[],
      model: {
        page: 1,
        size: 20,
        positionId: 0,
      },
      requestPost: {}, //记录过滤数据，下一页时不丢失过滤
      loading: false,
      getMore: true,
      isload: false,
      dropdownSelect: '',
      check: false,
      seach: '',
      errorSessionMsg: '',
    });

    watch(
      () => mixinState.requestPost,
      () => {
        mixinState.model.page = 1;
        mixinState.getMore = true;
      }
    );

    const sessionlist = computed(() => store.state.imModules.sessionlist); //原生列表
    const activeSessionId = computed(
      () => store.state.imModules.activeSessionId
    );
    const unreadCount = computed(() => {
      let count = 0;
      if (sessionlists.value) {
        sessionlists.value.forEach((item) => {
          if (item.unread > 0) count += item.unread;
        });
      }
      return count;
    });

    const sessionlists = computed(() => {
      //过滤列表
      let sessions = sessionlist;
      let list: sessions[] = [];
      if (mixinState.list.length > 0) {
        mixinState.list.map((item: sessionsItemMy) => {
          let session = sessions.value.find(
            (it) => it.id === `p2p-${item.accID}`
          );
          let id: string, unread: number, isBlack: boolean;
          isBlack = item.blocking;

          if (session) {
            id = session.id;
            unread = session.unread;
            isBlack = session.isBlack;
            const time = util.formatDate(session.updateTime, true);
            item.time = time;
            let lastMsg = session.lastMsg || {};
            // if (lastMsg.type === 'text') {
            //   item.lastContent = lastMsg.text || '';
            // } else if (lastMsg.type === 'custom') {
            //   item.lastContent = util.parseCustomMsg(lastMsg);
            // } else if (
            //   lastMsg.scene === 'team' &&
            //   lastMsg.type === 'notification'
            // ) {
            //   item.lastContent = util.generateTeamSysmMsg(lastMsg);
            // } else if (Object.keys(lastMsg).length === 0) {
            //   item.lastContent = session.lastContent;
            // } else if (util.mapMsgType(lastMsg)) {
            //   item.lastContent = `[${util.mapMsgType(lastMsg)}]`;
            // } else {
            //   item.lastContent = '';
            // }
          } else {
            id = `p2p-${item.accID}`;
            unread = 0;
          }

          list.push({ ...item, id, unread, isBlack });
        });
        if (mixinState.check && mixinState.seach) {

          list = list.filter((item) => {
            if (item.name.indexOf(mixinState.seach) > -1 && item.unread > 0) {
              return item;
            }
          });
        }
        if (mixinState.check) list = list.filter((item) => item.unread > 0);
        if (mixinState.seach) {
          list = list.filter((item) => {
            if (item.name.indexOf(mixinState.seach) > -1) {
              return item;
            }
          });
        }

        return list;
      }
    });
    const ActiveSessionId = computed(
      () => store.state.imModules.ActiveSessionId
    );
    const mixinMethods = {
      chat(sessionIds: string, type = 1) {
        
        let id = sessionIds.replace(/^p2p-ent/, '');
        let sessionId = ActiveSessionId.value;
        if (sessionId === id) return;
        store.commit('updateWidgetActiveSessionId', sessionIds);

        store.commit('updateActiveSessionId', id);

        if (type == 2) {
          try {
            throw "test"
            document.domain = import.meta.env.VITE_APP_DO_MAIN;
            if (window.self != window.top) {
              let el = parent.window.document.getElementById('im_widget');
              if (el) {
                el.style.width = '1100px';
                el.style.height = '80%';
                el.style.top = '50%';
              }
            }
          } catch {
            if (window.self != window.top) {
              window.parent.postMessage(
                {
                  methods: 'change_style',
                  data: {
                    el:'#im_widget',
                    value: [
                      { value: '80%', style: 'height' },
                      { value: '1100px', style: 'width' },
                      { value: '50%', style: 'top' },
                    ],
                  },
                },
                '*'
              );
            }
           }
        }
      },
    };
    return {
      sessionlists,
      ...toRefs(mixinState),
      activeSessionId,
      ...mixinMethods,
      initList,
      sessionScroll,
      handleScrollbar,
      updataCheck,
      updataSeach,
      unreadCount,
    };
  },
};
