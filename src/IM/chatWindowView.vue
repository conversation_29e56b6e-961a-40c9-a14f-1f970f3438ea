<template>
  <div
    class="chatContent chat-call"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
  >
    <div class="chat-box">
      <el-container style="height: 100%">
        <el-main style="height: 100%">
          <el-container class="c-container">
            <el-header class="chat-header" style="height: 48px">
              <div class="chat-session-panel">
                <div class="chat-session-panel__title">
                  <span v-if="positionName">沟通职位: {{ positionName }}</span>
                  <span v-else>{{ positionData?.enterpriseName }}</span>
                </div>

                <div
                  class="chat-session-panel__control"
                  @click="closeChatWindow"
                >
                  <i class="el-icon-circle-close"></i>
                </div>
              </div>
            </el-header>
            <el-main class="main">
              <p v-if="isHelper" class="tips">
                该职位为平台推荐，查看详情或点击沟通按钮即可立即开聊
              </p>
              <chatList
                v-if="isAsync && reLoad"
                type="session"
                :msglist="msglist"
                :account="account"
                :userInfos="userInfos"
                :myInfo="myInfo"
                :isRobot="isRobot"
                :deliverList="deliveredList"
                @getHistory="getHistoryMsgs"
                :isBlack="isBlack"
                @cancelBlack="changeBlack"
                :scrollHeight="scrollHeight"
                :avatar="avatar"
                :inviteChats="inviteChats"
                @refreshStatus="replyStatus"
              ></chatList>
            </el-main>
            <el-footer height="150" class="footer" v-if="!isHelper">
              <chat-editor
                type="session"
                :scene="scene"
                :to="to"
                :isRobot="isRobot"
                :invalid="teamInvalid || muteInTeam"
                :isBlack="isBlack"
                :wechatstatus="wechatstatu"
                :userWechat="wechat"
                editorHeight="height:79px"
                :positionData="positionData"
                operateHeight="height:150px"
                @refreshStatus="replyStatus"
              ></chat-editor>
            </el-footer>
          </el-container>
        </el-main>
      </el-container>
    </div>
  </div>
</template>

<script lang="ts">
import chatList from "./component/chatList.vue";
import chatEditor from "./component/chatEditor.vue";
import { ElMessage } from "element-plus";
import util from "../IM/utils/index";
import {
  ref,
  defineComponent,
  computed,
  reactive,
  onMounted,
  Ref,
  nextTick,
  toRefs,
} from "vue";
import { useStore } from "../store";
import {
  blackOpera,
  info,
  allMsgReply,
  getPosition,
  imDeliverpositionids,
} from "./api";
import { watch } from "vue";
export default defineComponent({
  components: {
    chatList,
    chatEditor,
  },
  setup() {
    const store = useStore();
    const sessionId = computed(() => {
      if (store.state.imModules.ActiveSessionId) {
        return `p2p-ent${store.state.imModules.ActiveSessionId}`;
      } else {
        return "";
      }
    });
    onMounted(async () => {});

    const state = reactive({
      wechatList: [],
      userwechat: "",
      wechatstatus: "",
      inviteChatsStatus:[],
      pid: 0,
      guid: "",
      seekerName: "",
      avatars: "",
      noresume: false,
      loading: false,
      reLoad: false,
      positionData: {},
      interviewReplyStatus: [], //视频面试雨现场面试等等状态 0 未处理 1同意 2拒绝
      interviewCheckIn: [], //是的 0:未签到 1:已签到  2:签到过期
    });

    const positionName = computed(() => {
      return state.positionData?.positionName;
    });
    const msglist = computed(() => {
      let msgs: Record<string, any>[] = store.state.imModules.currSessionMsgs;
      msgs = msgs.map((item) => {
        if (item.flow === "in" && item.content) {
          let content = JSON.parse(item.content);
          let event = content.rcevent;
          if (event == "ApplyWeChatExch_Step1") {
            let token = content.AskToken;
            let wx = wxlist.value.find((i) => i.msgGuid == token);
            if (wx) {
              item.wxStatus = wx.replyStatus;
            }
          }
          let AskToken = content.AskToken;
          if (state.interviewReplyStatus.length > 0 && AskToken) {
            let find = state.interviewReplyStatus.find((item2) => {
              return item2.msgGuid === AskToken;
            });
            if (find) {
              item.replyStatus = find.replyStatus;
            }
          }
          let askMsgGuid = content.askMsgGuid;

          if (state.interviewCheckIn.length > 0 && askMsgGuid) {
            let find = state.interviewCheckIn.find((item2) => {
              return item2.msgGuid === askMsgGuid;
            });
            if (find) {
              item.interviewCheckIn = find.replyStatus;
            }
          }
        }
        return { ...item };
      });
      // if (state.interviewReplyStatus.length > 0) {
      //   state.interviewReplyStatus.forEach((item: any) => {
      //     let find = msgs.find((item2) => {
      //       if (item2.flow === 'in' && item2.content) {

      //           return item.msgGuid == item2.askToken;
      //       }
      //     });

      //     if (find) {
      //       find.showCustom.replyStatus = item.replyStatus;
      //     }
      //   });
      // }
      return msgs;
    });
    const scrollHeight = computed(() => {
      return store.state.imModules.scrollHeiht;
    });
    const userInfos = computed(() => {
      return store.state.imModules.userInfos;
    });
    const isBlack = computed(() => {
      let info = Object.assign(
        {},
        store.state.imModules.userInfos[account.value]
      );
      return info.isBlack;
    });
    const isAsync = computed(() => {
      return store.state.imModules.isAsync;
    });
    const refreshReplyState = computed(() => {
      return store.state.imModules.refreshReplyState;
    });
    const myInfo = computed(() => {
      return store.state.imModules.myInfo;
    });
    const canLoadMore = computed(() => {
      return !store.state.imModules.noMoreHistoryMsgs;
    });
    const robotInfos = computed(() => {
      return store.state.imModules.robotInfos;
    });
    // 判断是否是机器人
    const isRobot = computed(() => {
      let sessionIds = sessionId.value;
      let user = null;
      if (/^p2p-/.test(sessionIds)) {
        user = sessionIds.replace(/^p2p-/, "");
        if (robotInfos.value[user]) {
          return true;
        }
      }
      return false;
    });
    //切换聊天对象会变化，代替im的路由切换
    const account = computed(() => {
      if (store.state.imModules.ActiveSessionId) {
        return `ent${store.state.imModules.ActiveSessionId}`;
      } else {
        return "";
      }
    });
    const wxlist: Ref<Record<string, any>[]> = computed(() => {
      return state.wechatList;
    });
    const wechat = computed(() => {
      return state.userwechat;
    });
    const wechatstatu = computed(() => {
      return state.wechatstatus;
    });
    const inviteChats = computed(() => {
      return state.inviteChatsStatus;
    });
    const scene = computed(() => {
      if (sessionId.value) {
        return util.parseSession(sessionId.value).scene;
      } else {
        return "";
      }
    });
    const to = computed(() => {
      if (sessionId.value) {
        return util.parseSession(sessionId.value).to;
      } else {
        return "";
      }
    });
    const resumeGuid = computed(() => {
      return state.guid;
    });
    const avatar = computed(() => {
      return state.avatars;
    });
    const teamInfo = computed(() => {
      if (scene.value === "team") {
        var teamId = sessionId.value.replace("team-", "");
        return store.state.imModules.teamlist.find((team: any) => {
          return team.teamId === teamId;
        });
      }
      return undefined;
    });
    const teamInvalid = computed(() => {
      if (scene.value === "team") {
        return !(teamInfo.value && teamInfo.value.validToCurrentUser);
      }
      return false;
    });
    const muteInTeam = computed(() => {
      if (scene.value !== "team") return false;
      var teamMembers = store.state.imModules.teamMembers;
      var Members = teamMembers && teamMembers[teamInfo.value?.teamId];
      var selfInTeam =
        Members &&
        Members.find((item) => {
          return item.account === store.state.imModules.userUID;
        });
      return (selfInTeam && selfInTeam.mute) || false;
    });
    watch(
      () => refreshReplyState.value,
      (val) => {
        if (val) {
          methods.replyStatus();
          store.commit("updateReplyState", false);
        }
      }
    );
    const methods = {
      getHistoryMsgs() {
        if (canLoadMore.value) {
          store.dispatch("getHistoryMsgs", {
            scene: scene.value,
            to: to.value,
          });
        }
      },
      async changeBlack() {
        // this.$store.dispatch('updateBlack', {
        //   account: this.account,
        //   isBlack: !this.isBlack
        // })
        let data: any = await blackOpera({
          enterId: to.value,
          blackType: 1,
          blackValue: 0,
        });
        if (data.code != 1) {
          ElMessage({
            message: data.message,
            type: "error",
          });
        } else {
          ElMessage({
            message: data.message,
            type: "success",
          });
        }
      },
      async replyStatus() {
        let { data } = await allMsgReply(account.value);
        if (data.interviewReplyStatus.length > 0) {
          state.interviewReplyStatus = data.interviewReplyStatus;
        }

        if (data.interviewCheckIn.length > 0) {
          state.interviewCheckIn = data.interviewCheckIn;
        }

        if (!data.error) {
          state.wechatList = data.weixinReplyStatus;
          state.userwechat = data.userwechat;
          state.wechatstatus = data.wechatStatus;
          state.inviteChatsStatus = data.inviteChats;
        }
      },
      async info() {
        let accounts = account.value;
        // return
      },
      closeChatWindow() {
        store.commit("updateActiveSessionId", "");
        store.commit("updateWidgetActiveSessionId", "");
        store.commit("updateCurrSessionId", { type: "destroy" });

        try {
          throw "test";
          document.domain = import.meta.env.VITE_APP_DO_MAIN;
          if (window.self != window.top) {
            let el = parent.window.document.getElementById("im_widget");

            if (el) {
              el.style.width = "310px";
            }
          }
        } catch {
          if (window.self != window.top) {
            window.parent.postMessage(
              {
                methods: "change_style",
                data: {
                  el: "#im_widget",
                  value: [
                    { value: "80%", style: "height" },
                    { value: "310px", style: "width" },
                    { value: "50%", style: "top" },
                  ],
                },
              },
              "*"
            );
          }
        }
      },
    };
    watch(
      () => sessionId.value,
      async (newValue, oldValue) => {
        state.loading = true;
        store.commit("updatePageUpDown", true);
        state.reLoad = false;

        if (sessionId.value) {
          methods.replyStatus();

          let data: any = await info(account.value);

          if (data.code == 1) {
            state.pid = data.data.dataid;
            state.guid = "";
            state.noresume = !true;
            state.seekerName = "";
            state.avatars = data.data.avatar;
            // this.userwechat = data.data.userwechat;
            // this.wechatstatus = data.data.wechatstatus;
          } else {
            alert(data.message);
          }
          if (to.value) {
            let positionData: Record<string, any> = await getPosition(to.value);

            if (positionData.code == 1) {
              state.positionData = positionData.data;
            }
          }

          store.dispatch("setCurrSession", sessionId.value);
          store.dispatch("resetNoMoreHistoryMsgs");
          state.loading = false;
        }
        nextTick(() => {
          setTimeout(() => {
            state.reLoad = true;
          }, 100);
        });
      },
      { deep: true, immediate: true }
    );

    const deliveredList: Ref<number[]> = ref([]);
    const isHelper = computed(() => {
      if (account.value === "ent1848764") {
        getDeliveredList();
        return true;
      } else {
        return false;
      }
    });
    const getDeliveredList = async () => {
      let data = await imDeliverpositionids();

      if (data && Array.isArray(data.data)) {
        deliveredList.value = data.data;
      }
    };

    return {
      positionName,
      msglist,
      userInfos,
      ...methods,
      account,
      myInfo,
      isRobot,
      isBlack,
      scrollHeight,
      avatar,
      isAsync,
      scene,
      to,
      teamInvalid,
      muteInTeam,
      wechatstatu,
      inviteChats,
      wechat,
      ...toRefs(state),
      isHelper,
      deliveredList,
    };
  },
});
</script>

<style lang="less" scoped>
.chat-call {
  height: 100%;
  width: 100%;
  *::before,
  *::after {
    box-sizing: inherit;
  }

  .c-container {
    height: 100%;
    border-right: 1px solid rgb(232, 235, 239);
  }
  .el-main {
    padding: 0;
  }
  .main {
    position: relative;
    width: 100%;
    height: 100%;
    .tips {
      position: absolute;
      left: 50%;
      z-index: 99;
      transform: translate(-50%, 0);
      color: #457ccf;
      font-size: 12px;
      height: 32px;
      background: #fff;
      width: 100%;
      text-align: center;
      line-height: 32px;
    }
  }
  .footer {
    position: relative;
    padding: 0;
  }
  .chat-box {
    position: relative;
    height: 100%;
  }
  .chat-header {
    background: rgba(252, 252, 252, 0.39);
    border-bottom: 1px solid #e8ebef;
  }
  .chat-session-panel {
    height: 100%;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    // justify-content: space-between;
    overflow: hidden;
    &__title {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex-grow: 1;
      align-items: baseline;
    }
    &__control {
      justify-items: flex-end;
      flex-shrink: 0;
      cursor: pointer;
      i {
        font-size: 22px;
      }
      .el-dropdown {
        cursor: pointer;
      }
    }
  }
  .r-aside {
    position: relative;
  }
  .chat-extra {
    height: 100%;
  }

  .chat-editor {
    // position: absolute;
    // bottom: 0;
    // left: 0;
    width: 100%;
    background-color: #fff;
    border-radius: 0 0 5px 5px;
    display: flex;
  }
  .box-sizing {
    box-sizing: border-box;
  }
  .btn-send {
    background-color: #0888ff;
    color: #fff;
    display: inline-block;
    width: 70px;
    height: 34px;
    line-height: 36px;
    text-align: center;
    position: absolute;
    top: 11px;
    right: 15px;
    font-size: 12px;
    border: 1px solid #0070d9;
  }
  .msg-input.p2p {
    margin-left: 10px;
    width: 550px;
  }

  .chat-editor .chat-btn {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    margin-right: 2px;
    /* vertical-align: top; */
  }
  .msg-input {
    font: 14px/1.5 Helvetica, Arial, Tahoma, "微软雅黑";
    width: 530px;
    height: 38px;
    outline: none;
    background: #fff;
    border: none;
    border: 1px solid #ccc;
    border: 1px solid #ccc\9;
    overflow-y: auto;
    padding: 6px 10px;
    margin-top: 10px;
    /* vertical-align: -18px!important; */
    /* margin-left: 65px; */
  }
  textarea {
    overflow: auto;
    resize: none;
  }
  .cloudMsg {
    position: absolute;
    line-height: 20px;
    width: 68px;
    height: 46px;
    border: 1px solid #ddd;
    right: 10px;
    top: 17px;
    cursor: pointer;
  }
  .radius4px {
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
  }

  .btnforsend {
    position: absolute;
    top: 11px;
    right: 15px;
  }

  .k-resize-bar--left {
    width: 4px;
    right: auto;
    cursor: ew-resize;
  }
  .k-resize-bar {
    z-index: 1;
  }

  .k-resize-bar__thumb {
    background: #333b52;
    opacity: 0;
    overflow: hidden;
  }
  .k-resize-bar__thumb:hover {
    opacity: 0.6;
  }
  .k-resize-bar__ghost--left {
    left: 0;
  }
  .k-resize-bar__ghost {
    width: 1px;
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
  }
  .k-resize-bar__ghost--limit::before {
    opacity: 0.6;
  }
  .k-resize-bar__ghost::before {
    content: "";
    border: 1px solid;
  }
  .k-resize-bar__ghost--limit::after {
    -webkit-animation: ghostViewReachLimit 0.4s linear;
    animation: ghostViewReachLimit 0.4s linear;
  }
  .k-resize-bar__ghost::after {
    content: "";
    background: #333b52;
    opacity: 0.2;
  }

  .k-resize-bar,
  .k-resize-bar__ghost::after,
  .k-resize-bar__ghost::before,
  .k-resize-bar__thumb {
    bottom: 0;
    left: 0;
    position: absolute !important;
    right: 0;
    top: 0;
  }
}
.el-dialog {
  box-shadow: 0 11px 15px -7px rgba(37, 42, 53, 0.2),
    0 24px 38px 3px rgba(37, 42, 53, 0.14),
    0 9px 46px 8px rgba(37, 42, 53, 0.12);
}
// *,
// *::before,
// *::after {
//   box-sizing: inherit;
// }
</style>
