let config = {
  sdk: 'NIM_Web_SDK_v8.9.128.js',
  rtcSdk:'NIM_Web_WebRTC_v8.0.0.js',
  // 用户自定义的登录注册地址
  loginUrl: '/',
  // 资源路径根目录，为了方便用户部署在二级以上URL路径上
  resourceUrl: 'http://yx-web.nos.netease.com/webdoc/h5',
  // 默认用户头像
  defaultUserIcon: 'http://yx-web.nos.netease.com/webdoc/h5/im/default-icon.png',
  // 本地消息显示数量，会影响性能
  localMsglimit: 10,
  useDb: false,
  //订阅服务
  openSubscription: false,
  isTest: process.env.NODE_ENV === 'production'||process.env.NODE_ENV === 'testproduction'? false : true
}
const env = 'online'
let appConfig = {
  online: {
    appkey: '1d401393a0774f1d0e6f7a375f286a07',
    postUrl: 'https://app.netease.im'
  }
}

config = Object.assign(config, appConfig[env])

export default config
