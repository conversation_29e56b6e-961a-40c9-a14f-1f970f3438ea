<template>
  <div class="imView">
    <div class="imView_header">
      <div class="imView_header_button">
        <FilterHeader></FilterHeader>
      </div>
    </div>
    <div class="imView_main">
      <div class="left_menu">
        <div class="imView_header_info">
          <header-title @AllRead="AllRead" :type="1"></header-title>
        </div>
        <div class="left_menu_search">
          <searchHeader
            v-model:Seach="seach"
            v-model:Check="check"
          ></searchHeader>
        </div>
        <div class="left_menu_list">
          <session :seach="seach" :check="check"></session>
        </div>
      </div>
      <div class="right_content">
        <!-- <chatWindowView></chatWindowView> -->
        <div v-if="show" class="main-empty">
          <div class="main-img">
            <img
              src="../IM/assets/images/<EMAIL>"
              style="width: 140px; height: 90px"
            />
          </div>
          <div style="color: #707e97">{{ nochooseText }}</div>
        </div>
        <chatWindowView v-else></chatWindowView>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useStore } from '../store/index';
import session from './component/session.vue';
import FilterHeader from './component/FilterHeader.vue';
import searchHeader from './component/searchHeader.vue';
import headerTitle from './component/headerTitleLeft.vue';
import chatWindowView from './chatWindowView.vue';
import { useRoute } from 'vue-router';
const store = useStore();
const route = useRoute();
let seach = ref('');
let check = ref(false);

const show = computed(() => {
  return store.state.imModules.ActiveSessionId == '';
});

const nochooseText = computed(() => {
  let {
    query: { jobname },
  } = route;
  if (jobname) return `好人才，等不来。快去简历库看看吧`;
  return '您还未选中聊天，快去和HR聊一聊吧';
});

const AllRead = () => {
};
onMounted(() => {
  store.commit('updateActiveSessionId', '');
  store.commit('updateWidgetActiveSessionId', '');
});
onUnmounted(() => {
  store.commit('updateActiveSessionId', '');
  store.commit('updateWidgetActiveSessionId', '');
});
</script>
<style lang="less" scoped>
.imView {
  height:100vh;
  width: 100%;
  background: rgba(244, 245, 249, 0.39);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .imView_header {
    .imView_header_button {
      height: 32px;

      margin-bottom: 10px;
    }
  }

  .imView_main {
    width: 100%;
    flex: 1;
    display: flex;
    overflow: hidden;
    .left_menu {
      width: 300px;
      min-width: 300px;
      height: 100%;
      display: flex;
      flex-direction: column;
      border-right: 1px solid #e8ebef;
      .imView_header_info {
        height: 48px;
        width: 100%;
      }
      .left_menu_search {
        height: 59px;
        width: 100%;
        background: #ccc;
      }
      .left_menu_list {
        flex: 1;
        height: 0;
        background: #fff;
      }
    }
    .right_content {
      height: 100%;
      flex: 1;
      background-color: #fff;
      .main-empty {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .main-img {
          background: #fff;
          overflow: hidden;
          display: inline-block;
          margin-bottom: 15px;
        }
      }
    }
  }
}
</style>
