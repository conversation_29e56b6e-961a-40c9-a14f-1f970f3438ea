import Axios from 'axios';
import { Response, Request } from './interceptors';
import { ElLoading } from 'element-plus';

Axios.defaults.withCredentials = true;
Axios.interceptors.request.use(Request);
Axios.interceptors.response.use(Response.success, Response.error);

let ENV = import.meta.env.VITE_APP_IM_ENV;
/**
 * http get请求
 * @param {string} url    请求的url，可用相对路径或者完整路径
 * @param {object} params 请求的参数
 */
interface loadingConfig {
  show: boolean;
  target: string;
}

export const get = async (
  url: string,
  params: Record<string, unknown>,
  loadingConfig?: loadingConfig
) => {
  let loading;
  if (loadingConfig && loadingConfig.show) {
    loading = ElLoading.service({
      lock: true,
      // text: 'Loading  ',
      background: 'rgba(0, 0, 0, 0)',
      spinner: 'el-icon-loading',
      target: loadingConfig.target,
      customClass:'loading_class_3_11'
    });
  }
  let resp = await Axios.get(ENV + url, {
    params: params,
  });
  loading?.close();
  return resp;
};

/**
 * post请求
 * @param {string} url    请求的url，可用相对路径或者完整路径
 * @param {object} params 请求的参数
 * @param {object} headers 请求的header
 */
export const post = async (
  url: string,
  params: Record<string, unknown>,
  headers = {}
) => {
  let resp = await Axios.post(ENV + url, params, headers);
  return resp;
};

/**
 * put请求
 * @param {string} url    请求的url，可用相对路径或者完整路径
 * @param {object} params 请求的参数
 */
export const put = async (
  url: string,
  params: Record<string, unknown>,
  headers = {}
) => {
  let resp = await Axios.put(ENV + url, params);
  return resp;
};

/**
 * delete请求
 * @param {string} url    请求的url，可用相对路径或者完整路径
 * @param {object} params 请求的参数
 */
export const deleted = async (url: string, params = {}, headers = {}) => {
  let resp = await Axios.delete(ENV + url, {
    data: params,
    headers: headers,
  });
  return resp;
};
