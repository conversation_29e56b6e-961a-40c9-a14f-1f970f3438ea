import {
  handler400,
  handler401,
  handler403,
  handler404,
  handler500
} from "./errorHandler";

export const Request = (config:any) =>{
  // let headerTest = localStorage.getItem("token") ? ('Bearer '+localStorage.getItem("token")) : ''
  //   headerTest = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJlaWQiOiI1MzA0NiIsInZjIjoiTEdETzVrRWtUanlYL1o5S2ZURzFCSzlCUzZTZHdXNURHUVNpeUFMOTExaz0iLCJuYmYiOjE2Mjc2MTE2NDksImV4cCI6MTYyNzY4OTYwMCwiaWF0IjoxNjI3NjExNjQ5LCJpc3MiOiJneHJjIiwiYXVkIjoidmlwIn0.cAbrJInTZfx8YIcW7UzA4JFfZmFKa76NmtrQpBJbhwc'
  //       // config.headers.post['Content-Type'] = 'application/json';
  //       config.headers.common['authorization'] = headerTest;
    return config
}
export const Response = {
  success: (resp:Record<string,any>) => {
    if(!resp) resp = {data:{}}
    if (resp.data === "") resp.data = {};
    return resp;
  },
  error: (resp:Record<string,any>) => {
    if (!resp.data) {
      const request = resp.request;
      const status = request.status;
      if (status === 403) {
        let result = JSON.parse(request.response)
        const err = {
          data: {
            error: true,
            status: 403,
            message: "您还未登录，请先登录",
            url: result.url
          }
        };
        handler403();
        return err;
      } else {
        const err = {
          data: {
            error: true,
            status: 500,
            message: "网络开了小差，请稍后重试"
          }
        };
        return err;
      }
    }

    let { data, status } = resp.response;
    switch (status) {
      case 400:
        data = handler400(resp);
        break;
      case 401:
        data = handler401();
        break;
      case 404:
        data = handler404();
        break;
      case 500:
        data = handler500();
        break;
      default:
        data = resp;
    }
    return data;
  }
};
