
import config from '../configs'

export const handler400 = (response:Record<string,Record<string,Record<string,any>>>) => {
    let res = response.response.data;
    let message = '';
    if (res.ModelState) {
        for (let v in res.ModelState) {
            message = message + "  " + res.ModelState[v];
        }
    } else if (res.error === 'invalid_grant') {
        message = res.error_description || 'invalid_grant';
    } else if (res.Message) {
        message = res.Message;
    }
    return {
        data: {
            error: true,
            message: message || "请求失败",
            status: 400
        }
    };
}

export const handler401 = () => {
    return {
        data: {
            error: true,
            status: 401,
            message: '登录超时'
        }
    };
}

export const handler403 = () =>{
    let t = config.isTest ? "t" : ""
    // location.href = `//vip.${t}gxrc.com/login?returnUrl=${location.href}`
}
export const handler404 = () => {
    return {
        data: {
            error: true,
            status: 404,
            message: '请求的接口无效'
        }
    }
}

export const handler500 = () => {
    return {
        data: {
            error: true,
            status: 500,
            message: '网络开了小差，请稍后重试'
        }
    }
}