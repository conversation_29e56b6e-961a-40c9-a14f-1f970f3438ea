<template>
  <div class="im_routerView" :style="{ height: hight }">
    <div class="imView_main" v-show="fixedWindowOpen">
      <div class="right_content" v-if="!show">
        <!-- <chatWindowView></chatWindowView> -->
        <div v-if="show" class="main-empty">
          <div class="main-img">
            <img
              src="../IM/assets/images/<EMAIL>"
              style="width: 200px; height: 200px"
            />
          </div>
          <div style="color: #707e97">{{ nochooseText }}</div>
        </div>
        <chatWindowView v-else></chatWindowView>
      </div>
      <div class="left_menu">
        <div class="imView_header_info">
          <header-title
            @AllRead="AllRead"
            @resizeHeight="resizeHeight"
            :type="2"
          ></header-title>
        </div>
        <div class="left_menu_search">
          <searchHeader
            v-model:Seach="seach"
            v-model:Check="check"
          ></searchHeader>
        </div>
        <div class="left_menu_list">
          <session
            :seach="seach"
            :check="check"
            :type="2"
            ref="sessionRef"
          ></session>
        </div>
      </div>
    </div>

    <sideFixed
      v-show="!fixedWindowOpen"
      :unreadCount="unreadCount"
      @openImView="openImView"
      :myself="true"
    ></sideFixed>
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue';
import { useStore } from '../store/index';
import session from './component/session.vue';
import FilterHeader from './component/FilterHeader.vue';
import searchHeader from './component/searchHeader.vue';
import headerTitle from './component/headerTitleLeft.vue';
import chatWindowView from './chatWindowView.vue';
import { useRoute } from 'vue-router';
import sideFixed from './component/sideFixed.vue';
const store = useStore();
const route = useRoute();
let seach = ref('');
let check = ref(false);
let hight = ref('100%');
const show = computed(() => {
  return store.state.imModules.ActiveSessionId == '';
});

const nochooseText = computed(() => {
  let {
    query: { jobname },
  } = route;
  if (jobname) return `好人才，等不来。快去简历库看看吧`;
  return '您还未选中聊天，快去和HR聊一聊吧';
});

const fixedWindowOpen = computed(() => store.state.imModules.fixedWindowOpen);

const AllRead = () => {};
const unreadCount = computed(() => {
  return store.state.imModules.unreadCount;
});
watch(
  () => unreadCount.value,
  () => {
    if (unreadCount.value > 0) {
      try {
        throw 'test';
        document.domain = import.meta.env.VITE_APP_DO_MAIN;
        let el = parent.window.document.getElementById('im-msgcount');
        if (window.self != window.top) {
          if (el) {
            el.style.display = 'block';
            el.innerHTML =
              unreadCount.value >= 99 ? '99' : unreadCount.value + '';
          }
        }
      } catch {
        if (window.self != window.top) {
          window.parent.postMessage(
            {
              methods: 'change_msgcount',
              data: {
                value: unreadCount.value >= 99 ? '99' : unreadCount.value + '',
              },
            },
            '*'
          );
        }
      }
    } else {
      try {
        throw 'test';
        document.domain = import.meta.env.VITE_APP_DO_MAIN;
        let el = parent.window.document.getElementById('im-msgcount');
        if (window.self != window.top) {
          if (el) {
            el.style.display = 'none';
            el.innerHTML = '';
          }
        }
      } catch {
        if (window.self != window.top) {
          window.parent.postMessage(
            {
              methods: 'change_msgcount',
              data: { value: '' },
            },
            '*'
          );
        }
      }
    }
  }
);
const openImView = () => {
  store.commit('updateFixedWindowOpen', true);

  try {
    throw "test"
    document.domain = import.meta.env.VITE_APP_DO_MAIN;
    if (window.self != window.top) {
      let el = parent.window.document.getElementById('im_widget');
      hight.value = '100%';
      if (el) {
        el.style.height = '80%';
        el.style.width = '310px';
        el.style.top = '50%';
      }
    }
  } catch {
    if (window.self != window.top) {
      window.parent.postMessage(
        {
          methods: 'change_style',
          data: {
            el:'#im_widget',
            value: [
              { value: '80%', style: 'height' },
              { value: '310px', style: 'width' },
              { value: '50%', style: 'top' },
            ],
          },
        },
        '*'
      );
    }
  }
};
const resizeHeight = (h) => {
  hight.value = h;
};

const sessionRef = ref(null);

defineExpose({
  sendChat(id: number) {
    sessionRef.value?.handleMessage({
      data: { entId: id, type: 1 },
      origin: window.location.href,
    });
  },
});
</script>
<style lang="less" scoped>
.im_routerView {
  position: fixed;
  // height: 80%;

  top: 0;
  z-index: 999;
  right: 0;
  max-width: 1100px;

  .imView_main {
    width: 100%;
    flex: 1;
    display: flex;
    overflow: hidden;
    height: 100%;
    .left_menu {
      width: 300px;
      min-width: 300px;
      // height: 100%;
      display: flex;
      flex-direction: column;
      margin: 10px 0 10px 10px;
      box-shadow: 0 5px 5px -3px rgb(112 126 151 / 20%),
        0 8px 10px 1px rgb(112 126 151 / 14%),
        0 3px 14px 2px rgb(112 126 151 / 12%);
      // border-right: 1px solid #e8ebef;
      .imView_header_info {
        height: 48px;
        width: 100%;
      }
      .left_menu_search {
        height: 59px;
        width: 100%;
        background: #ccc;
      }
      .left_menu_list {
        flex: 1;
        height: 0;
        background: #fff;
      }
    }
    .right_content {
      flex: 1;
      background-color: #fff;
      width: 700px;
      margin: 10px 10px 10px 10px;
      box-shadow: 0 5px 5px -3px rgb(112 126 151 / 20%),
        0 8px 10px 1px rgb(112 126 151 / 14%),
        0 3px 14px 2px rgb(112 126 151 / 12%);
      .main-empty {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .main-img {
          background: #fff;
          overflow: hidden;
          display: inline-block;
          margin-bottom: 15px;
        }
      }
    }
  }
}
</style>
