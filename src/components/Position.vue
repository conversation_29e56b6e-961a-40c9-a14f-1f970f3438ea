<template>
  <div
    class="pos-li"
    :class="props.item.positionState == 2 ? 'pos-li-ygq' : ''"
  >
    <div class="pos-wrap clearfix">
      <div class="pos-name">
        <a
          :href="`${mainUrl}jobDetail/${props.item.positionGuid}`"
          target="_blank"
          :title="props.item.positionName"
          >{{ props.item.positionName }}</a
        >
      </div>
      <div
        class="salary"
        v-if="props.item.payPackageName && props.item.payPackageName !== ''"
      >
        {{ props.item.payPackageName }}
      </div>
      <div
        class="salary"
        v-if="props.item.payPackage && props.item.payPackage != ''"
      >
        {{ props.item.payPackage }}
      </div>
    </div>
    <div class="other-wrap clearfix">
      <div class="box clearfix">
      <span v-if="props.item.workPlace && props.item.workPlace != ''">{{
        props.item.workPlace
      }}</span>
      <span v-if="props.item.workDistrict && props.item.workDistrict != ''">{{
        props.item.workDistrict
      }}</span>

      <span
        v-if="
          props.item.requirementOfWorkAgeName &&
          props.item.requirementOfWorkAgeName != ''
        "
        >{{ props.item.requirementOfWorkAgeName }}</span
      >
      <span v-if="props.item.workAge && props.item.workAge != ''">{{
        props.item.workAge
      }}</span>
      <span v-if="props.item.degreeName && props.item.degreeName != ''">{{
        props.item.degreeName
      }}</span>
      <span
        v-if="props.item.workPropertyName && props.item.workPropertyName != ''"
        >{{ props.item.workPropertyName }}</span
      >

      <span
        v-if="
          props.item.requirementOfEducationDegreeName &&
          props.item.requirementOfEducationDegreeName != ''
        "
        >{{ props.item.requirementOfEducationDegreeName }}</span
      >
      <span v-if="props.item.degree && props.item.degree != ''">{{
        props.item.degree
      }}</span>
        <span v-for="(items,index) in props.item.positionKeywords" :key="index">
            {{items.name}}
        </span>
      </div>
    </div>
    <div class="ent-wrap clearfix">
      <div class="ent-name">
        <a
          :href="`${mainUrl}company/${
            props.item.enterPriseGuid || props.item.enterpriseGuid
          }`"
          target="_blank"
          :title="props.item.enterpriseName"
          >{{ props.item.enterpriseName }}</a
        >
      </div>

      <div v-if="props.isMyViewed" class="option">
        <!-- <div v-if="props.isMyViewed" class="option" @mouseenter="methods.showDelete" @mouseleave="methods.hiddenDelete"> -->
        <div class="date">{{ props.item.viewTime }}</div>
        <!-- <div class="date" v-show="!state.showDelete">{{props.item.viewTime}}</div>
    <div class="delete" v-show="state.showDelete" @click="methods.deleteThisPosition"><i class="iconfont icon-trash4"></i>删除记录</div> -->
      </div>
      <div
        v-if="props.isFavorites"
        class="option"
        @mouseenter="methods.showFavorites"
        @mouseleave="methods.hiddenFavorites"
      >
        <div class="date" v-show="!state.showFavorites">
          <div v-if="props.item.positionState == 2">已过期</div>
          <div class="ysc" v-else>
            <i class="iconfont icon-star2"></i>已收藏
          </div>
        </div>
        <div
          class="delete"
          v-show="state.showFavorites"
          @click="methods.cancelThisFavorites(props.item.collectionId)"
        >
          × 取消收藏
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, watch ,onBeforeMount, computed} from "vue";
import { myDeleteCollection } from "@/http/api";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
export default defineComponent({
  props: ["item", "isMyViewed", "isFavorites"],
  setup(props, context) {
    const store = useStore();
    const state = reactive({
      showDelete: false,
      showFavorites: false,
    });
      const mainUrl=computed(()=>{
        return store.state.domainUrl?.endsWith('/') ? store.state.domainUrl : store.state.domainUrl + '/'
      })
    // onBeforeMount(()=>{
    //   console.log("获取缓存",store.state.domainUrl)
    // })
    const methods = {
      async cancelThisFavorites(collectionids: number) {
        const res = await myDeleteCollection({ collectionids: collectionids });
        if (res.code == 1) {
          ElMessage.success(res.message);
          context.emit("refreshList", true);
        } else {
          ElMessage.error(res.message);
        }
      },
      showFavorites() {
        state.showFavorites = true;
      },
      hiddenFavorites() {
        state.showFavorites = false;
      },
    };

    return {
      state,
      props,
      methods,
      mainUrl
    };
  },
});
</script>

<style lang="less">
.pos-li {
  float: left;
  width: 296px;
  height: 110px;
  background: #ffffff;
  padding: 15px 15px 0;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  .pos-wrap,
  .ent-wrap {
    display: flex;
  }
  .pos-name,
  .ent-name {
    flex: 2;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    a {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  .pos-name a {
    font-weight: bold;
  }
  .ent-name a {
    color: #999999;
  }
  .salary,
  .option {
    flex: 1;
    text-align: right;
  }
  .pos-wrap {
    font-size: 15px;
    padding-bottom: 12px;
    .pos-name {
      a:hover {
        color: #457ccf;
      }
    }
    .salary {
    }
  }
  .other-wrap {
    padding-bottom: 12px;
    border-bottom: 1px solid #f2f2f2;
    span {
      float: left;
      height: 16px;
      font-size: 12px;
      padding: 0 5px;
      margin-right: 5px;
      line-height: 16px;
      color: #666666;
      margin-bottom: 5px;
      border: 1px solid #eeeeee;
    }
    .box{
      height: 20px;
      overflow: hidden;
    }
  }
  .ent-wrap {
    font-size: 12px;
    padding-top: 0px;
    height: 40px;
    line-height: 40px;
    color: #999999;
    .ent-name {
      a:hover {
        color: #457ccf;
      }
    }
    .option {
      .date {
        i {
          font-size: 16px;
        }
        .ysc {
          color: #457ccf;
        }
      }
      .delete {
        cursor: pointer;
      }
      .delete:hover {
        color: #666666;
      }
    }
  }
}
.pos-li-ygq {
  color: #bbbbbb;
  a {
    color: #bbbbbb;
  }
  .other-wrap {
    span {
      color: #bbbbbb;
    }
  }
  .option {
    color: #bbbbbb;
  }
}
</style>


