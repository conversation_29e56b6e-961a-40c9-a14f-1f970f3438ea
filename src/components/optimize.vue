<template>
    <div class="pop-optimizeNumbers">
        <el-dialog
            v-model="dialogVisible"
            title="Tips"
            width="376px"
            :before-close="handleClose"
            :show-close="true"
            :close-on-click-modal="false"
        >
            <template #title>
                <h3 class="tit">完善简历提示</h3>
            </template>
            <div class="con">
                <h4>您有一份未完善的简历，还不能进行投递。</h4>
                <p class="tips">建议您马上完善简历！</p>
            </div>
            <template #footer>
                <a :href="`${url}`" target="_blank" class="goPerfect">完善简历</a>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import {
    computed,
    defineComponent,
    reactive,
    toRefs,
    ref,
    onBeforeMount,
    onMounted,
} from "vue";
import { useStore } from "vuex";
export default defineComponent({
    emits: ["cloes"],
    props: {
        url: {
            type: String,
            default: '/register/registerRouteView/registerBasicInfo',
        },
        defaultResumeId: {
            type: Number,
            default: 0,
        },
        

    },
    setup(props: any, { emit }: any) {
        const store = useStore();
        const state: any = reactive({
            dialogVisible: ref(true),
        });
        onBeforeMount(() => {

        });
        const methods = {
        };
        const fun = {
            handleClose(done: any) {
                // done()
                emit("cloes");
            },
        };
        return {
            ...toRefs(state),
            ...fun,
        };
    },
});
</script>


<style lang="less">
.pop-optimizeNumbers {
    .tit {
        font-size: 16px;
        color: #333;

    }
    .el-dialog__body {
        padding: 10px 24px 24px 24px;
        h4 {
            font-size: 14px;
            color: #333;
            font-weight: normal;
        }

        p.tips {
            font-size: 14px;
            color: #666;
            padding: 8px 0;
        }
    }
    .goPerfect {
        display: block;
        width: 160px;
        height: 40px;
        text-align: center;
        background: #4187f2;
        color: #fff;
        font-size: 14px;
        line-height: 40px;
        border-radius: 2px;
        margin: auto;
    }
}
</style>
