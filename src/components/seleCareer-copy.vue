<template>
  <div class="pop-sele-positionType">
    <el-dialog
      v-model="dialogVisible"
      title="Tips"
      width="800px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div>
        <rc-cascader
          v-model="value"
          :options="list"
          :valueId="hideValue"
          :placeholderSelect="'请选择项目'"
          :asyncCommit="true"
          :placeholder="'请选择'"
          :multiple="true"
          label="keywordName"
          children="children"
        ></rc-cascader>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  toRefs,
  ref,
  onBeforeMount,
  onMounted,
} from "vue";
import { useStore } from "vuex";
import { getPosition } from "../http/dictionary";
import { searchPosition } from "../http/searchAPI";
import { Cascader } from "gxrcw-ui";

export default defineComponent({
  emits: ["confirm"],
  props: {
    hideValue: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const state: any = reactive({
      list: [],
      level1Data: [],
      level2Data: [],
      level3Data: [],
      dialogVisible: ref(true),
      positionTypeName: "", //搜索
      positionTypeList: "", //搜索到的列表
      loading: false,
    });
    onBeforeMount(() => {
      //从store中获取专业字典
      // store.dispatch("getPositionTypesResource");
      let result = store.state.positionType;
      if (result.length <= 0) {
        methods.getData();
      } else {
        fun.assembleDATA(result);
      }
    });
    const methods = {
      async getData() {
        let data: any = await getPosition("");
        store.commit("setPositionTypesResource", data.data);
        fun.assembleDATA(data.data);
      },
      // 搜索
      async getpositionTypeName(text: any) {
        let data: any = await searchPosition(text);
        state.positionTypeList = data.data;
      },
    };
    const fun = {
      ChildrenPicker1(p: any) {
        if (p.open) return false;
        state.level1Data.forEach((i: any) => (i.open = false));
        p.open = true;
        state.level2Data = state.list.filter(
          (ps: any) => ps.parentID == p.keywordID
        );
        fun.ChildrenPicker2(state.level2Data[0]);
      },

      ChildrenPicker2(p: any) {
        if (p.open || !p) {
          return false;
        }
        state.level2Data.forEach((i: any) => (i.open = false));
        p.open = true;
        let arr: any = [
          {
            childrenSelectedCount: 0,
            hasNext: false,
            keywordID: p.keywordID,
            otherName: "全部",
            keywordName: p.keywordName,
            open: false,
            selected: false,
          },
        ];
        arr.push(...state.list.filter((ps: any) => ps.parentID == p.keywordID));
        state.level3Data = arr;
      },
      ChildrenPicker3(p: any) {
        state.list.forEach((i: any) => (i.selected = false));
        p.selected = true;
        emit("confirm", p);
      },
      handleSelect(p: any) {
        let item = {
          keywordID: p.id,
          keywordName: p.name,
        };

        this.ChildrenPicker3(item);
      },
      assembleDATA(List: any) {
        state.list = computed(() => {
          let arr = [];
          arr = List.map((i: any) => {
            return {
              ...i,
              selected: false,
              otherName: i.keywordName, //别名，区别于全部选择这种需要另起他名的情况
              childrenSelectedCount: 0,
              open: false,
            };
          });
          return arr;
        });
        state.list.forEach((i: any) =>
          i.keywordID == props.hideValue ? (i.selected = true) : ""
        );
        state.level1Data = state.list.filter((i: any) => i.parentID == 0);
        fun.ChildrenPicker1(state.level1Data[0]);
      },
      handleClose(done: any) {
        // done()
        emit("confirm", "");
      },
      //搜索
      SearchPositionType(query: string) {
        if (query) {
          setTimeout(() => {
            methods.getpositionTypeName(query);
          }, 200);
        } else {
        }
      },
    };
    const value = ref([
      ["", "", "", ""],
      ["", "", ""],
      ["", "", ""],
    ]);
    return {
      ...toRefs(state),
      ...fun,
      value,
    };
  },
});
</script>


<style lang="less">
.pop-sele-positionType {
  .el-dialog__header {
    border-bottom: 1px solid #f2f2f2;
    label.tit {
      font-size: 20px;
      color: #333;
    }
    .sele-box {
      width: 450px;
      margin-left: 20px;
      border-radius: 4px;
      .el-input__inner {
        width: 420px !important;
        background: #fafafa;
        color: #333;
        font-size: 14px;
        border: none;
        border-radius: 4px;
      }
    }
    .iconfont {
      padding-left: 4px;
    }
    .el-icon-arrow-up {
      display: none;
    }
  }
  .el-dialog__body {
    padding: 0 0 0 0;
  }
  .el-dialog__headerbtn .el-dialog__close {
    font-size: 30px;
    color: #d4d4d4;
  }
  .sel-box {
    height: 500px;
    overflow: hidden;
    .tBox {
      width: 160px;
      height: 500px;
      position: relative;
      overflow: hidden;
      ul {
        position: absolute;
        left: 0;
        top: 0;
        right: -17px;
        bottom: 0;
        overflow-x: hidden;
        overflow-y: scroll;
      }
      li {
        width: 122px;
        font-size: 14px;
        color: #666;
        line-height: 24px;
        cursor: pointer;
      }
      li.on {
        color: #457ccf;
      }
    }
    .tBox ::-webkit-scrollbar {
      width: 0 !important;
    }
    .tBox {
      -ms-overflow-style: none;
    }
    .tBox {
      overflow: -moz-scrollbars-none;
    }
    .firstBox {
      background: #ecedf4;
      li {
        padding: 18px 15px 18px 24px;
        background: #ecedf4;
      }
      li.on {
        background: #f4f5f9;
      }
    }
    .secondBox {
      background: #f4f5f9;
      li {
        padding: 18px 15px 18px 24px;
        background: #f4f5f9;
      }
      li.on {
        background: #ffffff;
      }
    }
    .thirdBox {
      width: 480px;
      li {
        float: left;
        background: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 18px 5px 18px 24px;
      }
    }
  }
}
.sele-li {
  padding: 10px;
  height: auto;
  p {
    line-height: 16px;
  }
}
.bod {
  color: #457ccf;
  font-size: 14px;
}
.lit {
  color: #999;
  font-size: 12px;
}
</style>
