<template>
  <div class="pop-sele-Industry">
    <el-dialog
      v-model="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <template #title>
        <div class="tit">
          选择行业
          <label>
            ( <span>{{ selectedCount }}</span
            >/{{maxCount}})</label
          >
        </div>
        <el-select
          v-model="value"
          filterable
          placeholder="搜索行业"
          :loading="loading"
          class="sele-box"
           :remote-method="searchIndustryType"
          remote
        >
          <template #prefix>
            <i class="iconfont icon-search1"></i>
          </template>
          <el-option
            v-for="(item, index) in IndustryTypeList"
            :key="index"
            @click="handleSelect(item)"
            class="sele-li"
            :value="item.name"
          >
            <p class="bod">{{ item.name }}</p>
            <p class="lit">{{ item.fullname }}</p>
          </el-option>
        </el-select>
      </template>
      <div class="selected-items clearfix">
        <div class="items">
          <span
            class="item"
            v-for="(p, index) in selectedArray"
            :key="p.keywordID"
            @click="onSelectindustryType(p, index)"
          >
            {{ p.keywordName }}
            <i>x</i>
          </span>
        </div>
        <el-button type="primary" round class="fr" @click="onConfirm"
          >确定</el-button
        >
      </div>
      <div class="sel-box clearfix">
        <div class="firstBox tBox fl">
          <ul>
            <li
              v-for="p in level1Data"
              :key="p.keywordID"
              @click="ChildrenPicker1(p, this)"
              :class="p.open ? 'on' : ''"
            >
              <span>{{ p.keywordName }}</span>
            </li>
          </ul>
        </div>
        <div class="secondBox tBox fl">
          <ul>
            <li
              v-for="p in level2Data"
              :key="p.keywordID"
              @click="ChildrenPicker2(p)"
              :class="p.open ? 'on' : ''"
            >
              <span>{{ p.keywordName }}</span>
            </li>
          </ul>
        </div>
        <div class="thirdBox tBox fl">
          <ul>
            <li
              v-for="p in level3Data"
              :key="p.keywordID"
              @click="ChildrenPicker3(p)"
              :class="{ on: p.selected }"
            >
              <span :title="p.otherName || p.keywordName">{{ p.otherName || p.keywordName }} </span>
              <i v-if="p.selected">✔</i>
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  toRefs,
  ref,
  onBeforeMount,
  onMounted,
  watch,
} from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { getIndustry } from "../http/dictionary";
import { searchIndustry } from "../http/searchAPI";
export default defineComponent({
  emits: ["confirm"],
  props: {
    // id数组
    hideValue: {
      type: Array || String || Number,
      default: [] || "" || 0,
    },
    maxCount: {
      type: Number,
      default: 3
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const state: any = reactive({
      list: [],
      level1Data: [],
      level2Data: [],
      level3Data: [],
      dialogVisible: true,
      IndustryTypeName: "", //搜索
      IndustryTypeList: [], //搜索到的列表
      selectedCount: 0 as any,
      selectedArray: [] as any,
      loading:false,
      values: computed(() => {
        //重新组装 id
        if (Array.isArray(props.hideValue)) {
          return props.hideValue;
        }
        let arr = `${props.hideValue}`
          .split(",")
          .map((i) => parseInt(i))
          .filter((i) => !!i);
        return arr;
      }),
    });
    onBeforeMount(() => {
      //从store中获取专业字典
      // store.dispatch("getIndustryTypesResource");
      let result = store.state.industry;
      if (result.length <= 0) {
        methods.getData();
      } else {
        fun.assembleDATA(result);
      }
    });
    const value = ref<string[]>([]);
    const methods = {
      async getData() {
        let data: any = await getIndustry("");
        store.commit("setIndustryResource", data.data);
        fun.assembleDATA(data.data);
      },
      // 搜索
      async getIndustryTypeName(text: any) {
        let data: any = await searchIndustry(text);
          state.IndustryTypeList = data.data;
      },
    };
    const fun = {
      ChildrenPicker1(p: any) {
        if (p.open) {
          return false;
        }
        state.level1Data.forEach((i: any) => (i.open = false));
        p.open = true;
        state.level2Data = state.list.filter(
          (ps: any) => ps.parentID == p.keywordID
        );
        state.level2Data.forEach((i: any) => (i.open = false));
        this.ChildrenPicker2(state.level2Data[0]);
      },
      ChildrenPicker2(p: any) {
        if (p.open || !p) {
          return false;
        }
        state.level2Data.forEach((i: any) => (i.open = false));
        p.open = true;
        let arr: any = [
          {
            hasNext: false,
            keywordID: p.keywordID,
            keywordName: p.keywordName,
            otherName: "全部",
            open: false,
            selected: p.selected,
          },
        ];
        arr.push(...state.list.filter((ps: any) => ps.parentID == p.keywordID));
        state.level3Data = arr;
      },
      ChildrenPicker3(p: any) {
        if (p.selected || !p) {
          return false;
        }
        this.onMultipleMode(p);
      },
      // 多选
      onMultipleMode(p: any) {
      //选择
        if (state.selectedCount >= props.maxCount) {
          let msg='最多只能选择'+props.maxCount+'个选项';
          ElMessage({
            message: msg,
            type: "warning",
          });
          return false;
        }
        // 查看是不是选择了全部-如果选择了全部就要把下面的子集去掉 false
        if (p.otherName == "全部") {
          state.list.forEach((i: any) =>
            i.parentID == p.keywordID ? (i.selected = false) : ""
          );
        } else {
          state.list.forEach((i: any) =>
            i.keywordID == p.parentID ? (i.selected = false) : ""
          );
          state.level3Data.forEach((i: any) =>
            i.otherName == "全部" ? (i.selected = false) : ""
          );
        }

        //重新加载选好的选项
        state.selectedArray = state.list.filter(
          (ps: any) => ps.selected == true
        );
        if (state.selectedCount >= props.maxCount) {
          let msg='最多只能选择'+props.maxCount+'个选项';
          ElMessage({
            message: msg,
            type: "warning",
          });
          return false;
        }
        p.selected = true;
        state.list.forEach((i: any) =>
          i.keywordID == p.keywordID ? (i.selected = true) : ""
        );
        //重新加载选好的选项
        state.selectedArray = state.list.filter(
          (ps: any) => ps.selected == true
        );
      },
      handleSelect(p: any) {
        let item = {
          keywordID: p.id,
          keywordName: p.name,
        };
        this.ChildrenPicker3(item);
      },
      assembleDATA(List: any) {
        state.list = computed(() => {
          let arr = [];
          arr = List.map((i: any) => {
            return {
              ...i,
              selected: false,
              otherName: i.keywordName, //别名，区别于全部选择这种需要另起他名的情况
              open: false,
            };
          });
          return arr;
        });
        state.values.forEach((id: any, index: number) => {
          let item = state.list.find((p: any) => p.keywordID == id);
          if (!item) return;
          item.selected = true;
          state.selectedArray.push(item);
        });
        fun.Count();
        state.level1Data = state.list.filter((i: any) => i.parentID == 0);
        fun.ChildrenPicker1(state.level1Data[0]);
      },
      handleClose(done: any) {
        // done()
        emit("confirm", "");
      },
      //搜索
      // searchIndustryType(val: any) {
      //   methods.getIndustryTypeName(val.data);
      // },
      searchIndustryType(query: string) {
        if (query) {
          state.loading = true
          setTimeout(() => {
            methods.getIndustryTypeName(query);
            state.loading = false
          }, 200)
        } else {
        }
      },
      //删除选择的数组
      onSelectindustryType(p: any, i: any) {
        state.list.forEach((i: any) =>
          i.keywordID == p.keywordID ? (i.selected = false) : ""
        );
        state.level3Data.forEach((i: any) =>
          i.keywordID == p.keywordID ? (i.selected = false) : ""
        );
        state.selectedArray = state.list.filter(
          (ps: any) => ps.selected == true
        );
        fun.Count();
      },
      //点击了确定---向父级传值
      onConfirm() {
        if (state.selectedArray.length < 1) {
          ElMessage({
            message: "至少选择一个",
            type: "warning",
          });
          return;
        }
        emit("confirm", state.selectedArray);
      },
      Count() {
        state.selectedCount = computed(() => {
          return state.selectedArray.length;
        });
      },
      changeList(p: any) {
        // state.list.forEach((i: any) => ( i.keywordID== p.keywordID,i.selected = true?false:true));
        state.list.forEach((i: any) => {
          if (i.keywordID == p.keywordID) {
            i.selected = true ? false : true;
          }
        });
      },
      selectedArrays() {
        state.selectedArray = state.list.filter(
          (ps: any) => ps.selected == true
        );
      },
    };
    return {
      ...toRefs(state),
      ...fun,
      value
    };
  },
});
</script>

<style lang="less" >
.pop-sele-Industry {
  .el-dialog__header {
    .tit {
      font-size: 20px;
      color: #333;
      float: left;
    }
    span {
      color: #457ccf;
    }
    label {
      font-size: 16px;
      color: #333;
    }

    .sele-box {
      width: 450px;
      margin-left: 20px;
      border-radius: 4px;
      .el-input__inner {
        width: 420px !important;
        background: #fafafa;
        color: #bbbbbb;
        font-size: 14px;
        border: none;
        border-radius: 4px;
      }
    }
    .iconfont {
      padding-left: 4px;
    }
    .el-icon-arrow-up {
      display: none;
    }
  }
  .selected-items {
    border-bottom: 1px solid #eee;
    padding: 8px 20px;
    .el-button {
      margin-left: -100px;
      position: relative;
    }
  }
  .selected-items .info {
    color: #999;
    font-size: 12px;
    padding: 8px 0px;
  }
  .selected-items .items {
    display: flex;
    flex-flow: wrap;
    justify-content: flex-start;
    flex-direction: row;
    padding-right: 100px;

    float: left;
  }
  .selected-items .items .item {
    color: #3366cc;
    border: 1px solid #3366cc;
    border-radius: 30px;
    padding: 1px 17px;
    margin-right: 8px;
    display: flex;
    height: 35px;
    line-height: 35px;
    margin-bottom: 5px;
    font-size: 14px;
    i {
      font-style: unset;
      padding-left: 5px;
      font-size: 18px;
      cursor: pointer;
    }
  }
  .el-dialog__body {
    padding: 0 0 0 0;
  }
  .sel-box {
    height: 500px;
    overflow: hidden;
    .tBox {
      width: 160px;
      height: 500px;
      position: relative;
      overflow: hidden;
      ul {
        position: absolute;
        left: 0;
        top: 0;
        right: -17px;
        bottom: 0;
        overflow-x: hidden;
        overflow-y: scroll;
      }
      li {
        width: 122px;
        font-size: 14px;
        color: #666;
        line-height: 24px;
        cursor: pointer;
      }
      li.on {
        color: #457ccf;
      }
    }
    .tBox ::-webkit-scrollbar {
      width: 0 !important;
    }
    .tBox {
      -ms-overflow-style: none;
    }
    .tBox {
      overflow: -moz-scrollbars-none;
    }
    .firstBox {
      background: #ecedf4;
      li {
        padding: 18px 15px 18px 24px;
        background: #ecedf4;
      }
      li.on {
        background: #f4f5f9;
      }
    }
    .secondBox {
      background: #f4f5f9;
      li {
        padding: 18px 15px 18px 24px;
        background: #f4f5f9;
      }
      li.on {
        background: #ffffff;
      }
    }
    .thirdBox {
      width: 580px;
      li {
        float: left;
        background: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 18px 5px 18px 24px;
        width: 152px;
      }
    }
  }

  .thirdBox li.on {
    i {
      color: #5f9efc;
      font-style: unset;
    }
  }
}
.sele-li {
  padding: 10px;
  height: auto;
  p {
    line-height: 16px;
  }
}
.bod {
  color: #457ccf;
  font-size: 14px;
}
.lit {
  color: #999;
  font-size: 12px;
}
</style>