<template>
  <div
    class="com-li"
    :class="props.item.positionState == 2 ? 'com-li-ygq' : ''"
  >

   <div class="mn">
    <div class="sdl">
      <el-image :src="props.item.logoUrl" fit="fill"></el-image>
    </div>
    <div class="sdr">
      <div class="pos-wrap clearfix">
      <div class="pos-name" id="0">
        <a
          :href="`//www.gxrc.com/company/${encodeURIComponent(props.item.enterPriseGuid)}`"
          target="_blank"
          :title="props.item.enterpriseName"
          >{{ props.item.enterpriseName }}</a
        >
      </div>
      <div
        class="salary"
        v-if="props.item.payPackageName && props.item.payPackageName !== ''"
      >
        {{ props.item.payPackageName }}
      </div>
      <div
        class="salary"
        v-if="props.item.payPackage && props.item.payPackage != ''"
      >
        {{ props.item.payPackage }}
      </div>
    </div>
    <div class="other-wrap clearfix">
      <span v-if="props.item.enterprisePropertyName" v-text="props.item.enterprisePropertyName"></span>
      <span v-if="props.item.enterpriseEmployeeNumberName" v-text="props.item.enterpriseEmployeeNumberName"></span>

      <span
        v-if="
          props.item.enterpriseIndustryName &&
          props.item.enterpriseIndustryName != ''
        "
        >{{ props.item.enterpriseIndustryName }}</span
      >
      <span v-if="props.item.address && props.item.address != ''">{{
        props.item.address
      }}</span>
      <span v-if="props.item.degreeName && props.item.degreeName != ''">{{
        props.item.degreeName
      }}</span>
    </div>
    </div>

   </div>
    <div class="ent-wrap clearfix">
      <div class="ent-name">
        <a
          :href="`//www.gxrc.com/company/${
            props.item.enterPriseGuid || props.item.enterPriseGuid
          }`"
          target="_blank"
          >在招职位<i class="bigblue">{{ props.item.positionCount }}</i>个</a
        >
      </div>

      <div v-if="props.isMyViewed" class="option">
        <div class="date">{{ props.item.viewTime }}</div>
      </div>
      <div
        v-if="props.isFavorites"
        class="option"
        @mouseenter="methods.showFavorites"
        @mouseleave="methods.hiddenFavorites"
      >
        <div class="date" v-show="!state.showFavorites">
          <div v-if="props.item.positionState == 2">已过期</div>
          <div class="ysc" v-else>
            <i class="iconfont icon-star2"></i>已收藏
          </div>
        </div>
        <div
          class="delete"
          v-show="state.showFavorites"
          @click="methods.cancelThisFavorites(props.item.collectionId)"
        >
          × 取消收藏
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive } from "vue";
import { companyCollection } from "@/http/api";
import { ElMessage } from "element-plus";
export default defineComponent({
  props: ["item", "isMyViewed", "isFavorites"],
  setup(props, context) {
    const state = reactive({
      showDelete: false,
      showFavorites: false,
    });

    const methods = {
      async cancelThisFavorites(collectionids: number) {
        const res = await companyCollection({ collectionids: collectionids });
        if (res && typeof res === 'object' && res.code === 1) {
          ElMessage.success(res.message);
          context.emit("refreshList", true);
        } else {
          ElMessage.error(res.message);
        }
      },
      showFavorites() {
        state.showFavorites = true;
      },
      hiddenFavorites() {
        state.showFavorites = false;
      },
    };

    return {
      state,
      props,
      methods,
    };
  },
});
</script>

<style lang="less">
.com-li {
  float: left;
  width: 296px;
  height: 110px;
  background: #ffffff;
  padding: 15px 15px 0;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  .pos-wrap,
  .ent-wrap {
    display: flex;
  }
  .pos-name,
  .ent-name {
    flex: 2;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    a {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .bigblue{
      color: #5F9EFC;
      font-style: normal;
    }
  }
  .pos-name a{
    font-weight: bold;
  }
  .ent-name a{
    color: #999999;
  }
  .salary,
  .option {
    flex: 1;
    text-align: right;
  }
  .pos-wrap {
    font-size: 15px;
    padding:2px 0 6px 0;
    .pos-name {
      a:hover {
        color: #457ccf;
      }
    }
    .salary {
    }
  }
  .other-wrap {
   
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    span {
      height: 16px;
      font-size: 12px;
      padding: 0 5px 0 0px;
      margin-right: 5px;
      line-height: 16px;
      color: #AAAAAA;
      border-right: 1px solid #f2f2f2;
    }
  }
  .ent-wrap {
    font-size: 12px;
    padding-top: 0px;
    height: 40px;
    line-height: 40px;
    color: #999999;
    .ent-name {
      a:hover {
        color: #457ccf;
      }
    }
    .option {
      .date {
        i {
          font-size: 16px;
        }
        .ysc {
          color: #457ccf;
        }
      }
      .delete {
        cursor: pointer;
      }
      .delete:hover {
        color: #666666;
      }
    }
  }
  .mn{
    display: flex;
    padding-bottom: 12px;
    border-bottom: 1px solid #f2f2f2;
    .sdl{
      padding-right: 8px;
      img{
       width: 48px;
       height: 48px; 
      }
    }
    .sdr{
      flex: 1;
      width: 100px;
    }
  }
}
.com-li-ygq {
  color: #bbbbbb;
  a {
    color: #bbbbbb;
  }
  .other-wrap {
    span {
      color: #bbbbbb;
    }
  }
  .option {
    color: #bbbbbb;
  }
}
</style>


