<template>
  <el-drawer
    v-model="props.drawer.value"
    title="vip"
    :with-header="false"
    size="375px"
    @opened="methods.opened"
  >
  
    <div class="vip-box">
      <div class="info" v-loading="state.loading">
        <!-- <noiframe>您的浏览器不支持框架</noiframe> -->
        <iframe
          ref="vipIframe"
          class="mymall"
          :style="{ height: winHeight }"
          :src="state.url"
          frameborder="0"
          :scrolling="props.isvip == 1 ? 'yes' : 'no'"
          allowTransparency="true"
          sandbox="allow-scripts allow-same-origin"
        ></iframe>
      </div>
      <!-- 雅诗说下面这些不要了 flq-->
      <!-- <ul class="jieshao clearfix" v-if="props.isvip != 1">
        <li>
          <img
            src="https://mymall.gxrc.com/Content/Images/myMall/vip-tq-jlzdsx.png"
          />
          <p>简历自动刷新</p>
          <i></i>
        </li>
        <li>
          <img
            src="https://mymall.gxrc.com/Content/Images/myMall/vip-tq-jzlfx.png"
          />
          <p>竞争力分析</p>
          <i></i>
        </li>
        <li>
          <img
            src="https://mymall.gxrc.com/Content/Images/myMall/vip-tq-tdzd.png"
          />
          <p>投递置顶</p>
          <i></i>
        </li>
        <li>
          <img
            src="https://mymall.gxrc.com/Content/Images/myMall/vip-tq-jlmb.png"
          />
          <p>简历模板</p>
          <i></i>
        </li>
        <li>
          <img
            src="https://mymall.gxrc.com/Content/Images/myMall/vip-tq-zgsfbs.png"
          />
          <p>尊贵身份标识</p>
          <i></i>
        </li>
      </ul> -->
      <div class="pic" v-if="props.isvip != 1">
        <el-image
          src="https://image.gxrc.com/gxrcsite/my/2021/mymall-vip-img.png"
          fit="fill"
        ></el-image>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, ref } from "vue";
export default defineComponent({
  props: ["drawer", "isvip"],
  setup(props) {
    const state = reactive({
      url: ``,
      loading: false,
      first: true,
    });
    const winHeight = computed(() => {
      if (props.isvip == 1) {
        return window.innerHeight + "px";
      } else {
        return "1370px";
      }
    });
    const vipIframe = ref(null);

    const methods = {
      opened() {
        const iframe = vipIframe.value;
        let host = "";
        if (location.href.indexOf(".tgxrc") > -1) {
          host = "http://m2.tgxrc.com";
        } else {
          host = "https://mymall.gxrc.com";
        }
        state.url = '/api/account/jumptoanywhere?url=' + encodeURIComponent(`${host}/MyMall/VIP/Index?onlybody=1&appbuy=1`);
        if (iframe && state.first == true) {
          state.loading = true;
          if (iframe.attachEvent) {
            iframe.attachEvent("onload", function () {
              state.loading = false;
              state.first = false;
            });
          } else {
            iframe.onload = function () {
              state.loading = false;
              state.first = false;
            };
          }
        }
      },
    };

    return {
      state,
      props,
      methods,
      vipIframe,
      winHeight,
    };
  },
});
</script>

<style lang="less">
.vip-box {
  height: 100%;
  background: #131939;
  overflow-y: auto;
  .info {
    line-height: 0;
    overflow: hidden;
    position: relative;
    .mymall {
      width: 100%;
    }
  }
  .jieshao {
    padding-left: 20px;
    li {
      float: left;
      width: 100px;
      height: 100px;
      text-align: center;
      img {
        width: 50px;
      }
      p {
        color: #fff;
        font-size: 14px;
      }
    }
  }
  .pic {
    line-height: 0;
    text-align: center;
    padding: 20px 0;
    img {
      width: 130px;
    }
  }
}
.el-drawer.rtl {
  overflow-y: auto;
}
.el-drawer__body {
  padding: 0;
  margin: 0;
}
</style>


