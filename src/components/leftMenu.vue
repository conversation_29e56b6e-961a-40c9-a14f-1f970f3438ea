<template>
  <div class="ve_nav_bar">
    <!-- <div class="service_bg">
      <a href="https://share.gxrc.com/CrossSiteLogin/ServiceHall" target="_blank">
        <img src="https://image.gxrc.com/gxrcsite/ad/2022/pc/service_bg.png" />
      </a>
    </div> -->
    <el-row class="tac">
      <el-col>
        <el-menu
          :uniqueOpened="false"
          :default-active="state.activeIndex"
          class="el-menu-vertical-demo"
          @open="methods.handleOpen"
          @close="methods.handleClose"
          background-color="#3E5E89"
          text-color="#fff"
          active-text-color="#ffd04b"
        >
          <el-menu-item index="0" @click="methods.golink('/')">
            <i class="iconfont icon-grzx"></i>个人中心
          </el-menu-item>

          <el-menu-item-group title>
            <el-sub-menu index="1">
              <template #title>
                <i class="icon-tdjl iconfont"></i>
                <span>投递记录</span>
              </template>
              <el-menu-item
                index="1-0"
                @click="methods.golink('/apply/0')"
                class="el-menu-item"
                >全部</el-menu-item
              >
              <el-menu-item index="1-1" @click="methods.golink('/apply/1')"
                >被查看</el-menu-item
              >
              <el-menu-item index="1-2" @click="methods.golink('/apply/2')"
                >邀面试</el-menu-item
              >
              <el-menu-item index="1-3" @click="methods.golink('/apply/3')"
                >不合适</el-menu-item
              >
            </el-sub-menu>

            <el-sub-menu index="2">
              <template #title>
                <i class="icon-jlgl iconfont"></i>
                <span>简历管理</span>
              </template>
              <el-menu-item index="2-0" @click="methods.golink('/resumeList')"
                >简历列表</el-menu-item
              >
              <el-menu-item
                index="2-1"
                @click="methods.golink('/companyShield')"
                >公司屏蔽</el-menu-item
              >
              <el-menu-item
                index="2-2"
                @click="methods.golink('/enterpriseView/0')"
                >谁看过我</el-menu-item
              >
            </el-sub-menu>
            <el-menu-item index="3" @click="methods.golink('/myViewed')">
              <i class="icon-lljl iconfont"></i>浏览记录
            </el-menu-item>
            <el-menu-item index="4" @click="methods.golink('/favorites')">
              <i class="icon-zwsc iconfont"></i>我的收藏
            </el-menu-item>
          </el-menu-item-group>

          <el-menu-item-group title>
            <el-menu-item index="12" @click="methods.golink('/imView')">
              <i class="icon-massage iconfont"></i>我的消息
              <a class="unreadCount_a" v-if="unreadCount > 0">{{
                unreadCount > 99 ? "99+" : unreadCount
              }}</a>
            </el-menu-item>
            <el-sub-menu index="5">
              <template #title>
                <i class="iconfont icon-xxgl"></i>
                <span>信息管理</span>
              </template>
              <el-menu-item
                index="5-0"
                @click="
                  methods.golink(
                    '/resume/' + state.userInfo.defaultResumeId + '#iseditInfo',
                    1
                  )
                "
                >个人信息</el-menu-item
              >
              <el-menu-item index="5-1" @click="methods.golink('/privateFile')"
                >建档立卡</el-menu-item
              >
              <el-menu-item index="5-2" @click="methods.golink('/password')"
                >密码管理</el-menu-item
              >
              <el-menu-item
                index="5-3"
                @click="methods.golink('/InternetAccount')"
                >账号绑定</el-menu-item
              >
            </el-sub-menu>
            <el-sub-menu index="6">
              <template #title>
                <i class="iconfont icon-wddd1"></i>
                <span>我的订单</span>
              </template>
              <el-menu-item index="6-0" @click="methods.golink('/manual/0')"
                >全部</el-menu-item
              >
              <el-menu-item index="6-1" @click="methods.golink('/manual/1')"
                >求职助手</el-menu-item
              >
              <el-menu-item index="6-2" @click="methods.golink('/manual/2')"
                >成长课堂</el-menu-item
              >
              <el-menu-item index="6-3" @click="methods.golink('/manual/3')"
                >薪酬报告</el-menu-item
              >
            </el-sub-menu>
          </el-menu-item-group>

          <el-menu-item-group title>
            <el-menu-item index="7">
              <a href="https://czkt.gxrc.com/" target="_blank">
                <i class="icon-czkt iconfont"></i>成长课堂
              </a>
            </el-menu-item>
            <el-menu-item index="8">
              <a href="https://news.gxrc.com/" target="_blank">
                <i class="icon-zxzx iconfont"></i>资讯中心
              </a>
            </el-menu-item>
            <el-menu-item index="9" @click="methods.golink('/otherService')">
              <i class="iconfont icon-rcfw"></i>人才服务
            </el-menu-item>
            <!-- <el-menu-item
              index="10"
              @click="methods.golink('/careerAssessment')"
            >
              <i class="iconfont icon-zycp"></i>职业测评
            </el-menu-item> -->
          </el-menu-item-group>

          <!-- <el-sub-menu index="10">
            <template #title>
              <i class="iconfont icon-xxgl"></i>
              <span>电子合同</span>
            </template>
            <el-menu-item
              v-for="(item, index) in state.menu"
              :key="index"
              :index="`10-${index}`"
              @click="methods.golink(item.url)"
              >{{ item.title }}</el-menu-item
            >
          </el-sub-menu> -->
          <!-- <el-menu-item index="11">
            <a href="https://appohsxaxqr7038.pc.xiaoe-tech.com/" target="_blank">
                <i class="icon-zaixianxuexi iconfont"></i>在线学习
              </a>
     
            </el-menu-item> -->
          <el-menu-item-group title>
            <el-menu-item index="11" @click="accountLogoutEvent()">
              <i class="icon-signout iconfont"></i>退出登录
            </el-menu-item>
          </el-menu-item-group>
        </el-menu>
      </el-col>
    </el-row>
  </div>
  <Carousel :type="2" :list="state.adList" v-if="state.adList" />
</template>
<script lang='ts'>
import {
  reactive,
  ref,
  onMounted,
  watch,
  defineComponent,
  computed,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import Carousel from "@/components/Carousel.vue";
import { accountLogoutEvent } from "@/utils/utils";
import { getIndexAdList } from "@/http/api";
import { getAuthHasright } from "@/http/dzhtApi";
import { getCookies } from "../utils/common";

export default defineComponent({
  name: "leftMenu",
  components: { Carousel },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    const state = reactive({
      menu: [
        { title: "合同管理", url: "/electronicContract/contractManagement" },
        { title: "合同验签", url: "/electronicContract/signedInspection" },
      ],
      // activeIndex: ref("1-0"),
      activeIndex: computed(() => {
        let url = route.fullPath;
        if (url == "/apply/0") {
          return "1-0";
        }
        if (url == "/apply/1") {
          return "1-1";
        }
        if (
          url == "/apply/2" ||
          url == "/apply/21" ||
          url == "/apply/22" ||
          url == "/apply/23"
        ) {
          return "1-2";
        }
        if (url == "/apply/3") {
          return "1-3";
        }
        if (url == "/resumeList") {
          return "2-0";
        }
        if (url == "/companyShield") {
          return "2-1";
        }
        if (url == "/enterpriseView/0") {
          return "2-2";
        }
        if (url == "/myViewed") {
          return "3";
        }
        if (url == "/favorites") {
          return "4";
        }
        if (url == "/imView") {
          return "12";
        }
        if (url == "/privateFile") {
          return "5-1";
        }
        if (url == "/password") {
          return "5-2";
        }
        if (url == "/InternetAccount") {
          return "5-3";
        }
        if (url == "/manual/0") {
          return "6-0";
        }
        if (url == "/manual/1") {
          return "6-1";
        }
        if (url == "/manual/2") {
          return "6-2";
        }
        if (url == "/manual/3") {
          return "6-3";
        }
        if (url == "/otherService") {
          return "9";
        }
        if (url == "/careerAssessment" || url == "/careerAssessment/myTest") {
          return "10";
        }
        if (url == "/electronicContract/certified") {
          return "10-0";
        }
        if (url == "/electronicContract/contractManagement") {
          return "10-1";
        }
        if (url == "/electronicContract/sealManagement") {
          return "10-2";
        }
        if (url == "/electronicContract/signedInspection") {
          return "10-3";
        }
        // return 1-0
      }),

      adList: [],
      userInfo: {}, //默认简历id
    });
    onMounted(() => {
      methods.getAdData();
      methods.getAuth();
      state.userInfo = store.state.userInfo;
    });

    const unreadCount = computed(() => store.state.imModules.unreadCount);
    watch(
      () => store.state.userInfo,
      (newValue, oldValue) => {
        state.userInfo = newValue;
      }
    );
    const methods = {
      async getAuth() {
        const res = await getAuthHasright();
        if (res.data) {
          state.menu = [
            { title: "账户认证", url: "/electronicContract/certified" },
            {
              title: "合同管理",
              url: "/electronicContract/contractManagement",
            },
            { title: "印章管理", url: "/electronicContract/sealManagement" },
            { title: "合同验签", url: "/electronicContract/signedInspection" },
          ];
        }
      },
      handleOpen(key: any, keyPath: any) {},
      handleClose(key: any, keyPath: any) {},
      golink(url: string, type: number) {
        if (type) {
          //新页面打开
          window.open(url);
          return false;
        }
        router.push({
          path: url,
        });
      },
      async getAdData() {
        let bid = getCookies("bid");
        const res = await getIndexAdList({ districtid: bid });
        if (res.code) {
          state.adList = store.state.adList.ad3;
          state.adList = res.data.ad3;
          store.commit("setAdList", res.data);
        }
      },
    };
    return {
      state,
      methods,
      accountLogoutEvent,
      unreadCount,
    };
  },
});
</script>
<style lang="less">
.ve_nav_bar {
  margin-bottom: 12px;
  height: auto;

  .el-menu-item i,
  .el-sub-menu__title i {
    color: #fff;
  }
  .tac {
    background: #3e5e89;
  }
  .el-sub-menu__title i.iconfont,
  .el-menu-item i.iconfont {
    padding-right: 10px;
    width: 25px;
  }
  .el-menu-item-group__title {
    display: none;
  }
  .el-menu-item a {
    color: #fff;
  }
  .el-menu-item a:active {
    background: #415572;
  }
  .el-sub-menu .el-menu-item {
    padding: 0px 0px 0 45px !important;
  }
  .el-menu-item {
    height: 48px !important;
    line-height: 48px !important;
  }
  .unreadCount_a {
    border-radius: 10px;
    color: #fff;
    display: inline-block;
    font-size: 12px;
    height: 18px;
    line-height: 18px;
    padding: 0 6px;
    text-align: center;
    white-space: nowrap;
    background: #fd4169;
  }
  .service_bg {
  }
}
</style>