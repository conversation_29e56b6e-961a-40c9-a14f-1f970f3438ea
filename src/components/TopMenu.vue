<template>
<ul class="top-menu">
  <li v-for="(item,index) in state.list" :key="index" :class="item.selected==true?`current`:``" @click="methods.goRouter(item.path)">{{item.name}}</li>
</ul>
</template>

<script lang="ts">
import { defineComponent,onMounted,reactive,watch} from 'vue'
import {useRouter } from 'vue-router'

export default defineComponent({
  props:['index','list','isInformation','isAssessment'],
    setup(props) {
const router = useRouter()
        const state = reactive({
    list:[],
    infoList:[{"name":"建档立卡贫困家庭高校毕业生","path":"/privateFile","selected":false}
    ,{"name":"密码管理","path":"/password","selected":false}
    ,{"name":"账号绑定","path":"/InternetAccount","selected":false}],
    asseList:[{"name":"测评产品","path":"/careerAssessment","selected":false}
    ,{"name":"我的测评","path":"/careerAssessment/myTest","selected":false}],
  })

onMounted(() => {
      if(props.list){
        state.list=props.list
      }
      if(props.isInformation){
        state.list=state.infoList
      }
      if(props.isAssessment){
        state.list=state.asseList
      }
      state.list.forEach(element => {
        element.selected=false
      });
      state.list[props.index].selected=true
    })
    watch(
    () => props.index,(newValue, oldValue) => {
      state.list.forEach(element => {
        element.selected=false
      });
      state.list[newValue*1].selected=true
    }
  )
const methods = {
        goRouter(url) {
          router.push({path: url});
      },
    }

   return {
        state,
        methods
      }
    },
})
</script>

<style lang="less">
.top-menu{display: flex; height: 52px;line-height: 52px;margin-bottom: 10px;background: #FFFFFF;color: #666;font-size: 14px;border-radius: 2px;
li{flex: 1;line-height: 52px;border-right: 1px solid #F2F2F2;text-align: center;cursor: pointer;}
li.current{color:#457CCF;}
li:last-child{border-right:0;}
}
</style>


