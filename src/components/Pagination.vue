<template>
<el-pagination background layout="prev, pager, next" :page-size="pageSize" :total="totalCount" @current-change="methods.handlePageChange">
  </el-pagination>
</template>

<script lang="ts">
import { defineComponent,reactive,toRefs,watch} from 'vue'
  export default defineComponent({
      props:['pageSize','totalCount'],
    setup(props,context) {
const methods = {
      handlePageChange(val) {
          context.emit('handlePageChange',val)
          document.body.scrollTop=document.documentElement.scrollTop = 0;
      },
    }

   return {
    //  state,
     ...toRefs(props),
        methods
      }
    }
  })
</script>

<style lang="less">
.el-pagination{text-align: center;padding-top: 20px;}
.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li{background-color: #fff;outline: none;}
</style>


