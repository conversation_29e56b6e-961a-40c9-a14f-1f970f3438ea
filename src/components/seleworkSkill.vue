<template>
    <div class="pop-sele-skills">
        <el-dialog v-model="dialogVisible" title="Tips" width="800px" :before-close="handleClose"
            :close-on-click-modal="false">
            <template #title>
                <div class="tit">请选择您的工作技能( <span class="bule">{{ selectedCount }}</span>/{{ maxCount }})</div>
                <p class="tip">添加精准的工作技能关键词，您的简历将获得更好的推荐效果</p>
            </template>
            <div class="sel-box clearfix">
                <div class="box-list">
                    <div class="overflow-y">
                        <div class="box" v-for="(item, inde ) in list" :key="inde">
                            <h3>{{ item.typeName }}</h3>
                            <ul>
                                <li v-for="(j, ind) in item.antistops" :key="ind" @click="selectItem(j)"
                                    :class="{ selected: j.selected }">{{
            j.keywordName }}</li>
                            </ul>
                        </div>
                    </div>

                </div>
                <div class="zdy-box">
                    <el-form :inline="true" class="demo-form-inline">
                        <el-form-item label="自定义关键词" label-width="110px">
                            <el-input v-model="keyWord" placeholder="输入关键词" maxlength="10"
                                :show-word-limit="true"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="additemZdy(keyWord)">添加</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="sele-box">
                    <ul>
                        <li class="" v-for="(val, index) in activeGroup" :key="val.keywordID"> {{ val.keywordName }} <i
                                class="el-icon-close" @click="deleteitem(index)"></i></li>
                    </ul>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="confirm">确 定</el-button>
                </span>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import {
    computed,
    defineComponent,
    reactive,
    toRefs,
    ref,
    onBeforeMount,
    watch
} from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { positionantistops } from "../http/dictionary";
export default defineComponent({
    emits: ["confirm"],
    props: {
        hideValue: {
            type: Array,
            default: [],
        },
        careerId: {
            type: Number,
            default: 0,
        },
        maxCount: {
            type: Number,
            default: 10,
        },
    },
    setup(props: any, { emit }: any) {
        const store = useStore();
        const state: any = reactive({
            list: [],
            dialogVisible: ref(true),
            loading: false,
            keyWord: ref(''),
            activeGroup: ref([]),
            selectedCount: computed(() => {
                return state.activeGroup.length || 0
            }),
            remain: computed(() => {
                return props.maxCount <= state.selectedCount
            }),
            userDefined: []//自定义
        });
        watch(
            () => props.careerId,
            (newValue, oldValue) => {
                console.log("有变化", newValue)
            }
        );
        onBeforeMount(() => {
            // console.log("工作技能", props.hideValue, props.careerId)
            methods.getData(props.careerId)
        });
        const methods = {
            async getData(id: number) {
                state.userDefined = [];
                let data: any = await positionantistops({ positiontypeid: id });
                if (data.code == 1) {
                    state.list = data.data
                    state.list.forEach((valueItems: any) => {
                        valueItems.antistops.forEach((j: any) => {
                            j.selected = false
                        })
                    })

                    if (props.hideValue) {
                        state.activeGroup = props.hideValue.map((j: any) => {
                            return {
                                ...j,
                                selected: true,
                            }
                        })
                        // ---------------------
                        props.hideValue.forEach((i: any) => {
                            state.list.forEach((p: any) => {
                                p.antistops.forEach((j: any) => {
                                    if (j.keywordName == i.keywordName) {
                                        j.selected = true;
                                    }
                                })
                            })

                        });

                    }
                }


            },
        };
        const fun = {
            //选择元素
            selectItem(item: any) {
                if (!item.selected) {
                    if (state.remain) {
                        ElMessage({
                            showClose: true,
                            message: `最多只能选择${props.maxCount}个选项`,
                            type: "warning",
                        });
                        return false
                    } else {
                        item.selected = true
                        state.activeGroup.push(item)
                    }
                } else {
                    item.selected = false
                    let index = state.activeGroup.findIndex((p:any) => p.keywordName === item.keywordName);
                    if (index !== -1) {
                        state.activeGroup.splice(index, 1);
                    }



                }
            },


            deleteitem(ind: number) {
                state.list.forEach((item: any) => {
                    // 遍历第二层数组
                    item.antistops.forEach((subItem: any) => {
                        if (subItem.keywordName == state.activeGroup[ind].keywordName) {
                            subItem.selected = false
                        }
                    });
                });
                state.activeGroup.splice(ind, 1)
            },
            handleClose(done: any) {
                emit("confirm", '', 0);
            },
            additemZdy(keyWord: string) {
                if (!keyWord) {
                    ElMessage({
                        showClose: true,
                        message: "请输入技能",
                        type: "warning",
                    });
                    return false
                }
                if (state.remain) {
                    ElMessage({
                        showClose: true,
                        message: `最多只能选择${props.maxCount}个选项`,
                        type: "warning",
                    });
                    return false
                }
                let ishas = false;
                state.list.forEach((item: any) => {
                    // 遍历第二层数组
                    item.antistops.forEach((subItem: any) => {
                        if (subItem.keywordName == keyWord) {
                            ishas = true
                            return false
                        }
                    });
                });
                state.activeGroup.forEach((item: any) => {
                    if (item.keywordName == keyWord) {
                        ishas = true
                        return false
                    }
                });

                if (ishas) {
                    ElMessage({
                        showClose: true,
                        message: `该技能已存在以上选择中`,
                        type: "warning",
                    });

                    return false
                }
                let arr = {
                    keywordID: 0,
                    keywordName: keyWord,
                    selected: true
                };
                state.activeGroup.push(arr)
                state.keyWord = '';
            },
            confirm() {
                emit("confirm", state.activeGroup, 1);
            }
        };
     
        return {
            ...toRefs(state),
            ...fun,
        };
    },
});
</script>


<style lang="less">
.pop-sele-skills {
    .el-dialog__header {
        border-bottom: 1px solid #f2f2f2;
        line-height: 20px;

        div.tit {
            font-size: 20px;
            color: #333;
        }

        p.tip {
            color: #5F9EFC;
            font-size: 14px;
        }

        .sele-box {
            width: 450px;
            margin-left: 20px;
            border-radius: 4px;

            .el-input__inner {
                width: 420px !important;
                background: #fafafa;
                color: #333;
                font-size: 14px;
                border: none;
                border-radius: 4px;
            }
        }

        .iconfont {
            padding-left: 4px;
        }

        .el-icon-arrow-up {
            display: none;
        }
    }

    .el-dialog__body {
        padding: 0 0 0 0;
    }

    .el-dialog__headerbtn .el-dialog__close {
        font-size: 30px;
        color: #d4d4d4;
    }

    .sel-box {
        overflow: hidden;

        .box-list {
            height: 320px;
            overflow: hidden;
            width: 100%;
            position: relative;

            .overflow-y {
                overflow-y: scroll;
                height: 320px;
                position: relative;
                right: -30px;
            }

            .box {
                padding: 0 30px 0 0;
            }

            ul {
                display: flex;
                flex-wrap: wrap;

                li {
                    margin: 5px;
                    background: #FAFAFA;
                    padding: 0px 10px;
                    line-height: 36px;
                    border-radius: 4px;
                    cursor: pointer;
                }

                li.selected {
                    background: #F2F7FF;
                    color: #457CCF;
                }
            }
        }

        .zdy-box {
            padding: 10px 30px;

            .el-input__inner {
                width: 500px !important;
            }
        }

        .sele-box {
            padding: 10px 30px;

            ul {
                display: flex;
                flex-wrap: wrap;

                li {
                    height: 35px;
                    line-height: 35px;
                    background: #F2F7FF;
                    color: #457CCF;
                    border: 1px solid #457CCF;
                    padding: 0 15px;
                    margin: 5px 12px 0 0px;
                    border-radius: 25px;

                    i {
                        cursor: pointer;
                    }
                }
            }
        }

        .dialog-footer {
            padding: 5px 30px 10px;
            border-radius: 30px;
        }
    }
}
</style>