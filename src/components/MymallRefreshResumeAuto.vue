<template>
<el-drawer v-model="props.drawer.value" title="简历自动刷新" :with-header="false" size="375px" @opened="methods.opened">
<div class="jlzdsx-box">
    <div class="info" v-loading="state.loading">
        <iframe ref="rraIframe" class="mymall" :src="state.url" frameborder="0" scrolling="no" allowTransparency="true"></iframe>
    </div>
    <div class="pic">
        <el-image src="https://image.gxrc.com/gxrcsite/my/2021/mymall-jlzdsx-img.png" fit="fill"></el-image>
      </div>
      </div>
  </el-drawer>
</template>

<script lang="ts">
import { defineComponent,reactive,ref} from 'vue'
  export default defineComponent({
      props:['drawer'],
    setup(props) {
      const state = reactive({
        url:``,
      loading: false,
      first:true
  })
const rraIframe = ref(null);

const methods = {
  opened(){
    const iframe = rraIframe.value
    let host=''
          if(location.href.indexOf('.tgxrc')>-1){
            host='http://m2.tgxrc.com'
          }else{
            host='https://mymall.gxrc.com'
          }
    state.url=`${host}/MyMall/RefreshResumeAuto/Index?onlybody=1&appbuy=1`
    if(iframe&&state.first==true){
      state.loading=true
      if (iframe.attachEvent) {
            iframe.attachEvent('onload', function () {
              state.loading=false
              state.first=false
            })
          } else {
            iframe.onload = function () {
              state.loading=false
              state.first=false
            }


            
          }
        }
  }
    }

   return {
     state,
     props,
     methods,
     rraIframe
      }
    }
  })
</script>

<style lang="less">
.jlzdsx-box{height: 100%;
    overflow-y: auto;
    .info{line-height: 0;overflow: hidden;
    .mymall{width: 100%;height:650px;}}
    .pic{line-height: 0;text-align:center;
    img{width:100%;}
    }
    }
    .el-drawer.rtl {overflow-y: auto;}
</style>


