<template>
  <div class="pop-sele-positionType">
    <el-dialog
      v-model="dialogVisible"
      title="Tips"
      width="800px"
      :before-close="handleClose"
      close-on-click-modal="false"
    >
      <template #title>
        <label class="tit">选择证书类别</label>
      </template>
      <div class="sel-box clearfix">
        <div class="secondBox tBox fl">
          <ul>
            <li
              v-for="p in level1Data"
              :key="p.id"
              @click="ChildrenPicker1(p)"
              :class="p.open ? 'on' : ''"
            >
              <span>{{ p.title }}</span>
              <!-- <span class="info" v-show="p.childrenSelectedCount"
                >（{{ p.childrenSelectedCount }}）</span
              > -->
            </li>
          </ul>
        </div>
        <div class="thirdBox tBox fl">
          <ul>
            <li
              v-for="p in level2Data"
              :key="p.id"
              @click="ChildrenPicker3(p)"
              :class="{ on: p.selected }"
            >
              <span>{{  p.title }}</span>
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  ref,
  onBeforeMount,
} from "vue";
import { useStore } from "vuex";
import { getCertificatelist } from "../http/dictionary";
export default defineComponent({
  emits: ["confirm"],
  props: {
    hideValue: {
      type: Number,
      default: 0
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const loading = ref(false);
    const state: any = reactive({
      dialogVisible: ref(true),
      list: [],
      level1Data: [],
      level2Data: [],
    });
     onBeforeMount(async() => {
      //从store中获取专业字典
      // store.dispatch("getPositionTypesResource");
       let result = store.state.certificatelList;
      if (result.length <= 0) {
        await  methods.getData();
      } else {
        fun.assembleDATA(result);
      }
    });
    const methods = {
      async getData() {
        let data: any = await getCertificatelist("");
        store.commit("setCertificatelList", data.data);
        fun.assembleDATA(data.data);
      },
    };
    const fun = {
      ChildrenPicker1(p: any) {
        if(p.open) return false;
        state.level1Data.forEach((i: any) => (i.open = false));
        p.open = true;
         state.level2Data=state.list.find((i:any)=>i.pid==p.id).nodeList.map((j:any)=>{
          return {
             ...j,
              selected: false,
          }
        });
        state.level2Data.forEach((i:any) => {
          if(i.id==props.hideValue){
              i.selected=true
          }
        });
      },
      ChildrenPicker3(p: any) {
        state.list.forEach((i: any) => (i.selected = false));
        p.selected = true;
        emit("confirm", p);
      },
      assembleDATA(List: any) {
        state.list=List;
        state.level1Data=List.find((i:any)=>i.pid==0).nodeList.map((j:any)=>{
          return {
             ...j,
              open: false,
          }
        });
         fun.ChildrenPicker1(state.level1Data[0]);
      },
      handleClose() {
        // done()
        emit("confirm", "");
      },

    };
    return {
      loading,
      ...toRefs(state),
      ...fun,
    };
  },
});
</script>


<style lang="less">
.pop-sele-positionType {
  .el-dialog__header {
    border-bottom: 1px solid #f2f2f2;
    label.tit {
      font-size: 20px;
      color: #333;
    }
    .sele-box {
      width: 450px;
      margin-left: 20px;
      border-radius: 4px;
      .el-input__inner {
        width: 420px !important;
        background: #fafafa;
        color: #333;
        font-size: 14px;
        border: none;
        border-radius: 4px;
      }
    }
    .iconfont {
      padding-left: 4px;
    }
    .el-icon-arrow-up {
      display: none;
    }
  }
  .el-dialog__body {
    padding: 0 0 0 0;
  }
  .el-dialog__headerbtn .el-dialog__close {
    font-size: 30px;
    color: #d4d4d4;
  }
  .sel-box {
    height: 500px;
    overflow: hidden;
    .tBox {
      width: 160px;
      height: 500px;
      position: relative;
      overflow: hidden;
      ul {
        position: absolute;
        left: 0;
        top: 0;
        right: -17px;
        bottom: 0;
        overflow-x: hidden;
        overflow-y: scroll;
      }
      li {
        width: 122px;
        font-size: 14px;
        color: #666;
        line-height: 24px;
        cursor: pointer;
      }
      li.on {
        color: #457ccf;
      }
    }
    .tBox ::-webkit-scrollbar {
      width: 0 !important;
    }
    .tBox {
      -ms-overflow-style: none;
    }
    .tBox {
      overflow: -moz-scrollbars-none;
    }
    .firstBox {
      background: #ecedf4;
      li {
        padding: 18px 15px 18px 24px;
        background: #ecedf4;
      }
      li.on {
        background: #f4f5f9;
      }
    }
    .secondBox {
      background: #f4f5f9;
      li {
        padding: 18px 15px 18px 24px;
        background: #f4f5f9;
      }
      li.on {
        background: #ffffff;
      }
    }
    .thirdBox {
      width: 480px;
      li {
        float: left;
        background: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 18px 5px 18px 24px;
      }
    }
  }
}
.sele-li {
  padding: 10px;
  height: auto;
  p {
    line-height: 16px;
  }
}
.bod {
  color: #457ccf;
  font-size: 14px;
}
.lit {
  color: #999;
  font-size: 12px;
}
</style>
