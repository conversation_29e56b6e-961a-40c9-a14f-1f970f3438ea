<template>
  <div class="stars clearfix">
    <div class="star-box">
      <el-rate v-model="props.item.stars" disabled text-color="#ff9900" score-template="{props.stars}"></el-rate>
    </div>
    <span class="fr time">{{ props.item.lastEditTime }}</span>
  </div>
  <div class="handle clearfix">
    <div class="item">
      <a :href="'/resume/' + props.item.resumeId" target="_blank">
        <i class="iconfont icon-edit12"></i>
        <p>编辑</p>
      </a>
    </div>
    <el-divider direction="vertical"></el-divider>
    <div
      class="item"
      :class="props.item.isDefault == true ? `item-default` : ``"
      @click="methods.postSetDefaultResume(props.item.resumeId)"
    >
      <router-link to="">
        <i class="iconfont icon-moren"></i>
        <p>默认</p>
      </router-link>
    </div>
    <el-divider direction="vertical"></el-divider>
    <div class="item blue" @click="methods.postRefreshresume(props.item.resumeId)">
      <router-link to>
        <i class="iconfont icon-refresh blue"></i>
        <p class="blue">刷新</p>
      </router-link>
    </div>
  </div>
  <MymallRefreshResumeAuto :drawer="state.rraDrawer" />
</template>

<script lang="ts">
import { defineComponent, watch, reactive } from 'vue'
import { refreshResume, setDefaultResume } from "@/http/api";
import MymallRefreshResumeAuto from "@/components/MymallRefreshResumeAuto.vue";
import { ElMessage } from 'element-plus';
export default defineComponent({
  components: {
    MymallRefreshResumeAuto,
  },
  props: ['item'],
  emits: ['handleDefaultResume'],
  setup(props, context) {
    const state = reactive({
      item: {},
      stars: 0,
      rraDrawer: { value: false },
    })
    const methods = {
      async postRefreshresume(resumeId: number) {
        const res = await refreshResume(resumeId)
        if (res.code == 1) {
          ElMessage.success(res.message);
        } else {
          ElMessage.error(res.message);
          state.rraDrawer.value = true
        }
      },
      async postSetDefaultResume(resumeId: number) {
        if (props.item.isDefault == false) {
          const res = await setDefaultResume(resumeId)
          if (res.code == 1) {
            ElMessage.success(res.message);
            let isDefault = true
            context.emit('handleDefaultResume', isDefault)
          } else {
            ElMessage.error(res.message);
          }
        }
      },
    }

    return {
      state,
      props,
      methods
    }
  }
})
</script>

<style lang="less">
</style>


