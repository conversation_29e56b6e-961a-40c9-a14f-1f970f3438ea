<template>
  <div class="index-top-box bg-white">
    <p class="icn-edit">
      <router-link
        :to="'/resume/' + userInfo.defaultResumeId + '#iseditInfo'"
        target="_blank"
      >
        <i class="iconfont icon-bianji"></i>
      </router-link>
    </p>
    <div class="head-portrait">
      <!-- <el-avatar :size="100" :src="userInfo.avatar" class="head-img" fit="cover"></el-avatar> -->
      <el-image
        style="width: 100px; height: 100px"
        :src="userInfo.avatar + '?v=' + Math.random() * 100"
        fit="cover"
      ></el-image>
    </div>

    <p class="name">
      {{ userInfo.jobSeekerName }}
      <span class="ico-vip" @click="methods.popVip()"  v-if="vipData.expireFlag==1">
        <em>vip</em>
        生效中
        <i class="iconfont icon-arrowRight"></i>
      </span>
      <span class="ico-vip" @click="methods.popVip()" v-else>
        升级
        <em>vip</em>
        <i class="iconfont icon-arrowRight"></i>
      </span>
      
    </p>
    <div class="info">
      <span>{{ userInfo.age }}岁</span>
      <el-divider direction="vertical"></el-divider>
      <span>{{ userInfo.workYear }}</span>
      <el-divider direction="vertical"></el-divider>
      <span>{{ userInfo.talent }}</span>
    </div>
    <div class="status">
      <el-select
        v-model="userInfo.workingStateName"
        :placeholder="userInfo.workingStateName"
        @change="methods.changeSelectEvent"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </div>
  </div>
  <div class="confirmIntention" v-if="hasQueren">
    <div class="cell1">求职意向更新提示<br/>
      您的求职意向可能会有变化，请确认</div>
    <router-link class="cell2" target="_blank" :to="'/resume/' + userInfo.defaultResumeId + '#iseditCarrer&isSure'">立即查看</router-link>
  </div>


  <!-- 简历列表 -->
  <div class="resume-list bg-white">
    <div class="title">
      <i class="iconfont icon-wodejianli"></i>
      <span class="tit">我的简历</span>
      <el-button round disabled v-if="resumeListCount == 2">创建简历</el-button>
      <el-button round v-else class="btn-cj" @click="methods.addresume"
        >创建简历</el-button
      >
    </div>

    <el-collapse v-model="activeResumeList" accordion class="list">
      <el-collapse-item
        class="li"
        v-for="(items, index) in resumeList"
        :key="index"
        :name="index == 0 ? '0' : '1'"
      >
        <template #title class="resume-name clearfix">
          <i class="shu"></i>
          <h2>{{ items.resumeName }}</h2>
          <el-tag v-if="items.isDefault">默认</el-tag>
        </template>
        <ResumeDetail
          :item="items"
          @handleDefaultResume="methods.handleDefaultResume"
        />
      </el-collapse-item>
    </el-collapse>
  </div>
  <!-- 简历附件 -->
  <div class="Resume-attachment bg-white">
    <h1>
      <i class="iconfont icon-wenjianshangchuan"></i>
      简历附件({{ attachmentList.length }}/3)
    </h1>
    <p class="cup">向企业展示您更多的个人信息</p>
    <div class="btn-upload">
      <ul class="attachment-list">
        <li
          class="clearfix"
          v-for="(item, index) in attachmentList"
          :key="index"
        >
          <span @click="methods.getAttachmentPreview(item.id)">
            <el-image :src="item.icon" fit="fill"></el-image>
            <span class="tit">{{ item.attachmentDisplayName }}</span>
          </span>
          <el-popover
            v-if="item.resumeAttachmentStatus != 1"
            :popper-class="
              item.resumeAttachmentStatus == 0
                ? 'attachment-dsh'
                : 'attachment-btg'
            "
            placement="bottom"
            :width="200"
            trigger="hover"
            :content="item.auditMessage"
          >
            <template #reference>
              <span
                class="state"
                :class="item.resumeAttachmentStatus == 0 ? 'dsh' : 'btg'"
                >{{ item.resumeAttachmentStatusName }}</span
              >
            </template>
          </el-popover>
        </li>
      </ul>
      <el-button type="primary" @click="methods.uploadResumeAttachment()"
        >管理简历附件</el-button
      >
    </div>
  </div>
  <!-- vip服务 -->
  <div class="vip-sever-list bg-white">
    <div class="head">
      <i class="iconfont icon-a-jianqu34"></i>
      <span class="tit">{{vipData.expireFlag==1?vipData.memberEndTime+'到期':'通关达人必备'}}</span>
      <el-link :underline="false" class="ico" @click="methods.popVip()">
        {{vipData.expireFlag==1?'立即续费':'升级VIP'}}
        <i class="iconfont icon-arrowRight"></i>
      </el-link>
    </div>
    <div>
      <ul class="list bottom-line clearfix">
        <li class="clearfix" @click="methods.popRefreshResumeAuto()">
          <div class="sidl">
            <h3>简历自动刷新</h3>
            <p>每天3次自动刷新，简历排名更靠前</p>
          </div>
          <div class="sidr ico1"></div>
        </li>
        <li class="clearfix" @click="methods.popTopResume()">
          <div class="sidl">
            <h3>投递置顶</h3>
            <p>投递后简历占据NO.1位置</p>
          </div>
          <div class="sidr ico2"></div>
        </li>
        <li class="clearfix" @click="methods.popCompetionAnalysis(0)">
          <div class="sidl">
            <h3>竞争力分析</h3>
            <p>知己知彼，提高命中率</p>
          </div>
          <div class="sidr ico3"></div>
        </li>
        <li class="clearfix" @click="methods.popTemplate()">
          <div class="sidl">
            <h3>简历模板</h3>
            <p>让简历与众不同</p>
          </div>
          <div class="sidr ico4"></div>
        </li>
      </ul>
    </div>
  </div>

  <UploadResumeAttachment
    :dialogResumeAttachmentVisible="dialogResumeAttachmentVisible"
    :attachmentList="attachmentList"
    @closedResumeAttachment="methods.closedResumeAttachment"
  />
  <MymallCompetionAnalysis :drawer="caDrawer" :positionGuid="positionGuid" />
  <MymallVip :drawer="vipDrawer" :isvip="vipData.expireFlag"/>
  <MymallRefreshResumeAuto :drawer="rraDrawer" />
  <MymallTopResume :drawer="trDrawer" />
  <MymallTemplate :drawer="teDrawer" />
  <Optimize
    @cloes="methods.cloes"
    v-if="dialogVisible"
    :defaultResumeId="defaultResumeId"
    url="/register/registerRouteView/registerBasicInfo"
  />
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  onMounted,
  watch,
  ref,
  toRefs,
  onUpdated,
} from "vue";
import topBar from "@/components/topBar.vue";
import ResumeDetail from "@/components/ResumeDetail.vue";
import UploadResumeAttachment from "@/components/UploadResumeAttachment.vue";
import MymallCompetionAnalysis from "@/components/MymallCompetionAnalysis.vue";
import MymallVip from "@/components/MymallVip.vue";
import MymallRefreshResumeAuto from "@/components/MymallRefreshResumeAuto.vue";
import MymallTopResume from "@/components/MymallTopResume.vue";
import MymallTemplate from "@/components/MymallTemplate.vue";
import Optimize from "@/components/optimize.vue";
import { ElMessage, ElLoading } from "element-plus";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { addResume,affirmjobintension } from "@/http/resumeApi";
import { myInfo,getMyServices} from "@/http/api";
import {
  getResumeList,
  updateWorkingState,
  attachmentlist,
  attachmentPreview,
  registerstep,
} from "@/http/api";
export default defineComponent({
  components: {
    topBar,
    ResumeDetail,
    UploadResumeAttachment,
    MymallCompetionAnalysis,
    MymallVip,
    MymallRefreshResumeAuto,
    MymallTopResume,
    MymallTemplate,
    Optimize,
  },
  name: "rightSide",
  setup() {
    const store = useStore();
    const router = useRouter();
    const state = reactive({
      options: ref([
        {
          value: "913",
          label: "已离职可立即上岗",
        },
        {
          value: "914",
          label: "仍在职希望换工作",
        },
        {
          value: "919",
          label: "仍在岗等更好机会",
        },
        {
          value: "920",
          label: "刚刚毕业找工作中",
        },
        {
          value: "921",
          label: "目前暂无跳槽打算",
        },
        {
          value: "1100",
          label: "在校生",
        },
      ]),
      value: ref(""),
      resumeList: ref([]),
      resumeListCount: 0,
      attachmentList: [],
      userInfo: {
        avatar: "",
        defaultResumeId:0
      },
      dialogVisible: false,
      activeResumeList: ref("0"),
      dialogResumeAttachmentVisible: { value: false },
      caDrawer: { value: false },
      positionGuid: 0,
      vipDrawer: { value: false },
      rraDrawer: { value: false },
      trDrawer: { value: false },
      teDrawer: { value: false },
      defaultResumeId: 0, //默认简历的id
      vipData:{
        expireFlag:0,
        memberEndTime:''
      },
      hasQueren:false,
      // goregister:`/resume/${state.defaultResumeId}`
    });
    onMounted(() => {
      state.userInfo = store.state.userInfo;
      methods.getResumeList();
      methods.getAttachmentlist();
      methods.getregisterstep();
      methods.getmyServices();
      methods.getAffirmjobintension();
    });
    // watch(
    //   () => store.state.userInfo,
    //   (newValue, oldValue) => {
    //     state.userInfo = newValue;
    //   }
    // );
    watch(
      () => store.state.showVipPOP,
      (newValue, oldValue) => {
        state.vipDrawer.value = true;
      }
    );

    const methods = {
      async getResumeList() {
        const res = await getResumeList();
        state.resumeList = res.data.resumeList;
        state.resumeListCount = res.data.resumeList.length;
        //methods.getUserInfo();
      },
      
      async getAttachmentlist() {
        const res = await attachmentlist();
        state.attachmentList = res.data;
      },
      async changeSelectEvent(val: any) {
        if (store.state.userInfo.workingState != val) {
          const res = await updateWorkingState({
            resumeid: state.userInfo.defaultResumeId,
            state: val,
          });
          state.defaultResumeId =Number(state.userInfo.defaultResumeId);
          if (res.code == 1) {
            ElMessage.success(res.message);
          } else {
            // ElMessage.error(res.message);
          }
        }
      },
      async getAttachmentPreview(id: any) {
        var tempage = window.open(" ",'_blank')
        const res = await attachmentPreview({ id: id });
        if (res.code == 1) {
          //window.open(res.data, "_blank"); //todo  跳出去的链接不显示文档，显示xml
          setTimeout(() => {
            tempage.location= res.data
          }, 0);
        } else {
          // ElMessage.error(res.message);
        }
      },
      async getregisterstep() {
        const res = await registerstep();
        // state.goregister = res.data.step;
        if (res.data.step == "hasresume") {
          state.dialogVisible = false;
        } else {
          state.dialogVisible = true;

        }
      },
      //获取vip服务信息
      async getmyServices(){
        const res = await getMyServices(0);
        if(res.code==1){
          state.vipData=res.data.memberInfo
        }
      },
      //获取是否确认过求职意向
      async getAffirmjobintension(){
        const res = await affirmjobintension(0);
        if(res.code==1){
          state.hasQueren=res.data.jobIntensionType == 1
          store.commit("setHasQueren", res.data.isExpectCareer);
        }
      },
      uploadResumeAttachment() {
        state.dialogResumeAttachmentVisible.value = true;
      },
      closedResumeAttachment(val: any) {
        if (val.listIsChange) {
          state.attachmentList = val.list;
        }
      },
      handleDefaultResume(isDefault: any) {
        if (isDefault) {
          methods.getResumeList();
        }
      },
      popCompetionAnalysis(guid: any) {
        state.caDrawer.value = true;
        state.positionGuid = guid;
      },
      popVip() {
        state.vipDrawer.value = true;
      },
      popRefreshResumeAuto() {
        state.rraDrawer.value = true;
      },
      popTopResume() {
        state.trDrawer.value = true;
      },
      popTemplate() {
        state.teDrawer.value = true;
      },
      //添加简历
      async addresume() {
        if (state.resumeListCount >= 2) {
          ElMessage.warning("温馨提示:您已经创建了两份简历");
        } else {
          const loading = ElLoading.service({
            lock: true,
            text: "设置中...",
            background: "rgba(0, 0, 0, 0.7)",
          });

          let data = await addResume(0);
          loading.close();
          if (data.code == 1) {
            let resumeId = data.data.resumeId;
            let url = `/resume/${resumeId}`;
            // router.push({ path: `/resume/${resumeId}` });
            methods.getResumeList();
            window.open(url);
            ElMessage.success(data.message);
          } else {
            // ElMessage.error(data.message);
          }
        }
      },
      cloes() {
        state.dialogVisible = false;
      },
    };
    return {
      ...toRefs(state),
      methods,
    };
  },
});
</script>
<style lang="less">
.index-mn {
  .index-top-box {
    padding: 20px;
    text-align: center;
    .name {
      font-size: 24px;
      color: #333;
      .ico-vip {
        display: inline-block;
        border-radius: 9px;
        padding: 2px 5px;
        background: #faeac5;
        font-size: 12px;
        color: #cc6d00;
        cursor: pointer;
        .icon-arrowRight {
          font-size: 12px;
        }
      }
      em {
        font-style: unset;
        font-weight: bold;
      }
    }
    .icn-edit {
      text-align: right;
      a {
        color: #457ccf;
      }
    }
    .head-img {
    }
    .status {
      padding: 20px 0 10px 0;
      .el-select {
        width: 100%;
      }
      .el-input,
      .el-input__icon {
        line-height: 32px;
      }
      .el-input__inner {
        height: 32px;
        line-height: 32px;
        font-size: 12px;
      }
    }
    .head-portrait {
      text-align: center;
      .el-image {
        border-radius: 50%;
      }
    }
    .info {
      font-size: 12px;
      color: #bbb;
      padding: 8px 0 0 0;
    }
  }
  .confirmIntention{
    background: #F2F7FF;
    display: flex;
    border: 1px dashed #457CCF;
    padding: 4px 8px 8px 16px;
    font-size: 12px;
    margin: 12px 0 0 0;
    .cell1{
      color: #457CCF;
    }
    .cell2{
      background: #457CCF;
      width: 64px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      color: #fff;
      border-radius: 30px;
      margin: 5px 0px 0 9px;
      cursor: pointer;
    }
  }
  .resume-list {
    margin: 12px 0 0 0;
    padding: 0px 0;
    border-radius: 2px;
    .title {
      padding: 20px;
      i {
        font-size: 18px;
        color: #5f9efc;
        padding: 0 8px 0px 0;
      }
      span.tit {
        font-size: 16px;
        color: #333;
      }
      button.el-button.is-round {
        width: 80px;
        height: 28px;
        float: right;
        line-height: 28px;
        min-height: unset;
        padding: 0 0;
        span {
          font-size: 14px;
          color: #bbb;
        }
      }
      button.el-button.is-round.btn-cj {
        border: 1px solid #5999f9;
        span {
          color: #5999f8;
        }
      }
    }
    .list {
      .li {
        padding: 15px 0px;
        border-bottom: 1px solid #f2f2f2;
        .el-collapse-item__content {
          padding-bottom: 0;
        }
      }
      .shu {
        display: block;
        float: left;
        width: 4px;
        height: 20px;
        background: #5f9efc;
      }
      h2 {
        width: 213px;
        height: auto;
        font-size: 15px;
        color: #333;
        float: left;
        padding: 0 0 0 16px;
        line-height: 20px;
      }
      .el-tag {
        float: right;
        margin-right: 20px;
        height: 19px;
        width: 32px;
        line-height: 19px;
        background: #edf7ff;
        color: #347eed;
        padding: 0 0;
        text-align: center;
      }
      .el-collapse-item__header,
      .el-collapse-item__wrap {
        border-bottom: none;
      }
      .el-collapse-item__wrap {
        padding-bottom: 0;
      }
    }
    .stars {
      padding: 10px 20px;
      .star-box {
        float: left;
      }
      .time {
        font-size: 12px;
        color: #999;
      }
      .iconfont {
        font-size: 20px;
        margin-right: 0px;
      }
    }
    .Refresh {
      padding: 10px 20px;
      line-height: 32px;
      .tit {
        font-size: 12px;
        color: #999;
      }
      .el-button.is-round {
        width: 88px;
        height: 32px;
        float: right;
        line-height: 32px;
        min-height: unset;
        padding: 0 0;
        border: 1px solid #5999f9;
        span {
          font-size: 14px;
          color: #5999f9;
        }
      }
    }
    .handle {
      background: #fafafa;
      border-radius: 2px;
      padding: 10px 0px;
      margin: 0 20px;
      display: flex;
      .el-divider--vertical {
        float: left;
        margin: 15px 0 0 0;
        height: 32px;
        background-color: #f2f2f2;
      }
      .item {
        text-align: center;
        flex: 1;
        a {
          width: 100%;
          text-align: center;
          color: #666;
          display: inline;
          p {
            font-size: 12px;
          }
        }
      }
      .item-default {
        i {
          color: #5999f9;
        }
      }
    }
  }
  .Resume-attachment {
    padding: 20px;
    border-radius: 2px;
    margin: 12px 0 0 0;
    h1 {
      color: #333;
      font-size: 16px;
      i {
        color: #5999f8;
        padding-right: 10px;
      }
    }
    p.cup {
      color: #999;
      font-size: 12px;
      padding: 5px 0 10px 0;
    }
    .btn-upload {
      .el-button {
        width: 100%;
        background-color: #edf7ff;
        color: #457ccf;
        font-size: 14px;
        height: 35px;
        border: none;
        margin: 15px 0 0 0;
      }
      .attachment-list {
        li {
          cursor: pointer;
          border-bottom: 1px solid #f2f2f2;
          padding: 15px 15px 15px 0;
          .el-image {
            vertical-align: middle;
          }
          img {
            width: 30px;
          }
          span.tit {
            font-size: 14px;
            color: #333;
          }
          span.state {
            display: inline-block;
            font-size: 12px;
            margin-left: 5px;
            color: #f1aa59;
            padding: 3px 5px;
            background: #fff4e6;
            border-radius: 2px;
          }
          .state.dsh {
            color: #f1aa59;
            background: #fff4e6;
          }
          .state.btg {
            color: #fe5c5b;
            background: #ffeaea;
          }
        }
        li:last-child {
          border-bottom: none;
        }
      }
    }
  }
  .vip-sever-list {
    margin: 12px 0 0 0;
    border-radius: 2px;
    .head {
      padding: 0 10px 0 20px;
      height: 61px;
      line-height: 61px;
      background: url(../assets/img/tou.png) no-repeat;
      color: #ebd4b0;
      span.tit {
        color: #ebd4b0;
        font-size: 16px;
      }
      a.ico {
        color: #363231;
        font-size: 12px;
        float: right;
        width: 75px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        border-radius: 20px;
        margin: 18px 0 0 0;
        background-image: linear-gradient(90deg, #ead2af, #feeacd);
        .el-link--inner {
          padding-left: 5px;
        }
      }
      i.iconfont {
        padding-right: 5px;
      }
      i.ico {
      }
      .icon-arrowRight {
        font-size: 12px;
      }
    }
    .list {
      padding: 0 20px;
      li {
        cursor: pointer;
        padding: 15px 0;
        width: 100%;
        float: left;
        border-bottom: 1px solid #f2f2f2;
      }
      h3 {
        font-size: 14px;
        color: #333;
      }
      p {
        font-size: 12px;
        color: #999;
      }
      .sidl {
        float: left;
        margin-right: 40px;
      }
      .sidr {
        width: 40px;
        height: 40px;
        background: url(../assets/img/ico2.png) no-repeat;
        background-size: 100%;
        border-radius: 20px;
        float: right;
        margin-left: -40px;
      }
      .ico1 {
        background-position: 0 0px;
      }
      .ico2 {
        background-position: 0 -55px;
      }
      .ico3 {
        background-position: 0 -110px;
      }
      .ico4 {
        background-position: 0 -165px;
      }
      li:not(:last-child):after {
        right: 0;
        left: 0;
      }
    }
  }
  .el-rate__icon {
    margin: 0 -2px 0 -2px;
  }
}
.el-popover.el-popper.attachment-option {
  .el-popover-del {
    cursor: pointer;
  }
  .el-popover-del:hover {
    color: #5f9efc;
  }
}
.el-popover.el-popper.attachment-dsh,
.el-popover.el-popper.attachment-btg {
  border-radius: 4px;
  font-size: 11px;
  line-height: 18px;
  border: none;
}
.el-popper.is-light.attachment-dsh .el-popper__arrow::before {
  background: #fff4e6;
  border: none;
}
.el-popper.is-light.attachment-btg .el-popper__arrow::before {
  background: #fff5f5;
  border: none;
}
.el-popover.el-popper.attachment-dsh {
  background: #fff4e6;
  color: #f1aa59;
}
.el-popover.el-popper.attachment-btg {
  background: #fff5f5;
  color: #fe5c5b;
}
</style>