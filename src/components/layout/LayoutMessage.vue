<template>
  <div class="index-page">
    <topBar></topBar>
    <div class="content clearfix">
      <div class="left fl">
        <LeftMenu></LeftMenu>
      </div>
      <div class="right fr">
        <router-view></router-view>
      </div>
    </div>

    <div class="footer-box">
      <Footer />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, onMounted, computed } from "vue";
import topBar from "../topBar.vue";
import LeftMenu from "@/views/message/components/LeftMenu.vue";
import Footer from "@/components/Footer.vue";

</script>

<style lang="less">
.index-page {
  .content {
    width: 1200px;
    margin: 60px auto auto;
    padding: 12px 0 50px 0;
  }

  .el-main {
    padding: 0 !important;
    overflow: hidden;
  }

  .left {
    float: left;
    width: 220px;
    min-height: 639px;
    overflow: hidden;
    background: #FFFFFF;
    box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.05);
    border-radius: 12px 12px 12px 12px;
  }

  .right {
    width: 963px;
    min-height: 639px;
    background: #FFFFFF;
    box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.05);
    border-radius: 12px 12px 12px 12px;
    overflow: hidden;
  }

  .dzht-content {
    background: #fff;
  }

  .footer-box {
    background: #fff;
  }
}
</style>
