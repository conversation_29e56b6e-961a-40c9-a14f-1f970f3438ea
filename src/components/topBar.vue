<template>
  <el-header class="header clearfix">
    <div class="ve_top_bar">
      <ul class="clearfix">
        <li class="logo clearfix">
          <Logo :bid="bid" />
          <i class="shu">|</i>
          <span class="wenzi">{{ subheading }}</span>
        </li>
        <li class="nav" v-if="type == 3">
          <el-link target="_blank" :underline="false" href="https://news.gxrc.com/">求职攻略</el-link>
          <el-link target="_blank" :underline="false" href="https://czkt.gxrc.com/">成长课堂</el-link>
          <router-link to="/otherService">人才服务</router-link>
          <!-- <router-link to="/careerAssessment">职业测评</router-link> -->
        </li>
        <li class="search-box clearfix" v-if="type == 3">
          <el-select v-model="searchType" placeholder="请选择" popper-class="select" value-key="searchTypeId"
            @change="methods.searchTypeChange">
            <el-option v-for="item in searchTypeOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
          <el-input v-model="keyword" class="inp" @keydown.enter="methods.search"></el-input>
          <div class="position-input-wrap">
            <input id="txt_position" type="text" :readonly="true" class="search-position-input" placeholder="选择职位"
              v-model="CareerName" @click="dialogVisibleC = true" />
            <seleCareer @confirm="methods.confirmCareer" v-if="dialogVisibleC" :hideValue="CareerId" :hasAll="true">
            </seleCareer>
            <!-- <input type="hidden" id="hid_position" name="PosType" value="" /> -->
          </div>
          <el-button type="primary" icon="el-icon-search" class="btn-search" @click="methods.search"></el-button>
        </li>

        <li class="portrait" v-if="type == 3 || type == 1">
          <el-popover placement="bottom" trigger="hover" class="custom-service" style="text-align: center" :width="112">
            <template #reference>
              <el-image style="width: 40px; height: 40px" :src="avatar" fit="cover"></el-image>
            </template>
            <template #default>
              <div class="avatar-pop" @click="methods.golink('/message/commonPhrases')">
                消息通知
              </div>
              <div class="avatar-pop" @click="accountLogoutEvent()">
                退出登录
              </div>
            </template>
          </el-popover>
        </li>
        <li class="ico-box" v-if="type == 3 || type == 1">
          <!-- <el-popover
            placement="bottom"
            :width="215"
            trigger="hover"
            class="custom-service"
          >
            <template #reference>
              <i class="iconfont icon-fankui"></i>
            </template>
            <div class="service-pop">
              <div class="li">
                <span class="cell w1">求职客服</span
                ><a
                  class="qq"
                  href="http://wpa.qq.com/msgrd?v=3&amp;uin=**********&amp;site=qq&amp;menu=yes"
                  target="_blank"
                  ><span class="iconfont icon-qq2"></span>QQ交谈</a
                >
              </div>
              <div class="li">
                <span class="cell w1">企业客服</span
                ><a
                  class="qq"
                  href="http://wpa.qq.com/msgrd?v=3&amp;uin=3007270867&amp;site=qq&amp;menu=yes"
                  target="_blank"
                  ><span class="iconfont icon-qq2"></span>QQ交谈</a
                >
              </div>
              <div class="li">
                <span class="cell w1">毕业生免费<br />就业指导预约</span
                ><a
                  class="qq"
                  href="http://wpa.qq.com/msgrd?v=3&amp;uin=3486024887&amp;site=qq&amp;menu=yes"
                  target="_blank"
                  ><span class="iconfont icon-qq2"></span>QQ交谈</a
                >
              </div>
              <div class="li">
                <h3>400-0771-056</h3>
                <p class="shift">企业服务转1 求职服务转2</p>
              </div>
              <div class="date"><p>周一至周五 9:00～18:00</p></div>
            </div>
          </el-popover> -->
          <el-popover placement="bottom" trigger="hover" class="custom-service" style="text-align: center" :width="112">
            <template #reference>
              <i class="iconfont icon-weixin1"></i>
            </template>
            <div class="weixin-pop" style="text-align: center">
              <img src="https://image.gxrc.com/gxrcsite/global/qrcode_gxrcw2003_s.jpg?v=2" class="qrcode-img"
                width="100" />
              <p>微信公众号</p>
            </div>
          </el-popover>
          <div class="news">
            <i class="iconfont icon-xiaoxi" @click="methods.golink('/imView')"></i>
            <a class="unreadCount_a" v-if="unreadCount > 0">{{
              unreadCount > 99 ? "99+" : unreadCount
            }}</a>
          </div>
        </li>
      </ul>
    </div>
  </el-header>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  onMounted,
  ref,
  toRefs,
  watch,
  computed,
  onUpdated,
  onBeforeUpdate,
  nextTick, h,
} from "vue";
import { useStore } from "vuex";
import { myInfo } from "@/http/api";
import seleCareer from "@/components/seleCareer.vue";
import { accountLogoutEvent } from "@/utils/utils";
import Logo from "./logo.vue";
import { getCookies } from "@/utils/common";
import { useRouter } from "vue-router";
import { ElNotification } from 'element-plus'
export default defineComponent({
  components: { seleCareer, Logo },
  props: {
    type: {
      type: Number,
      default: 3,
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const router = useRouter();
    const unreadCount = computed(() => store.state.imModules.unreadCount);

    const state = reactive({
      searchTypeOptions: [
        { value: "1", label: "职位" },
        { value: "2", label: "公司" },
      ],
      searchType: "职位",
      searchTypeId: "1",
      keyword: "",
      avatar: "", //头像
      dialogVisibleC: false,
      CareerName: "",
      CareerId: 0,
      subheading: computed(() => {
        if (props.type == 3) {
          return "个人中心";
        } else if (props.type == 2) {
          return "简历预览";
        } else {
          return "简历编辑";
        }
      }),
      bid: computed(() => {
        return parseInt(getCookies("bid"));
      }),
    });
    const headImgchange = computed(() => store.state.headImgchange);
    watch(
      () => headImgchange.value,
      (newValue, oldValue) => {

        state.avatar =
          store.getters.userInfo.avatar + "?v=" + headImgchange.value;
      }
    );
    onMounted(async () => {

      if (props.type == 2) {
        return false;
      }
      if (Object.keys(store.getters.userInfo).length == 0) {
        await store.dispatch('getUserInfo')
      }
      state.avatar =
        store.getters.userInfo.avatar + "?v=" + Math.random() * 100;

    });

    watch(
      () => store.state.imModules.unreadCount,
      (newValue, oldValue) => {
        if (newValue > 0 && newValue > oldValue && store.state.imModules.allowNewMessageReminders) {
          ElNotification({
            title: '',
            message: h('p', { 
              style: 'cursor: pointer',
              onClick: () => {
                router.push('/imView')
              }
            }, [
              h('img', { src: 'https://avatar.gxrc.com/gxrcsite/vip/headpic_girl.jpg', style: 'width: 28px;height: 28px;border-radius: 50%;vertical-align: middle;margin-right: 10px;' }),
              h('span', null, 'HR发来了一条消息'),
            ]),
            duration: 3000,
            position: 'top-right'
          })
        }
      },
      { immediate: true }
    );

    const methods = {
      searchTypeChange(val: any) {
        state.searchTypeId = val;
      },
      searchPosition() { },
      search() {
        if (state.searchTypeId == 1) {
          window.open(
            `https://s.gxrc.com/sJob?schType=1&keyword=${state.keyword}&posType=${state.CareerId}`
          );
        } else {
          window.open(
            `https://s.gxrc.com/sEnt?schType=2&keyword=${state.keyword}&posType=${state.CareerId}`
          );
        }
      },
      //接收从子集传过来的数据---职位
      confirmCareer(p: any) {
        state.dialogVisibleC = false;
        if (p) {
          state.CareerName = p.keywordName;
          state.CareerId = p.keywordID;
        }
      },
      golink(url: string) {
        router.push({
          path: url,
        });
      },
    };
    return {
      ...toRefs(state),
      methods,
      accountLogoutEvent,
      unreadCount,
    };
  },
});
</script>
<style lang="less">
.header {
  width: 100%;
  background: #fff;
  height: 60px;
  line-height: 60px;
}

.ve_top_bar {
  width: 1200px;
  margin: auto;

  li {
    float: left;
  }

  .logo {
    float: left;
  }

  .logo-img {
    height: 37px;
    float: left;
    padding: 12px 0 0 0;
  }

  .shu {
    color: #f2f2f2;
    padding: 0 10px;
    font-style: normal;
  }

  .wenzi {
    font-size: 18px;
    color: #457ccf;
  }

  .nav {
    padding: 0px 13px;
    line-height: 60px;

    a {
      display: inline-flex;
      color: #333333;
      font-size: 14px;
      padding: 0 20px;
    }

    a:hover {
      color: #5f9efc;
    }
  }

  .search-box {
    width: 310px;
    height: 38px;
    line-height: 38px;
    border: 1px solid #5f9efc;
    border-radius: 30px;
    margin: 10px 20px;
    overflow: hidden;

    .el-select {
      float: left;
      padding: 0px 0px 0 15px;
    }

    .el-input {
      width: 56px;
      height: 38px;
      line-height: 38px;
    }

    .el-input--suffix .el-input__inner {
      border: none;
      padding: 0 0;
    }

    .inp {
      float: left;
      width: 100px;

      .el-input__inner {
        border: none;
        padding: 0 0;
      }
    }

    .position-input-wrap {
      float: left;
      cursor: pointer;

      .search-position-input {
        width: 73px;
        padding: 0px 12px 0 1px;
        text-align: center;
        color: #6a6a6a;
        border: none;
        border-left: 1px solid #efeded;
        outline: none;
        background: url(//image.gxrc.com/gxrcsite/global/jt.png) no-repeat 72px 6px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        cursor: pointer;
        margin: 11px 0 0 0;
      }
    }

    .btn-search {
      float: right;
      width: 51px;
      height: 39px;
      line-height: 18px;
      margin: -1px;
      border-radius: 0 20px 20px 0;
      background-color: #5f9efc;
      border-color: #5f9efc;

      .el-icon-search {
        margin-left: -8px;
        margin-top: -2px;
        font-size: 18px;
      }
    }
  }

  .ico-box {
    height: 60px;
    line-height: 60px;
    padding-right: 20px;
    float: right;

    .iconfont {
      font-size: 30px;
      padding: 0 8px;
    }

    .weixin-pop {
      width: 132px;

      img {
        width: 100px;
      }
    }

    .news {
      display: inline-block;
      position: relative;
    }

    .icon-xiaoxi {
      cursor: pointer;
    }

    .unreadCount_a {
      border-radius: 10px;
      color: #fff;
      display: inline-block;
      font-size: 12px;
      height: 18px;
      line-height: 18px;
      padding: 0 6px;
      text-align: center;
      white-space: nowrap;
      background: #fd4169;
      position: absolute;
      top: 10px;
      right: 0px;
    }
  }

  .el-popover.el-popper {
    min-width: 100px;
    text-align: center;
  }

  .portrait {
    float: right;
    padding: 10px 0 0 0;
    height: 50px;

    .el-image {
      box-shadow: 0 0 10px 2px #00000016;
      border-radius: 50%;
    }
  }
}

.avatar-pop {
  line-height: 40px;
  cursor: pointer;
  font-size: 14px;
}

.service-pop {
  .li {
    border-bottom: 1px solid #f2f2f2;
    padding: 10px 0;
    zoom: 1;
  }

  .li:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
  }

  .cell {
    color: #333;
    font-size: 13px;
  }

  .qq {
    color: #2052a4;
    font-size: 12px;
    font-weight: normal;
    border: 1px solid #5f9efc;
    border-radius: 4px;
    padding: 2px 4px;
    margin-left: 10px;
    float: right;
  }

  .qq .iconfont {
    color: #5f9efc;
    font-size: 12px;
    margin-right: 5px;
  }

  .qq:hover {
    text-decoration: none;
    color: #f60;
    border: 1px solid #f60;
  }

  .qq:hover .iconfont {
    color: #f60;
  }

  .date {
    color: #999999;
    padding: 10px 0 2px 0;
  }

  .shift {
    font-size: 12px;
    color: #999;
  }
}

.el-popover.el-popper {
  min-width: unset !important;

  .weixin-pop {
    p {
      font-size: 12px;
      color: #666;
    }
  }

  .app-pop {
    p.a {
      font-size: 13px;
      color: #666;
    }

    p.b {
      font-size: 13px;
      color: #333;
    }
  }
}

.header {
  box-shadow: 0 3px 6px #00000029;
  position: fixed;
  z-index: 99;
  top: 0;
}
</style>
