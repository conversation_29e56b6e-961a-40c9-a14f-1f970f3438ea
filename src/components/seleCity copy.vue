<template>
  <div class="pop-sele-city">
    <el-dialog
      v-model="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <template #title>
        {{ title }}
        <label>
          ( <span>{{ selectedCount }}</span
          >/{{maxCount}})</label
        ></template
      >
      <div class="selected-items clearfix">
        <div class="items">
          <span
            class="item"
            v-for="(p, index) in selectedArray"
            :key="p.keywordID"
            @click="onSelectindustryType(p, index)"
          >
            {{ p.keywordName }}
            <i>x</i>
          </span>
        </div>
        <el-button type="primary" round class="fr" @click="onConfirm"
          >确定</el-button
        >
      </div>
      <div class="sel-box clearfix">
        
        <div class="secondBox tBox fl">
          <ul>
            <li
              v-for="p in level1Data"
              :key="p.keywordID"
              @click="showChildrenPicker(p, this)"
              :class="p.open ? 'on' : ''"
            >
              <span>{{ p.keywordName }}</span>
              <span class="info" v-show="p.childrenSelectedCount"
                >（{{ p.childrenSelectedCount }}）</span
              >
            </li>
          </ul>
        </div>
        <div class="thirdBox tBox fl">
          <ul>
            <li
              v-for="p in level2Data"
              :key="p.keywordID"
              @click="onSelectindustryType(p)"
              :class="p.selected ? 'on' : ''"
            >
              <span>{{ p.otherName }}</span>
              <i v-if="p.selected">✔</i>
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  toRefs,
  onBeforeMount,
  onMounted,
  watch,
} from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { getCity } from "../http/dictionary";
export default defineComponent({
  emits: ["confirmCity"],
  props: {
    title: {
      type: String,
      default: "期望工作地",
    },
    maxCount: {
      type: Number,
      default: 1,
    },
    hideValue: {
      type: Array ||String|| Number,
      default: []||''||0,
    },
    value: {
      type: Boolean,
      default: false,
    },
    dialogVisible: {
      type: Boolean,
      default: true,
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const state: any = reactive({
      loading: false,
      list: [],
      clientHeight: "100%",
      valueItems: [],
      fixed: true,
      showPicker: false,
      level1Data: [],
      level2Data: [],
      popup: false,
      selectedArray: [],
      selectedCount: 0,
      singleMode: computed(() => {
        return props.maxCount <= 1;
      }),
      multipleMode: computed(() => {
        return props.maxCount > 1;
      }),
      values: computed(() => {
        if (Array.isArray(props.hideValue)) {
          return props.hideValue;
        }
        let arr = `${props.hideValue}`
          .split(",")
          .map((i) => parseInt(i))
          .filter((i) => !!i);
        return arr;
      }),
    });
    watch(
      () => state.selectedArray,
      () => {
        state.selectedCount = state.selectedArray.length;
      }
    ),
      onBeforeMount(() => {
        //籍贯---城市
        let result = store.state.cityList;
          if (result.length <= 0) {
               methods.getCity();
          }else{
              fun.assembleDATA(result)
          }

         
      });
    onMounted(() => {});
    const methods = {
      // 籍贯---城市
      async getCity() {
        let result: any | object = await getCity("");
          store.commit("setCityList", result.data);
        fun.assembleDATA(result.data);
      },
    };
    const handleClose = (done: any) => {
      // done()
      emit("confirmCity", "");
    };
    const fun = {
      assembleDATA(List: any){
            state.list = List.map((i: any) => {
            return {
              ...i,
              selected: false,
              otherName: i.keywordName, //别名，区别于全部选择这种需要另起他名的情况
              childrenSelectedCount: 0,
              open: false,
            };
          });

        state.level1Data = state.list.filter((i: any) => i.parentID == -1);

        state.level1Data[0].open = true;
        fun.showChildrenPicker(state.level1Data[0]);

        state.values.forEach((id: any, index: number) => {
          let item = state.list.find((p: any) => p.keywordID == id);
          if (!item) return;
          item.selected = true;
          item.childrenSelectedCount = 1;
          state.selectedArray.push(item);
          state.selectedCount = state.selectedArray.length;
          let parent = state.list.find(
            (i: any) => i.keywordID == item.parentID
          );
          fun.resetData(parent);
        });
      },
      showChildrenPicker(p: any) {
        if (!p) return;
        state.level1Data.forEach((i: any) => (i.open = false));
        p.open = true;
        p.otherName = "全部";
        let level2Data = [p];
        level2Data.push(
          ...state.list.filter((ps: any) => ps.parentID == p.keywordID)
        );
        state.level2Data = level2Data;
        this.resetData(p);
      },
      onSelectindustryType(item: object | any) {
        if (state.singleMode) {
          this.onSingleMode(item);
        } else {
          this.onMultipleMode(item);
        }
      },
      onSingleMode(item: object | any) {
        state.list.forEach((i: any) => {
          i.selected = false;
          i.childrenSelectedCount = 0;
        });
        item.selected = true;
        state.selectedArray = state.list.filter((i: any) => i.selected);
        this.onConfirm();
      },
      //多选
      onMultipleMode(item: object | any) {
        let isChecked = !item.selected;
        //选中的对象如果是已有选项的子节点，放弃
        let parent = state.level1Data.find(
          (i: any) => i.keywordID == item.parentID
        );
        //如果没选 ---那就选择
        if (isChecked) {
          if (parent) parent.selected = false; //取消父节点选中
          let children = state.selectedArray.filter(
            (i: any) => i.parentID == item.keywordID
          );
          children.forEach((i: any) => (i.selected = false)); //取消子节点选中
          state.selectedArray = state.list.filter((i: any) => i.selected);
          let Count=state.selectedArray.length;
          let remain = props.maxCount - Count;
          if (remain <= 0) {
            let mst = `最多只能选择${props.maxCount}个选项`;
            ElMessage({
              showClose: true,
              message: mst,
              type: "warning",
            });
          } else {
            item.selected = true;
            item.childrenSelectedCount = 1;
            this.resetData(parent);
          }
        } else {
          //取消---反选
          item.selected = false;
          item.childrenSelectedCount = 0;
          this.resetData(parent);
        }
        state.selectedArray = state.list.filter((i: any) => i.selected);
      },
      resetData(parent: any) {
        if (!parent) return;
        parent.childrenSelectedCount = this.sum(
          state.list.filter(
            (ps: any) =>
              ps.selected &&
              (ps.parentID == parent.keywordID ||
                ps.keywordID == parent.keywordID)
          )
        );

        let p = state.level1Data.find(
          (i: any) => i.keywordID == parent.parentID
        );
        if (p)
          p.childrenSelectedCount = this.sum(
            state.list.filter((i: any) => i.parentID == p.keywordID)
          );
      },
      sum(array: any) {
        let sum = 0;
        array.forEach((i: any) => (sum += i.childrenSelectedCount));
        return sum;
      },
      onConfirm() {
        if (state.selectedArray.length < 1) {
          ElMessage({
            showClose: true,
            message: "请至少选择一个",
            type: "warning",
          });
          return;
        }
         emit("confirmCity", state.selectedArray);
      },
    };
    return {
      handleClose,
      ...fun,
      ...toRefs(state),
    };
  },
});
</script>
<style lang="less">
.pop-sele-city {
  .el-dialog__header {
    border-bottom: 1px solid #f2f2f2;
    label.tit {
      font-size: 20px;
      color: #333;
    }
    .sele-box {
      width: 450px;
      margin-left: 20px;
      border-radius: 4px;
      .el-input__inner {
        width: 420px !important;
        background: #fafafa;
        color: #bbbbbb;
        font-size: 14px;
        border: none;
        border-radius: 4px;
      }
    }
    .iconfont {
      padding-left: 4px;
    }
    .el-icon-arrow-up {
      display: none;
    }
  }
  .el-dialog__body {
    padding: 0 0 0 0;
  }
  .el-dialog__headerbtn .el-dialog__close {
    font-size: 30px;
    color: #d4d4d4;
  }
  .sel-box {
    height: 500px;
    overflow: hidden;
    .tBox {
      width: 160px;
      height: 500px;
      position: relative;
      overflow: hidden;
      ul {
        position: absolute;
        left: 0;
        top: 0;
        right: -17px;
        bottom: 0;
        overflow-x: hidden;
        overflow-y: scroll;
      }
      li {
        width: 122px;
        font-size: 14px;
        color: #666;
        line-height: 24px;
        cursor: pointer;
      }
      li.on {
        color: #457ccf;
      }
    }
    .tBox ::-webkit-scrollbar {
      width: 0 !important;
    }
    .tBox {
      -ms-overflow-style: none;
    }
    .tBox {
      overflow: -moz-scrollbars-none;
    }
    .firstBox {
      background: #ecedf4;
      li {
        padding: 18px 15px 18px 24px;
        background: #ecedf4;
      }
      li.on {
        background: #f4f5f9;
      }
    }
    .secondBox {
      background: #f4f5f9;
      li {
        padding: 18px 15px 18px 24px;
        background: #f4f5f9;
      }
      li.on {
        background: #ffffff;
      }
    }
    .thirdBox {
      width: 480px;
      li {
        float: left;
        background: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 18px 5px 18px 24px;
      }
    }
  }
    .selected-items {
    border-bottom: 1px solid #eee;
    padding: 8px 20px;
    .el-button {
      margin-left: -100px;
      position: relative;
    }
  }
  .selected-items .info {
    color: #999;
    font-size: 12px;
    padding: 8px 0px;
  }
  .selected-items .items {
    display: flex;
    flex-flow: wrap;
    justify-content: flex-start;
    flex-direction: row;
    padding-right: 100px;

    float: left;
  }
  .selected-items .items .item {
    color: #3366cc;
    border: 1px solid #3366cc;
    border-radius: 30px;
    padding: 1px 17px;
    margin-right: 8px;
    display: flex;
    height: 35px;
    line-height: 35px;
    margin-bottom: 5px;
    font-size: 14px;
    i {
      font-style: unset;
      padding-left: 5px;
      font-size: 18px;
      cursor: pointer;
    }
  }
}
.sele-li {
  padding: 10px;
  height: auto;
  p {
    line-height: 16px;
  }
}
.bod {
  color: #457ccf;
  font-size: 14px;
}
.lit {
  color: #999;
  font-size: 12px;
}
</style>