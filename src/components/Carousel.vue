<template>
  <el-carousel v-if="state.list != ''" :class="props.type == 1 ? 'hengfu1' : 'hengfu2'"
    :direction="props.type == 1 ? 'vertical' : 'horizontal'" arrow="never">
    <el-carousel-item v-for="(item, index) in state.list" :key="index" @click="methods.countLogo(item.logoID, 0)">
      <!-- <a :href="item.linkUrl" target="_blank"> -->
      <a :href="methods.validateLink(item.linkUrl)" target="_blank">
        <el-image :src="item.logoSrc" fit="fill" alt=""></el-image>
      </a>
    </el-carousel-item>
  </el-carousel>
</template>
<script lang="ts">
import { defineComponent, reactive, onMounted, watch, ref } from "vue";
import { ClickCount } from "@/utils/utils";
export default defineComponent({
  props: ["list", "type"],
  setup(props) {
    const state = reactive({
      list: [],
    });
    onMounted(() => {
      state.list = props.list;
    });
    watch(
      () => props.list,
      (newValue, oldValue) => {
        state.list = newValue;
      }
    );
    const methods = {
      //广告点击统计
      async countLogo(logoID: number, from: number) {
        try {
          let form = {
            logoID: logoID,
            from: from
          };
          await ClickCount(form);
        } catch (error) {
          console.error('Error counting logo clicks:', error);
        }
      },
      validateLink(url: string) {
        const urlPattern = /^(http|https):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/i;
        if (!urlPattern.test(url)) {
          console.error(`Invalid URL: ${url}`);
          return null;
        }
        return url;
      }

    };
    return {
      state,
      props,
      methods,
    };
  },
});
</script>
<style lang="less">
.hengfu1 {
  height: 100px;
  overflow: hidden;
  margin-bottom: 12px;

  img {
    width: 100%;
    height: 100px;
  }

  .el-carousel__indicator--vertical {
    height: 3px;
    overflow: hidden;
  }
}

.hengfu2 {
  width: 195px;
  height: 180px;
  overflow: hidden;

  .el-carousel__container {
    height: 180px;
  }

  img {
    width: 100%;
    height: 180px;
  }

  .el-carousel__indicator {
    width: 3px;
    overflow: hidden;
  }
}
</style>
