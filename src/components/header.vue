<template>
  <el-header class="logo-common">
    <div class="w1200 clearfix">
      <Logo :bid="bid" />
      <span class="shu fl">|</span>
      <div class="title">{{ name }}</div>
      <div class="app-down" v-if="link">
        <a href="https://image.gxrc.com/gxrcsite/zt/MobileApp/index.htm" target="_blank">
          <i class="iconfont icon-phone9"></i>
          <span>手机客户端</span>
        </a>
      </div>
    </div>
  </el-header>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent } from "vue";
import Logo from "./logo.vue";
export default defineComponent({
  components: { Logo },
  props: {
    name: {
      type: String,
      default: "",
    },
    link: {
      type: Boolean,
      default: false,
    },
    bid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const state = reactive({});
    return { ...toRefs(state) };
  },
});
</script>
<style lang="less">
.logo-common {
  background: #fff;
  height: 80px;
  line-height: 80px;
  width: 100%;
  .logo {
    float: left;
    
  }
  .logo-img {
      // width: 150px;
      margin: 19px 15px 0 0;
      height: 55px;
    }
  .title {
    float: left;
    font-size: 20px;
    color: #333333;
    padding-left: 15px;
  }
  .app-down {
    float: right;
    a {
      color: #5f9efc;
      font-size: 16px;
    }
  }
  .shu {
    font-size: 14px;
    color: #dddddd;
  }
}
</style>