<template>
<el-drawer v-model="props.drawer.value" title="简历模板" :with-header="false" size="375px">
<div class="jlmb-box">
   <div class="banner">
    <el-image src="https://image.gxrc.com/gxrcsite/my/2021/mymall-template-banner.png" fit="fill"></el-image>
      </div>
    <ul class="list clearfix">
                <li><img src="https://image.gxrc.com/gxrcsite/my/2021/mymall-template-img1.png" /><p>蓝色简约</p><i></i></li>
                <li><img src="https://image.gxrc.com/gxrcsite/my/2021/mymall-template-img2.png" /><p>清新商务</p><i></i></li>
                <li><img src="https://image.gxrc.com/gxrcsite/my/2021/mymall-template-img3.png" /><p>经典简约</p><i></i></li>
                <li><img src="https://image.gxrc.com/gxrcsite/my/2021/mymall-template-img4.png" /><p>白色经典</p><i></i></li>
                <li><img src="https://image.gxrc.com/gxrcsite/my/2021/mymall-template-img5.png" /><p>简约商务</p><i></i></li>
                <li><img src="https://image.gxrc.com/gxrcsite/my/2021/mymall-template-img6.png" /><p>高级商务</p><i></i></li>
            </ul>
    <div class="pic">
        <el-image src="https://image.gxrc.com/gxrcsite/my/2021/mymall-template-ewm.png" fit="fill"></el-image>
        <p>APP内购买后查看</p>
      </div>
      </div>
  </el-drawer>
</template>

<script lang="ts">
import { defineComponent,reactive,onMounted} from 'vue'
  export default defineComponent({
      props:['drawer'],
    setup(props) {
      const state = reactive({
        url:``,
  })
  onMounted(() => {
   
})
   return {
     state,
     props
      }
    }
  })
</script>

<style lang="less">
.jlmb-box{height: 100%;
background: #4246E9;
    overflow-y: auto;
    .banner{img{width:100%;}}
    .list{padding-left: 20px;
        li{float: left;width:167px;height:200px;text-align:left;
        img{width:157px;height:150px;}
        p{color: #fff;font-size: 14px;}}
    }
    .pic{line-height: 0;text-align:center;padding:30px 0 60px;
    img{width:120px;margin-bottom: 20px;}
    p{width: 130px;color: #fff;font-size: 13px;margin: 0 auto;
height: 35px;line-height: 35px;
background: rgba(0, 0, 0, 0.33);
border-radius: 18px;}
    }
    }
    .el-drawer.rtl {overflow-y: auto;}
</style>


