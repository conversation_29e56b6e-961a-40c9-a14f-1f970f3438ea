<template>
  <el-row :gutter="12" class="articleList">
    <el-col :span="8" v-for="(o, index) in 3"  :key="o">
      <el-card shadow="hover">
        <div style="" class="img clearfix">
          <el-link :underline="false">
            <div class="tit">
              <p>接触和终止劳动合同补…</p>
              <span class="num">23456阅读</span>
            </div>
          </el-link>
        </div>
        <ul>
          <li>
            <el-link :underline="false">
              <el-tag>2</el-tag>
              <p>怎样向领导汇报工作？</p>
              <span class="num">12110阅读</span>
            </el-link>
          </li>
          <li>
            <el-link :underline="false">
              <el-tag>3</el-tag>
              <p>怎样向领导汇报工作？</p>
              <span class="num">12110阅读</span>
            </el-link>
          </li>
          <li>
            <el-link :underline="false">
              <el-tag class="dian">●</el-tag>
              <p>怎样向领导汇报工作？</p>
              <span class="num">12110阅读</span>
            </el-link>
          </li>
        </ul>
      </el-card>
    </el-col>
  </el-row>
</template>

<script lang="ts">
</script>

<style lang="less">
.articleList {
  .el-card__body {
    padding: 0 0;
  }
  .img a {
    display: block;
    height: 140px;
    background-size: 100%;
    // background: url(//logo.gxrc.com/Logo/81046651-e3d9-41c5-a87c-02c6a4e4fa4a/sh210907.gif) no-repeat center top;
    background: #000;
    .tit {
      color: #fff;
      font-size: 14px;
      padding: 100px 14px 0 14px;
      p {
        float: left;
      }
      span.num {
        float: right;
      }
    }
  }
  ul {
    padding: 14px;
    li a {
      padding: 5px 0;
      display: block;
      float: left;
      width: 100%;
    }
    .el-tag {
      float: left;
      height: 17px;
      width: 17px;
      color: #fff;
      background: #ec8e2f;
      line-height: 17px;
      text-align: center;
      padding: 0 0;
       border: none;
    }
    .dian {
      float: left;
      font-size: 12px;
      color: #b8defd;
      background: none;
     
    }
    p {
      float: left;
      font-size: 14px;
      color: #333;
      padding-left: 5px;
    }
    .num {
      float: right;
      font-size: 12px;
      color: #999;
    }
  }
}
</style>