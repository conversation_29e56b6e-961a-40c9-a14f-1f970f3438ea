<template>
  <div class="news-list-one">
    <div class="tit">
      <a :href="props.url" target="_blank">{{ props.title }}</a>
    </div>
    <div class="con">
      <div class="top">
          <a v-if="state.list && state.list.length > 0 && state.list[0].link" :href="state.list[0].link" target="_blank">
          <div class="pic">
            <el-image :src="state.list[0] && state.list[0].imgUrl" fit="fill">
              <template #error>
                <div class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </template>
            </el-image>
            <span class="yd"
              >{{ state.list[0] && state.list[0].count }}阅读</span
            >
          </div>
          <h3>{{ state.list[0] && state.list[0].title }}</h3>
        </a>
      </div>
      <ul>
        <li>
          <a :href="state.list[1] && state.list[1].link" target="_blank">
            <div class="info">
              <div class="title">
                {{ state.list[1] && state.list[1].title }}
              </div>
              <div class="qt">
                <div class="yd">
                  {{ state.list[1] && state.list[1].count }}阅读
                </div>
              </div>
            </div>
            <div class="pic">
              <el-image :src="state.list[1] && state.list[1].imgUrl" fit="fill">
                <template #error>
                  <div class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div> </template
              ></el-image>
            </div>
          </a>
        </li>
        <li>
          <a :href="state.list[2] && state.list[2].link" target="_blank">
            <div class="info">
              <div class="title">
                {{ state.list[2] && state.list[2].title }}
              </div>
              <div class="qt">
                <div class="yd">
                  {{ state.list[2] && state.list[2].count }}阅读
                </div>
              </div>
            </div>
            <div class="pic">
              <el-image :src="state.list[2] && state.list[2].imgUrl" fit="fill">
                <template #error>
                  <div class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div> </template
              ></el-image>
            </div>
          </a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, watch } from "vue";
export default defineComponent({
  props: ["title", "list", "url"],
  setup(props) {
    const state = reactive({
      list: [],
    });
    watch(
      () => props.list,
      (newValue, oldValue) => {
        state.list = newValue;
      }
    );
    return {
      state,
      props,
    };
  },
});
</script>

<style lang="less">
.news-list-one {
  float: left;
  width: 321px;
  background: #fff;
  padding: 20px 20px 10px;
  height: 522px;
  overflow: hidden;
  box-sizing: border-box;
  border-radius: 4px;
  border: 1px solid #f2f2f2;
  .tit {
    height: 50px;
    line-height: 40px;
    border-bottom: 1px solid #f2f2f2;
    font-size: 22px;
  }
  .con {
    padding-top: 20px;
    .image-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      color: var(--el-text-color-secondary);
      font-size: 30px;
    }
    .top {
      padding-bottom: 30px;
      .pic {
        position: relative;
        .el-image,
        img {
          width: 100%;
          height: 130px;
          border-radius: 4px;
        }
        .yd {
          font-size: 14px;
          height: 40px;
          line-height: 40px;
          padding: 0 10px;
          color: #fff;
          position: absolute;
          left: 0;
          bottom: 0;
        }
      }
      h3 {
        font-size: 18px;
        padding-top: 10px;
      }
    }
    ul {
      li {
        padding-bottom: 20px;
        a {
          display: flex;
          .info {
            flex: 4;
            .title {
              height: 60px;
              line-height: 20px;
              font-size: 14px;
              font-weight: bold;
            }
            .qt {
              display: flex;
              .yd {
                flex: 1;
                color: #999;
                font-size: 12px;
              }
            }
          }
          .pic {
            flex: 1;
            padding-left: 10px;
            .el-image,
            img {
              width: 114px;
              height: 86px;
              border-radius: 2px;
            }
            .image-slot {
              font-size: 20px;
            }
          }
        }
      }
    }
  }
}
</style>


