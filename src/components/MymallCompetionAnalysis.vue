<template>
  <el-drawer
    v-model="props.drawer.value"
    title="竞争力分析"
    :with-header="false"
    size="375px"
    @opened="methods.opened"
  >
    <div class="jzlfx-box">
      <div class="pic" v-if="state.guid == 0">
        <el-image
          src="https://image.gxrc.com/gxrcsite/my/2021/mymall-jzlfx-img1.png?v=3"
          fit="fill"
        ></el-image>
      </div>
      <div v-if="state.guid == 0" class="info">
        <el-image
          src="https://image.gxrc.com/gxrcsite/my/2021/mymall-jzlfx-img3.png?v=3"
          fit="fill"
        ></el-image>
      </div>
      <div v-else class="info" v-loading="state.loading">
        <iframe
          ref="caIframe"
          class="mymall"
          :style="{ height: winHeight }"
          :src="state.url"
          frameborder="0"
          :scrolling="state.guid == 0?'no':'yes'"
          allowTransparency="true"
          sandbox="allow-scripts allow-same-origin"
        ></iframe>
        <a class="shelter" :href="`/resume/${state.resumeId}`" target="_blank"></a>
      </div>
      <div class="pic" v-if="state.guid == 0">
        <el-image
          src="https://image.gxrc.com/gxrcsite/my/2021/mymall-jzlfx-img2.png?v=3"
          fit="fill"
        ></el-image>
      </div>
      
    </div>
  </el-drawer>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  onMounted,
  watch,
  computed,
  ref,
  getCurrentInstance,
} from "vue";
export default defineComponent({
  props: ["drawer", "positionGuid","resumeId"],
  setup(props) {
    const state = reactive({
      url: ``,
      guidTemp: 0,
      guid: 0,
      loading: false,
      first: true,
      resumeId:0
    });
    const caIframe = ref(null);
    onMounted(() => {
      state.guid = props.positionGuid;
      state.resumeId = props.resumeId?props.resumeId:0;
    });

    watch(
      () => props.positionGuid,
      (newValue, oldValue) => {
        state.guid = newValue;
      }
    );
    watch(
      () => props.resumeId,
      (newValue, oldValue) => {
        state.resumeId = newValue?newValue:0;
      }
    );
    const winHeight = computed(() => {
      if (props.positionGuid == 0) {
        return "432px";
      } else {
        return window.innerHeight + "px";
      }
    });
    const methods = {
      opened() {
        if (state.guid != 0) {
          const iframe = caIframe.value;
          let host = "";
          if (location.href.indexOf(".tgxrc") > -1) {
            host = "http://m2.tgxrc.com";
          } else {
            host = "https://mymall.gxrc.com";
          }
          state.url = `${host}/MyMall/CompetionAnalysis/competionanlsrslt?positionguid=${state.guid}&onlybody=1&appbuy=1`;
          if (iframe && state.first == true) {
            state.loading = true;
            if (iframe.attachEvent) {
              iframe.attachEvent("onload", function () {
                state.loading = false;
                state.first = false;
              });
            } else {
              iframe.onload = function () {
                state.loading = false;
                state.first = false;
              };
            }
          }
        }
      },
    };

    return {
      state,
      props,
      methods,
      caIframe,
      winHeight
    };
  },
});
</script>

<style lang="less">
.jzlfx-box {
  height: 100%;
  background: #5c6cff;
  overflow-y: auto;
  .pic {
    line-height: 0;
    img {
      width: 100%;
    }
  }
  
  .info {
    line-height: 0;
    overflow: hidden;
    position: relative;
    .el-image {
      padding: 20px 0 10px;
      img {
        width: 100%;
      }
    }
    .mymall {
      width: 100%;
    }
    .shelter{
    display: block;
    height: 100px;
    width: 375px;
    position: absolute;
    bottom: 0;
    z-index: 99;
  }
  }
}
.el-drawer.rtl {
  overflow-y: auto;
}
.el-drawer__body{
  padding: 0;
}
</style>


