<template>
  <div class="pop-sele-major">
    <el-dialog
      v-model="dialogVisible"
      title="Tips"
      width="800px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <template #title>
        <label class="tit">选择专业类别</label>
        <el-select
          v-model="majorName"
          filterable
          placeholder="搜索专业类别"
          :loading="loading"
          class="sele-box"
          :remote-method="majorNameNameText"
          remote
        >
        <template #prefix>
          <i class="iconfont icon-search1"></i>
        </template>
          <el-option
            v-for="(item, index) in majorList"
            :key="index"
            :value="item.name"
            @click="ChildrenPicker3(item)"
            class="sele-li"
          >
            <p class="bod">{{ item.name }}</p>
            <p class="lit">{{ item.fullname }}</p>
          </el-option>
        </el-select>
      </template>
      <div class="sel-box clearfix">
        <div class="firstBox tBox fl">
          <ul>
            <li
              v-for="p in level1Data"
              :key="p.id"
              @click="ChildrenPicker1(p, this)"
              :class="p.open ? 'on' : ''"
            >
              <span>{{ p.name }}</span>
            </li>
          </ul>
        </div>
        <div class="secondBox tBox fl">
          <ul>
            <li
              v-for="p in level2Data"
              :key="p.id"
              @click="ChildrenPicker2(p)"
              :class="p.open ? 'on' : ''"
            >
              <span>{{ p.name }}</span>
            </li>
          </ul>
        </div>
        <div class="thirdBox tBox fl">
          <ul>
            <li
              v-for="p in level3Data"
              :key="p.id"
              @click="ChildrenPicker3(p)"
              :class="{ on: p.selected }"
            >
              <span>{{ p.name }}</span>
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  toRefs,
  ref,
  onBeforeMount,
  onMounted,
} from "vue";
import { useStore } from "vuex";
import { getMajorResource } from "../http/dictionary";
import { searchmajor } from "../http/searchAPI";
export default defineComponent({
  emits: ["confirmSpeciality"],
  props: {
    hideValue: {
      type: Number,
      default: true,
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const loading = ref(false);
    const state: any = reactive({
      list: [],
      level1Data: [],
      level2Data: [],
      level3Data: [],
      dialogVisible: true,
      majorName: "", //搜索
      majorList: [], //搜索到的列表

    });
    onBeforeMount(() => {
      //从store中获取专业字典
      let result = store.state.major;
      if (result.length <= 0) {
        methods.getData();
      } else {
        fun.assembleDATA(result);
      }
    });
    onMounted(() => {});
    const methods = {
      async getData() {
        let data: any = await getMajorResource("");
        store.commit("getMajorResource", data.data);
        fun.assembleDATA(data.data);
      },
      // 专业--搜索
      async getMajorNameText(text: any) {
        let data: any = await searchmajor(text);
          state.majorList = data.data;
      },
    };
    const fun = {
      ChildrenPicker1(p: any) {
        state.level1Data.forEach((i: any) => (i.open = false));
        p.open = true;
        state.level2Data = state.list.filter((ps: any) => ps.pid == p.id);
        state.level2Data.forEach((i: any) => (i.open = false));
        state.level2Data[0].open = true;
        state.level3Data = state.list.filter(
          (ps: any) => ps.pid == state.level2Data[0].id
        );
      },
      ChildrenPicker2(p: any) {
        if (!p) return;
        state.level2Data.forEach((i: any) => (i.open = false));
        p.open = true;
        state.level3Data = state.list.filter((ps: any) => ps.pid == p.id);
      },
      ChildrenPicker3(p: any) {
        state.list.forEach((i: any) => (i.selected = false));
        p.selected = true;
         emit("confirmSpeciality", p);
      },
      assembleDATA(List: any) {
        state.list = computed(() => {
          let arr = [];
          arr = List.map((i: any) => {
            return {
              ...i,
              selected: false,
              open: false,
            };
          });
          return arr;
        });
        state.list.forEach((i: any) => ( i.id== props.hideValue?i.selected = true:''));
        state.level1Data = state.list.filter((i: any) => i.pid == 0);
        fun.ChildrenPicker1(state.level1Data[0]);
      },
      handleClose(done: any) {
        // done()
        emit("confirmSpeciality", "");
      },
      //搜索
      // majorNameNameText(val: any) {
      //   methods.getMajorNameText(val.data);
      // },
       majorNameNameText(query: string) {
        if (query) {
          setTimeout(() => {
            methods.getMajorNameText(query);
          }, 200)
        } else {
        }
      },

    };
    return {
      loading,
      ...toRefs(state),
      ...fun,
    };
  },
});
</script>
<style lang="less">
.pop-sele-major {
  .el-dialog__header {
    border-bottom: 1px solid #F2F2F2;
    label.tit {
      font-size: 20px;
      color: #333;

    }
    .sele-box {
      width: 450px;
      margin-left: 20px;
      border-radius: 4px;
      .el-input__inner{
        background: #FAFAFA;
    color: #BBBBBB;
    font-size: 14px;
    border: none;
    border-radius: 4px;
      }
    }
    .iconfont{
      padding-left: 4px;
    }
    .el-icon-arrow-up{
      display: none;
    }
  }
  .el-dialog__body {
    padding: 0 0 0 0;
  }
  .el-dialog__headerbtn .el-dialog__close {
    font-size: 30px;
    color: #d4d4d4;
  }
  .sel-box {
    height: 500px;
    overflow: hidden;
    .tBox {
      width: 160px;
      height: 500px;
      position: relative;
      overflow: hidden;
      ul {
        position: absolute;
        left: 0;
        top: 0;
        right: -17px;
        bottom: 0;
        overflow-x: hidden;
        overflow-y: scroll;
      }
      li {
        width: 132px;
        font-size: 14px;
        color: #666;
        height: 55px;
        line-height: 55px;
        padding: 0px 5px 0 24px;
        cursor: pointer;
      }
      li.on {
        color: #457ccf;
      }
    }
    .tBox ::-webkit-scrollbar {
      width: 0 !important;
    }
    .tBox {
      -ms-overflow-style: none;
    }
    .tBox {
      overflow: -moz-scrollbars-none;
    }
    .firstBox {
      background: #ecedf4;
      li {
        background: #ecedf4;
      }
      li.on {
        background: #f4f5f9;
      }
    }
    .secondBox {
      background: #f4f5f9;
      li {
        background: #f4f5f9;
      }
      li.on {
        background: #ffffff;
      }
    }
    .thirdBox {
      width: 480px;
      li {
        float: left;
        background: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
.sele-li {
  padding: 10px;
  height: auto;
  p {
    line-height: 16px;
  }
}
.bod {
  color: #457ccf;
  font-size: 14px;
}
.lit {
  color: #999;
  font-size: 12px;
}
</style>
