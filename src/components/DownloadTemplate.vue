<template>
    <div class="download-pop">
        <el-dialog
            v-model="dialogVisible"
            title="请选择您要保存的文件格式"
            width="412px"
            center
            :before-close="handleClose"
            :close-on-click-modal="false"
        >
            <div class="down-btn clearfix">
                <div :class="['box', { sel: type == 1 }]" @click="DownloadResume(1)">
                    <el-image
                        style="width: 32px; height: 39px"
                        src="https://image.gxrc.com/app/resume/doc.png"
                        fit="cover"
                    ></el-image>
                    
                    <div>doc格式</div>
                </div>
                <!-- <div :class="['box fr', { sel: type == 2 }]" @click="DownloadResume(2)">
                    <el-image
                        style="width: 32px; height: 39px"
                        src="https://image.gxrc.com/app/resume/pdf.png"
                        fit="cover"
                    ></el-image>
                    <div style="hi">pdf格式</div>
                </div> -->
            </div>
            <!-- 大数据哪个 都注释掉的代码了，还要修复，原先是是用v-html 的 -->
            <!-- <div v-dom="btnHtml"></div> -->  
            <template #footer>
                <el-button class="dialog-footer" type="primary" round @click="showMould">
                    选择更多简历模板
                    <el-icon class="el-icon--right">
                        <i class="iconfont icon-arrowRight13"></i>
                    </el-icon>
                </el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, ref } from "vue";
import { ResumeDoc, ResumePdf } from "@/http/resumeApi";
import {
    ElMessage,
    ElLoading
} from 'element-plus'
export default defineComponent({
    emits: ["ClosePop"],
    props: {
        resumeid: {
            type: Number,
            default: 0,
        },
        dialogVisible: {
            type: Boolean,
            default: true,
        },
    resumeName: {
      type: String,
      default: '简历名称'
    }
    },
    setup(props: any, { emit }) {
        const state = reactive({
            type: 0,
            // btnHtml: "",
            centerDialogVisible: ref(true),
        });
        let loadingInstance: any;
        function Export(blob: any, fileName: any) {
            if ("download" in document.createElement("a")) {
                // 非IE下载
                var elink = document.createElement("a");
                elink.setAttribute("download", fileName);
                elink.style.display = "none";
                elink.href = window.URL.createObjectURL(blob);
                document.body.appendChild(elink);
                elink.click();
                window.URL.revokeObjectURL(elink.href); // 释放URL 对象
                document.body.removeChild(elink);
            } else {
                // IE10+下载
                // 　　　　navigator.msSaveBlob(blob, fileName);
            }
        }
        const methods = {
            //下载·························································下载
            async Download(type: number) {
                let form = {
                    "resumeId": props.resumeid
                };
                loadingInstance = ElLoading.service({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    target: '.resume_comment',
                    background: 'rgba(0, 0, 0, 0.5)'
                });
                let name;
                let res;
                if (type == 1) {
                    name = props.resumeName + "的简历.doc"
                    res = await ResumeDoc(form);
                } else {
                    name = props.resumeName + "的简历.pdf"
                    res = await ResumePdf(form);
                }

                loadingInstance.close()
                Export(res, name);
            },
        };
        const fun = {
            DownloadResume(type: number) {
                state.type = type;
                methods.Download(type);
            },
            showMould() {
                emit("ClosePop", 2);
            },
            handleClose() {
                emit("ClosePop", 1);
            }
        };

        return { ...toRefs(state), ...fun };
    },
});
</script>
<style lang="less">
.download-pop {
    .el-dialog__title {
        font-size: 16px;
        color: #333;
    }
    .box {
        width: 160px;
        height: 100px;
        border: 1px solid #f3f2f2;
        text-align: center;
        border-radius: 4px;
        margin: auto;
        cursor: pointer;
        .el-image {
            padding: 16px 0 0 0;
        }
        p.geshi {
            font-size: 16px;
            color: #3333;
        }
    }
    p{
        line-height: 20px;
        height: 20px;
    }
    .sel {
        border: 1px solid #457ccf;
        background: #f5faff;
    }
    .el-dialog__footer {
        height: 67px;
        background: #2b7cff url("@/assets/img/jlmb_bg.png") no-repeat top;
        background-size: 100%;
        padding: 0 0 0 0;
    }
    .dialog-footer {
        width: 160px;
        height: 35px;
        padding: 0 0;
        margin: 17px 0 0 210px;
        i {
            color: #fff;
        }
        span {
            color: #fff;
            font-size: 13px;
        }
    }
    .el-dialog__headerbtn {
        top: 10px;
        right: 10px;
        i.el-dialog__close {
            font-size: 20px;
        }
    }
    .el-dialog__header{
        padding: 0px 0 0px 0;
        height: 60px;
        line-height: 60px;
    }
}
</style>