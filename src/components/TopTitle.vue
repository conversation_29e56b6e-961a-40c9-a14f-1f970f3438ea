<template>
  <div class="top-title">
    <slot></slot>
  </div>
</template>

<script  lang="ts">
import { defineComponent } from "vue";
export default defineComponent({
  setup() {
    return {};
  },
});
</script>

<style lang="less">
.top-title {
  display: flex;
  height: 50px;
  line-height: 50px;
  padding: 0 15px 0 0;
  margin-bottom: 10px;
  background: #ffffff;
  color: #999999;
  font-size: 14px;
  .top-l {
      font-size: 18px;
      color: #999999;
      padding:0 20px;
      font-weight: bold;
      cursor: pointer;
    .zs {
      color: #999999;
      font-size: 16px;
      padding-left: 5px;
    }
  }
  .pitch-on{
    color: #333333;
    border-bottom: 2px solid #457CCF;
    .zs {
      color: #457ccf;
      font-size: 16px;
    }
  }
  .top-r {
    flex: 1;
    text-align: right;
  }
  .delete {
    cursor: pointer;
  }
  .delete:hover {
    color: #fc5c5b;
  }
}
</style>


