<template>
  <div class="foot-all w1200">
    <div class="copyright clearfix">
      <div class="sdl">
        <p v-html="info.jobseekerFooterText"></p>
        <!-- <p>企业服务热线、服务监督热线：400-0771-056</p>
        <p>广西人才网（www.gxrc.com）— 中国广西人才市场主办</p>
        <p>
          本网站之所有招聘信息、作品、版权均归广西人才网科技有限公司所有，未经书面授权不得转载。
        </p> -->
      </div>
      <!-- <div class="sdr">
        <div class="security-wrap">
          <a
            href="http://si.trustutn.org/info?sn=253161219026245481869&amp;certType=1"
            target="_blank"
            class="shiming"
            ><img
              src="https://image.gxrc.com/gxrcsite/global/renZhengShiMing.png"
              alt="实名认证"
              title="实名认证"
          /></a>
        </div>
      </div> -->
    </div>
    <div class="site-nav">
      <div class="w1200">
        <a
          href="//image.gxrc.com/gxrcsite/zt/MobileApp/index.htm"
          target="_blank"
          >手机版</a
        ><el-divider direction="vertical"></el-divider
        ><a href="//www.gxrc.com/About/Gxrcsc" target="_blank"
          >中国广西人才市场</a
        ><el-divider direction="vertical"></el-divider
        ><a href="//www.gxrc.com/About/Gxrcw" target="_blank">广西人才网</a
        ><el-divider direction="vertical"></el-divider
        ><a href="//www.gxrc.com/About/Index" target="_blank">企业服务</a
        ><el-divider direction="vertical"></el-divider
        ><a href="//www.gxrc.com/About/JobSeekerService" target="_blank"
          >人才服务</a
        ><el-divider direction="vertical"></el-divider
        ><a href="//www.gxrc.com/About/Legal" target="_blank">法律声明</a
        ><el-divider direction="vertical"></el-divider
        ><a href="//www.gxrc.com/About/Bank" target="_blank" class="bank"
          >银行账户</a
        ><el-divider direction="vertical"></el-divider
        ><a href="//www.gxrc.com/About/SiteMap" target="_blank">网站导航</a
        ><el-divider direction="vertical"></el-divider
        ><a href="//my.gxrc.com/Qa" target="_blank">用户意见</a
        ><el-divider direction="vertical"></el-divider
        ><a href="//www.gxrc.com/About/FriendsLink" target="_blank">友情链接</a>
      </div>
    </div>
  </div>
</template>


<script lang="ts">
import {
  defineComponent,
  onBeforeMount,
  computed,
  watch,
  reactive,
  toRefs,
} from "vue";
import { GetWlxtSettingByDistrictId } from "@/http/api";
import { useStore } from "vuex";
import { getCookies } from "../utils/common";
export default defineComponent({
  setup(props: any, { emit }: any) {
    const store = useStore();
    const state = reactive({
      info: "",
    });
    onBeforeMount(() => {
      let bid = getCookies("bid");
      methods.getData(bid);
    });

    watch(
      () => store.state.logoPagBid,
      (newValue, oldValue) => {
        methods.getData(newValue);
      }
    );

    const logoPagBid = computed(() => {
      return store.state.logoPagBid;
    });

    const methods = {
      async getData(bid: string) {
        let res: any = await GetWlxtSettingByDistrictId({ DistrictID: bid });
        if (res.code == 1) {
          store.commit("setDomainUrl", res.data.defaultPageUrl);   
          store.commit("setLogoUrl", res.data.logoUrl);
          state.info = res.data;
        }
      },
    };
    return { logoPagBid, ...toRefs(state) };
  },
});
</script>

<style lang="less">
.el-footer {
  height: auto;
}
.foot-all {
  background: #fff;

  .copyright {
    padding: 30px 0;
    p {
      color: #bbb;
      font-size: 12px;
      line-height: 18px;
      text-align: left;
    }
    .sdl {
      position: relative;
      .security-wrap{
        position: absolute;
        right: 0;
        top: 0;
      }
    }

    // .sdr {
    //   float: right;
    //   margin-left: -110px;
    //   position: relative;
    // }
    border-bottom: 1px solid #f2f2f2;
  }

  .site-nav {
    padding: 20px 0;
    a {
      font-size: 12px;
      color: #666;
    }
    .el-divider--vertical {
      margin: 0 15px;
      height: 8px;
    }
  }
}
</style>