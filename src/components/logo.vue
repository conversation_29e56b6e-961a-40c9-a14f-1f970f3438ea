<template>
  <div class="fl">
    <a :href="mainURl" title="返回首页">
      <img :src="logoImg" class="logo-img" />
    </a>
  </div>
</template>
<script lang="ts">
import { reactive, toRefs, defineComponent, onMounted,watch } from "vue";
import { useStore } from "vuex";
export default defineComponent({
  props: {
    bid: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }) {
    const store = useStore();
    const state = reactive({
      mainURl: "https://www.gxrc.com/",
      logoImg: "//image.gxrc.com/gxrcsite/ds/logo/logo.png",
    });
    watch(
      () => store.state.domainUrl,
      (newValue, oldValue) => {
        state.mainURl=newValue
      }
    );
    watch(
      () => store.state.logoUrl,
      (newValue, oldValue) => {
        state.logoImg=newValue
      }
    );
    onMounted(async () => {
      let urlLogn = "https://image.gxrc.com/gxrcsite/ds/logo/";
      if (props.bid == 1) {
        state.mainURl = "https://gl.gxrc.com/";
        state.logoImg = urlLogn + "logo_gl.png";
      }
      if (props.bid == 2) {
        state.mainURl = "https://lz.gxrc.com/";
        state.logoImg = urlLogn + "logo_lz.png";
      }

      if (props.bid == 4) {
        state.mainURl = "https://wz.gxrc.com/";
        state.logoImg = urlLogn + "logo_wz.png";
      }
      if (props.bid == 5) {
        state.mainURl = "https://gp.gxrc.com/";
        state.logoImg = urlLogn + "logo_gp.png";
      }
      if (props.bid == 6) {
        state.mainURl = "https://bs.gxrc.com/";
        state.logoImg = urlLogn + "logo_bs.png";
      }
      if (props.bid == 7) {
        state.mainURl = "https://qz.gxrc.com/";
        state.logoImg = urlLogn + "logo_qz.png";
      }
      if (props.bid == 8) {
        state.mainURl = "https://hc.gxrc.com/";
        state.logoImg = urlLogn + "logo_hc.png";
      }
      if (props.bid == 9) {
        state.mainURl = "https://bh.gxrc.com/";
        state.logoImg = urlLogn + "logo_bh.png";
      }
      if (props.bid == 11) {
        state.mainURl = "https://fcg.gxrc.com/";
        state.logoImg = urlLogn + "logo_fcg.png";
      }
      if (props.bid == 12) {
        state.mainURl = "https://yl.gxrc.com/";
        state.logoImg = urlLogn + "logo_yl.png";
      }
      if (props.bid == 13) {
        state.mainURl = "https://cz.gxrc.com/";
        state.logoImg = urlLogn + "logo_cz.png";
      }
      if (props.bid == 14) {
        state.mainURl = "https://gg.gxrc.com/";
        state.logoImg = urlLogn + "logo_gg.png";
      }
      if (props.bid == 15) {
        state.mainURl = "https://lb.gxrc.com/";
        state.logoImg = urlLogn + "logo_lb.png";
      }
      if (props.bid == 18) {
        state.mainURl = "https://hz.gxrc.com/";
        state.logoImg = urlLogn + "logo_hz.png";
      }
      if (props.bid == 20) {
        state.mainURl = "https://pn.gxrc.com/";
        state.logoImg = urlLogn + "logo_pn.png";
      }
    });
    return { ...toRefs(state) };
  },
});
</script>
<style lang="less">
</style>