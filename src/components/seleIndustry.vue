<template>
  <div class="pop-sele-Industry">
    <el-dialog
      v-model="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <template #title>
        <div class="tit">
          选择行业
          <label>
            ( <span>{{ selectedCount }}</span
            >/{{ maxCount }})</label
          >
        </div>
        <el-select
          v-model="value"
          filterable
          placeholder="搜索行业"
          :loading="loading"
          class="sele-box"
          :remote-method="searchIndustryType"
          remote
        >
          <template #prefix>
            <i class="iconfont icon-search1"></i>
          </template>
          <el-option
            v-for="(item, index) in IndustryTypeList"
            :key="index"
            @click="handleSelect(item)"
            class="sele-li"
            :value="item.name"
          >
            <p class="bod">{{ item.name }}</p>
            <p class="lit">{{ item.fullname }}</p>
          </el-option>
        </el-select>
      </template>
      <div class="selected-items clearfix">
        <div class="items">
          <span
            class="item"
            v-for="(p, index) in selectedArray"
            :key="index"
            @click="onSelectindustryType(p)"
          >
            {{ p.keywordName }}
            <i>x</i>
          </span>
        </div>
        <el-button type="primary" round class="fr" @click="onConfirm"
          >确定</el-button
        >
      </div>
      <div class="youWant" v-if="showWant">
        <div class="sdl">您可能想选：</div>
        <div class="sdr">
          <ul class="clearfix">
            <li
              :class="{ selected: p.selected }"
              v-for="(p, ing) in wantArr"
              :key="ing"
              @click="onSelectWant(p)"
            >
              {{ p.keywordName }}
            </li>
          </ul>
        </div>
        <div class="close">
          <i class="el-icon-close" @click="showWant = false"></i>
        </div>
      </div>
      <div class="sel-box clearfix">
        <div class="firstBox tBox fl">
          <ul>
            <li
              v-for="p in level1Data"
              :key="p.keywordID"
              @click="ChildrenPicker1(p, this)"
              :class="p.open ? 'on' : ''"
            >
              <span :title="p.keywordName">{{ p.keywordName }}</span>
            </li>
          </ul>
        </div>
        <div class="thirdBox tBox fl">
          <ul>
            <li
              v-for="p in level2Data"
              :key="p.keywordID"
              @click="ChildrenPicker3(p)"
              :class="{ on: p.selected }"
            >
              <span :title="p.keywordName"
                >{{ p.otherName || p.keywordName }}
              </span>
              <i v-if="p.selected">✔</i>
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  toRefs,
  ref,
  onBeforeMount,
  onMounted,
  watch,
} from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { getIndustry } from "../http/dictionary";
import { searchsecondindustry } from "../http/searchAPI";
import { recommendindustrydata } from "@/http/resumeApi";
export default defineComponent({
  emits: ["confirm"],
  props: {
    // id数组
    hideValue: {
      type: Array || String || Number,
      default: [] || "" || 0,
    },
    maxCount: {
      type: Number,
      default: 3,
    },
    positionId: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, { emit }: any) {
    const store = useStore();
    const state: any = reactive({
      list: [],
      level1Data: [],
      level2Data: [],
      dialogVisible: true,
      IndustryTypeName: "", //搜索
      IndustryTypeList: [], //搜索到的列表
      selectedCount: 0 as any,
      selectedArray: [] as any,
      loading: false,
      wantArr: [], //推荐的职位
      showWant: true,
      values: computed(() => {
        //重新组装 id
        if (Array.isArray(props.hideValue)) {
          return props.hideValue;
        }
        let arr = `${props.hideValue}`
          .split(",")
          .map((i) => parseInt(i))
          .filter((i) => !!i);
        return arr;
      }),
    });
    onBeforeMount(() => {
      //从store中获取专业字典
      // store.dispatch("getIndustryTypesResource");
      let result = store.state.industry;
      if (result.length <= 0) {
        methods.getData();
      } else {
        fun.assembleDATA(result);
      }
      if (props.positionId) {
        methods.getwantArr(props.positionId);
        state.showWant = true;
      } else {
        state.showWant = false;
      }
    });
    const value = ref<string[]>([]);
    const methods = {
      async getData() {
        let data: any = await getIndustry("");
        store.commit("setIndustryResource", data.data);
        fun.assembleDATA(data.data);
      },
      // 搜索
      async getIndustryTypeName(text: any) {
        let data: any = await searchsecondindustry(text);
        state.IndustryTypeList = data.data;
      },
      async getwantArr(id: number) {
        let data: any = await recommendindustrydata(id, true, "");
        if (data.code == 1) {
          state.wantArr = data.data.map((i: any) => {
            return {
              ...i,
              selected: false,
            };
          });
          state.values.forEach((id: any, index: number) => {
            let item = state.wantArr.find((p: any) => p.keywordID == id);
            if (!item) return;
            item.selected = true;
          });
        }
      },
    };
    const fun = {
      ChildrenPicker1(p: any) {
        if (p.open) {
          return false;
        }
        state.level1Data.forEach((i: any) => (i.open = false));
        p.open = true;
        state.level2Data = state.list.filter(
          (ps: any) => ps.parentID == p.keywordID
        );
        state.level2Data.forEach((i: any) => (i.open = false));
      },
      ChildrenPicker3(p: any) {
        if (!p) {
          return false;
        }
        if (p.selected) {
          // 取消
          fun.onSelectindustryType(p);
        } else {
          //选择
          this.onMultipleMode(p);
        }
      },
      //选择
      onMultipleMode(p: any) {
        if (state.selectedCount >= props.maxCount && p.keywordID != 10472) {
          let msg = "最多只能选择" + props.maxCount + "个选项";
          ElMessage({
            message: msg,
            type: "warning",
          });
          return false;
        }
        const item = state.selectedArray.find(
          (item: any) => item.keywordID == 10472
        );
        if (item && p.keywordID != 10472) {
          fun.onSelectindustryType(item);
        }
        p.selected = true;

        state.list.forEach((i: any) => {
          if (p.keywordID == 10472) {
            i.selected = false;
          }
          i.keywordID == p.keywordID ? (i.selected = true) : "";
        });
        state.wantArr.forEach((i: any) => {
          if (p.keywordID == 10472) {
            i.selected = false;
          }
          i.keywordID == p.keywordID ? (i.selected = true) : "";
        });
        fun.selectedArrays();
        fun.Count();
        //重新加载选好的选项
      },
      handleSelect(p: any) {
        let item = {
          keywordID: p.id,
          keywordName: p.name,
        };
        this.ChildrenPicker3(item);
      },
      assembleDATA(List: any) {
        state.list = computed(() => {
          let arr = [];
          arr = List.map((i: any) => {
            return {
              ...i,
              selected: false,
              otherName: i.keywordName, //别名，区别于全部选择这种需要另起他名的情况
              open: false,
            };
          });
          return arr;
        });
        state.values.forEach((id: any, index: number) => {
          let item = state.list.find((p: any) => p.keywordID == id);
          if (!item) return;
          item.selected = true;
          state.selectedArray.push(item);
        });
        fun.Count();
        state.level1Data = state.list.filter((i: any) => i.parentID == 0);
        fun.ChildrenPicker1(state.level1Data[0]);
      },
      handleClose(done: any) {
        // done()
        emit("confirm", "");
      },
      //搜索
      searchIndustryType(query: string) {
        if (query) {
          state.loading = true;
          setTimeout(() => {
            methods.getIndustryTypeName(query);
            state.loading = false;
          }, 200);
        } else {
        }
      },
      //删除选择的数组
      onSelectindustryType(p: any) {
        state.list.forEach((i: any) =>
          i.keywordID == p.keywordID ? (i.selected = false) : ""
        );
        state.wantArr.forEach((i: any) =>
          i.keywordID == p.keywordID ? (i.selected = false) : ""
        );
        fun.selectedArrays();
        fun.Count();
      },
      //点击了确定---向父级传值
      onConfirm() {
        if (state.selectedArray.length < 1) {
          ElMessage({
            message: "至少选择一个",
            type: "warning",
          });
          return;
        }
        emit("confirm", state.selectedArray);
      },
      Count() {
        state.selectedCount = computed(() => {
          return state.selectedArray.length;
        });
      },
      // changeList(p: any) {
      //   // state.list.forEach((i: any) => ( i.keywordID== p.keywordID,i.selected = true?false:true));
      //   state.list.forEach((i: any) => {
      //     if (i.keywordID == p.keywordID) {
      //       i.selected = true ? false : true;
      //     }
      //   });
      // },
      selectedArrays() {
        state.selectedArray = state.list.filter(
          (ps: any) => ps.selected == true
        );
      },
      onSelectWant(p: any) {
        if (p.selected) {
          p.selected = false;
          let industryType = state.list.find(
            (i: any) => i.keywordID == p.keywordID
          );
          if (industryType) industryType.selected = false;
        } else {
          let remain = props.maxCount - state.selectedCount;
          // const item = state.selectedArray.find(
          //   (item: any) => item.keywordID == 10472
          // );
          // if (item && p.keywordID != 10472) {
          //   fun.onSelectindustryType(item);
          // }
          if (remain <= 0 && p.keywordID != 10472) {
            let msg = "最多只能选择" + props.maxCount + "个选项";
            ElMessage({
              message: msg,
              type: "warning",
            });
          } else {
            //p.selected = true;
            if (p.keywordID == 10472) {
              state.list.forEach((i: any) => (i.selected = false));
            } else {
              const item = state.list.find(
                (item: any) => item.keywordID == 10472
              );
              item.selected = false;
            }
            let industryType = state.list.find(
              (i: any) => i.keywordID == p.keywordID
            );
            if (industryType) {
              industryType.selected = true;
            }
          }
        }
        fun.selectedArrays();
      },
    };
    return {
      ...toRefs(state),
      ...fun,
      value,
    };
  },
});
</script>

<style lang="less">
.pop-sele-Industry {
  .el-dialog__header {
    .tit {
      font-size: 20px;
      color: #333;
      float: left;
    }

    span {
      color: #457ccf;
    }

    label {
      font-size: 16px;
      color: #333;
    }

    .sele-box {
      width: 450px;
      margin-left: 20px;
      border-radius: 4px;

      .el-input__inner {
        width: 420px !important;
        background: #fafafa;
        color: #bbbbbb;
        font-size: 14px;
        border: none;
        border-radius: 4px;
      }
    }

    .iconfont {
      padding-left: 4px;
    }

    .el-icon-arrow-up {
      display: none;
    }
  }

  .selected-items {
    border-bottom: 1px solid #eee;
    padding: 8px 20px;

    .el-button {
      margin-left: -100px;
      position: relative;
    }
  }

  .selected-items .info {
    color: #999;
    font-size: 12px;
    padding: 8px 0px;
  }

  .selected-items .items {
    display: flex;
    flex-flow: wrap;
    justify-content: flex-start;
    flex-direction: row;
    padding-right: 100px;

    float: left;
  }

  .selected-items .items .item {
    color: #3366cc;
    border: 1px solid #3366cc;
    border-radius: 30px;
    padding: 1px 17px;
    margin-right: 8px;
    display: flex;
    height: 35px;
    line-height: 35px;
    margin-bottom: 5px;
    font-size: 14px;

    i {
      font-style: unset;
      padding-left: 5px;
      font-size: 18px;
      cursor: pointer;
    }
  }

  .youWant {
    display: flex;
    font-size: 16px;
    color: #666666;
    padding: 12px 30px;

    .sdl {
      width: 100px;
      height: 35px;
      line-height: 35px;
    }

    .sdr {
      flex: 1;

      ul {
        display: flex;
        flex-wrap: wrap;

        li {
          background: #f5f7fa;
          margin-left: 12px;
          border-radius: 30px;
          padding: 0 12px;
          cursor: pointer;
          height: 35px;
          line-height: 35px;
        }

        li.selected {
          background: #f2f7ff;
          color: #3366cc;
        }
      }
    }

    .close {
      width: 30px;
      text-align: right;

      .el-icon-close {
        font-size: 20px;
        cursor: pointer;
      }
    }
  }

  .el-dialog__body {
    padding: 0 0 0 0;
  }

  .sel-box {
    height: 500px;
    overflow: hidden;

    .tBox {
      width: 160px;
      height: 500px;
      position: relative;
      overflow: hidden;

      ul {
        position: absolute;
        left: 0;
        top: 0;
        right: -17px;
        bottom: 0;
        overflow-x: hidden;
        overflow-y: scroll;
      }

      li {
        width: 122px;
        font-size: 14px;
        color: #666;
        line-height: 24px;
        cursor: pointer;
      }

      li.on {
        color: #457ccf;
      }
    }

    .tBox ::-webkit-scrollbar {
      width: 0 !important;
    }

    .tBox {
      -ms-overflow-style: none;
    }

    .tBox {
      overflow: -moz-scrollbars-none;
    }

    .firstBox {
      background: #ecedf4;

      li {
        padding: 18px 15px 18px 24px;
        background: #ecedf4;
      }

      li.on {
        background: #f4f5f9;
      }
    }

    // .secondBox {
    //   background: #f4f5f9;
    //   li {
    //     padding: 18px 15px 18px 24px;
    //     background: #f4f5f9;
    //   }
    //   li.on {
    //     background: #ffffff;
    //   }
    // }
    .thirdBox {
      width: 580px;

      li {
        float: left;
        background: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 18px 5px 18px 24px;
        width: 152px;
      }
    }
  }

  .thirdBox li.on {
    i {
      color: #5f9efc;
      font-style: unset;
    }
  }
}

.sele-li {
  padding: 10px;
  height: auto;

  p {
    line-height: 16px;
  }
}

.bod {
  color: #457ccf;
  font-size: 14px;
}

.lit {
  color: #999;
  font-size: 12px;
}
</style>
