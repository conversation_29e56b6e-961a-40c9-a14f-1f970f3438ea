<template>
  <div class="pop-sele-positionType" :style="{ width: widthInput + 'px' }">
    <el-input
      :placeholder="placeholder"
      @focus="openCascaderDialog"
      ref="inputRefCascaderDialog"
      :style="{ width: widthInput + 'px' }"
      v-model="textValue"
    >
      <template #suffix>
        <i
          @click="clickIcon"
          style="cursor: pointer;"
          class="el-input__icon"
          :class="Model && showClearable ? 'el-icon-circle-close' : 'el-icon-arrow-down'"
        ></i>
      </template>
    </el-input>
    <el-dialog
      v-if="showCascaderDialog"
      v-model="showCascaderDialog"
      title="Tips"
      width="800px"
      @close="handleClose"
      ref="refCascaderDialog"
      custom-class="RadiusDialog_11_10"
    >
      <template #title>
        <label class="tit">
          {{ title }}
          <label>
            (
            <span>{{ SelectArr.length }}</span>
            /{{ maxSelect }})
          </label>
        </label>
        <el-select
          v-model="positionTypeName"
          filterable
          placeholder="请输入关键字"
          :loading="loading"
          class="sele-box"
          :remote-method="SearchPositionType"
          remote
        >
          <template #prefix>
            <i style="margin-left: 7px;" class="el-icon-search"></i>
          </template>
          <el-option
            v-for="(item, index) in positionTypeList"
            :key="index"
            @click="handleSelect(item)"
            class="sele-li"
            :value="item[propSeach.name]"
          >
            <p class="bod">{{ item[propSeach.name] }}</p>
            <p class="lit">{{ item[propSeach.names].toString() }}</p>
          </el-option>
        </el-select>
      </template>
      <template #default>
        <div class="selected-items clearfix" v-if="maxSelect > 1 && SelectArr.length > 0">
          <div class="items">
            <span class="item" v-for="(p, index) in SelectArr" :key="p[prop.id]">
              {{ p[prop.name] }}
              <i class="el-icon-close" @click="onSelectindustryType(p)"></i>
            </span>
          </div>
          <el-button type="primary" round class="fr" @click="onConfirm">确定</el-button>
        </div>
        <div class="sel-box clearfix">
          <div class="firstBox tBox fl">
            <ul>
              <li
                v-for="p in level1Data"
                :key="p[prop.id]"
                @click="ChildrenPicker1(p)"
                :class="p.open ? 'on' : ''"
              >
                <span>{{ p[prop.name] }}</span>
              </li>
            </ul>
          </div>
          <div class="secondBox tBox fl">
            <ul>
              <li
                v-for="p in level2Data"
                :key="p[prop.id]"
                @click="ChildrenPicker2(p)"
                :class="p.open ? 'on' : ''"
              >
                <span>{{ p[prop.name] }}</span>
              </li>
            </ul>
          </div>
          <div class="thirdBox tBox fl">
            <ul>
              <li
                v-for="p in level3Data"
                :key="p[prop.id]"
                @click="ChildrenPicker3(p)"
                :class="{ on: isSelect(p[prop.id]) }"
              >
                <span>{{ p.otherName || p.otherName }}</span>
              </li>
            </ul>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  toRefs,
  ref,
  onBeforeMount,
  getCurrentInstance,
  nextTick,
  watch
} from 'vue';
import { useStore } from 'vuex';
import { deepClone } from '../function';
export default defineComponent({
  emits: ['update:Model'],
  props: {
    title: {
      type: String,
      default: '',
      //标题
    },
    Model: {
      type: String,
      default: '',
      //v-model绑定的数据
    },
    url: {
      type: String,
      default: '/api/options/position?parentid=-1&withcache=true',
      //总字典的接口
    },
    prop: {
      type: Object,
      default: () => {
        //字典配置项，对应字典的id 名字 从属的父级1d
        return {
          id: 'keywordID',
          name: 'keywordName',
          pid: 'parentID',
        };
      },
    },
    propSeach: {
      type: Object,
      default: () => {
        //搜索配置项
        return {
          id: 'id',
          name: 'name',
          names: 'fullname',
        };
      },
    },
    widthInput: {
      type: Number,
      default: 160,
      //展示的input宽度
    },
    placeholder: {
      type: String,
      default: '不限',
      //提示语
    },
    maxSelect: {
      type: Number,
      default: 1,
      //大于1为多选，等于一为单选
    },
    SeachUrl: {
      type: String,
      //搜索的接口，使用请查看拼接方式
      default: '/api/auto-complete/search-position'
    },
    SeachUrlType: {
      type: String,
      default: 'post'
      //搜索的请求方式
    },
    autoClose: {
      type: Boolean,
      default: true
      //单选时是否自动关闭
    },
    showClearable: {
      type: Boolean,
      default: true
    },
    Storage: {
      type: String,
      default: ''
    }
  },
  setup(props: any, { emit }: any) {

    function isSelect(id: number) {
      let index = state.SelectArr.findIndex((item: any) => {
        if (item) {
          return item[props.prop.id] == id
        } else {
          return false
        }
      })
      if (index > -1) {
        return true
      } else {
        return false
      }

    }
    const inputRefCascaderDialog: any = ref(null);
    const refCascaderDialog: any = ref(null)
    const { proxy }: any = getCurrentInstance();
    const store = useStore();
    const state = reactive({
      showCascaderDialog: false,
      list: [] as any,
      level1Data: [] as any[],
      level2Data: [] as any[],
      level3Data: [] as any[],
      positionTypeName: '', //搜索
      positionTypeList: [] as any[], //搜索到的列表
      SelectArr: [] as any,
      textValue: "",
      loading:false,
    });

    watch(() => props.Model, () => {
      state.textValue = getText(props.Model)
      if (state && state.list.length > 0 && props.Model) {
        //把已经选择的参数加入选择的数组
        state.SelectArr = deepClone([])
        let ids = props.Model
        // let value = getText(props.Model, data.data);
        let idarr: string[] = ids.split(',');
        let arr: any[] = idarr.map((item) => {
          return state.list.find((item2: any) => item == item2[props.prop.id]);
        });
        arr.forEach((item) => {
          if (item) {
            state.SelectArr.push(item);
          }
        })
        if (state.SelectArr.length > 0) {
          let selectRouter = getRoute(state.SelectArr[0][props.prop.id]).reverse()

          fun.ChildrenPicker1(selectRouter[0]);
          nextTick(() => {
            fun.ChildrenPicker2(selectRouter[1])
          })

        }
      }
    }, {
      immediate: true,
      deep: true
    })

    onBeforeMount(() => {
      //从store中获取专业字典
      // store.dispatch("getPositionTypesResource");
      let result: [] = JSON.parse(sessionStorage.getItem(props.Storage)) ? JSON.parse(sessionStorage.getItem(props.Storage)) : []
      
      if (result.length <= 0) {
        methods.getData();
      } else {
        fun.assembleDATA(result);
      }
      if (state && state.list.length > 0 && props.Model) {
        state.textValue = getText(props.Model)
        //把已经选择的参数加入选择的数组
        state.SelectArr = deepClone([])
        let ids = props.Model
        // let value = getText(props.Model, data.data);
        let idarr: string[] = ids.split(',');
        let arr: any[] = idarr.map((item) => {
          return state.list.find((item2: any) => item == item2[props.prop.id]);
        });
        arr.forEach((item) => {
          if (item) {
            state.SelectArr.push(item);
          }
        })
        if (state.SelectArr.length > 0) {
          let selectRouter = getRoute(state.SelectArr[0][props.prop.id]).reverse()

          fun.ChildrenPicker1(selectRouter[0]);
          nextTick(() => {
            fun.ChildrenPicker2(selectRouter[1])
          })

        }
      }
    });
    function getRoute(id: number) {
      let arr: any[] = [];
      fun(id);
      function fun(id: number) {

        let ids = state.list.find((item1: any) => {
          return item1[props.prop.id] == id;
        });
        arr.push(ids);
        if (ids[props.prop.pid] == 0) {
          return;
        } else {
          fun(ids[props.prop.pid]);
        }
      }
      return arr;
    }
    function getText(ids: string) {
      if (ids) {
        let idarr: string[] = ids.split(',');
        let arr = idarr.map((item) => {
          return state.list.find((item2: any) => item == item2[props.prop.id]);
        });

        return arr
          .map((item3: any) => {
            if (item3) {
              return item3[props.prop.name].toString();
            } else {
              return '数据有误';
            }
          })
          .join('/');
      } else {
        return '';
      }
    }
    const methods = {

      async getData() {
        let data: any = await proxy.$http({
          url: props.url,
          showLoading: false,
        });

        // store.commit("setPositionTypesResource", data.data);
        if (data.code == 1) {
          fun.assembleDATA(data.data);
          sessionStorage.setItem(props.Storage, JSON.stringify(data.data))
        }

      },
      // 搜索
      async getpositionTypeName(text: any) {
        let data: any = await proxy.$http({
          url: `${props.SeachUrl}?keyword=${text}`,
          type: props.SeachUrlType,
        });
        if (data.code == 1) {
          state.positionTypeList = data.data.data || data.data;
        }
      },
    };
    const fun = {
      clickIcon() {
        if (props.Model) {
          if (props.showClearable) {
            state.SelectArr = []
            emit('update:Model', '');
          } else {
            fun.openCascaderDialog()
          }

        } else {
          fun.openCascaderDialog()
        }
      },
      openCascaderDialog() {
        state.showCascaderDialog = true;
      },
      onSelectindustryType(p: any) {
        let index = state.SelectArr.findIndex(
          (item: any) => item[props.prop.id] == p[props.prop.id]
        );
        state.SelectArr.splice(index, 1);
        // state.list.forEach((i: any) => {
        //   if (i[props.prop.id] == p[props.prop.id]) {
        //     i.selected = false;
        //   }
        // });
      },
      onConfirm() {
        emit('update:Model', state.SelectArr.map((item: any) => item[props.prop.id]).toString());
        fun.handleClose()
      },
      ChildrenPicker1(p: any) {
        // if (p.open) return false;
        state.level1Data.forEach((i: any, index) => {
          state.level1Data[index].open = false
          if (p[props.prop.id] == i[props.prop.id]) {
            state.level1Data[index].open = true
          }
        });
        state.level2Data = state.list.filter(
          (ps: any) => ps[props.prop.pid] == p[props.prop.id]
        );

        fun.ChildrenPicker2(state.level2Data[0]);
      },

      ChildrenPicker2(p: any) {
        // if (p.open || !p) {
        //   return false;
        // }
        state.level3Data = deepClone([])
        // state.level2Data.forEach((i: any) => (i.open = false));
        // p.open = true;
        state.level2Data.forEach((i: any, index) => {
          state.level2Data[index].open = false
          if (p[props.prop.id] == i[props.prop.id]) {
            state.level2Data[index].open = true
          }
        });
        let arr: any = [
          {
            childrenSelectedCount: 0,
            hasNext: false,
            [props.prop.id]: p[props.prop.id],
            otherName: '全部',
            [props.prop.name]: p[props.prop.name],
            open: false,
            selected: false,
          },
        ];
        arr.push(
          ...state.list.filter(
            (ps: any) => ps[props.prop.pid] == p[props.prop.id]
          )
        );
        state.level3Data = arr;
      },
      ChildrenPicker3(p: any) {
        if (isSelect(p[props.prop.id])) {
          fun.onSelectindustryType(p)
          return
        }
        if (props.maxSelect == 1) {
          //单选
          // state.list.forEach((i: any) => (i.selected = false));
          // p.selected = true;
          fun.onSelectindustryType(state.SelectArr[0])
          state.SelectArr.push(p);
          emit('update:Model', p[props.prop.id].toString());
          if (props.autoClose) {
            fun.handleClose()
          }
        } else {


          if (state.SelectArr.length < props.maxSelect) {

            // p.selected = true;
            state.SelectArr.push(p);
          } else {
            fun.onSelectindustryType(state.SelectArr[0])

            // p.selected = true;
            nextTick(() => {
              state.SelectArr.push(p);
            })

          }
        }
      },

      handleSelect(p: any) {
        let arr: [] = [];

        let item = {
          [props.prop.id]: p[props.propSeach.id],
          [props.prop.name]: p[props.propSeach.name],
        };

        let selectRouter = getRoute(item[props.prop.id]).reverse()

        fun.ChildrenPicker1(selectRouter[0]);
        fun.ChildrenPicker2(selectRouter[1])
        fun.ChildrenPicker3(item);
      },
      assembleDATA(List: any) {
        state.list = computed(() => {
          let arr = [];
          arr = List.map((i: any) => {
            return {
              ...i,
              // selected: false,
              otherName: i[props.prop.name], //别名，区别于全部选择这种需要另起他名的情况
              childrenSelectedCount: 0,
              open: false,
            };
          });
          return arr;
        });
        // state.list.forEach((i: any) =>
        //   i[props.prop.id] ==
        //     props.Model.split(',')[props.Model.split(',').length - 1]
        //     ? (i.selected = true)
        //     : ''
        // );
        state.level1Data = state.list.filter(
          (i: any) => i[props.prop.pid] == 0
        );
        // state.level1Data[0].open = true;
        fun.ChildrenPicker1(state.level1Data[0]);
        // fun.ChildrenPicker2(state.level2Data[0]);
      },
      handleClose() {
        // done()
        state.showCascaderDialog = false;
        inputRefCascaderDialog.value.blur();
      },
      //搜索
      SearchPositionType(query: string) {
        if (query) {
          state.loading = true
          setTimeout(() => {
            methods.getpositionTypeName(query);
            state.loading = false
          }, 200)
        } else {
        }
      },
    };
    return {
      ...toRefs(state),
      ...fun,
      ...toRefs(props),
      getText,
      inputRefCascaderDialog,
      isSelect,
      refCascaderDialog
    };
  },
});
</script>
<style lang="scss">
.RadiusDialog_11_10 {
  .el-dialog__body {
    padding: 0 !important;
  }
}
</style>

<style lang="scss" scoped>
.selected-items {
  border-bottom: 1px solid #eee;
  padding: 8px 20px;
  .el-button {
    margin-left: -100px;
    position: relative;
  }
}
.selected-items .info {
  color: #999;
  font-size: 12px;
  padding: 8px 0px;
}
.selected-items .items {
  display: flex;
  flex-flow: wrap;
  justify-content: flex-start;
  flex-direction: row;
  padding-right: 100px;

  float: left;
}
.selected-items .items {
  color: #3366cc;

  display: flex;
  height: 35px;
  line-height: 35px;
  margin-bottom: 5px;
  font-size: 14px;
  .item {
    align-items: center;
    display: flex !important;
    border-radius: 30px;
    margin-right: 8px;
    border: 1px solid #3366cc;
    padding: 1px 17px;
  }
  i {
    padding-left: 9px;
    font-size: 12px;
    cursor: pointer;
    display: inline-block;
  }
}
ul,
li {
  padding: 0;
  margin: 0;
}
li {
  list-style: none;
}
.pop-sele-positionType {
  display: inline-block;
  .clearfix:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
  }
  .clearfix {
    // zoom: 1;
  }
  .clear {
    height: 0;
    clear: both;
    overflow: hidden;
  }
  .fl {
    float: left;
  }
  .fr {
    float: right;
  }
  .el-dialog__header {
    border-bottom: 1px solid #f2f2f2;
    label.tit {
      font-size: 20px;
      color: #333;
      label {
        font-size: 14px;
        span {
          color: #457ccf;
        }
      }
    }
    .sele-box {
      width: 450px;
      margin-left: 20px;
      border-radius: 4px;
      .el-input__inner {
        width: 420px !important;
        background: #fafafa;
        color: #333;
        font-size: 14px;
        border: none;
        border-radius: 4px;
      }
    }
    .iconfont {
      padding-left: 4px;
    }
    .el-icon-arrow-up {
      display: none;
    }
  }
  .el-dialog__body {
    padding: 0 0 0 0;
  }
  .el-dialog__headerbtn .el-dialog__close {
    font-size: 30px;
    color: #d4d4d4;
  }
  .sel-box {
    height: 500px;
    overflow: hidden;
    .tBox {
      width: 160px;
      height: 500px;
      position: relative;
      overflow: hidden;
      ul {
        position: absolute;
        left: 0;
        top: 0;
        right: -17px;
        bottom: 0;
        overflow-x: hidden;
        overflow-y: scroll;
      }
      li {
        width: 122px;
        font-size: 14px;
        color: #666;
        line-height: 24px;
        cursor: pointer;
      }
      li.on {
        color: #457ccf;
      }
    }
    .tBox ::-webkit-scrollbar {
      width: 0 !important;
    }
    .tBox {
      -ms-overflow-style: none;
    }
    .tBox {
      overflow: -moz-scrollbars-none;
    }
    .firstBox {
      background: #ecedf4;
      li {
        padding: 18px 15px 18px 24px;
        background: #ecedf4;
      }
      li.on {
        background: #f4f5f9;
      }
    }
    .secondBox {
      background: #f4f5f9;
      li {
        padding: 18px 15px 18px 24px;
        background: #f4f5f9;
      }
      li.on {
        background: #ffffff;
      }
    }
    .thirdBox {
      width: 480px;
      li {
        float: left;
        background: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 18px 5px 18px 24px;
      }
    }
  }
}
.sele-li {
  padding: 10px;
  height: auto;
  p {
    line-height: 16px;
  }
}
.bod {
  color: #457ccf;
  font-size: 14px;
}
.lit {
  color: #999;
  font-size: 12px;
}
</style>
