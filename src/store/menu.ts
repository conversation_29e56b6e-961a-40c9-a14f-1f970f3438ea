// type==1有链接本页面打开  type==2有链接新页面打开 
// type==3只针对个人信息因为后面要加动态id   type==4只针对退出登录,
// hasIcon==1 ,我的消息有数字



const menuList=[
    {
      title:'个人中心' ,
       path:'/',
       type:1,
       icon:'iconfont icon-grzx',
       index:"0",
    },
    {
        title:'投递记录',
        type:2,
        icon:'iconfont icon-grzx',
        index:'1',
        class:'padtop20',
        children:[
            {
                title:'全部' ,
                path:'/apply/0',
                index:"1-0", 
                type:1,
            },
            {
                title:'被查看' ,
                path:'/apply/1',
                index:"1-1", 
                type:1,
            },
            {
                title:'邀面试' ,
                path:'/apply/2',
                index:"1-2", 
                type:1,
            },
            {
                title:'不合适' ,
                path:'/apply/3',
                index:"1-3", 
                type:1,
            },
            {
                title:'已撤销投递' ,
                path:'/apply/4',
                index:"1-4", 
                type:1,
            },
        ]
     },
     {
        title:'简历管理' ,
        type:1,
        icon:'icon-jlgl iconfont',
        index:"2",
        children:[
            {
                title:'简历列表',
                path:'/resumeList',
                index:"2-0", 
                type:1,
            },
            {
                title:'公司屏蔽' ,
                path:'/companyShield',
                index:"2-1", 
                type:1,
            },
            {
                title:'谁看过我' ,
                path:'/enterpriseView/0',
                index:'2-2', 
                type:1,
            }
        ]
     },
     {
       title:'浏览记录',
       type:1,
       icon:'icon-lljl iconfont',
       index:'3',
       path:'/myViewed',
    },
    {
       title:'职位收藏',
       type:1,
       icon:'icon-zwsc iconfont',
       index:'4',
       path:'/favorites',
    },
    {
        title:'我的消息',
        path:'/mynews',
        type:2,
        class:'padtop20',
        icon:'icon-xiaoxi iconfont',
        index:'12',
        hasIcon:'1',
     },
    {
        title:'信息管理',
        path:'/',
        type:1,
        icon:'iconfont icon-xxgl',
        index:'5',
        children:[
            {
                title:'个人信息',
                path:'/resume/',
                index:'5-0', 
                type:3,
            },
            {
                title:'建档立卡',
                path:'/privateFile',
                index:'5-1', 
                type:1,
            },
            {
                title:'密码管理',
                path:'/password',
                index:'5-2', 
                type:1,
            },
            {
                title:'账号绑定',
                path:'/InternetAccount',
                index:'5-3', 
                type:1,
            }
        ]
     },
     {
        title:'我的订单' ,
        type:1,
        icon:'iconfont icon-wddd1',
        index:'6',
        children:[
            {
                title:'全部' ,
                path:'/manual/0',
                index:'6-0', 
                type:1,
            },
            {
                title:'求职助手' ,
                path:'/manual/1',
                index:'6-1', 
                type:1,
            },
            {
                title:'成长课堂' ,
                path:'/manual/2',
                index:'6-2', 
                type:1,
            },
            {
                title:'薪酬报告' ,
                path:'/manual/3',
                index:'6-3', 
                type:1,
            },
        ]
     },
     {
        title:'成长课堂',
        path:'https://czkt.gxrc.com/',
        type:2,
        class:'padtop20',
        icon:'icon-czkt iconfont',
        index:'7',
        isNewpage:true,
      },
      {
        title:'资讯中心',
        path:'https://news.gxrc.com/',
        type:2,
        icon:'icon-zxzx iconfont',
        index:'8',
        isNewpage:true,
      },
      {
        title:'人才服务',
        path:'/otherService',
        type:1,
        icon:'iconfont icon-rcfw',
        index:'9',
        isNewpage:false,
      },
    //   {
    //     title:'职业测评',
    //     path:'/careerAssessment',
    //     type:1,
    //     icon:'iconfont icon-zycp',
    //     index:'10',
    //     isNewpage:false,
    //   },
      {
        title:'退出登录',
        path:'#',
        type:4,
        icon:'icon-signout iconfont',
        index:'11',
        isNewpage:false,
        class:'padtop20'
      },

]
export default menuList