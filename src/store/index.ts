import { createStore,Store,useStore as baseUseStore } from 'vuex';
import { InjectionKey } from 'vue'
import resource from "./modules/resource";
import { getMajorResource, getPosition } from "../http/dictionary";
import imModules from './Imstore/index'
import type {ImStateType} from "./Imstore/state"
import { myInfo } from '../http/api';
export type State = {
  imModules:ImStateType,
  resumeList: any[]
}
export const key: InjectionKey<Store<State>> = Symbol()
export default createStore({
  state() {
    return {
      userInfo: {},
      adList: {},
      editorid: 0,
      major: [],//专业类别
      positionType: [],//职位
      industry: [],//行业
      workstatuoptions: [],//
      educationalList: [],//学历
      certificatelList: [],//证书类别
      levelsList: [],//等级  --初级 中级  高级
      companyNatureList: [],//公司性质
      positionGradeList: [],//职位等级
      workTypeList: [],//工作方式-
      companyScaleList: [],//公司规模
      cityList:[],//城市列表
      resumeList: [],// 简历列表
      iseditInfo:false,//编辑信息
      iseditCarrer:false,//编辑求职意向
      accountAuth:{},
      showVipPOP:false,//打开右侧vip广告
      domainUrl:'https://www.gxrc.com',
      logoPagBid:'0',//默认bid
      logoUrl:'//image.gxrc.com/gxrcsite/ds/logo/logo_20.png',//logo
      hasQueren:false,//是否需要确认求职意向 0：没有重审过，无需提示1：求职意向已确认2：求职意向未确认，今天未弹窗3：求职意向未确认，今天已弹窗
      headImgchange:0,//头像修改,
      commonPhraseslist:[],//常用语列表
    };
  },
  mutations: {
    setResumeList(state, resumeList) {
      state.resumeList = resumeList;
    },
    setAccountAuth(state,accountAuth){
      state.accountAuth=accountAuth;
    },
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo;
      
    },
    setAdList(state, adList) {
      state.adList = adList;
    },
    editorShow(state, editorid) {
      state.editorid = editorid;
    },
    //专业字典
    getMajorResource(state, major) {
      state.major = major;
    },
    //职位字典
    setPositionTypesResource(state, positionType) {
      state.positionType = positionType;
    },
    //行业字典
    setIndustryResource(state, industry) {
      state.industry = industry;
    },
    //行业字典
    setWorkstatuoptions(state, workstatuoptions) {
      state.workstatuoptions = workstatuoptions;
    },
    //教育
    setEducationalList(state, educationalList) {
      state.educationalList = educationalList;
    },
    //证书类别
    setCertificatelList(state, certificatelList) {
      state.certificatelList = certificatelList;
    },
    //等级
    setLevelsList(state, levelsList) {
      state.levelsList = levelsList;
    },

    //公司性质
    setCompanyNatureList(state, companyNatureList) {
      state.companyNatureList = companyNatureList;
    },
    //职位等级
    setPositionGradeList(state, positionGradeList) {
      state.positionGradeList = positionGradeList;
    },
    //工作方式
    setWorkTypeListt(state, workTypeList) {
      state.workTypeList = workTypeList;
    },
    //公司规模
    setCompanyScaleList(state, companyScaleList) {
      state.companyScaleList = companyScaleList;
    },
    //城市列表
    setCityList(state, cityList) {
      state.cityList = cityList;
    },
     //编辑个人信息
     setEditiInfo(state, type) {
      state.iseditInfo = type;
    },
     //编辑求职意向
     setEditCarrer(state, type) {
      state.iseditCarrer = type;
    },
    //编辑个人信息
    setVipPOP(state, type) {
      state.showVipPOP = type;
    },
     //设置主域名
     setDomainUrl(state, type) {
      state.domainUrl = type;
    },
     //设置logo图片地址
     setLogoUrl(state, type) {
      state.logoUrl = type;
    },
    //是否确认过求职意向的更新
    setHasQueren(state, type) {
      state.hasQueren = type;
    },
    //设置头像修改
    setHeadImgchange(state, type) {
      state.headImgchange = type;
    },
    //用于登录页面
    setLogoPagBid(state, type) {
      state.logoPagBid = type;
    },
  },

  getters: {
    userInfo: (state) => state.userInfo
  },

  actions: {
    async getMajorResource(context: any) {
      if (context.state.major && context.state.major.length) return;
      let data: any = await getMajorResource('');
      context.commit('getMajorResource', data.data);
    },
    async getPositionTypesResource(context: any) {
      if (context.state.positionType && context.state.positionType.length) return;
      let data: any = await getPosition('');
      context.commit('setPositionTypesResource', data.data);
    },
    async getUserInfo({ commit }){
      const res = await myInfo();
      commit('setUserInfo',res.data)
      return res.data
    }
  },

  modules: { resource,imModules:imModules },
});
export function useStore(): Store<State> {
  return baseUseStore();
}