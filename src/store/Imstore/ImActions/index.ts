// Action 提交的是 mutation，而不是直接变更状态。
// Action 可以包含任意异步操作。
import cookie from '../../../IM/utils/cookie'
import { removeCookies } from "../../../utils/common";
import pageUtil from '../../../IM/utils/page'
import { ElMessage } from "element-plus";
// import Vue from 'vue'
/* 导出actions方法 */
import { showLoading, hideLoading, showFullscreenImg, hideFullscreenImg } from './widgetUi'
import { initNimSDK } from './initNimSDK'
import { initWebRtcSDK } from './initWebRtcSDK'
import { updateBlack } from './blacks'
import { updateFriend, addFriend, deleteFriend } from './friends'
import { resetSearchResult, searchUsers, searchTeam } from './search'
import { deleteSession, setCurrSession, resetCurrSession } from './session'
import { sendMsg,sendImage, sendFileMsg, sendMsgReceipt, sendRobotMsg, revocateMsg, updateLocalMsg, getHistoryMsgs, resetNoMoreHistoryMsgs, continueRobotMsg, markMsgRead, sendLocalMsg } from './msgs'
import { markSysMsgRead, resetSysMsgs, deleteSysMsgs, markCustomSysMsgRead } from './sysMsgs'

// import {sendChatroomMsg, sendChatroomRobotMsg, sendChatroomFileMsg, getChatroomHistoryMsgs} from './chatroomMsgs'
// import { delegateTeamFunction, onTeamNotificationMsg, enterSettingPage, getTeamMembers, checkTeamMsgReceipt, getTeamMsgReads} from './team'
import { login } from "../../../IM/api"
import config from '../../../IM/configs'
import { replyStatus,recordPosition } from './service'
// import { from } from 'core-js/fn/array'
// var vm = new Vue()
async function connectNim({ state, commit, dispatch }, obj) {
  let { force } = Object.assign({}, obj)
  // 操作为内容页刷新页面，此时无nim实例
  let t = config.isTest ? "t" : ""
  if (!state.nim || force) {
    dispatch('showLoading')
    let data = await login()
    if (data.code == 1) {
      if (data.data.name || true) {//name 返回的始终是null
        let loginInfo = {
          uid: data.data.accid,//ent53046
          sdktoken: data.data.token,
        }
        if (!loginInfo.uid) {
          // alert('无历史登录记录，请重新登录')
          ElMessage.error('无历史登录记录，请重新登录');
        } else {
          dispatch('initNimSDK', { loginInfo, obj })
        }
      } else {
        // alert(data.data.message)
        ElMessage(data.data.message);
      }
    } else {
      data.message && ElMessage(data.message);
      if (window.self !== window.top) {
        const el = document.getElementById("widget-im");
        if (el) {
          el.style.display = 'none';
        }
      } else {

        //location.href = `//vip.${t}gxrc.com/login?returnUrl=${location.href}`
      }
    }
  }
}


export default {
  updateRefreshState({ commit }) {
    commit('updateRefreshState')
  },

  // UI 及页面状态变更
  showLoading,
  hideLoading,
  showFullscreenImg,
  hideFullscreenImg,
  continueRobotMsg,

  // 连接sdk请求，false表示强制重连
  connect(store, obj) {
    let { type } = Object.assign({}, obj)
    // type 可为 nim chatroom
    type = type || 'nim'
    if (window.location.href.indexOf('/findpwd') > -1 || window.location.href.indexOf('/login') > -1 || window.location.href.indexOf('/oAuth/Login') > -1 || window.location.href.indexOf('/oAuth/Callback') > -1 || window.location.href.indexOf('/ServiceCenterGrantAuth') > -1) {
    } else {
      connectNim(store, obj)
    }
  },
  // 用户触发的登出逻辑
  logout({ state, commit }) {
    cookie.delCookie('uid')
    cookie.delCookie('sdktoken')
    cookie.delCookie('js')
    cookie.delCookie('ASP.NET_SessionId')
    if (state.nim) {
      state.nim.disconnect()
    }
    pageUtil.turnPage('', 'login')
  },

  // 初始化 重新连接SDK
  initNimSDK,
  //初始化音视频
  initWebRtcSDK,
  // 清空所有搜索历史纪录
  resetSearchResult,
  // 搜索用户信息
  searchUsers,
  // 更新黑名单
  updateBlack,
  // 更新好友
  addFriend,
  deleteFriend,
  updateFriend,
  // 删除会话
  deleteSession,
  // 设置当前会话
  setCurrSession,
  // 重置当前会话
  resetCurrSession,
  // 发送消息
  sendMsg,
  sendImage,
  sendFileMsg,
  sendRobotMsg,
  //发送本地消息
  sendLocalMsg,
  // 发送消息已读回执
  sendMsgReceipt,
  // 消息撤回
  revocateMsg,
  // 更新本地消息
  updateLocalMsg,
  //已读消息
  markMsgRead,
  getHistoryMsgs,
  // 重置历史消息状态
  resetNoMoreHistoryMsgs,
  // 标记系统消息已读
  markSysMsgRead,
  markCustomSysMsgRead,
  resetSysMsgs,
  deleteSysMsgs,


  // 搜索群
  searchTeam,
  //查询状态
  replyStatus,
  recordPosition
}
