import { allMsgReply,imSendmsgupdateposition } from "../../../IM/api";
import store from '../../index'
export function replyStatus({commit},sessionIds){
    const sessionId = sessionIds.replace('p2p-','')
    allMsgReply(sessionId).then(result =>{
        let { data } = result;
        const wechatReply = data.find(item => item.msgType == 65);
        if(wechatReply){
            let item = {...wechatReply}
            commit('updateReply',{item,sessionId})
        }
    })
}

export async function recordPosition({state,commit},sendMsgTpye: 1 | 2 | 3) {
    if (sendMsgTpye == 3 && !state.positionId) return;
    const res = await imSendmsgupdateposition({
        platform: 0,
        enterId: Number(state.ActiveSessionId || 0),
        positionId: state.positionId,
        isDirectSent: 1,
        sendMsgTpye: sendMsgTpye,
    });
    // 如果是前端直接发送需要填写发送的消息类型 1:语音 2:图片
    commit('updatePositionId',0)
}