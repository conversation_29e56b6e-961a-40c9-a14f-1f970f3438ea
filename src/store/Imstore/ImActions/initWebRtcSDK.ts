import config from '../../../IM/configs'


import '../../../IM/lib/NIM_Web_SDK_v8.9.128.js'
import '../../../IM/lib/NIM_Web_WebRTC_v8.0.0.js'

export function initWebRtcSDK({ state, commit, dispatch }){
    if(state.nim && !state.netcall){
        SDK.NIM.use(WebRTC)
        state.webrtcSDK = WebRTC
        state.netcall = WebRTC.getInstance({
            nim: state.nim,
            debug: process.env.NODE_ENV === 'production' ? false : true
        })
    }
}
