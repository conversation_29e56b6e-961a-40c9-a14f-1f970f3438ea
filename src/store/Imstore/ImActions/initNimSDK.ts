/*
 * SDK连接相关
 */

import config from '../../../IM/configs';
import pageUtil from '../../../IM/utils/page';
import util from '../../../IM/utils/index';
import store from '../../index';
// import { onFriends, onSyncFriendAction } from './friends'
import { onRobots } from './robots';
import { onBlacklist, onMarkInBlacklist } from './blacks';
import { onMyInfo, onUserInfo } from './userInfo';
import { onSessions, onUpdateSession } from './session';
import { onRoamingMsgs, onOfflineMsgs, onMsg } from './msgs';
import {
  onSysMsgs,
  onSysMsg,
  onSysMsgUnread,
  onCustomSysMsgs,
} from './sysMsgs';
// import { onTeams, onSynCreateTeam, onCreateTeam, onUpdateTeam, onTeamMembers, onUpdateTeamMember, onAddTeamMembers, onRemoveTeamMembers, onUpdateTeamManagers, onDismissTeam, onUpdateTeamMembersMute, onTeamMsgReceipt } from './team'

// const vm = new Vue()
let t = config.isTest ? 't' : '';
// 重新初始化 NIM SDK
import '../../../IM/lib/NIM_Web_SDK_v8.9.128.js';
import '../../../IM/lib/NIM_Web_WebRTC_v8.0.0.js';
export function initNimSDK({ state, commit, dispatch }, model) {
  if (state.nim) {
    state.nim.disconnect();
  }
  const SDK = window.SDK
  const WebRTC = window.WebRTC
  let { loginInfo, obj } = model;
  //dispatch('showLoading')
  SDK.NIM.use(WebRTC);

  // 初始化SDK
  state.nim = SDK.NIM.getInstance({
    debug: process.env.NODE_ENV === 'production' ? false :false,
    appKey: config.appkey,
    account: loginInfo.uid,
    token: loginInfo.sdktoken,
    // transports: ['websocket'],
    db: state.useDb,
    // logFunc: new SDK.NIM.LoggerPlugin({
    //   url: '/webdemo/h5/getlogger',
    //   level: 'info'
    // }),
    noCacheLinkUrl: true,
    syncSessionUnread: true,
    syncRobots: true,
    autoMarkRead: true, // 默认为true
    onconnect: function (event) {
      if (loginInfo) {
        // 连接上以后更新uid
        commit('updateUserUID', loginInfo);
        // 获取反垃圾词库
        state.nim.getClientAntispamLexicon({
          done: function (error, file) {},
        });
      }
    },
    onerror: function onError(event) {
      // alert(JSON.stringify(event))
      if (window.self != window.top) {
        location.reload();
      } else {
        //弹框
        // vm.$alert('连接已断开，请重新进入', {
        //   confirmButtonText: '确定',
        //   callback: action => {
        //     window.location.href = `//vip.${t}gxrc.com`
        //   }
        // })
      }
    },
    onwillreconnect: function onWillReconnect() {
      if (window.self != window.top) {
        location.reload();
      } else {
      }
    },
    ondisconnect: function onDisconnect(error) {
      switch (error.code) {
        // 账号或者密码错误, 请跳转到登录页面并提示错误
        case 302:
          //如果是widget iframe
          if (window.self != window.top) {
            // window.parent.postMessage(
            //   {
            //     methods: 'change_style',
            //     data: {
            //       el: '#im_widget',
            //       value: [
            //         { value: 'none', style: 'display' }
            //       ],
            //     },
            //   },
            //   '*'
            // );
          } else {
            // pageUtil.turnPage('帐号或密码错误', 'login');
          }

          break;
        // 被踢, 请提示错误后跳转到登录页面
        case 'kicked':
          let map = {
            PC: '电脑版',
            Web: '网页版',
            Android: '手机版',
            iOS: '手机版',
            WindowsPhone: '手机版',
          };
          let str = error.from;
          let errorMsg = `你的帐号于${util.formatDate(new Date())}被${
            map[str] || '其他端'
          }踢出下线，请确定帐号信息安全!`;
          if (window.self != window.top) {
            // window.parent.postMessage(
            //   {
            //     methods: 'change_style',
            //     data: {
            //       el: '#im_widget',
            //       value: [
            //         { value: 'none', style: 'display' }
            //       ],
            //     },
            //   },
            //   '*'
            // );
          } else {
            pageUtil.turnPage(errorMsg, 'login');
          }

          break;
        default:
          // let t = config.isTest ? "t" : ""
          // location.href = `//vip.${t}gxrc.com/login?returnUrl=//vip.${t}gxrc.com/im`
          state.nim.connect();
          break;
      }
    },
    // // 多端登录
    // onloginportschange: onLoginPortsChange,
    // 用户关系及好友关系
    onblacklist: onBlacklist,
    onsyncmarkinblacklist: onMarkInBlacklist,
    // onmutelist: onMutelist,
    //onsyncmarkinmutelist: onMarkInMutelist,
    //onfriends: onFriends,
    //onsyncfriendaction: onSyncFriendAction,
    // 机器人
    onrobots: onRobots,
    // 用户名片 - actions/userInfo
    onmyinfo: onMyInfo,
    onupdatemyinfo: onMyInfo,
    onusers: onUserInfo,
    onupdateuser: onUserInfo,
    // // 群组
    //onteams: onTeams,
    //onsynccreateteam: onSynCreateTeam,
    syncTeams: true,
    //onteammembers: onTeamMembers,
    //onCreateTeam: onCreateTeam,
    //onDismissTeam: onDismissTeam,
    //onUpdateTeam: onUpdateTeam,
    //onAddTeamMembers: onAddTeamMembers,
    //onRemoveTeamMembers: onRemoveTeamMembers,
    //onUpdateTeamManagers: onUpdateTeamManagers,
    //onupdateteammember: onUpdateTeamMember,
    //onUpdateTeamMembersMute: onUpdateTeamMembersMute,
    //onTeamMsgReceipt: onTeamMsgReceipt,
    // // 会话
    onsessions: onSessions,
    onupdatesession: onUpdateSession,
    // // 消息
    onroamingmsgs: onRoamingMsgs,
    onofflinemsgs: onOfflineMsgs,
    onmsg: onMsg,
    // // 系统通知
    onsysmsg: onSysMsg,
    onofflinesysmsgs: onSysMsgs,
    onupdatesysmsg: onSysMsg, // 通过、拒绝好友申请会收到此回调

    onsysmsgunread: onSysMsgUnread,
    onupdatesysmsgunread: onSysMsgUnread,

    onofflinecustomsysmsgs: onCustomSysMsgs,
    oncustomsysmsg: onCustomSysMsgs,
    //监听订阅事件列表
    //onpushevents:onPushEvents,
    // // 同步完成
    onsyncdone: function onSyncDone() {
      // 说明在聊天列表页
      if (store.state.imModules.currSessionId) {
        dispatch('setCurrSession', store.state.imModules.currSessionId);
      }
      commit('updateIsAsync');
    },
  });
  let { video } = Object.assign({}, obj);
  if (video) {
    setTimeout(() => {
      if (store.state.imModules.isAsync) {
        state.netcall = WebRTC.getInstance({
          nim: state.nim,
          debug: process.env.NODE_ENV === 'production' ? false : true,
        });
        state.webrtcSDK = WebRTC;
      }
    }, 800);
  }
  //state.nim.useDb = config.useDb
}

function onPushEvents(param) {
  if (!config.openSubscription) {
    return;
  }
  if (param.msgEvents) {
    var msgEvents = param.msgEvents;
    store.commit('updatePersonSubscribe', msgEvents);
  }
}
