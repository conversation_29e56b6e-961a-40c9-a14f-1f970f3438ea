import store from "../../index";
import config from "../../../IM/configs";
import util from "../../../IM/utils/index";
// import Vue from "vue";
// var vm = new Vue();
import { setRead } from "../../../IM/api";
import { ElMessageBox } from "element-plus";

export function formatMsg(msg) {
  const nim = store.state.imModules.nim;
  if (msg.type === "robot") {
    if (msg.content && msg.content.flag === "bot") {
      if (msg.content.message) {
        msg.content.message = msg.content.message.map((item) => {
          switch (item.type) {
            case "template":
              item.content = nim.parseRobotTemplate(item.content);
              break;
            case "text":
            case "image":
            case "answer":
              break;
          }
          return item;
        });
      }
    }
  }
  return msg;
}

export function onRoamingMsgs(obj) {
  let msgs = obj.msgs.map((msg) => {
    return formatMsg(msg);
  });
  store.commit("updateMsgs", msgs);
}

export function onOfflineMsgs(obj) {
  let msgs = obj.msgs.map((msg) => {
    return formatMsg(msg);
  });
  store.commit("updateMsgs", msgs);
}

export function onMsg(msg) {
  console.log(msg);

  msg = formatMsg(msg);
  store.commit("putMsg", msg);
  if (msg.sessionId === store.state.imModules.currSessionId) {
    // if(msg.type == 'custom' && msg.content){
    //   let content = JSON.parse(msg.content);
    //   let event = content.rcevent
    //   if(event === 'ApplyWeChatExch_Step1'){
    //     store.commit('updateReplyState',true)
    //   }
    // }
    store.commit("updateCurrSessionMsgs", {
      type: "put",
      msg,
    });
    // 发送已读回执
    store.dispatch("sendMsgReceipt");
  }
  // if (msg.scene === "team" && msg.type === "notification") {
  //   store.dispatch("onTeamNotificationMsg", msg);
  // }
}

function onSendMsgDone(error, msg) {
  store.dispatch("hideLoading");

  if (error) {
    // 被拉黑
    if (error.code === 7101) {
      msg.status = "success";
      alert(error.message);
    } else {
      alert(error.message);
    }
  }
  onMsg(msg);
}

// 消息撤回
export function onRevocateMsg(error, msg) {
  const nim = store.state.imModules.nim;
  if (error) {
    if (error.code === 508) {
      alert("发送时间超过2分钟的消息，不能被撤回");
    } else {
    }
    return;
  }
  let tip = "";
  if (msg.from === store.state.imModules.userUID) {
    tip = "你撤回了一条消息";
  } else {
    let userInfo = store.state.imModules.userInfos[msg.from];
    if (userInfo) {
      tip = `${util.getFriendAlias(userInfo)}撤回了一条消息`;
    } else {
      tip = "对方撤回了一条消息";
    }
  }

  nim.sendTipMsg({
    isLocal: true,
    scene: msg.scene,
    to: msg.to,
    tip,
    time: msg.time,
    done: function sendTipMsgDone(error, tipMsg) {
      let idClient = msg.deletedIdClient || msg.idClient;
      store.commit("replaceMsg", {
        sessionId: msg.sessionId,
        idClient,
        msg: tipMsg,
      });
      if (
        tipMsg.sessionId === store.state.imModules.currSessionId ||
        msg.msg.sessionId === store.state.imModules.currSessionId
      ) {
        store.commit('updateCurrSessionMsgs', {
          type: 'replace',
          idClient,
          msg: tipMsg,
        });
        // store.commit("updateCurrSessionMsgs", {
        //   type: "delete",
        //   idClient,
        //   msg: tipMsg,
        // });
      }
    },
  });
}
export function revocateMsg({ state, commit }, msg) {
  const nim = state.nim;

  let { idClient } = msg;
  msg = Object.assign(msg, state.msgsMap[idClient]);
  nim.deleteMsg({
    msg,
    done: function deleteMsgDone(error,data) {
      onRevocateMsg(error, msg);
    },
  });
}
export function updateLocalMsg({ state, commit }, msg) {
  store.commit("updateCurrSessionMsgs", {
    type: "replace",
    idClient: msg.idClient,
    msg: msg,
  });
  state.nim.updateLocalMsg({
    idClient: msg.idClient,
    localCustom: msg.localCustom,
  });
  store.commit("replaceMsg", {
    sessionId: msg.sessionId,
    idClient: msg.idClient,
    msg: msg,
  });
}
// 发送普通消息
export function sendMsg({ state, commit }, obj) {
  const nim = state.nim;
  obj = obj || {};
  let type = obj.type || "";

  switch (type) {
    case "text":
      nim.sendText({
        scene: obj.scene,
        to: obj.to,
        text: obj.text,
        done: onSendMsgDone,
        needMsgReceipt: obj.needMsgReceipt || false,
      });
      break;
    case "custom":
      nim.sendCustomMsg({
        scene: obj.scene,
        to: obj.to,
        pushContent: obj.pushContent,
        content: JSON.stringify(obj.content),
        done: onSendMsgDone,
      });
  }
}

export function sendImage(
  { state, commit },
  { scene, to, fileInput, callback }
) {
  const nim = state.nim;
  console.log(fileInput);
  nim.previewFile({
    type: "image",
    fileInput: fileInput,
    uploadprogress: function (obj) {
      callback(Object.assign(obj, { end: false }));
    },
    done: function (error, file) {
      console.log("上传image" + (!error ? "成功" : "失败"));
      // show file to the user

      if (!error) {
        var msg = nim.sendFile({
          scene: scene,
          to: to,
          file: file,
          type: "image",
          antiSpamContent: JSON.stringify({
            type: 2, // 1:文本，2：图片，3视频
            data: file.url, // 文本内容or图片地址or视频地址
          }),
          pushInfo:{
            pushPayload:JSON.stringify({
              "pushTitle": "广西人才网",
              "nim": 1,
              "custom": {
                  "PushType": "21"
              },
              "apsField": {
                  "alert": {
                      "title": "广西人才网",
                      "body": "您有一条新消息"
                  },
                  "badge": 1,
                  "ext": "ext",
                  "sound": "default"
              },
              "vivoField": {
                  "classification": 1,
                  "skipType": 4,
                  "category": "IM",
                  "skipContent": "gxrcw_qyb://com.gxrcw.activity/push_event?custom_content={\"PushID\":0,\"PushType\":21,\"Url\":\"\",\"PositionId\":\"\",\"PositionGuid\":\"\"}"
              },
              "oppoField": {
                  "channel_id": "122298",
                  "click_action_type": 5,
                  "title": "广西人才网",
                  "content": "您有一条新消息",
                  "show_start_time": 0,
                  "show_end_time": 0,
                  "click_action_url": "gxrcw_qyb://com.gxrcw.activity/push_event?custom_content={\"PushID\":0,\"PushType\":21,\"Url\":\"\",\"PositionId\":\"\",\"PositionGuid\":\"\"}"
              },
              "hwField": {
                  "importance": "NORMAL",
                  "channel_id": "122298",
                  "androidConfig": {
                      "category": "IM"
                  },
                  "click_action": {
                      "type": 1,
                      "intent": "gxrcw_qyb://com.gxrcw.activity/push_event?custom_content={\"PushID\":0,\"PushType\":21,\"Url\":\"\",\"PositionId\":\"\",\"PositionGuid\":\"\"}"
                  }
              },
              "honorField": {
                  "notification": {
                      "bigBody": "您有一条新消息",
                      "bigTitle": "广西人才网",
                      "body": "您有一条新消息",
                      "title": "广西人才网",
                      "clickAction": {
                          "type": 1,
                          "intent": "gxrcw_qyb://com.gxrcw.activity/push_event?#Intent;launchFlags=0x4000000;package=com.gxrcw.activity;component=com.gxrcw.activity/com.gxrc.qyb.ui.activity.main.MainActivity;S.custom_content=%7B%5C%22PushID%5C%22%3A0%2C%5C%22PushType%5C%22%3A21%2C%5C%22Url%5C%22%3A%5C%22%5C%22%2C%5C%22PositionId%5C%22%3A%5C%22%5C%22%2C%5C%22PositionGuid%5C%22%3A%5C%22%5C%22%7D;end"
                      },
                      "importance": "NORMAL",
                      "style": 0
                  }
              },
              "fcmField": {
                  "click_action": "",
                  "channel_id": "122298"
              },
              "channel_id": "122298",
              "notify_effect": "2",
              "intent_uri": "gxrcw_qyb://com.gxrcw.activity/push_event?custom_content={\"PushID\":0,\"PushType\":21,\"Url\":\"\",\"PositionId\":\"\",\"PositionGuid\":\"\"}"
          })
          },
          done: (error, data) => {
            callback(Object.assign({ percentage: 100 }, { end: true }));
            console.log(error, data);
            if (data.yidunAntiSpamRes) {
              const res = JSON.parse(data.yidunAntiSpamRes);
              res.content = JSON.parse(res.ext);
              console.log(res);
              if (res.suggestion == 2) {
                ElMessageBox.alert("图片不可包含色情、暴力、二维码", {
                  showCancelButton: false,
                });
              } else {
                store.commit("updateCurrSessionMsgs", {
                  type: "put",
                  msg: data,
                });

                commit("putMsg", data);
              }
            } else {
              store.commit("updateCurrSessionMsgs", {
                type: "put",
                msg: data,
              });

              commit("putMsg", data);
            }
          },
        });
      } else {
        ElMessageBox.alert("图片发送失败，请稍后再试", {
          showCancelButton: false,
        });
        console.log(error);
      }
    },
  });
}

export function sendLocalMsg({ state, commit }, obj) {
  const nim = state.nim;
  obj = obj || {};
  let type = obj.type || "";

  switch (type) {
    case "text":
      nim.sendText({
        scene: obj.scene,
        to: obj.to,
        text: obj.text,
        isLocal: true,
        done: onSendMsgDone,
        needMsgReceipt: obj.needMsgReceipt || false,
      });
      break;
    case "custom":
      nim.sendCustomMsg({
        scene: obj.scene,
        to: obj.to,
        content: JSON.stringify(obj.content),
        isLocal: true,
        done: onSendMsgDone,
      });
  }
}

// 发送文件消息
export function sendFileMsg({ state, commit }, obj) {
  const nim = state.nim;
  let { type, fileInput } = obj;
  if (!type && fileInput) {
    type = "file";
    if (/\.(png|jpg|bmp|jpeg|gif)$/i.test(fileInput.value)) {
      type = "image";
    } else if (/\.(mov|mp4|ogg|webm)$/i.test(fileInput.value)) {
      type = "video";
    }
  }
  store.dispatch("showLoading");
  const data = Object.assign(
    {
      type,
      uploadprogress: function (data) {},
      uploaderror: function () {
        fileInput.value = "";
        console && console.log("上传失败");
      },
      uploaddone: function (error, file) {
        fileInput.value = "";
      },
      beforesend: function (msg) {},
      done: function (error, msg) {
        onSendMsgDone(error, msg);
      },
    },
    obj
  );
  nim.sendFile(data);
}
// 发送机器人消息
export function sendRobotMsg({ state, commit }, obj) {
  const nim = state.nim;
  let { type, scene, to, robotAccid, content, params, target, body } = obj;
  scene = scene || "p2p";
  if (type === "text") {
    nim.sendRobotMsg({
      scene,
      to,
      robotAccid: robotAccid || to,
      content: {
        type: "text",
        content,
      },
      body,
      done: onSendMsgDone,
    });
  } else if (type === "welcome") {
    nim.sendRobotMsg({
      scene,
      to,
      robotAccid: robotAccid || to,
      content: {
        type: "welcome",
      },
      body,
      done: onSendMsgDone,
    });
  } else if (type === "link") {
    nim.sendRobotMsg({
      scene,
      to,
      robotAccid: robotAccid || to,
      content: {
        type: "link",
        params,
        target,
      },
      body,
      done: onSendMsgDone,
    });
  }
}
// 发送消息已读回执
export function sendMsgReceipt({ state, commit }) {
  // 如果有当前会话
  let currSessionId = store.state.imModules.currSessionId;
  if (currSessionId) {
    // 只有点对点消息才发已读回执
    if (util.parseSession(currSessionId).scene === "p2p") {
      let msgs = store.state.imModules.currSessionMsgs;
      const nim = state.nim;
      if (state.sessionMap[currSessionId]) {
        let lastMsg = state.sessionMap[currSessionId].lastMsg;
        nim.sendMsgReceipt({
          msg: state.sessionMap[currSessionId].lastMsg,
          done: sendMsgReceiptDone,
        });

        // 设置已读，不管是接收还是发送消息都会触发
        //lastMsg有时没有
        setRead({
          time: lastMsg ? lastMsg.time : "",
          entId: currSessionId.replace(/^p2p-ent/, ""),
        }).then((result) => {
          let { data } = result;
        });
      }
    }
  }
}

function sendMsgReceiptDone(error, obj) {
  if (error) {
    console.log("发送消息已读回执" + (!error ? "成功" : "失败"), error, obj);
  }
}

export function getHistoryMsgs({ state, commit }, obj) {
  const nim = state.nim;
  if (nim) {
    //过滤之前的记录
    let filterTime = new Date();
    //let time = new Date(`${filterTime.getFullYear()}/${filterTime.getMonth() - 2}/1`).getTime();
    let { scene, to } = obj;
    let options = {
      scene,
      to,
      reverse: false,
      asc: true,
      beginTime: 0,
      limit: obj.msgLimit || config.localMsglimit,
      done: function getHistoryMsgsDone(error, obj) {
        if (obj.msgs) {
          if (obj.msgs.length === 0) {
            commit("setNoMoreHistoryMsgs");
          } else {
            let msgs = obj.msgs.map((msg) => {
              return formatMsg(msg);
            });
            commit("updateCurrSessionMsgs", {
              type: "concat",
              msgs: msgs,
            });
          }
        }
        //store.dispatch('hideLoading')
      },
    };
    if (state.currSessionLastMsg) {
      options = Object.assign(options, {
        lastMsgId: state.currSessionLastMsg.idServer,
        endTime: state.currSessionLastMsg.time,
      });
    }
    // store.dispatch('showLoading')
    nim.getHistoryMsgs(options);
  }
}

export function resetNoMoreHistoryMsgs({ commit }) {
  commit("resetNoMoreHistoryMsgs");
}

// 继续与机器人会话交互
export function continueRobotMsg({ commit }, robotAccid) {
  commit("continueRobotMsg", robotAccid);
}
//标记已读
export function markMsgRead({ state, commit }) {
  if (state.currSessionId) {
    let msgs = state.currSessionMsgs;
    msgs = msgs.map((item) => {
      if (
        (item.scene === "p2p" && !item.isInBlackList) ||
        (item.scene === "p2p" && !item.idServer)
      ) {
        if (item.scene === "p2p" && !item.idServer) {
          //图片消息无法使用isMsgRemoteRead
          //此接口用于查询p2p消息是否已接收已读回执，目前已废弃
          //查询已读回执
          //如何判断消息是否接收已读回执？当 msg.scene === 'p2p' && msg.flow === 'out' && msg.time <= session.msgReceiptTime 时，即可认为消息已收到已读回执
          let msgReceiptTime = 0;
          const row = state.sessionlist.find(
            (item) => item.id == state.currSessionId
          );
          if (row) {
            msgReceiptTime = row.msgReceiptTime;
            if (item.time <= msgReceiptTime) {
              return {
                ...item,
                isRead: true,
              };
            }
          }
        }
        if (
          item.type !== "tip" &&
          item.flow === "out" &&
          state.nim.isMsgRemoteRead(item)
        ) {
          return { ...item, isRead: true };
        }
      }
      return item;
    });
    store.commit("updateMarkMsgRead", msgs);
  }
}
