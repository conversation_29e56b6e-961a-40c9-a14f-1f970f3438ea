import { getMajorResource } from "../../http/dictionary";

const resouce = {
    state: {
        major: [],//专业类别
    },
    mutations: {
        getMajorResource(state:any, major:object|any) {
            state.major = major;
        },
    },
    actions:{
       
        async getMajorResource(context:any) {
            if (context.state.major && context.state.major.length) return;
            let data:any = await getMajorResource('');
            if(data && Array.isArray(data.data)) {
                context.commit('getMajorResource', data.data);
            } else {
                console.error('Received data is not an array as expected');
            }


        },
    },
    plugins: [(store:any) => {
        store.subscribe((mutation:any, state:any) => {
        });
    }]
}

export default resouce;