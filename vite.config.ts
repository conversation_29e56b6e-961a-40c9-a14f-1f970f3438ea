import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from "path"
import copy from 'rollup-plugin-copy'
import { codeInspectorPlugin } from 'code-inspector-plugin';
//  const path = require('path')
// https://vitejs.dev/config/
export default defineConfig({
  // define: {
  //   'development': {
  //     'BASE_API': '/imApi',
  //     IM_API: '/imApi'
  //   },
  //   'process.env': {
  //     'BASE_API': "http://localhost:8021/api",
  //   }
  // },
  plugins: [
    vue(),
    copy({
      targets: [
        { src: 'src/IM/lib/NIM_Web_SDK_v8.9.128.js', dest: 'dist/public' },
        { src: 'src/IM/lib/NIM_Web_WebRTC_v8.0.0.js', dest: 'dist/public' }
      ]
    }),
    codeInspectorPlugin({
      bundler: 'vite',
      editor: 'cursor'
    })
  ],
  server: {
    //  host: 'my2.tgxrc.com',
    host: 'my2.tgxrc.com',
    port: 8086,
    // https: true,
    proxy: {
      '/api': {
        target: 'http://************:5000',
        changeOrigin: true,
        ws: true,
        //rewrite: (path) => path.replace(/^\/api/, ''),
      },
      '/home': {
        target: 'http://************:5020',
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/home/<USER>'')
      },
      '/imApi': {
        target: 'http://************:8066',
        ws: true,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/imApi/, '')
      }
    },
    open: true,
    fs: {
      strict: false
    }
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
      "#": path.resolve(__dirname, "node_modules"),
    }
  },
  css: {
    //* css模块化
    modules: { // css模块化 文件以.module.[css|less|scss]结尾
      generateScopedName: '[name]__[local]___[hash:base64:5]',
      hashPrefix: 'prefix',
    },
    //* 预编译支持less
    preprocessorOptions: {
      less: {
        // 支持内联 JavaScript
        javascriptEnabled: true,
      },
    },
  }
})
