const { generateApi } = require('swagger-typescript-api');
const path = require('path');
const fs = require('fs');

generateApi({
  name: 'app.ts',
  output: path.resolve(process.cwd(), './src/http/app'),
  url: 'http://************:5000/swagger/app/swagger.json',
  templates: path.resolve(__dirname, './api-templates-app'),
  modular: true,
  httpClientType: 'axios', // or "fetch"
  defaultResponseAsSuccess: true,
  generateRouteTypes: false,
  generateResponses: true,
  generateClient: true,
  disableStrictSSL: true,
  toJS: false,
  extractRequestParams: true,
  extractRequestBody: false,
  prettier: {
    printWidth: 100,
    tabWidth: 2,
    trailingComma: 'all',
    parser: 'typescript',
  },
  defaultResponseType: 'void',
  singleHttpClient: true,
  cleanOutput: true,
  enumNamesAsValues: false,
  moduleNameFirstTag: true,
  generateUnionEnums: false,

  extraTemplates: [],
  hooks: {
    onCreateComponent: (component) => {},
    onCreateRequestParams: (rawType) => {},
    onCreateRoute: (routeData) => {
      routeData.response.type = routeData.response.type
        .replace('RestfulResultList', '')
        .replace('RestfulResult', '');
    },
    onCreateRouteName: (routeNameInfo, rawRouteInfo) => {},
    onFormatRouteName: (routeInfo, templateRouteName) => {
      // routeInfo.responsesTypes.forEach((item) => {
      //   item.type = item.type.replace('RestfulResultList', '').replace('RestfulResult', '')
      // })
      // const { moduleName } = routeInfo
      // return (templateRouteName = templateRouteName.replace(moduleName, ''))
    },
    onFormatTypeName: (typeName, rawTypeName) => {},
    onInit: (configuration) => {},
    onParseSchema: (originalSchema, parsedSchema) => {},
    onPrepareConfig: (currentConfiguration) => {
      const { config } = currentConfiguration;
      const { constants } = config;

      return {
        ...currentConfiguration,
        config: {
          ...config,
          constants: {
            ...constants,
            SERVER_NAME: 'test',
            SERVER_URL: '************:5020',
          },
        },
      };
    },
  },
});
