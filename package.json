{"name": "pc-new", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build --mode production", "preview": "vite preview", "buildtest": "vite build --mode testproduction", "buildDev": "vite build --mode test"}, "dependencies": {"@element-plus/icons": "0.0.11", "@element-plus/icons-vue": "^0.2.4", "@types/ali-oss": "^6.16.2", "@vitejs/plugin-legacy": "^1.7.1", "ali-oss": "^6.16.0", "axios": "^0.24.0", "babel-plugin-import": "^1.13.3", "element-plus": "1.1.0-beta.19", "gxrcw-ui": "^1.2.5", "js-cookie": "^3.0.1", "less": "^4.1.2", "less-loader": "^10.2.0", "path": "^0.12.7", "pc-new": "file:", "rollup-plugin-copy": "^3.4.0", "swagger-typescript-api": "^13.0.23", "vite-plugin-style-import": "^1.4.1", "vue": "3.2.25", "vue-clipboard3": "^1.0.1", "vue-router": "^4.0.9", "vuex": "^4.0.2", "dompurify": "^3.0.8"}, "devDependencies": {"@types/node": "^17.0.8", "@vitejs/plugin-vue": "^2.0.0", "code-inspector-plugin": "^0.20.10", "typescript": "^4.4.4", "vite": "^2.7.2", "vue-tsc": "^0.29.8", "@types/dompurify": "^3.0.5"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}